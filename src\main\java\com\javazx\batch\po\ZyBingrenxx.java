package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 住院_病人信息
 * @TableName ZY_BINGRENXX
 */
@TableName(value ="ZY_BINGRENXX")
@Data
@NoArgsConstructor
public class ZyBingrenxx implements Serializable {
    /**
     * 病人住院ID
     */
    @TableId(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 社保编号
     */
    @TableField(value = "SHEBAOBH")
    private String SHEBAOBH;

    /**
     * 医保卡号
     */
    @TableField(value = "YIBAOKH")
    private String YIBAOKH;

    /**
     * 就诊卡号
     */
    @TableField(value = "JIUZHENKH")
    private String JIUZHENKH;

    /**
     * 社区编号
     */
    @TableField(value = "SHEQUBH")
    private String SHEQUBH;

    /**
     * 费用类别
     */
    @TableField(value = "FEIYONGLB")
    private String FEIYONGLB;

    /**
     * 费用性质
     */
    @TableField(value = "FEIYONGXZ")
    private String FEIYONGXZ;

    /**
     * 优惠类别
     */
    @TableField(value = "YOUHUILB")
    private String YOUHUILB;

    /**
     * 公费证号
     */
    @TableField(value = "GONGFEIZH")
    private String GONGFEIZH;

    /**
     * 公费单位
     */
    @TableField(value = "GONGFEIDW")
    private String GONGFEIDW;

    /**
     * 公费单位名称
     */
    @TableField(value = "GONGFEIDWMC")
    private String GONGFEIDWMC;

    /**
     * 姓名
     */
    @TableField(value = "XINGMING")
    private String XINGMING;

    /**
     * 输入码1
     */
    @TableField(value = "SHURUMA1")
    private String SHURUMA1;

    /**
     * 输入码2
     */
    @TableField(value = "SHURUMA2")
    private String SHURUMA2;

    /**
     * 输入码3
     */
    @TableField(value = "SHURUMA3")
    private String SHURUMA3;

    /**
     * 性别
     */
    @TableField(value = "XINGBIE")
    private String XINGBIE;

    /**
     * 年龄单位
     */
    @TableField(value = "NIANLINGDW")
    private String NIANLINGDW;

    /**
     * 身份证号
     */
    @TableField(value = "SHENFENZH")
    private String SHENFENZH;

    /**
     * 出生日期
     */
    @TableField(value = "CHUSHENGRQ")
    private LocalDateTime CHUSHENGRQ;

    /**
     * 工作单位
     */
    @TableField(value = "GONGZUODW")
    private String GONGZUODW;

    /**
     * 单位电话
     */
    @TableField(value = "DANWEIDH")
    private String DANWEIDH;

    /**
     * 单位邮编
     */
    @TableField(value = "DANWEIYB")
    private String DANWEIYB;

    /**
     * 家庭地址
     */
    @TableField(value = "JIATINGDZ")
    private String JIATINGDZ;

    /**
     * 家庭电话
     */
    @TableField(value = "JIATINGDH")
    private String JIATINGDH;

    /**
     * 家庭邮编
     */
    @TableField(value = "JIATINGYB")
    private String JIATINGYB;

    /**
     * 血型
     */
    @TableField(value = "XUEXING")
    private String XUEXING;

    /**
     * 婚姻
     */
    @TableField(value = "HUNYIN")
    private String HUNYIN;

    /**
     * 职业
     */
    @TableField(value = "ZHIYE")
    private String ZHIYE;

    /**
     * 国籍
     */
    @TableField(value = "GUOJI")
    private String GUOJI;

    /**
     * 民族
     */
    @TableField(value = "MINZU")
    private String MINZU;

    /**
     * 省份
     */
    @TableField(value = "SHENGFEN")
    private String SHENGFEN;

    /**
     * 乡镇街道
     */
    @TableField(value = "XIANGZHENJD")
    private String XIANGZHENJD;

    /**
     * 市地区
     */
    @TableField(value = "SHIDIQU")
    private String SHIDIQU;

    /**
     * 籍贯
     */
    @TableField(value = "JIGUAN")
    private String JIGUAN;

    /**
     * 出生地
     */
    @TableField(value = "CHUSHENGDI")
    private String CHUSHENGDI;

    /**
     * 邮编
     */
    @TableField(value = "YOUBIAN")
    private String YOUBIAN;

    /**
     * 联系人
     */
    @TableField(value = "LIANXIREN")
    private String LIANXIREN;

    /**
     * 关系
     */
    @TableField(value = "GUANXI")
    private String GUANXI;

    /**
     * 联系人地址
     */
    @TableField(value = "LIANXIRDZ")
    private String LIANXIRDZ;

    /**
     * 联系人电话
     */
    @TableField(value = "LIANXIRDH")
    private String LIANXIRDH;

    /**
     * 联系人邮编
     */
    @TableField(value = "LIANXIRYB")
    private String LIANXIRYB;

    /**
     * 既往史
     */
    @TableField(value = "JIWANGSHI")
    private String JIWANGSHI;

    /**
     * 过敏史
     */
    @TableField(value = "GUOMINSHI")
    private String GUOMINSHI;

    /**
     * 区域
     */
    @TableField(value = "QUYU")
    private String QUYU;

    /**
     * 外地病人标志
     */
    @TableField(value = "WAIDIBRBZ")
    private Integer WAIDIBRBZ;

    /**
     * 建档人
     */
    @TableField(value = "JIANDANGREN")
    private String JIANDANGREN;

    /**
     * 建档日期
     */
    @TableField(value = "JIANDANGRQ")
    private LocalDateTime JIANDANGRQ;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private LocalDateTime XIUGAISJ;

    /**
     * 婴儿标志
     */
    @TableField(value = "YINGERBZ")
    private Integer YINGERBZ;

    /**
     * 住院次数
     */
    @TableField(value = "ZHUYUANCS")
    private BigDecimal ZHUYUANCS;

    /**
     * 入院日期
     */
    @TableField(value = "RUYUANRQ")
    private LocalDateTime RUYUANRQ;

    /**
     * 预出院日期
     */
    @TableField(value = "YUCHUYRQ")
    private LocalDateTime YUCHUYRQ;

    /**
     * 出院日期
     */
    @TableField(value = "CHUYUANRQ")
    private LocalDateTime CHUYUANRQ;

    /**
     * 在院状态：0在院，1预出院，2出院
     */
    @TableField(value = "ZAIYUANZT")
    private String ZAIYUANZT;

    /**
     * 入院途径 急诊2
门诊1
其他医疗机构转入3
其他 9
     */
    @TableField(value = "RUYUANTJ")
    private String RUYUANTJ;

    /**
     * 入院科室
     */
    @TableField(value = "RUYUANKS")
    private String RUYUANKS;

    /**
     * 入院病区
     */
    @TableField(value = "RUYUANBQ")
    private String RUYUANBQ;

    /**
     * 入院床位HR3-32668(319040)
     */
    @TableField(value = "RUYUANCW")
    private String RUYUANCW;

    /**
     * 当前科室
     */
    @TableField(value = "DANGQIANKS")
    private String DANGQIANKS;

    /**
     * 当前病区
     */
    @TableField(value = "DANGQIANBQ")
    private String DANGQIANBQ;

    /**
     * 当前床位
     */
    @TableField(value = "DANGQIANCW")
    private String DANGQIANCW;

    /**
     * 管理科室
     */
    @TableField(value = "GUANLIKS")
    private String GUANLIKS;

    /**
     * 借床标志
     */
    @TableField(value = "JIECHUANGBZ")
    private Integer JIECHUANGBZ;

    /**
     * 离院去向
     */
    @TableField(value = "LIYUANQX")
    private String LIYUANQX;

    /**
     * 门诊诊断代码
     */
    @TableField(value = "MENZHENZDDM")
    private String MENZHENZDDM;

    /**
     * 门诊诊断名称
     */
    @TableField(value = "MENZHENZDMC")
    private String MENZHENZDMC;

    /**
     * 入院诊断代码
     */
    @TableField(value = "RUYUANZDDM")
    private String RUYUANZDDM;

    /**
     * 入院诊断名称
     */
    @TableField(value = "RUYUANZDMC")
    private String RUYUANZDMC;

    /**
     * 出院诊断代码
     */
    @TableField(value = "CHUYUANZDDM")
    private String CHUYUANZDDM;

    /**
     * 出院诊断名称
     */
    @TableField(value = "CHUYUANZDMC")
    private String CHUYUANZDMC;

    /**
     * 出院诊断代码2
     */
    @TableField(value = "CHUYUANZDDM2")
    private String CHUYUANZDDM2;

    /**
     * 出院诊断名称2
     */
    @TableField(value = "CHUYUANZDMC2")
    private String CHUYUANZDMC2;

    /**
     * 出院诊断名称3
     */
    @TableField(value = "CHUYUANZDMC3")
    private String CHUYUANZDMC3;

    /**
     * 出院诊断代码3
     */
    @TableField(value = "CHUYUANZDDM3")
    private String CHUYUANZDDM3;

    /**
     * 家庭病床标志
     */
    @TableField(value = "JIATINGBCBZ")
    private Integer JIATINGBCBZ;

    /**
     * 病情
     */
    @TableField(value = "BINGQING")
    private String BINGQING;

    /**
     * 分娩
     */
    @TableField(value = "FENMIAN")
    private String FENMIAN;

    /**
     * 上传标志
     */
    @TableField(value = "SHANGCHUANBZ")
    private Integer SHANGCHUANBZ;

    /**
     * 上传日期
     */
    @TableField(value = "SHANGCHUANRQ")
    private LocalDateTime SHANGCHUANRQ;

    /**
     * 担保人
     */
    @TableField(value = "DANBAOREN")
    private String DANBAOREN;

    /**
     * 担保金额
     */
    @TableField(value = "DANBAOJE")
    private BigDecimal DANBAOJE;

    /**
     * 家长姓名
     */
    @TableField(value = "JIAZHANGXM")
    private String JIAZHANGXM;

    /**
     * 住院号
     */
    @TableField(value = "ZHUYUANHAO")
    private String ZHUYUANHAO;

    /**
     * 病案号
     */
    @TableField(value = "BINGANHAO")
    private String BINGANHAO;

    /**
     * 病人ID
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 结算序号
     */
    @TableField(value = "JIESUANXH")
    private Integer JIESUANXH;

    /**
     * 年龄
     */
    @TableField(value = "NIANLING")
    private Integer NIANLING;

    /**
     * 门诊医师
     */
    @TableField(value = "MENZHENYS")
    private String MENZHENYS;

    /**
     * 收治医师
     */
    @TableField(value = "SHOUZHIYS")
    private String SHOUZHIYS;

    /**
     * 住院医师
     */
    @TableField(value = "ZHUYUANYS")
    private String ZHUYUANYS;

    /**
     * 主治医师
     */
    @TableField(value = "ZHUZHIYS")
    private String ZHUZHIYS;

    /**
     * 预约ID
     */
    @TableField(value = "YUYUEID")
    private String YUYUEID;

    /**
     * 审核标志
     */
    @TableField(value = "SHENHEBZ")
    private Integer SHENHEBZ;

    /**
     * 器官移植标志
     */
    @TableField(value = "QIGUANYZBZ")
    private Integer QIGUANYZBZ;

    /**
     * 交易流水号
     */
    @TableField(value = "JIAOYILSH")
    private String JIAOYILSH;

    /**
     * 个人编号
     */
    @TableField(value = "GERENBH")
    private String GERENBH;

    /**
     * IC
     */
    @TableField(value = "IC")
    private String IC;

    /**
     * 市民卡外卡号
     */
    @TableField(value = "SHIMINKWKH")
    private String SHIMINKWKH;

    /**
     * 医疗组ID
     */
    @TableField(value = "YILIAOZID")
    private String YILIAOZID;

    /**
     * 医疗组名
     */
    @TableField(value = "YILIAOZM")
    private String YILIAOZM;

    /**
     * 入科日期
     */
    @TableField(value = "RUKERQ")
    private LocalDateTime RUKERQ;

    /**
     * 入科标志
     */
    @TableField(value = "RUKEBZ")
    private Integer RUKEBZ;

    /**
     * 占床标志
     */
    @TableField(value = "ZHANCHUANGBZ")
    private Integer ZHANCHUANGBZ;

    /**
     * 入院科室名称
     */
    @TableField(value = "RUYUANKSMC")
    private String RUYUANKSMC;

    /**
     * 入院病区名称
     */
    @TableField(value = "RUYUANBQMC")
    private String RUYUANBQMC;

    /**
     * 当前科室名称
     */
    @TableField(value = "DANGQIANKSMC")
    private String DANGQIANKSMC;

    /**
     * 当前病区名称
     */
    @TableField(value = "DANGQIANBQMC")
    private String DANGQIANBQMC;

    /**
     * 管理科室名称
     */
    @TableField(value = "GUANLIKSMC")
    private String GUANLIKSMC;

    /**
     * 门诊医师名称
     */
    @TableField(value = "MENZHENYSXM")
    private String MENZHENYSXM;

    /**
     * 收治医师名称
     */
    @TableField(value = "SHOUZHIYSXM")
    private String SHOUZHIYSXM;

    /**
     * 住院医师名称
     */
    @TableField(value = "ZHUYUANYSXM")
    private String ZHUYUANYSXM;

    /**
     * 主治医师名称
     */
    @TableField(value = "ZHUZHIYSXM")
    private String ZHUZHIYSXM;

    /**
     * 病理生理状态
     */
    @TableField(value = "BINGLISLZT")
    private String BINGLISLZT;

    /**
     * 费用性质转换标志
     */
    @TableField(value = "FEIYONGXZZHBZ")
    private Integer FEIYONGXZZHBZ;

    /**
     * 费用性质转换人
     */
    @TableField(value = "FEIYONGXZZHR")
    private String FEIYONGXZZHR;

    /**
     * 费用性质转换日期
     */
    @TableField(value = "FEIYONGXZZHRQ")
    private LocalDateTime FEIYONGXZZHRQ;

    /**
     * 医保医疗类别HR3-19389(221569)
     */
    @TableField(value = "YIBAOYLLB")
    private String YIBAOYLLB;

    /**
     * 特殊病种标志
     */
    @TableField(value = "TESHUBZBZ")
    private Integer TESHUBZBZ;

    /**
     * 特殊病种名称
     */
    @TableField(value = "TESHUBZMC")
    private String TESHUBZMC;

    /**
     * 特殊病种编码
     */
    @TableField(value = "TESHUBZBM")
    private String TESHUBZBM;

    /**
     * 门诊中医诊断代码
     */
    @TableField(value = "MENZHENZYZDDM")
    private String MENZHENZYZDDM;

    /**
     * 门诊中医诊断名称
     */
    @TableField(value = "MENZHENZYZDMC")
    private String MENZHENZYZDMC;

    /**
     * 入院中医诊断代码
     */
    @TableField(value = "RUYUANZYZDDM")
    private String RUYUANZYZDDM;

    /**
     * 入院中医诊断名称
     */
    @TableField(value = "RUYUANZYZDMC")
    private String RUYUANZYZDMC;

    /**
     * 入院前经外院诊断标志
     */
    @TableField(value = "WAIYUANZDBZ")
    private Integer WAIYUANZDBZ;

    /**
     * 入院中医症候代码
     */
    @TableField(value = "RUYUANZYZHDM")
    private String RUYUANZYZHDM;

    /**
     * 入院中医症候名称
     */
    @TableField(value = "RUYUANZYZHMC")
    private String RUYUANZYZHMC;

    /**
     * 药品不转移标志
     */
    @TableField(value = "YAOPINBZYBZ")
    private Integer YAOPINBZYBZ;

    /**
     * 转病区标志【1.转病区；2.转床】
     */
    @TableField(value = "ZHUANBINGQBZ")
    private Integer ZHUANBINGQBZ;

    /**
     * 母亲住院ID
     */
    @TableField(value = "MUQINZYID")
    private String MUQINZYID;

    /**
     * 产妇标志
     */
    @TableField(value = "CHANFUBZ")
    private Integer CHANFUBZ;

    /**
     * 临床路径标志：0未入径 1入径 2正常出径 3中途出径
     */
    @TableField(value = "LINCHUANGLJBZ")
    private Integer LINCHUANGLJBZ;

    /**
     * 取消入院标志
     */
    @TableField(value = "QUXIAORYBZ")
    private Integer QUXIAORYBZ;

    /**
     * 请假标志
     */
    @TableField(value = "QINGJIABZ")
    private Integer QINGJIABZ;

    /**
     * 原病人住院ID
     */
    @TableField(value = "YUANBINGRZYID")
    private String YUANBINGRZYID;

    /**
     * 绿色通道标志
     */
    @TableField(value = "LVSETDBZ")
    private Integer LVSETDBZ;

    /**
     * 绿色通道开启日期
     */
    @TableField(value = "LVSETDKQRQ")
    private LocalDateTime LVSETDKQRQ;

    /**
     * 医保明细反交易记录数
     */
    @TableField(value = "YIBAOMXFJYJLS")
    private Integer YIBAOMXFJYJLS;

    /**
     * 特殊标志
     */
    @TableField(value = "TESHUBZ")
    private Integer TESHUBZ;

    /**
     * 医疗卡号内部医疗账户号
     */
    @TableField(value = "YILIAOKH")
    private String YILIAOKH;

    /**
     * 介绍人
     */
    @TableField(value = "JIESHAOREN")
    private String JIESHAOREN;

    /**
     * 婴儿数量
     */
    @TableField(value = "YINGERSL")
    private Integer YINGERSL;

    /**
     * 优惠类别列表
     */
    @TableField(value = "YOUHUILBLB")
    private String YOUHUILBLB;

    /**
     * 取消预出院日期
     */
    @TableField(value = "QUXIAOYCYRQ")
    private LocalDateTime QUXIAOYCYRQ;

    /**
     * 入院诊断代码2
     */
    @TableField(value = "RUYUANZDDM2")
    private String RUYUANZDDM2;

    /**
     * 入院诊断名称2
     */
    @TableField(value = "RUYUANZDMC2")
    private String RUYUANZDMC2;

    /**
     * 入院诊断代码3
     */
    @TableField(value = "RUYUANZDDM3")
    private String RUYUANZDDM3;

    /**
     * 入院诊断名称3
     */
    @TableField(value = "RUYUANZDMC3")
    private String RUYUANZDMC3;

    /**
     * 医保病人信息
     */
    @TableField(value = "YIBAOBRXX")
    private String YIBAOBRXX;

    /**
     * 费用类别2
     */
    @TableField(value = "FEIYONGLB2")
    private String FEIYONGLB2;

    /**
     * 费用性质2
     */
    @TableField(value = "FEIYONGXZ2")
    private String FEIYONGXZ2;

    /**
     * 医疗救助登记标注
     */
    @TableField(value = "YILIAOJZDJBZ")
    private Integer YILIAOJZDJBZ;

    /**
     * 字符1医保
     */
    @TableField(value = "ZHIFU1YB")
    private String ZHIFU1YB;

    /**
     * 字符2医保
     */
    @TableField(value = "ZHIFU2YB")
    private String ZHIFU2YB;

    /**
     * 字符3医保
     */
    @TableField(value = "ZHIFU3YB")
    private String ZHIFU3YB;

    /**
     * 字符4医保
     */
    @TableField(value = "ZHIFU4YB")
    private String ZHIFU4YB;

    /**
     * 字符5医保
     */
    @TableField(value = "ZHIFU5YB")
    private String ZHIFU5YB;

    /**
     * 字符6医保
     */
    @TableField(value = "ZHIFU6YB")
    private String ZHIFU6YB;

    /**
     * 字符7医保
     */
    @TableField(value = "ZHIFU7YB")
    private String ZHIFU7YB;

    /**
     * 字符8医保（日间手术名称）
     */
    @TableField(value = "ZHIFU8YB")
    private String ZHIFU8YB;

    /**
     * 字符9医保
     */
    @TableField(value = "ZHIFU9YB")
    private String ZHIFU9YB;

    /**
     * 字符10医保（日间手术代码）
     */
    @TableField(value = "ZHIFU10YB")
    private String ZHIFU10YB;

    /**
     * 字符11医保
     */
    @TableField(value = "ZHIFU11YB")
    private String ZHIFU11YB;

    /**
     * 字符12医保
     */
    @TableField(value = "ZHIFU12YB")
    private String ZHIFU12YB;

    /**
     * 数值1医保
     */
    @TableField(value = "SHUZHI1YB")
    private BigDecimal SHUZHI1YB;

    /**
     * 数值2医保
     */
    @TableField(value = "SHUZHI2YB")
    private BigDecimal SHUZHI2YB;

    /**
     * 数值3医保
     */
    @TableField(value = "SHUZHI3YB")
    private BigDecimal SHUZHI3YB;

    /**
     * 数值4医保
     */
    @TableField(value = "SHUZHI4YB")
    private BigDecimal SHUZHI4YB;

    /**
     * 数值5医保
     */
    @TableField(value = "SHUZHI5YB")
    private BigDecimal SHUZHI5YB;

    /**
     * 数值6医保
     */
    @TableField(value = "SHUZHI6YB")
    private BigDecimal SHUZHI6YB;

    /**
     * 数值7医保
     */
    @TableField(value = "SHUZHI7YB")
    private BigDecimal SHUZHI7YB;

    /**
     * 数值8医保
     */
    @TableField(value = "SHUZHI8YB")
    private BigDecimal SHUZHI8YB;

    /**
     * 数值9医保
     */
    @TableField(value = "SHUZHI9YB")
    private BigDecimal SHUZHI9YB;

    /**
     * 数值10医保
     */
    @TableField(value = "SHUZHI10YB")
    private BigDecimal SHUZHI10YB;

    /**
     * 医保出院登记标志145122
     */
    @TableField(value = "YIBAOCYDJBZ")
    private Integer YIBAOCYDJBZ;

    /**
     * 医中医疗标志HR3-10998(149753)
     */
    @TableField(value = "YIZHONGYLBZ")
    private Integer YIZHONGYLBZ;

    /**
     * 医中医疗类别HR3-10998(149753)
     */
    @TableField(value = "YIZHONGYLLB")
    private String YIZHONGYLLB;

    /**
     * 医中医疗协议金额HR3-10998(149753)
     */
    @TableField(value = "YIZHONGYLXYJE")
    private BigDecimal YIZHONGYLXYJE;

    /**
     * 医中医疗备注说明HR3-10998(149753)
     */
    @TableField(value = "YIZHONGYLBZSM")
    private String YIZHONGYLBZSM;

    /**
     * 分娩日期HR3-11436(153413)
     */
    @TableField(value = "FENMIANRQ")
    private LocalDateTime FENMIANRQ;

    /**
     * 并发症HR3-11436(153413)
     */
    @TableField(value = "BINGFAZHENG")
    private String BINGFAZHENG;

    /**
     * 补助卡编号HR3-11436(153413)
     */
    @TableField(value = "BUZHUKBH")
    private String BUZHUKBH;

    /**
     * 医保卡号2HR3-11582
     */
    @TableField(value = "YIBAOKH2")
    private String YIBAOKH2;

    /**
     * IC2HR3-11582
     */
    @TableField(value = "IC2")
    private String IC2;

    /**
     * 取消预出院原因1.费用处理2.持续治疗HR3-12279(160965)
     */
    @TableField(value = "QUXIAOYCYYY")
    private String QUXIAOYCYYY;

    /**
     * 备注HR3-12759(165878)
     */
    @TableField(value = "BEIZHU")
    private String BEIZHU;

    /**
     * 责任护士HR3-13246(171002)
     */
    @TableField(value = "ZERENHS")
    private String ZERENHS;

    /**
     * 责任护士姓名HR3-13246(171002)
     */
    @TableField(value = "ZERENHSXM")
    private String ZERENHSXM;

    /**
     * 纠纷病人标志HR3-13272(171228)
     */
    @TableField(value = "JIUFENBRBZ")
    private Integer JIUFENBRBZ;

    /**
     * 证件类型HR3-13459(173157)
     */
    @TableField(value = "ZHENGJIANLX")
    private String ZHENGJIANLX;

    /**
     * 监护人身份证号HR3-14834(187391)
     */
    @TableField(value = "JIANHURENSFZH")
    private String JIANHURENSFZH;

    /**
     * 隔离类型
     */
    @TableField(value = "GELILX")
    private String GELILX;

    /**
     * 上次预出院日期HR3-16239(199856)
     */
    @TableField(value = "LASTYUCHUYRQ")
    private LocalDateTime LASTYUCHUYRQ;

    /**
     * 上次出院日期HR3-16239(199856)
     */
    @TableField(value = "LASTCHUYUANRQ")
    private LocalDateTime LASTCHUYUANRQ;

    /**
     * 预入院标志HR3-17203(206253)
     */
    @TableField(value = "YURUYUANBZ")
    private Integer YURUYUANBZ;

    /**
     * 入院登记类型HR3-17801(209933)
     */
    @TableField(value = "RUYUANDJLX")
    private String RUYUANDJLX;

    /**
     * 县区HR3-17804
     */
    @TableField(value = "XIANQU")
    private String XIANQU;

    /**
     * 年龄1HR3-17941(210691)
     */
    @TableField(value = "NIANLING1")
    private Integer NIANLING1;

    /**
     * 年龄单位1HR3-17941(210691)
     */
    @TableField(value = "NIANLINGDW1")
    private String NIANLINGDW1;

    /**
     * 工伤康复登记号HR3-17763(209699)
     */
    @TableField(value = "GONGSHANGKFDJH")
    private String GONGSHANGKFDJH;

    /**
     * 工伤康复日期HR3-17763(209699)
     */
    @TableField(value = "GONGSHANGKFRQ")
    private LocalDateTime GONGSHANGKFRQ;

    /**
     * 临床路径单病种标志HR3-18946(217335)
     */
    @TableField(value = "LINCHUANGLJDBZBZ")
    private Integer LINCHUANGLJDBZBZ;

    /**
     * 身高CM--HR3-18912(217153)
     */
    @TableField(value = "SHENGAO")
    private BigDecimal SHENGAO;

    /**
     * 体重KG--HR3-18912(217153)
     */
    @TableField(value = "TIZHONG")
    private BigDecimal TIZHONG;

    /**
     * ADL评分--HR3-18912(217153)
     */
    @TableField(value = "ADLSCORE")
    private String ADLSCORE;

    /**
     * 审核人HR3-19101(218437)
     */
    @TableField(value = "SHENHEREN")
    private String SHENHEREN;

    /**
     * 审核日期HR3-19101(218437)
     */
    @TableField(value = "SHENHERQ")
    private LocalDateTime SHENHERQ;

    /**
     * 挂账标志HR3-19485(222557)
     */
    @TableField(value = "GUAZHANGBZ")
    private Integer GUAZHANGBZ;

    /**
     * 证件扫描文件HR3-19769(226192)
     */
    @TableField(value = "ZHENGJIANSMWJ")
    private String ZHENGJIANSMWJ;

    /**
     * 保密级别[1、保密，0、否]HR3-21300(244424)
     */
    @TableField(value = "BAOMIJB")
    private String BAOMIJB;

    /**
     * 户口地址HR3-20470(237669)
     */
    @TableField(value = "HUKOUDZ")
    private String HUKOUDZ;

    /**
     * 户口省份HR3-20470(237669)
     */
    @TableField(value = "HUKOUSF")
    private String HUKOUSF;

    /**
     * 户口市地区HR3-20470(237669)
     */
    @TableField(value = "HUKOUSDQ")
    private String HUKOUSDQ;

    /**
     * 户口县区HR3-20470(237669)
     */
    @TableField(value = "HUKOUXQ")
    private String HUKOUXQ;

    /**
     * 户口乡镇街道HR3-20470(237669)
     */
    @TableField(value = "HUKOUXZJD")
    private String HUKOUXZJD;

    /**
     * 家庭地址类别 HR3-20955(241982) 
     */
    @TableField(value = "JIATINGDZLB")
    private String JIATINGDZLB;

    /**
     * 户口地址类别 HR3-20955(241982) 
     */
    @TableField(value = "HUKOUDZLB")
    private String HUKOUDZLB;

    /**
     * 健康卡号HR3-20873(241354)
     */
    @TableField(value = "JIANKANGKH")
    private String JIANKANGKH;

    /**
     * 肇事肇祸标志HR3BY-14365(248467)
     */
    @TableField(value = "ZHAOSHIZHBZ")
    private Integer ZHAOSHIZHBZ;

    /**
     * 健康卡信息HR3-22272(251934)
     */
    @TableField(value = "JIANKANGKXX")
    private String JIANKANGKXX;

    /**
     * 日间住院标志HR3-22292(252104)
     */
    @TableField(value = "RIJIANZYBZ")
    private Integer RIJIANZYBZ;

    /**
     * 床位占用标志HR3-20439(237322)
     */
    @TableField(value = "CHUANGWEIZYBZ")
    private Integer CHUANGWEIZYBZ;

    /**
     * 护士长HR3-22460(253357)
     */
    @TableField(value = "HUSHIZHANG")
    private String HUSHIZHANG;

    /**
     * 护士长姓名HR3-22460(253357)
     */
    @TableField(value = "HUSHIZXM")
    private String HUSHIZXM;

    /**
     * 急诊优先标志HR3-21783(248182)
     */
    @TableField(value = "JIZHENYXBZ")
    private Integer JIZHENYXBZ;

    /**
     * 预出院系统时间(257488)
     */
    @TableField(value = "YUCHUYXTSJ")
    private LocalDateTime YUCHUYXTSJ;

    /**
     * 体表面积HR3-23298(258669)
     */
    @TableField(value = "TIBIAOMJ")
    private BigDecimal TIBIAOMJ;

    /**
     * 意外伤害标志0-否,1-是HR3-24502(265973)
     */
    @TableField(value = "YIWAISHBZ")
    private String YIWAISHBZ;

    /**
     * 出生地省份HR3-24641(267014)
     */
    @TableField(value = "CHUSHENGDSF")
    private String CHUSHENGDSF;

    /**
     * 出生地市地区HR3-24641(267014)
     */
    @TableField(value = "CHUSHENGDSDQ")
    private String CHUSHENGDSDQ;

    /**
     * 出生地县区HR3-24641(267014)
     */
    @TableField(value = "CHUSHENGDXQ")
    private String CHUSHENGDXQ;

    /**
     * 籍贯省份HR3-24641(267014)
     */
    @TableField(value = "JIGUANSF")
    private String JIGUANSF;

    /**
     * 籍贯市地区HR3-24641(267014)
     */
    @TableField(value = "JIGUANSDQ")
    private String JIGUANSDQ;

    /**
     * 现住址省份HR3-24641(267014)
     */
    @TableField(value = "XIANZHUZSF")
    private String XIANZHUZSF;

    /**
     * 现住址市地区HR3-24641(267014)
     */
    @TableField(value = "XIANZHUZSDQ")
    private String XIANZHUZSDQ;

    /**
     * 现住址县区HR3-24641(267014)
     */
    @TableField(value = "XIANZHUZXQ")
    private String XIANZHUZXQ;

    /**
     * 现住址其他HR3-24641(267014)
     */
    @TableField(value = "XIANZHUZQT")
    private String XIANZHUZQT;

    /**
     * 户口地址其他HR3-24641(267014)
     */
    @TableField(value = "HUKOUDZQT")
    private String HUKOUDZQT;

    /**
     * 消息来源HR3-24809(268182)
     */
    @TableField(value = "XIAOXILY")
    private String XIAOXILY;

    /**
     * 网络服务标志HR3-25562(273650)
     */
    @TableField(value = "WANGLUOFWBZ")
    private Integer WANGLUOFWBZ;

    /**
     * 大病标志HR3-23419(259519)
     */
    @TableField(value = "DABINGBZ")
    private Integer DABINGBZ;

    /**
     * 是否出示身份证
     */
    @TableField(value = "SHIFOUCSSFZ")
    private Integer SHIFOUCSSFZ;

    /**
     * 孕次HR3-26532(279271)
     */
    @TableField(value = "YUNCI")
    private String YUNCI;

    /**
     * 产次HR3-26532(279271)
     */
    @TableField(value = "CHANCI")
    private String CHANCI;

    /**
     * 胎次HR3-26532(279271)
     */
    @TableField(value = "TAICI")
    private String TAICI;

    /**
     * 预产期HR3-26532(279271)
     */
    @TableField(value = "YUCHANQI")
    private String YUCHANQI;

    /**
     * 区域编号HR3-26532(279271)
     */
    @TableField(value = "QUYUBH")
    private String QUYUBH;

    /**
     * 末次月经HR3-26532(279271)
     */
    @TableField(value = "MOCIYJ")
    private String MOCIYJ;

    /**
     * 免费券类型HR3-26532(279271)
     */
    @TableField(value = "MIANFEIQLX")
    private String MIANFEIQLX;

    /**
     * 户口邮编HR3-27179(283756)
     */
    @TableField(value = "HUKOUYB")
    private String HUKOUYB;

    /**
     * 十八中大病代码
     */
    @TableField(value = "SHIBAZDBDM")
    private String SHIBAZDBDM;

    /**
     * 末次月经时间
     */
    @TableField(value = "MOCIYUEJINGSJ")
    private LocalDateTime MOCIYUEJINGSJ;

    /**
     * 计算用末次月经时间
     */
    @TableField(value = "JSMOCIYJSJ")
    private LocalDateTime JSMOCIYJSJ;

    /**
     * 孕周
     */
    @TableField(value = "YUNZHOU")
    private Long YUNZHOU;

    /**
     * 孕天
     */
    @TableField(value = "YUNTIAN")
    private Long YUNTIAN;

    /**
     * 胎产次
     */
    @TableField(value = "TAICHANCI")
    private String TAICHANCI;

    /**
     * 不随访标志HR3-30460(305798)
     */
    @TableField(value = "BUSUIFANGBZ")
    private Integer BUSUIFANGBZ;

    /**
     * 院区IDHR3-32029(315245)
     */
    @TableField(value = "YUANQUID")
    private String YUANQUID;

    /**
     * 是否收取起伏线HR3-33756(325871)
     */
    @TableField(value = "SHIFOUSQQFX")
    private String SHIFOUSQQFX;

    /**
     * 营养膳食费用标志HR3-33378(323469)
     */
    @TableField(value = "SHANSHIFYBZ")
    private Integer SHANSHIFYBZ;

    /**
     * 首发精神分裂症标志
     */
    @TableField(value = "SFJSFLZBZ")
    private Integer SFJSFLZBZ;

    /**
     * 不入临床路径理由HR3-31405(311428)
     */
    @TableField(value = "BURUJINGLY")
    private String BURUJINGLY;

    /**
     * 医联体标志HR3-34466(330330)
     */
    @TableField(value = "YILIANTBZ")
    private String YILIANTBZ;

    /**
     * 医联体编号HR3-34466(330330)
     */
    @TableField(value = "YILIANTBH")
    private String YILIANTBH;

    /**
     * 医联体申请单IDHR3-34466(330330)
     */
    @TableField(value = "YILIANTSQDID")
    private String YILIANTSQDID;

    /**
     * 主任医生HR3-35491(335395)
     */
    @TableField(value = "ZHURENYS")
    private String ZHURENYS;

    /**
     * 主任医生名称HR3-35491(335395)
     */
    @TableField(value = "ZHURENYSXM")
    private String ZHURENYSXM;

    /**
     * 生育标志HR3-37163(344121)
     */
    @TableField(value = "SHENGYUBZ")
    private Integer SHENGYUBZ;

    /**
     * 120标志HR3-39758(361431)
     */
    @TableField(value = "BIAOZHI120")
    private Integer BIAOZHI120;

    /**
     * 疑难病人标志HR3-41664
     */
    @TableField(value = "YINANBRBZ")
    private Integer YINANBRBZ;

    /**
     * 入院原因HR3-42447(376875)
     */
    @TableField(value = "RUYUANYY")
    private String RUYUANYY;

    /**
     * 再次入院类型1:7天;2:31天HR3-42447(376875)
     */
    @TableField(value = "ZAICIRYLX")
    private Integer ZAICIRYLX;

    /**
     * 转诊单号HR3-42411(376652)
     */
    @TableField(value = "ZHUANZHENDH")
    private String ZHUANZHENDH;

    /**
     * 原入科日期384476
     */
    @TableField(value = "YUANRUKERQ")
    private LocalDateTime YUANRUKERQ;

    /**
     * 婴儿床位费记账状态HR3-44295(386318)
     */
    @TableField(value = "YINGERCWFJZZT")
    private Integer YINGERCWFJZZT;

    /**
     * 婴儿床位费记账调整日期HR3-44295(386318)
     */
    @TableField(value = "YINGERCWFJZTZRQ")
    private LocalDateTime YINGERCWFJZTZRQ;

    /**
     * 婴儿床位费记账调整人HR3-44295(386318)
     */
    @TableField(value = "YINGERCWFJZTZR")
    private String YINGERCWFJZTZR;

    /**
     * 快速入院标志HR3-45077(390486)
     */
    @TableField(value = "KUAISURYBZ")
    private Integer KUAISURYBZ;

    /**
     * 身份证不详原因HR3-46327(397284)
     */
    @TableField(value = "SHENFENZBXYY")
    private String SHENFENZBXYY;

    /**
     * 异地人员标志
     */
    @TableField(value = "YDRYBZ")
    private String YDRYBZ;

    /**
     * 统筹区标志
     */
    @TableField(value = "TCQBZ")
    private String TCQBZ;

    /**
     * 森林防火病人标志HR3-54823(446985)
     */
    @TableField(value = "SENLINFHBRBZ")
    private Integer SENLINFHBRBZ;

    /**
     * 森林防火审批日期HR3-54823(446985)
     */
    @TableField(value = "SENLINFHSPRQ")
    private LocalDateTime SENLINFHSPRQ;

    /**
     * 森林防火备注HR3-54823(446985)
     */
    @TableField(value = "SENLINFHBZ")
    private String SENLINFHBZ;

    /**
     * MPI
     */
    @TableField(value = "MPI")
    private String MPI;

    /**
     * 首次入科人HR6-355(468160)
     */
    @TableField(value = "SHOUCIRKR")
    private String SHOUCIRKR;

    /**
     * 首次入科人姓名HR6-355(468160)
     */
    @TableField(value = "SHOUCIRKRXM")
    private String SHOUCIRKRXM;

    /**
     * 
     */
    @TableField(value = "JIUZHENQRRQ")
    private LocalDateTime JIUZHENQRRQ;

    /**
     * 
     */
    @TableField(value = "JIUZHENQRBZ")
    private Integer JIUZHENQRBZ;

    /**
     * 
     */
    @TableField(value = "QUERENSFRQ_HIS")
    private LocalDateTime QUERENSFRQ_HIS;

    /**
     * 
     */
    @TableField(value = "QUERENSFBZ_HIS")
    private Integer QUERENSFBZ_HIS;

    /**
     * 
     */
    @TableField(value = "DENGJISY")
    private String DENGJISY;

    /**
     * 
     */
    @TableField(value = "YUNYIXYED")
    private String YUNYIXYED;

    /**
     * 
     */
    @TableField(value = "YUNYIQYBZ")
    private Integer YUNYIQYBZ;

    /**
     * 
     */
    @TableField(value = "ZHUANDRYELB")
    private String ZHUANDRYELB;

    /**
     * 留观标志（1：留观   0：非留观）HR6-1864(530725)
     */
    @TableField(value = "LIUGUANBZ")
    private Integer LIUGUANBZ;

    /**
     *  留观申请单ID HR6-1864(530725)（关联JZ_LIUGUANSQD表）
     */
    @TableField(value = "LIUGUANSQDID")
    private String LIUGUANSQDID;

    /**
     * 就诊ID  HR6-1864(530725)
     */
    @TableField(value = "JIUZHENID")
    private String JIUZHENID;

    /**
     * 分诊时间 HR6-1864(530725)
     */
    @TableField(value = "FENZHENSJ")
    private LocalDateTime FENZHENSJ;

    /**
     * 入院途径HR6-1955(535061)
     */
    @TableField(value = "LAIYUAN")
    private String LAIYUAN;

    /**
     * 分诊员HR6-1864(530725)
     */
    @TableField(value = "FENZHENYUAN")
    private String FENZHENYUAN;

    /**
     * 分诊员ID HR6-1864(530725)
     */
    @TableField(value = "FENZHENYUANID")
    private String FENZHENYUANID;

    /**
     * 分诊级别HR6-1864(530725)
     */
    @TableField(value = "FENZHENLB")
    private String FENZHENLB;

    /**
     * 是否调整出院日期（1：是，0：否）
     */
    @TableField(value = "SHIFUTZRQ")
    private Integer SHIFUTZRQ;

    /**
     * 个人电话 hr6-7536(550518)
     */
    @TableField(value = "DIANHUA")
    private String DIANHUA;

    /**
     * 新冠标志：1是新冠，0不是新冠  HR6-2798(563768)
     */
    @TableField(value = "XINGUANBZ")
    private Integer XINGUANBZ;

    /**
     * 三大中心：1脑卒中2胸痛3创伤HR6-2798(563768)
     */
    @TableField(value = "SANDAZXBZ")
    private Integer SANDAZXBZ;

    /**
     * 其他标志内容：HR6-2798(563768)
     */
    @TableField(value = "QITABZNR")
    private String QITABZNR;

    /**
     * 急诊转住院新病人住院idHR6-2806(563784)
     */
    @TableField(value = "XINBINGRENZYID")
    private String XINBINGRENZYID;

    /**
     * 留观病人住院id(急诊转住院后保存原来老的留观病人住院id)HR6-2986(569149)
     */
    @TableField(value = "LIUGUANBRZYID")
    private String LIUGUANBRZYID;

    /**
     * 现住址HR6-1875(531180)
     */
    @TableField(value = "XIANZHUZHI")
    private String XIANZHUZHI;

    /**
     * 病案上报标志(0 否，1 是 )
     */
    @TableField(value = "BINGANSBBZ")
    private Integer BINGANSBBZ;

    /**
     * 双向转诊标识(0)
     */
    @TableField(value = "SHUANGXIANGJZBZ")
    private Integer SHUANGXIANGJZBZ;

    /**
     * 中转科室id
     */
    @TableField(value = "ZHONGZHUANKS")
    private String ZHONGZHUANKS;

    /**
     * 中转科室名称
     */
    @TableField(value = "ZHONGZHUANKEMC")
    private String ZHONGZHUANKEMC;

    /**
     * 跌倒评估标志（1.表示是跌倒评估 0 表示非跌倒评估）R6-2982(569072)
     */
    @TableField(value = "DIEDAOPGBZ")
    private Integer DIEDAOPGBZ;

    /**
     * 第三方表主键(病人信息的主键563784)
     */
    @TableField(value = "DISANFBZJ")
    private String DISANFBZJ;

    /**
     * 分级非四级的添加病人去向，急诊内科和急诊外科（HR6-3930(591678)）
     */
    @TableField(value = "BINGRENQX")
    private String BINGRENQX;

    /**
     * 分诊级别HR6-4056(595445)
     */
    @TableField(value = "FENZHENJB")
    private String FENZHENJB;

    /**
     * 有效诊断名称
     */
    @TableField(value = "YOUXIAOZDMC")
    private String YOUXIAOZDMC;

    /**
     * 
     */
    @TableField(value = "MINZHENGXX")
    private String MINZHENGXX;

    /**
     * 病情摘要
     */
    @TableField(value = "BINGQINGZY")
    private String BINGQINGZY;

    /**
     * 眼科慈善 0：否 1：是 HR6-5373(623318)
     */
    @TableField(value = "YANKECS")
    private Integer YANKECS;

    /**
     * 其他医疗系统病人标志(0:halo入院登记病人，1:his通过转床换床等操作同步标志)
     */
    @TableField(value = "QITAYLXTBRBZ")
    private Integer QITAYLXTBRBZ;

    /**
     * GCP类型 HR6-5559(627379)
     */
    @TableField(value = "GCPLX")
    private String GCPLX;

    /**
     * 不适用单病种原因(hr6-6216)
     */
    @TableField(value = "BUSHIYDBZYY")
    private String BUSHIYDBZYY;

    /**
     * 是否挂账标志--HR5-8340(607762)
     */
    @TableField(value = "SHIFOUGZ")
    private Integer SHIFOUGZ;

    /**
     * 云嘉主索引
     */
    @TableField(value = "EMPI_YJ")
    private String EMPI_YJ;

    /**
     * 三级医生
     */
    @TableField(value = "SANJIYS")
    private String SANJIYS;

    /**
     * 三级医生姓名
     */
    @TableField(value = "SANJIYSMC")
    private String SANJIYSMC;

    /**
     * 科主任医生
     */
    @TableField(value = "KEZHURYS")
    private String KEZHURYS;

    /**
     * 科主任姓名
     */
    @TableField(value = "KEZHURYSXM")
    private String KEZHURYSXM;

    /**
     * 二级医生
     */
    @TableField(value = "ERJIYS")
    private String ERJIYS;

    /**
     * 二级医生名称
     */
    @TableField(value = "ERJIYSMC")
    private String ERJIYSMC;

    /**
     * 一级医生
     */
    @TableField(value = "YIJIYS")
    private String YIJIYS;

    /**
     * 一级医生名称
     */
    @TableField(value = "YIJIYSMC")
    private String YIJIYSMC;

    /**
     * 
     */
    @TableField(value = "SHANGCICKSJ")
    private LocalDateTime SHANGCICKSJ;

    /**
     * 综合病房标志:HOCR32008 省肿瘤：病区医生站、病区护士站增加术后综合病房流程
     */
    @TableField(value = "ZONGHEBFBZ")
    private Integer ZONGHEBFBZ;

    /**
     * 综合病区:HOCR32008 省肿瘤：病区医生站、病区护士站增加术后综合病房流程
     */
    @TableField(value = "ZONGHEBQ")
    private String ZONGHEBQ;

    /**
     * 综合病区床位:HOCR32008 省肿瘤：病区医生站、病区护士站增加术后综合病房流程
     */
    @TableField(value = "ZONGHEBQCW")
    private String ZONGHEBQCW;

    /**
     * 综合病房进入时间:HOCR32008 省肿瘤：病区医生站、病区护士站增加术后综合病房流程
     */
    @TableField(value = "ZONGHEBFJRSJ")
    private LocalDateTime ZONGHEBFJRSJ;

    /**
     * 综合病区名称:HOCR32008 省肿瘤：病区医生站、病区护士站增加术后综合病房流程
     */
    @TableField(value = "ZONGHEBQMC")
    private String ZONGHEBQMC;

    /**
     * 
     */
    @TableField(value = "QIANTIANJH")
    private Integer QIANTIANJH;

    /**
     * 
     */
    @TableField(value = "ZHOUQI")
    private String ZHOUQI;

    /**
     * 
     */
    @TableField(value = "JINGQI")
    private String JINGQI;

    /**
     * 
     */
    @TableField(value = "YXJM")
    private Integer YXJM;

    /**
     * 
     */
    @TableField(value = "XINXITBZ")
    private Integer XINXITBZ;

    /**
     * 
     */
    @TableField(value = "CHUANGTOUKDYBZ")
    private Integer CHUANGTOUKDYBZ;

    /**
     * 
     */
    @TableField(value = "FLSBZ")
    private Integer FLSBZ;

    /**
     * 性别代码
     */
    @TableField(value = "XINGBIEDM")
    private String XINGBIEDM;

    /**
     * 婚姻代码
     */
    @TableField(value = "HUNYINDM")
    private String HUNYINDM;

    /**
     * 职业代码
     */
    @TableField(value = "ZHIYEDM")
    private String ZHIYEDM;

    /**
     * 国籍代码
     */
    @TableField(value = "GUOJIDM")
    private String GUOJIDM;

    /**
     * 民族代码
     */
    @TableField(value = "MINZUDM")
    private String MINZUDM;

    /**
     * 省份代码
     */
    @TableField(value = "SHENGFENDM")
    private String SHENGFENDM;

    /**
     * 籍贯代码
     */
    @TableField(value = "JIGUANDM")
    private String JIGUANDM;

    /**
     * 出生地代码
     */
    @TableField(value = "CHUSHENGDDM")
    private String CHUSHENGDDM;

    /**
     * 计划生育服务证号
     */
    @TableField(value = "JIHUASYFWZH")
    private String JIHUASYFWZH;

    /**
     * 生育类别
     */
    @TableField(value = "SHENGYULB")
    private String SHENGYULB;

    /**
     * 晚育标志 -- 0否    1是
     */
    @TableField(value = "WANYUBZ")
    private Integer WANYUBZ;

    /**
     * 早产标志 -- 0否    1是
     */
    @TableField(value = "ZAOCHANBZ")
    private Integer ZAOCHANBZ;

    /**
     * 计划生育手术类别
     */
    @TableField(value = "JIHUASYSSLB")
    private String JIHUASYSSLB;

    /**
     * 学历
     */
    @TableField(value = "XUELI")
    private String XUELI;

    /**
     * 31天内再入院患者标志 -- 0-当天 1-15天内 2-31天内 3-否
     */
    @TableField(value = "ZAIRUYHZBZ")
    private Integer ZAIRUYHZBZ;

    /**
     * 
     */
    @TableField(value = "XUETANGSQZ")
    private String XUETANGSQZ;

    /**
     * 1:血糖医生发过通知 2:主管医生申请通过 3:主管医生不申请通过 4:血糖管理医生同意 5:血糖管理医生不同意 6:完成管理 7:取消管理
     */
    @TableField(value = "XUETANGSQZT")
    private String XUETANGSQZT;

    /**
     * 筛查地区
     */
    @TableField(value = "SHAICHADQ")
    private String SHAICHADQ;

    /**
     * 献血者标志
     */
    @TableField(value = "XIANXUEZBZ")
    private Integer XIANXUEZBZ;

    /**
     * 集成平台主索引ID
     */
    @TableField(value = "JCPTEMPI")
    private String JCPTEMPI;

    /**
     * 智慧医保特殊申请ID
     */
    @TableField(value = "IPT_PSN_SP_FLAG_DETL_ID")
    private String IPT_PSN_SP_FLAG_DETL_ID;

    /**
     * 
     */
    @TableField(value = "LINCHUANGLJID")
    private String LINCHUANGLJID;

    /**
     * 是否允许自主结算；1允许
     */
    @TableField(value = "SHIFOUYXZZJS")
    private String SHIFOUYXZZJS;

    /**
     * 陪客就诊卡号
     */
    @TableField(value = "PEIKEKH")
    private String PEIKEKH;

    /**
     * 1:康复医生发过通知 2:主管医生申请通过 3:主管医生不申请通过 4:康复管理医生同意并分配康复治疗师 5:康复管理医生不同意 6:完成管理 7:取消管理
     */
    @TableField(value = "KANGFUSQZT")
    private String KANGFUSQZT;

    /**
     * 康复治疗师
     */
    @TableField(value = "KANGFUZLS")
    private String KANGFUZLS;

    /**
     * 康复治疗师姓名
     */
    @TableField(value = "KANGFUZLSXM")
    private String KANGFUZLSXM;

    /**
     * 长护险标志
     */
    @TableField(value = "CHANGHUXBRBZ")
    private Integer CHANGHUXBRBZ;

    /**
     * 优抚病人标志
     */
    @TableField(value = "YOUFUBRBZ")
    private Integer YOUFUBRBZ;

    /**
     * 联系人2（HOCR371308）
     */
    @TableField(value = "LIANXIREN2")
    private String LIANXIREN2;

    /**
     * 联系人电话2（HOCR371308）
     */
    @TableField(value = "LIANXIRDH2")
    private String LIANXIRDH2;

    /**
     * 关系2（HOCR371308）
     */
    @TableField(value = "GUANXI2")
    private String GUANXI2;

    /**
     * 院前科室ID
     */
    @TableField(value = "YUANQIANBQID")
    private String YUANQIANBQID;

    /**
     * 主管医生
     */
    @TableField(value = "ZHUGUANYS")
    private String ZHUGUANYS;

    /**
     * 留抢转科标志
     */
    @TableField(value = "LIUQIANGZKBZ")
    private Integer LIUQIANGZKBZ;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}