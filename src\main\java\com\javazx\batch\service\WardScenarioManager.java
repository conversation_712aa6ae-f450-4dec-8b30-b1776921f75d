package com.javazx.batch.service;

import com.javazx.batch.config.DynamicBatchConfig;
import com.javazx.batch.scenario.doctor.DoctorScenario;
import com.javazx.batch.scenario.doctor.DoctorWardScenario;
import com.javazx.batch.scenario.patientInfor.PatientScenario;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.*;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 病区场景管理器
 * 用于管理不同病区的独立同步场景
 */
@Service
public class WardScenarioManager {

    private static final Logger log = LoggerFactory.getLogger(WardScenarioManager.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private DynamicBatchConfig dynamicBatchConfig;

    // 缓存不同病区的患者场景实例
    private final Map<String, PatientScenario> patientScenarioCache = new ConcurrentHashMap<>();

    // 缓存不同病区的医生场景实例
    private final Map<String, DoctorWardScenario> doctorScenarioCache = new ConcurrentHashMap<>();

    /**
     * 获取指定病区的患者同步场景
     * @param wardId 病区ID
     * @return 患者同步场景实例
     */
    public PatientScenario getPatientScenario(String wardId) {
        if (wardId == null || wardId.trim().isEmpty()) {
            throw new IllegalArgumentException("病区ID不能为空");
        }

        return patientScenarioCache.computeIfAbsent(wardId, id -> {
            log.info("创建病区{}的患者同步场景", id);
            
            // 从Spring容器获取PatientScenario的原型实例
            PatientScenario scenario = applicationContext.getBean(PatientScenario.class);
            scenario.setWardId(id);
            
            return scenario;
        });
    }

    /**
     * 获取指定病区的医生同步场景
     * @param wardId 病区ID
     * @return 医生同步场景实例
     */
    public DoctorWardScenario getDoctorScenario(String wardId) {
        if (wardId == null || wardId.trim().isEmpty()) {
            throw new IllegalArgumentException("病区ID不能为空");
        }

        return doctorScenarioCache.computeIfAbsent(wardId, id -> {
            log.info("创建病区{}的医生同步场景", id);
            
            // 从Spring容器获取DoctorWardScenario的原型实例
            DoctorWardScenario scenario = applicationContext.getBean(DoctorWardScenario.class);
            scenario.setWardId(id);
            
            return scenario;
        });
    }

    /**
     * 执行指定病区的患者数据同步
     * @param wardId 病区ID
     * @throws Exception 执行异常
     */
    public void executePatientSync(String wardId) throws Exception {
        try {
            log.info("开始执行病区{}的患者数据同步", wardId);
            PatientScenario scenario = getPatientScenario(wardId);

            // 动态创建Job并执行
            dynamicBatchConfig.addScenarioJob(scenario);
            Job job = dynamicBatchConfig.getJob(scenario.getJobName());

            if (job != null) {
                JobParameters jobParameters = new JobParametersBuilder()
                        .addLong("time", System.currentTimeMillis())
                        .addString("wardId", wardId)
                        .toJobParameters();

                JobExecution execution = jobLauncher.run(job, jobParameters);
                log.info("病区{}患者数据同步执行完成，状态: {}", wardId, execution.getStatus());
            } else {
                throw new RuntimeException("无法创建病区" + wardId + "的患者同步Job");
            }

            log.info("完成病区{}的患者数据同步", wardId);
        } catch (Exception e) {
            log.error("病区{}患者数据同步失败: {}", wardId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行指定病区的医生数据同步
     * @param wardId 病区ID
     * @throws Exception 执行异常
     */
    public void executeDoctorSync(String wardId) throws Exception {
        try {
            log.info("开始执行病区{}的医生数据同步", wardId);
            DoctorWardScenario scenario = getDoctorScenario(wardId);

            // 动态创建Job并执行
            dynamicBatchConfig.addScenarioJob(scenario);
            Job job = dynamicBatchConfig.getJob(scenario.getJobName());

            if (job != null) {
                JobParameters jobParameters = new JobParametersBuilder()
                        .addLong("time", System.currentTimeMillis())
                        .addString("wardId", wardId)
                        .toJobParameters();

                JobExecution execution = jobLauncher.run(job, jobParameters);
                log.info("病区{}医生数据同步执行完成，状态: {}", wardId, execution.getStatus());
            } else {
                throw new RuntimeException("无法创建病区" + wardId + "的医生同步Job");
            }

            log.info("完成病区{}的医生数据同步", wardId);
        } catch (Exception e) {
            log.error("病区{}医生数据同步失败: {}", wardId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行指定病区的完整数据同步（患者+医生）
     * @param wardId 病区ID
     * @throws Exception 执行异常
     */
    public void executeFullSync(String wardId) throws Exception {
        try {
            log.info("开始执行病区{}的完整数据同步", wardId);
            
            // 先同步医生数据
            executeDoctorSync(wardId);
            
            // 再同步患者数据
            executePatientSync(wardId);
            
            log.info("完成病区{}的完整数据同步", wardId);
        } catch (Exception e) {
            log.error("病区{}完整数据同步失败: {}", wardId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量执行多个病区的患者数据同步
     * @param wardIds 病区ID列表
     */
    public void executePatientSyncBatch(String... wardIds) {
        for (String wardId : wardIds) {
            try {
                executePatientSync(wardId);
            } catch (Exception e) {
                log.error("病区{}患者数据同步失败，继续处理其他病区", wardId);
            }
        }
    }

    /**
     * 批量执行多个病区的医生数据同步
     * @param wardIds 病区ID列表
     */
    public void executeDoctorSyncBatch(String... wardIds) {
        for (String wardId : wardIds) {
            try {
                executeDoctorSync(wardId);
            } catch (Exception e) {
                log.error("病区{}医生数据同步失败，继续处理其他病区", wardId);
            }
        }
    }

    /**
     * 批量执行多个病区的完整数据同步
     * @param wardIds 病区ID列表
     */
    public void executeFullSyncBatch(String... wardIds) {
        for (String wardId : wardIds) {
            try {
                executeFullSync(wardId);
            } catch (Exception e) {
                log.error("病区{}完整数据同步失败，继续处理其他病区", wardId);
            }
        }
    }

    /**
     * 清除指定病区的场景缓存
     * @param wardId 病区ID
     */
    public void clearWardCache(String wardId) {
        patientScenarioCache.remove(wardId);
        doctorScenarioCache.remove(wardId);
        log.info("已清除病区{}的场景缓存", wardId);
    }

    /**
     * 清除所有病区的场景缓存
     */
    public void clearAllCache() {
        patientScenarioCache.clear();
        doctorScenarioCache.clear();
        log.info("已清除所有病区的场景缓存");
    }

    /**
     * 获取当前缓存的病区数量
     * @return 缓存的病区数量统计
     */
    public Map<String, Integer> getCacheStats() {
        Map<String, Integer> stats = new HashMap<>();
        stats.put("patientScenarios", patientScenarioCache.size());
        stats.put("doctorScenarios", doctorScenarioCache.size());
        return stats;
    }
}
