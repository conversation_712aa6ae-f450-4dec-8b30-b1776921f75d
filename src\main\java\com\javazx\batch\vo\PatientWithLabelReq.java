package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 患者标签请求对象
 * chagine.smartward.DTO.request.patient.PatientWithLabelReq
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientWithLabelReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者拥有的标签列表
     */
    private List<PatientLabelReq> labelPatientOwnList;

    /**
     * 患者信息
     */
    private PatientInfoReq patientInfo;
}
