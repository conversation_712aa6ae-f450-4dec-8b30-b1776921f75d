package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 床位信息请求对象
 * 对应 ApifoxModel 中的 BedInfoReq 结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BedInfoReq {
    /**
     * 床位编号
     */
    private String bedNo;
    
    /**
     * 病区ID
     */
    private String wardCode;
}
