package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 护理任务请求对象
 * 对应 ApifoxModel 中的护理任务结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class NurseTaskReq {
    /**
     * 床位号
     */
    private String bedNo;
    
    /**
     * 床位备注
     */
    private String bedRemark;
    
    /**
     * 结束时间
     */
    private String endRemindTime;
    
    /**
     * 是否执行(0-未执行, 1-已执行)
     */
    private Long isExecuted;
    
    /**
     * 护理任务编码
     */
    private String nurseTaskCode;
    
    /**
     * 排序号
     */
    private Long orderNum;
    
    /**
     * 提醒时间
     */
    private String startRemindTime;
    
    /**
     * 病区编码
     */
    private String wardCode;
}
