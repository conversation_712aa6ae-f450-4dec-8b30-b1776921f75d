package com.javazx.batch.scenario.doctor;

import com.javazx.batch.po.GyZhigongxx;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.DoctorDataService;
import com.javazx.batch.service.JobTitleService;
import com.javazx.batch.vo.UserInfoReq;
import com.javazx.batch.vo.UserSyncRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 医护人员数据同步场景
 * 从数据库读取医护人员数据并同步到外部系统
 */
@Component
public class DoctorScenario extends AbstractBatchScenario<GyZhigongxx, UserInfoReq> {

    private static final Logger log = LoggerFactory.getLogger(DoctorScenario.class);

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private DoctorDataService doctorDataService;

    @Autowired
    private JobTitleService jobTitleService;

    public DoctorScenario() {
        super("doctor", "医护人员数据同步场景：同步医护人员信息数据");
        this.setCommitInterval(100);
        this.setPageSize(50);
    }

    @Override
    public ItemReader<GyZhigongxx> createReader() {
        log.info("创建医护人员数据读取器，页面大小: {}", getPageSize());

        // 创建通用分页读取器
        GenericPagingItemReader<GyZhigongxx> reader = new GenericPagingItemReader<>(
                "医护人员",
                getPageSize(),
                (offset, limit) -> doctorDataService.selectDoctorsByPage(offset, limit),
                () -> doctorDataService.countDoctors()
        );

        // 记录总医护人员数量
        int totalCount = reader.getTotalCount();
        log.info("医护人员总数: {}", totalCount);

        return reader;
    }

    @Override
    public ItemProcessor<GyZhigongxx, UserInfoReq> createProcessor() {
        log.info("创建医护人员数据转换器");
        return new DoctorProcessor(doctorDataService, jobTitleService);
    }
    
    @Override
    public ItemWriter<UserInfoReq> createWriter() {
        log.info("创建医护人员信息同步数据写入器");
        return items -> {
            UserSyncRequest request = new UserSyncRequest();
            request.setUserInfoList(List.copyOf(items));

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理医护人员数据量: {}", threadName, items.size());

            try {
                List<String> userNames = getUserNameFromRequest(List.copyOf(items));
                String userNamesStr = String.join(", ", userNames);
                smartwardWebClient
                        .post()
                        .uri("/sync/user/sync_user_info")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request.getUserInfoList())
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("医生 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("医生 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送医护人员信息数据: {} (线程: {})", userNamesStr, threadName);
            } catch (Exception e) {
                log.error("医护人员信息同步时发生异常: {}", e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理医护人员数据量: {}", threadName, items.size());
        };
    }


    /**
     * 从请求对象中获取医护人员姓名
     */
    private List<String> getUserNameFromRequest(List<UserInfoReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知用户");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(UserInfoReq::getUserName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
