package com.javazx.batch.util;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.PatientDataService;
import com.javazx.batch.vo.PatientInfoReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 医嘱创建工具类
 * 提供通用的患者信息创建方法，供各个处理器使用
 */
@Component
public class YiZhuInfoUtil {

    private static final Logger log = LoggerFactory.getLogger(YiZhuInfoUtil.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 转换医嘱类型 1 诊疗2西药3成药4草药7大输液
     * 8文本9检查10检验11血液12手术13会诊14高压氧
     */
    public String convertAdviceType(String yizhufl) {
        if (yizhufl == null) {
            return "其他";
        }

        switch (yizhufl.trim()) {
            case "1":
                return "诊疗";
            case "2":
                return "西药";
            case "3":
                return "成药";
            case "4":
                return "草药";
            case "7":
                return "大输液";
            case "8":
                return "文本";
            case "9":
                return "检查";
            case "10":
                return "检验";
            case "11":
                return "血液";
            case "12":
                return "手术";
            case "13":
                return "会诊";
            case "14":
                return "高压氧";
            default:
                return "其他";
        }
    }

    /**
     * 转换医嘱状态
     */
    public String convertAdviceStatus(String Yizhufl,String yizhuzt) {
        if (Yizhufl  == null){
            return "未知";
        }
        if (yizhuzt == null) {
            return "未知";
        }

        if ("1".equals(Yizhufl)){
            //静脉抽血
            //当前状态:1未确认,2已确认,3新更动,4未执行,5已执行,6不执行,7已停止,8待撤销,9已撤销,10待退药,11已退药,12已发药,13缺药,14已完成
            switch (yizhuzt.trim()) {
                case "1":
                    return "未确认";
                case "2":
                    return "已确认";
                case "3":
                    return "新更动";
                case "4":
                    return "未执行";
                case "5":
                    return "已执行";
                case "6":
                    return "不执行";
                case "7":
                    return "已停止";
                case "8":
                    return "待撤销";
                case "9":
                    return "已撤销";
                case "10":
                    return "待退药";
                case "11":
                    return "已退药";
                case "12":
                    return "已发药";
                case "13":
                    return "缺药";
                case "14":
                    return "已完成";
                default:
                    return "未知";
            }
        }else {
            //1新开单2待划价3待登记4已预约5已安排6已完成7已报告8已打印9已撤销10已退单11已发送未接收
            switch (yizhuzt.trim()) {
                case "1":
                    return "新开单";
                case "2":
                    return "待划价";
                case "3":
                    return "待登记";
                case "4":
                    return "已预约";
                case "5":
                    return "已安排";
                case "6":
                    return "已完成";
                case "7":
                    return "已报告";
                case "8":
                    return "已打印";
                case "9":
                    return "已撤销";
                case "10":
                    return "已退单";
                case "11":
                    return "已发送未接收";
                default:
                    return "未知";
            }
        }
    }


    /**
     * 转换完成状态
     */
    public Long convertFinishStatus(String dangqianzt) {
        return "6".equals(dangqianzt) || "7".equals(dangqianzt) || "8".equals(dangqianzt) ? 1L : 0L;
    }
}
