package com.javazx.batch.mapper.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医嘱穿刺静脉对象
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YzChuanCIJingMaiResp implements Serializable {

    /**
     * 医嘱ID
     */
    private String YIZHUID;

    /**
     * 病人住院ID
     */
    private String BINGRENZYID;


    /**
     * 医嘱名称
     */
    private String YIZHUMC;

    /**
     * 医嘱分类
     */
    private String YIZHUFL;

    /**
     * 预约申请日期
     */
    private LocalDateTime YUYUESQRQ;

    /**
     * 当前状态1新开单2待划价3待登记4已预约5已安排6已完成7已报告8已打印9已撤销10已退单11已发送未接收
     * 静脉抽血 医嘱状态 当前状态:1未确认,2已确认,3新更动,4未执行,5已执行,6不执行,7已停止,8待撤销,9已撤销,10待退药,11已退药,12已发药,13缺药,14已完成
     */
    private String DANGQIANZT;

    /**
     * 开嘱时间
     */
    private LocalDateTime KAIZHUSJ;


    /**
     * 检查地址
     */
    private String JIANCHADZ;

    /**
     * 当前病区
     */
    private String DANGQIANBQ;

    /**
     * 当前床位
     */
    private String DANGQIANCW;

    /**
     * 患者姓名
     */
    private String XINGMING;

    /**
     * 住院号
     */
    private String ZHUYUANHAO;

    /**
     * 病案号
     */
    private String BINGANGHAO;

    /**
     * 医生嘱托
     */
    private String YISHENGZT;

    /**
     * 执行状态:2点滴开始 3点滴结束 4点滴暂停 5点击续打6终止7配奶8收药9配药10配药复核11收血12收血核对13取血14镇痛泵回收15镇痛泵销毁16静配送出17静配接收
     */
    private String ZHIXINGZT;

    /**
     * 执行开始时间;点滴开始时间
     */
    private LocalDateTime ZHIXINGKSSJ;

}