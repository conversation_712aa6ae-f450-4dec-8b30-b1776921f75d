package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.BlBinglijl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description 针对表【BL_BINGLIJL(病历记录)】的数据库操作Mapper
 * @createDate 2025-06-17 16:41:00
 * @Entity generator.domain.BlBinglijl
 */
@Mapper
@DS("hzzyy")
public interface BlBinglijlMapper extends BaseMapper<BlBinglijl> {

    /**
     * 查询疼痛评分（NRS评估表）
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return 疼痛评分
     */
    @Select("""
            SELECT fanhuizhi
            FROM (
                SELECT t.<PERSON><PERSON>zy<PERSON>, 
                       Row_Number() Over(Partition By t.Bingrenzyid Order By binglisj desc) As <PERSON>hao,
                       decode(trim(translate(fanhuizhi, '0123456789', ' ')), null, to_number(fanhuizhi), 0) fanhuizhi
                FROM his6.BL_BINGLIJL t 
                WHERE MENZHENZYBZ = 1 AND mobandm = 'TTPFB1' AND zuofeibz = 0 
            ) m, HIS6.zy_bingrenxx z
            WHERE m.bingrenzyid = z.bingrenzyid 
              AND m.xuhao = 1 
              AND z.zaiyuanzt = 0 
              AND z.bingrenzyid = #{bingrenzyid}
              AND z.dangqianbq = #{dangqianbq}
            """)
    Integer getPainScore(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);

    /**
     * 查询压力性损伤评分（Braden评估表）
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return 压力性损伤评分
     */
    @Select("""
            SELECT fanhuizhi
            FROM (
                SELECT t.Bingrenzyid, 
                       Row_Number() Over(Partition By t.Bingrenzyid Order By binglisj desc) As Xuhao,
                       decode(trim(translate(fanhuizhi, '0123456789', ' ')), null, to_number(fanhuizhi), 0) fanhuizhi
                FROM his6.BL_BINGLIJL t 
                WHERE MENZHENZYBZ = 1 AND mobandm = 'YLXSSWXYSPGB' AND zuofeibz = 0 
            ) m, HIS6.zy_bingrenxx z
            WHERE m.bingrenzyid = z.bingrenzyid 
              AND m.xuhao = 1 
              AND z.zaiyuanzt = 0 
              AND z.bingrenzyid = #{bingrenzyid}
              AND z.dangqianbq = #{dangqianbq}
            """)
    Integer getPressureInjuryScore(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);

    /**
     * 查询跌倒评分（约翰霍普金斯量表）
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return 跌倒评分
     */
    @Select("""
            SELECT fanhuizhi
            FROM (
                SELECT t.Bingrenzyid, 
                       Row_Number() Over(Partition By t.Bingrenzyid Order By binglisj desc) As Xuhao,
                       decode(trim(translate(fanhuizhi, '0123456789', ' ')), null, to_number(fanhuizhi), 0) fanhuizhi
                FROM his6.BL_BINGLIJL t 
                WHERE MENZHENZYBZ = 1 AND mobandm = 'YHHPJSDDFXPGLB' AND zuofeibz = 0 
            ) m, HIS6.zy_bingrenxx z
            WHERE m.bingrenzyid = z.bingrenzyid 
              AND m.xuhao = 1 
              AND z.zaiyuanzt = 0 
              AND z.bingrenzyid = #{bingrenzyid}
              AND z.dangqianbq = #{dangqianbq}
            """)
    Integer getFallRiskScore(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);

    /**
     * 查询VTE评估结果（Caprini量表）
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return VTE评估结果
     */
    @Select("""
            SELECT fanhuizhi
            FROM (
                SELECT t.Bingrenzyid, 
                       Row_Number() Over(Partition By t.Bingrenzyid Order By binglisj desc) As Xuhao,
                       t.fanhuizhi
                FROM his6.BL_BINGLIJL t 
                WHERE MENZHENZYBZ = 1 AND mobandm = 'VTEGYCLBC' AND zuofeibz = 0 
            ) m, HIS6.zy_bingrenxx z
            WHERE m.bingrenzyid = z.bingrenzyid 
              AND m.xuhao = 1 
              AND z.zaiyuanzt = 0 
              AND fanhuizhi = '高危'
              AND z.bingrenzyid = #{bingrenzyid}
              AND z.dangqianbq = #{dangqianbq}
            """)
    String getVTERiskLevel(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);
}
