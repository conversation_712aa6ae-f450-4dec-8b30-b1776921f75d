package com.javazx.batch.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.ZyChuangweibd;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【ZY_CHUANGWEIBD(住院_床位变动)】的数据库操作Mapper
* @createDate 2025-06-12 15:27:15
* @Entity generator.domain.ZyChuangweibd
*/
@Mapper
@DS("hzzyy")
public interface ZyChuangweibdMapper extends BaseMapper<ZyChuangweibd> {

    int deleteByPrimaryKey(Long id);

    int insert(ZyChuangweibd record);

    int insertSelective(ZyChuangweibd record);

    ZyChuangweibd selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZyChuangweibd record);

    int updateByPrimaryKey(ZyChuangweibd record);

    /**
     * 查询转入记录数量
     * @param bingrenzyid 病人住院ID
     * @return 转入记录数量
     */
    @Select("""
            SELECT COUNT(*) FROM HIS6.zy_CHUANGWEIBD
            WHERE BIANDONGYY = 4 AND BINGRENZYID = #{bingrenzyid}
            """)
    Integer getTransferInCount(@Param("bingrenzyid") String bingrenzyid);

    /**
     * 查询转出记录数量
     * @param bingrenzyid 病人住院ID
     * @return 转出记录数量
     */
    @Select("""
            SELECT COUNT(*) FROM HIS6.zy_CHUANGWEIBD
            WHERE BIANDONGYY = 4 AND BINGRENZYID = #{bingrenzyid}
            """)
    Integer getTransferOutCount(@Param("bingrenzyid") String bingrenzyid);

    /**
     * 查询借床记录数量
     * @param bingrenzyid 病人住院ID
     * @return 借床记录数量
     */
    @Select("""
            SELECT COUNT(*) FROM HIS6.zy_CHUANGWEIBD
            WHERE BIANDONGYY = 2 AND BINGRENZYID = #{bingrenzyid}
            """)
    Integer getBorrowedBedCount(@Param("bingrenzyid") String bingrenzyid);

}
