<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.BlBinglijlMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.BlBinglijl">
            <id property="BINGLIJLXH" column="BINGLIJLXH" jdbcType="DECIMAL"/>
            <result property="BINGANH" column="BINGANH" jdbcType="VARCHAR"/>
            <result property="ZHUYUANHAO" column="ZHUYUANHAO" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="MENZHENZYBZ" column="MENZHENZYBZ" jdbcType="DECIMAL"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="MOBANLXDM" column="MOBANLXDM" jdbcType="VARCHAR"/>
            <result property="MOBANDM" column="MOBANDM" jdbcType="VARCHAR"/>
            <result property="MOBANBH" column="MOBANBH" jdbcType="DECIMAL"/>
            <result property="GONGNENGDXH" column="GONGNENGDXH" jdbcType="VARCHAR"/>
            <result property="BINGLIMC" column="BINGLIMC" jdbcType="VARCHAR"/>
            <result property="CHUANGJIANSJ" column="CHUANGJIANSJ" jdbcType="TIMESTAMP"/>
            <result property="CHUANGJIANREN" column="CHUANGJIANREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="WANGKADZ" column="WANGKADZ" jdbcType="VARCHAR"/>
            <result property="XUSHENGHBZ" column="XUSHENGHBZ" jdbcType="DECIMAL"/>
            <result property="SHENGHEBZ" column="SHENGHEBZ" jdbcType="DECIMAL"/>
            <result property="SHENGHEREN" column="SHENGHEREN" jdbcType="VARCHAR"/>
            <result property="SHENGHESJ" column="SHENGHESJ" jdbcType="TIMESTAMP"/>
            <result property="BINGLIJLNRXH" column="BINGLIJLNRXH" jdbcType="DECIMAL"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="DANGQIANKS" column="DANGQIANKS" jdbcType="VARCHAR"/>
            <result property="DANGQIANBQ" column="DANGQIANBQ" jdbcType="VARCHAR"/>
            <result property="DANGQIANCW" column="DANGQIANCW" jdbcType="VARCHAR"/>
            <result property="ZANCUNBZ" column="ZANCUNBZ" jdbcType="DECIMAL"/>
            <result property="BINGCHENGBZ" column="BINGCHENGBZ" jdbcType="DECIMAL"/>
            <result property="BINGLISJ" column="BINGLISJ" jdbcType="TIMESTAMP"/>
            <result property="HUANYEBZ" column="HUANYEBZ" jdbcType="VARCHAR"/>
            <result property="FANHUIZHI" column="FANHUIZHI" jdbcType="VARCHAR"/>
            <result property="YUANSUID" column="YUANSUID" jdbcType="VARCHAR"/>
            <result property="SHENQINGDID" column="SHENQINGDID" jdbcType="VARCHAR"/>
            <result property="YEWULX" column="YEWULX" jdbcType="DECIMAL"/>
            <result property="DIANZIBLSY" column="DIANZIBLSY" jdbcType="VARCHAR"/>
            <result property="SHANGJIYSXM" column="SHANGJIYSXM" jdbcType="VARCHAR"/>
            <result property="SHANGJIYSGH" column="SHANGJIYSGH" jdbcType="VARCHAR"/>
            <result property="SHANGJIBLJLXH" column="SHANGJIBLJLXH" jdbcType="DECIMAL"/>
            <result property="DAYINBZ" column="DAYINBZ" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        BINGLIJLXH,BINGANH,ZHUYUANHAO,
        BINGRENID,MENZHENZYBZ,BINGRENZYID,
        MOBANLXDM,MOBANDM,MOBANBH,
        GONGNENGDXH,BINGLIMC,CHUANGJIANSJ,
        CHUANGJIANREN,XIUGAISJ,XIUGAIREN,
        WANGKADZ,XUSHENGHBZ,SHENGHEBZ,
        SHENGHEREN,SHENGHESJ,BINGLIJLNRXH,
        ZUOFEIBZ,DANGQIANKS,DANGQIANBQ,
        DANGQIANCW,ZANCUNBZ,BINGCHENGBZ,
        BINGLISJ,HUANYEBZ,FANHUIZHI,
        YUANSUID,SHENQINGDID,YEWULX,
        DIANZIBLSY,SHANGJIYSXM,SHANGJIYSGH,
        SHANGJIBLJLXH,DAYINBZ
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from BL_BINGLIJL
        where  BINGLIJLXH = #{BINGLIJLXH,jdbcType=DECIMAL} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from BL_BINGLIJL
        where  BINGLIJLXH = #{BINGLIJLXH,jdbcType=DECIMAL} 
    </delete>
    <insert id="insert" keyColumn="BINGLIJLXH" keyProperty="BINGLIJLXH" parameterType="com.javazx.batch.po.BlBinglijl" useGeneratedKeys="true">
        insert into BL_BINGLIJL
        ( BINGLIJLXH,BINGANH,ZHUYUANHAO
        ,BINGRENID,MENZHENZYBZ,BINGRENZYID
        ,MOBANLXDM,MOBANDM,MOBANBH
        ,GONGNENGDXH,BINGLIMC,CHUANGJIANSJ
        ,CHUANGJIANREN,XIUGAISJ,XIUGAIREN
        ,WANGKADZ,XUSHENGHBZ,SHENGHEBZ
        ,SHENGHEREN,SHENGHESJ,BINGLIJLNRXH
        ,ZUOFEIBZ,DANGQIANKS,DANGQIANBQ
        ,DANGQIANCW,ZANCUNBZ,BINGCHENGBZ
        ,BINGLISJ,HUANYEBZ,FANHUIZHI
        ,YUANSUID,SHENQINGDID,YEWULX
        ,DIANZIBLSY,SHANGJIYSXM,SHANGJIYSGH
        ,SHANGJIBLJLXH,DAYINBZ)
        values (#{BINGLIJLXH,jdbcType=DECIMAL},#{BINGANH,jdbcType=VARCHAR},#{ZHUYUANHAO,jdbcType=VARCHAR}
        ,#{BINGRENID,jdbcType=VARCHAR},#{MENZHENZYBZ,jdbcType=DECIMAL},#{BINGRENZYID,jdbcType=VARCHAR}
        ,#{MOBANLXDM,jdbcType=VARCHAR},#{MOBANDM,jdbcType=VARCHAR},#{MOBANBH,jdbcType=DECIMAL}
        ,#{GONGNENGDXH,jdbcType=VARCHAR},#{BINGLIMC,jdbcType=VARCHAR},#{CHUANGJIANSJ,jdbcType=TIMESTAMP}
        ,#{CHUANGJIANREN,jdbcType=VARCHAR},#{XIUGAISJ,jdbcType=TIMESTAMP},#{XIUGAIREN,jdbcType=VARCHAR}
        ,#{WANGKADZ,jdbcType=VARCHAR},#{XUSHENGHBZ,jdbcType=DECIMAL},#{SHENGHEBZ,jdbcType=DECIMAL}
        ,#{SHENGHEREN,jdbcType=VARCHAR},#{SHENGHESJ,jdbcType=TIMESTAMP},#{BINGLIJLNRXH,jdbcType=DECIMAL}
        ,#{ZUOFEIBZ,jdbcType=DECIMAL},#{DANGQIANKS,jdbcType=VARCHAR},#{DANGQIANBQ,jdbcType=VARCHAR}
        ,#{DANGQIANCW,jdbcType=VARCHAR},#{ZANCUNBZ,jdbcType=DECIMAL},#{BINGCHENGBZ,jdbcType=DECIMAL}
        ,#{BINGLISJ,jdbcType=TIMESTAMP},#{HUANYEBZ,jdbcType=VARCHAR},#{FANHUIZHI,jdbcType=VARCHAR}
        ,#{YUANSUID,jdbcType=VARCHAR},#{SHENQINGDID,jdbcType=VARCHAR},#{YEWULX,jdbcType=DECIMAL}
        ,#{DIANZIBLSY,jdbcType=VARCHAR},#{SHANGJIYSXM,jdbcType=VARCHAR},#{SHANGJIYSGH,jdbcType=VARCHAR}
        ,#{SHANGJIBLJLXH,jdbcType=DECIMAL},#{DAYINBZ,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="BINGLIJLXH" keyProperty="BINGLIJLXH" parameterType="com.javazx.batch.po.BlBinglijl" useGeneratedKeys="true">
        insert into BL_BINGLIJL
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="BINGLIJLXH != null">BINGLIJLXH,</if>
                <if test="BINGANH != null">BINGANH,</if>
                <if test="ZHUYUANHAO != null">ZHUYUANHAO,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="MENZHENZYBZ != null">MENZHENZYBZ,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="MOBANLXDM != null">MOBANLXDM,</if>
                <if test="MOBANDM != null">MOBANDM,</if>
                <if test="MOBANBH != null">MOBANBH,</if>
                <if test="GONGNENGDXH != null">GONGNENGDXH,</if>
                <if test="BINGLIMC != null">BINGLIMC,</if>
                <if test="CHUANGJIANSJ != null">CHUANGJIANSJ,</if>
                <if test="CHUANGJIANREN != null">CHUANGJIANREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="WANGKADZ != null">WANGKADZ,</if>
                <if test="XUSHENGHBZ != null">XUSHENGHBZ,</if>
                <if test="SHENGHEBZ != null">SHENGHEBZ,</if>
                <if test="SHENGHEREN != null">SHENGHEREN,</if>
                <if test="SHENGHESJ != null">SHENGHESJ,</if>
                <if test="BINGLIJLNRXH != null">BINGLIJLNRXH,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="DANGQIANKS != null">DANGQIANKS,</if>
                <if test="DANGQIANBQ != null">DANGQIANBQ,</if>
                <if test="DANGQIANCW != null">DANGQIANCW,</if>
                <if test="ZANCUNBZ != null">ZANCUNBZ,</if>
                <if test="BINGCHENGBZ != null">BINGCHENGBZ,</if>
                <if test="BINGLISJ != null">BINGLISJ,</if>
                <if test="HUANYEBZ != null">HUANYEBZ,</if>
                <if test="FANHUIZHI != null">FANHUIZHI,</if>
                <if test="YUANSUID != null">YUANSUID,</if>
                <if test="SHENQINGDID != null">SHENQINGDID,</if>
                <if test="YEWULX != null">YEWULX,</if>
                <if test="DIANZIBLSY != null">DIANZIBLSY,</if>
                <if test="SHANGJIYSXM != null">SHANGJIYSXM,</if>
                <if test="SHANGJIYSGH != null">SHANGJIYSGH,</if>
                <if test="SHANGJIBLJLXH != null">SHANGJIBLJLXH,</if>
                <if test="DAYINBZ != null">DAYINBZ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="BINGLIJLXH != null">#{BINGLIJLXH,jdbcType=DECIMAL},</if>
                <if test="BINGANH != null">#{BINGANH,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANHAO != null">#{ZHUYUANHAO,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="MENZHENZYBZ != null">#{MENZHENZYBZ,jdbcType=DECIMAL},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="MOBANLXDM != null">#{MOBANLXDM,jdbcType=VARCHAR},</if>
                <if test="MOBANDM != null">#{MOBANDM,jdbcType=VARCHAR},</if>
                <if test="MOBANBH != null">#{MOBANBH,jdbcType=DECIMAL},</if>
                <if test="GONGNENGDXH != null">#{GONGNENGDXH,jdbcType=VARCHAR},</if>
                <if test="BINGLIMC != null">#{BINGLIMC,jdbcType=VARCHAR},</if>
                <if test="CHUANGJIANSJ != null">#{CHUANGJIANSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUANGJIANREN != null">#{CHUANGJIANREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="WANGKADZ != null">#{WANGKADZ,jdbcType=VARCHAR},</if>
                <if test="XUSHENGHBZ != null">#{XUSHENGHBZ,jdbcType=DECIMAL},</if>
                <if test="SHENGHEBZ != null">#{SHENGHEBZ,jdbcType=DECIMAL},</if>
                <if test="SHENGHEREN != null">#{SHENGHEREN,jdbcType=VARCHAR},</if>
                <if test="SHENGHESJ != null">#{SHENGHESJ,jdbcType=TIMESTAMP},</if>
                <if test="BINGLIJLNRXH != null">#{BINGLIJLNRXH,jdbcType=DECIMAL},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="DANGQIANKS != null">#{DANGQIANKS,jdbcType=VARCHAR},</if>
                <if test="DANGQIANBQ != null">#{DANGQIANBQ,jdbcType=VARCHAR},</if>
                <if test="DANGQIANCW != null">#{DANGQIANCW,jdbcType=VARCHAR},</if>
                <if test="ZANCUNBZ != null">#{ZANCUNBZ,jdbcType=DECIMAL},</if>
                <if test="BINGCHENGBZ != null">#{BINGCHENGBZ,jdbcType=DECIMAL},</if>
                <if test="BINGLISJ != null">#{BINGLISJ,jdbcType=TIMESTAMP},</if>
                <if test="HUANYEBZ != null">#{HUANYEBZ,jdbcType=VARCHAR},</if>
                <if test="FANHUIZHI != null">#{FANHUIZHI,jdbcType=VARCHAR},</if>
                <if test="YUANSUID != null">#{YUANSUID,jdbcType=VARCHAR},</if>
                <if test="SHENQINGDID != null">#{SHENQINGDID,jdbcType=VARCHAR},</if>
                <if test="YEWULX != null">#{YEWULX,jdbcType=DECIMAL},</if>
                <if test="DIANZIBLSY != null">#{DIANZIBLSY,jdbcType=VARCHAR},</if>
                <if test="SHANGJIYSXM != null">#{SHANGJIYSXM,jdbcType=VARCHAR},</if>
                <if test="SHANGJIYSGH != null">#{SHANGJIYSGH,jdbcType=VARCHAR},</if>
                <if test="SHANGJIBLJLXH != null">#{SHANGJIBLJLXH,jdbcType=DECIMAL},</if>
                <if test="DAYINBZ != null">#{DAYINBZ,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.BlBinglijl">
        update BL_BINGLIJL
        <set>
                <if test="BINGANH != null">
                    BINGANH = #{BINGANH,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANHAO != null">
                    ZHUYUANHAO = #{ZHUYUANHAO,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENZYBZ != null">
                    MENZHENZYBZ = #{MENZHENZYBZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="MOBANLXDM != null">
                    MOBANLXDM = #{MOBANLXDM,jdbcType=VARCHAR},
                </if>
                <if test="MOBANDM != null">
                    MOBANDM = #{MOBANDM,jdbcType=VARCHAR},
                </if>
                <if test="MOBANBH != null">
                    MOBANBH = #{MOBANBH,jdbcType=DECIMAL},
                </if>
                <if test="GONGNENGDXH != null">
                    GONGNENGDXH = #{GONGNENGDXH,jdbcType=VARCHAR},
                </if>
                <if test="BINGLIMC != null">
                    BINGLIMC = #{BINGLIMC,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGJIANSJ != null">
                    CHUANGJIANSJ = #{CHUANGJIANSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUANGJIANREN != null">
                    CHUANGJIANREN = #{CHUANGJIANREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="WANGKADZ != null">
                    WANGKADZ = #{WANGKADZ,jdbcType=VARCHAR},
                </if>
                <if test="XUSHENGHBZ != null">
                    XUSHENGHBZ = #{XUSHENGHBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENGHEBZ != null">
                    SHENGHEBZ = #{SHENGHEBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENGHEREN != null">
                    SHENGHEREN = #{SHENGHEREN,jdbcType=VARCHAR},
                </if>
                <if test="SHENGHESJ != null">
                    SHENGHESJ = #{SHENGHESJ,jdbcType=TIMESTAMP},
                </if>
                <if test="BINGLIJLNRXH != null">
                    BINGLIJLNRXH = #{BINGLIJLNRXH,jdbcType=DECIMAL},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="DANGQIANKS != null">
                    DANGQIANKS = #{DANGQIANKS,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANBQ != null">
                    DANGQIANBQ = #{DANGQIANBQ,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANCW != null">
                    DANGQIANCW = #{DANGQIANCW,jdbcType=VARCHAR},
                </if>
                <if test="ZANCUNBZ != null">
                    ZANCUNBZ = #{ZANCUNBZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGCHENGBZ != null">
                    BINGCHENGBZ = #{BINGCHENGBZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGLISJ != null">
                    BINGLISJ = #{BINGLISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="HUANYEBZ != null">
                    HUANYEBZ = #{HUANYEBZ,jdbcType=VARCHAR},
                </if>
                <if test="FANHUIZHI != null">
                    FANHUIZHI = #{FANHUIZHI,jdbcType=VARCHAR},
                </if>
                <if test="YUANSUID != null">
                    YUANSUID = #{YUANSUID,jdbcType=VARCHAR},
                </if>
                <if test="SHENQINGDID != null">
                    SHENQINGDID = #{SHENQINGDID,jdbcType=VARCHAR},
                </if>
                <if test="YEWULX != null">
                    YEWULX = #{YEWULX,jdbcType=DECIMAL},
                </if>
                <if test="DIANZIBLSY != null">
                    DIANZIBLSY = #{DIANZIBLSY,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIYSXM != null">
                    SHANGJIYSXM = #{SHANGJIYSXM,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIYSGH != null">
                    SHANGJIYSGH = #{SHANGJIYSGH,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIBLJLXH != null">
                    SHANGJIBLJLXH = #{SHANGJIBLJLXH,jdbcType=DECIMAL},
                </if>
                <if test="DAYINBZ != null">
                    DAYINBZ = #{DAYINBZ,jdbcType=DECIMAL},
                </if>
        </set>
        where   BINGLIJLXH = #{BINGLIJLXH,jdbcType=DECIMAL} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.BlBinglijl">
        update BL_BINGLIJL
        set 
            BINGANH =  #{BINGANH,jdbcType=VARCHAR},
            ZHUYUANHAO =  #{ZHUYUANHAO,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            MENZHENZYBZ =  #{MENZHENZYBZ,jdbcType=DECIMAL},
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            MOBANLXDM =  #{MOBANLXDM,jdbcType=VARCHAR},
            MOBANDM =  #{MOBANDM,jdbcType=VARCHAR},
            MOBANBH =  #{MOBANBH,jdbcType=DECIMAL},
            GONGNENGDXH =  #{GONGNENGDXH,jdbcType=VARCHAR},
            BINGLIMC =  #{BINGLIMC,jdbcType=VARCHAR},
            CHUANGJIANSJ =  #{CHUANGJIANSJ,jdbcType=TIMESTAMP},
            CHUANGJIANREN =  #{CHUANGJIANREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            WANGKADZ =  #{WANGKADZ,jdbcType=VARCHAR},
            XUSHENGHBZ =  #{XUSHENGHBZ,jdbcType=DECIMAL},
            SHENGHEBZ =  #{SHENGHEBZ,jdbcType=DECIMAL},
            SHENGHEREN =  #{SHENGHEREN,jdbcType=VARCHAR},
            SHENGHESJ =  #{SHENGHESJ,jdbcType=TIMESTAMP},
            BINGLIJLNRXH =  #{BINGLIJLNRXH,jdbcType=DECIMAL},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            DANGQIANKS =  #{DANGQIANKS,jdbcType=VARCHAR},
            DANGQIANBQ =  #{DANGQIANBQ,jdbcType=VARCHAR},
            DANGQIANCW =  #{DANGQIANCW,jdbcType=VARCHAR},
            ZANCUNBZ =  #{ZANCUNBZ,jdbcType=DECIMAL},
            BINGCHENGBZ =  #{BINGCHENGBZ,jdbcType=DECIMAL},
            BINGLISJ =  #{BINGLISJ,jdbcType=TIMESTAMP},
            HUANYEBZ =  #{HUANYEBZ,jdbcType=VARCHAR},
            FANHUIZHI =  #{FANHUIZHI,jdbcType=VARCHAR},
            YUANSUID =  #{YUANSUID,jdbcType=VARCHAR},
            SHENQINGDID =  #{SHENQINGDID,jdbcType=VARCHAR},
            YEWULX =  #{YEWULX,jdbcType=DECIMAL},
            DIANZIBLSY =  #{DIANZIBLSY,jdbcType=VARCHAR},
            SHANGJIYSXM =  #{SHANGJIYSXM,jdbcType=VARCHAR},
            SHANGJIYSGH =  #{SHANGJIYSGH,jdbcType=VARCHAR},
            SHANGJIBLJLXH =  #{SHANGJIBLJLXH,jdbcType=DECIMAL},
            DAYINBZ =  #{DAYINBZ,jdbcType=DECIMAL}
        where   BINGLIJLXH = #{BINGLIJLXH,jdbcType=DECIMAL} 
    </update>
</mapper>
