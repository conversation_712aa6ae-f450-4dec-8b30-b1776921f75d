<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.GyZhigongksMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.GyZhigongks">
            <id property="ZHIGONGID" column="ZHIGONGID" jdbcType="VARCHAR"/>
            <id property="KESHIBQID" column="KESHIBQID" jdbcType="VARCHAR"/>
            <id property="KESHIBQBZ" column="KESHIBQBZ" jdbcType="DECIMAL"/>
            <result property="SHUNXUHAO" column="SHUNXUHAO" jdbcType="DECIMAL"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="KESHIBQMC" column="KESHIBQMC" jdbcType="VARCHAR"/>
            <result property="MENZHENBZ" column="MENZHENBZ" jdbcType="DECIMAL"/>
            <result property="ZHUYUANBZ" column="ZHUYUANBZ" jdbcType="DECIMAL"/>
            <result property="ZHIWUID" column="ZHIWUID" jdbcType="VARCHAR"/>
            <result property="ZHIWUMC" column="ZHIWUMC" jdbcType="VARCHAR"/>
            <result property="LIUQIANGSY" column="LIUQIANGSY" jdbcType="DECIMAL"/>
            <result property="TIJIANBZ" column="TIJIANBZ" jdbcType="DECIMAL"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ZHIGONGID,KESHIBQID,KESHIBQBZ,
        SHUNXUHAO,XIUGAIREN,XIUGAISJ,
        KESHIBQMC,MENZHENBZ,ZHUYUANBZ,
        ZHIWUID,ZHIWUMC,LIUQIANGSY,
        TIJIANBZ,YUANQUID
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from GY_ZHIGONGKS
        where  ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} AND KESHIBQID = #{KESHIBQID,jdbcType=VARCHAR} AND KESHIBQBZ = #{KESHIBQBZ,jdbcType=DECIMAL} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from GY_ZHIGONGKS
        where  ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} AND KESHIBQID = #{KESHIBQID,jdbcType=VARCHAR} AND KESHIBQBZ = #{KESHIBQBZ,jdbcType=DECIMAL} 
    </delete>
    <insert id="insert">
        insert into GY_ZHIGONGKS
        ( ZHIGONGID,KESHIBQID,KESHIBQBZ
        ,SHUNXUHAO,XIUGAIREN,XIUGAISJ
        ,KESHIBQMC,MENZHENBZ,ZHUYUANBZ
        ,ZHIWUID,ZHIWUMC,LIUQIANGSY
        ,TIJIANBZ,YUANQUID)
        values (#{ZHIGONGID,jdbcType=VARCHAR},#{KESHIBQID,jdbcType=VARCHAR},#{KESHIBQBZ,jdbcType=DECIMAL}
        ,#{SHUNXUHAO,jdbcType=DECIMAL},#{XIUGAIREN,jdbcType=VARCHAR},#{XIUGAISJ,jdbcType=TIMESTAMP}
        ,#{KESHIBQMC,jdbcType=VARCHAR},#{MENZHENBZ,jdbcType=DECIMAL},#{ZHUYUANBZ,jdbcType=DECIMAL}
        ,#{ZHIWUID,jdbcType=VARCHAR},#{ZHIWUMC,jdbcType=VARCHAR},#{LIUQIANGSY,jdbcType=DECIMAL}
        ,#{TIJIANBZ,jdbcType=DECIMAL},#{YUANQUID,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective">
        insert into GY_ZHIGONGKS
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="ZHIGONGID != null">ZHIGONGID,</if>
                <if test="KESHIBQID != null">KESHIBQID,</if>
                <if test="KESHIBQBZ != null">KESHIBQBZ,</if>
                <if test="SHUNXUHAO != null">SHUNXUHAO,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="KESHIBQMC != null">KESHIBQMC,</if>
                <if test="MENZHENBZ != null">MENZHENBZ,</if>
                <if test="ZHUYUANBZ != null">ZHUYUANBZ,</if>
                <if test="ZHIWUID != null">ZHIWUID,</if>
                <if test="ZHIWUMC != null">ZHIWUMC,</if>
                <if test="LIUQIANGSY != null">LIUQIANGSY,</if>
                <if test="TIJIANBZ != null">TIJIANBZ,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="ZHIGONGID != null">#{ZHIGONGID,jdbcType=VARCHAR},</if>
                <if test="KESHIBQID != null">#{KESHIBQID,jdbcType=VARCHAR},</if>
                <if test="KESHIBQBZ != null">#{KESHIBQBZ,jdbcType=DECIMAL},</if>
                <if test="SHUNXUHAO != null">#{SHUNXUHAO,jdbcType=DECIMAL},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="KESHIBQMC != null">#{KESHIBQMC,jdbcType=VARCHAR},</if>
                <if test="MENZHENBZ != null">#{MENZHENBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUYUANBZ != null">#{ZHUYUANBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIWUID != null">#{ZHIWUID,jdbcType=VARCHAR},</if>
                <if test="ZHIWUMC != null">#{ZHIWUMC,jdbcType=VARCHAR},</if>
                <if test="LIUQIANGSY != null">#{LIUQIANGSY,jdbcType=DECIMAL},</if>
                <if test="TIJIANBZ != null">#{TIJIANBZ,jdbcType=DECIMAL},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.GyZhigongks">
        update GY_ZHIGONGKS
        <set>
                <if test="SHUNXUHAO != null">
                    SHUNXUHAO = #{SHUNXUHAO,jdbcType=DECIMAL},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="KESHIBQMC != null">
                    KESHIBQMC = #{KESHIBQMC,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENBZ != null">
                    MENZHENBZ = #{MENZHENBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUYUANBZ != null">
                    ZHUYUANBZ = #{ZHUYUANBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIWUID != null">
                    ZHIWUID = #{ZHIWUID,jdbcType=VARCHAR},
                </if>
                <if test="ZHIWUMC != null">
                    ZHIWUMC = #{ZHIWUMC,jdbcType=VARCHAR},
                </if>
                <if test="LIUQIANGSY != null">
                    LIUQIANGSY = #{LIUQIANGSY,jdbcType=DECIMAL},
                </if>
                <if test="TIJIANBZ != null">
                    TIJIANBZ = #{TIJIANBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=DECIMAL},
                </if>
        </set>
        where   ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} AND KESHIBQID = #{KESHIBQID,jdbcType=VARCHAR} AND KESHIBQBZ = #{KESHIBQBZ,jdbcType=DECIMAL} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.GyZhigongks">
        update GY_ZHIGONGKS
        set 
            SHUNXUHAO =  #{SHUNXUHAO,jdbcType=DECIMAL},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            KESHIBQMC =  #{KESHIBQMC,jdbcType=VARCHAR},
            MENZHENBZ =  #{MENZHENBZ,jdbcType=DECIMAL},
            ZHUYUANBZ =  #{ZHUYUANBZ,jdbcType=DECIMAL},
            ZHIWUID =  #{ZHIWUID,jdbcType=VARCHAR},
            ZHIWUMC =  #{ZHIWUMC,jdbcType=VARCHAR},
            LIUQIANGSY =  #{LIUQIANGSY,jdbcType=DECIMAL},
            TIJIANBZ =  #{TIJIANBZ,jdbcType=DECIMAL},
            YUANQUID =  #{YUANQUID,jdbcType=DECIMAL}
        where   ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} AND KESHIBQID = #{KESHIBQID,jdbcType=VARCHAR} AND KESHIBQBZ = #{KESHIBQBZ,jdbcType=DECIMAL} 
    </update>
</mapper>
