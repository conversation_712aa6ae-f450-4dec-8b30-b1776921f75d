package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 医护人员信息请求对象
 * 对应 ApifoxModel 中的用户信息结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserInfoReq {
    /**
     * 所属病区
     */
    private List<String> belongWard;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 性别:(0-全部, 1-男, 2-女)
     */
    private Long gender;
    
    /**
     * 工号
     */
    private String jobNumber;
    
    /**
     * 工作职称
     */
    private String jobTitle;
    
    /**
     * 工作类别(0-全部, 1-管理员, 2-医生, 3-护士)
     */
    private Long jobType;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 用户姓名
     */
    private String userName;
}
