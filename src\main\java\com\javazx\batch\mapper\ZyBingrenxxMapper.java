package com.javazx.batch.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.ZyBingrenxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【ZY_BINGRENXX(住院_病人信息)】的数据库操作Mapper
* @createDate 2025-06-11 17:42:00
* @Entity generator.domain.ZyBingrenxx
*/
@Mapper
@DS("hzzyy")
public interface ZyBingrenxxMapper extends BaseMapper<ZyBingrenxx> {

    int deleteByPrimaryKey(Long id);

    int insert(ZyBingrenxx record);

    int insertSelective(ZyBingrenxx record);

    ZyBingrenxx selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ZyBingrenxx record);

    int updateByPrimaryKey(ZyBingrenxx record);

    /**
     * 分页查询在院患者列表
     * @param dangqianbq 当前病区
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 患者列表
     */
    @Select("""
              SELECT *
              FROM (
                  SELECT BINGRENZYID, BINGRENID, XINGMING, XINGBIE, NIANLING, NIANLINGDW, GUOMINSHI,
                         RUYUANRQ, DANGQIANKS, DANGQIANBQ, DANGQIANCW, RUYUANZDMC,
                         ZHUYUANHAO, BINGANHAO, ZHUZHIYSXM, ZERENHSXM,
                         ROW_NUMBER() OVER (ORDER BY RUYUANRQ DESC) AS ROW_NUM
                  FROM HIS6.ZY_BINGRENXX
                  WHERE zaiyuanzt = 0 AND DANGQIANBQ = #{dangqianbq}
              )
              WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<ZyBingrenxx> selectPatientsByPage(@Param("dangqianbq") String dangqianbq,
                                          @Param("offset") int offset,
                                          @Param("limit") int limit);

    /**
     * 统计在院患者总数
     * @param dangqianbq 当前病区
     * @return 患者总数
     */
    @Select("""
            SELECT COUNT(*) FROM HIS6.ZY_BINGRENXX WHERE zaiyuanzt = 0 AND DANGQIANBQ = #{dangqianbq}
            """)
    int countPatients(@Param("dangqianbq") String dangqianbq);


}




