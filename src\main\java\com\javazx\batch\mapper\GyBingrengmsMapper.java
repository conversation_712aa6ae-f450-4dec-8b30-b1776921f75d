package com.javazx.batch.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.GyBingrengms;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【GY_BINGRENGMS(公用_病人过敏史)】的数据库操作Mapper
* @createDate 2025-06-12 15:24:16
* @Entity generator.domain.GyBingrengms
*/
@Mapper
@DS("hzzyy")
public interface GyBingrengmsMapper extends BaseMapper<GyBingrengms> {

    int deleteByPrimaryKey(Long id);

    int insert(GyBingrengms record);

    int insertSelective(GyBingrengms record);

    GyBingrengms selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GyBingrengms record);

    int updateByPrimaryKey(GyBingrengms record);

    /**
     * 查询患者是否有药品过敏史
     * @param bingrenid 病人ID
     * @return 过敏史记录数量
     */
    @Select("""
            SELECT count(1)
            FROM HIS6.GY_BINGRENGMS
            WHERE BINGRENID = #{bingrenid}
              AND YAOPINMC IS NOT NULL
            """)
    Integer getAllergyCount(@Param("bingrenid") String bingrenid);

}
