package com.javazx.batch.service;

import com.javazx.batch.mapper.BlBinglijlMapper;
import com.javazx.batch.mapper.GyBingrengmsMapper;
import com.javazx.batch.mapper.SmShoushuxxMapper;
import com.javazx.batch.mapper.YzBingrenyzMapper;
import com.javazx.batch.mapper.ZyBingrenxxMapper;
import com.javazx.batch.mapper.ZyChuangweibdMapper;
import com.javazx.batch.mapper.ZyhlZhongyihljlMapper;
import com.javazx.batch.po.ZyBingrenxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 患者数据服务类
 * 专门处理Oracle数据源的数据访问
 */
@Service
public class PatientDataService {

    private static final Logger log = LoggerFactory.getLogger(PatientDataService.class);

    @Autowired
    private ZyBingrenxxMapper zyBingrenxxMapper;

    @Autowired
    private YzBingrenyzMapper yzBingrenyzMapper;

    @Autowired
    private SmShoushuxxMapper smShoushuxxMapper;

    @Autowired
    private ZyChuangweibdMapper zyChuangweibdMapper;

    @Autowired
    private BlBinglijlMapper blBinglijlMapper;

    @Autowired
    private GyBingrengmsMapper gyBingrengmsMapper;

    @Autowired
    private ZyhlZhongyihljlMapper zyhlZhongyihljlMapper;

    /**
     * 分页查询患者数据
     */
    public List<ZyBingrenxx> selectPatientsByPage(String dangqianbq, int offset, int limit) {
        try {
            log.debug("查询患者数据 - 病区: {}, 偏移: {}, 限制: {}", dangqianbq, offset, limit);
            return zyBingrenxxMapper.selectPatientsByPage(dangqianbq, offset, limit);
        } catch (Exception e) {
            log.error("查询患者数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计患者总数
     */
    public int countPatients(String dangqianbq) {
        try {
            return zyBingrenxxMapper.countPatients(dangqianbq);
        } catch (Exception e) {
            log.error("统计患者总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询护理等级医嘱项目ID
     */
    public String getNursingLevelOrderId(String bingrenzyid, String bingquid) {
        try {
            return yzBingrenyzMapper.getNursingLevelOrderId(bingrenzyid, bingquid);
        } catch (Exception e) {
            log.error("查询护理等级失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 查询今日手术数量
     */
    public Integer getTodaySurgeryCount(String bingrenzyid, String bingquid) {
        return smShoushuxxMapper.getTodaySurgeryCount(bingrenzyid, bingquid);
    }

    /**
     * 查询转入记录数量
     */
    public Integer getTransferInCount(String bingrenzyid) {
        return zyChuangweibdMapper.getTransferInCount(bingrenzyid);
    }

    /**
     * 查询转出记录数量
     */
    public Integer getTransferOutCount(String bingrenzyid) {
        return zyChuangweibdMapper.getTransferOutCount(bingrenzyid);
    }

    /**
     * 查询借床记录数量
     */
    public Integer getBorrowedBedCount(String bingrenzyid) {
        return zyChuangweibdMapper.getBorrowedBedCount(bingrenzyid);
    }

    /**
     * 查询危重患者记录数量
     */
    public Integer getCriticalPatientCount(String bingrenzyid, String dangqianbq) {
        try {
            return yzBingrenyzMapper.getCriticalPatientCount(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询危重患者记录失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询多重耐药记录数量
     */
    public Integer getContactIsolationCount(String bingrenzyid, String dangqianbq) {
        try {
            return yzBingrenyzMapper.getContactIsolationCount(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询多重耐药记录失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询过敏史记录数量
     */
    public Integer getAllergyCount(String bingrenzyid) {
        try {
            return gyBingrengmsMapper.getAllergyCount(bingrenzyid);
        } catch (Exception e) {
            log.error("查询过敏史记录失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询疼痛评分
     */
    public Integer getPainScore(String bingrenzyid, String dangqianbq) {
        try {
            return blBinglijlMapper.getPainScore(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询疼痛评分失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询压力性损伤评分
     */
    public Integer getPressureInjuryScore(String bingrenzyid, String dangqianbq) {
        try {
            return blBinglijlMapper.getPressureInjuryScore(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询压力性损伤评分失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询跌倒风险评分
     */
    public Integer getFallRiskScore(String bingrenzyid, String dangqianbq) {
        try {
            return blBinglijlMapper.getFallRiskScore(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询跌倒风险评分失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询VTE风险等级
     */
    public String getVTERiskLevel(String bingrenzyid, String dangqianbq) {
        try {
            return blBinglijlMapper.getVTERiskLevel(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询VTE风险等级失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 查询中医护理方案记录数量
     */
    public Integer getChineseMedicineNursingCount(String bingrenzyid, String dangqianbq) {
        try {
            return zyhlZhongyihljlMapper.getChineseMedicineNursingCount(bingrenzyid, dangqianbq);
        } catch (Exception e) {
            log.error("查询中医护理方案记录失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
