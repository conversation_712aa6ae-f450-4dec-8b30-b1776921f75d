package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.GyZhigongks;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【GY_ZHIGONGKS(公用_职工科室)】的数据库操作Mapper
* @createDate 2025-06-19 09:44:17
* @Entity com.javazx.batch.po.GyZhigongks
*/
@Mapper
@DS("hzzyy")
public interface GyZhigongksMapper extends BaseMapper<GyZhigongks> {

    int deleteByPrimaryKey(Long id);

    int insert(GyZhigongks record);

    int insertSelective(GyZhigongks record);

    GyZhigongks selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GyZhigongks record);

    int updateByPrimaryKey(GyZhigongks record);

}
