package com.javazx.batch.scenario;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批处理场景管理器
 * 负责管理所有的批处理场景
 */
@Component
public class BatchScenarioManager {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private Map<String, BatchScenario> scenarioMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        Map<String, BatchScenario> scenarios = applicationContext.getBeansOfType(BatchScenario.class);
        for (BatchScenario scenario : scenarios.values()) {
            if (scenario.isEnabled()) {
                scenarioMap.put(scenario.getScenarioName(), scenario);
            }
        }
    }
    
    /**
     * 获取指定名称的场景
     * @param scenarioName 场景名称
     * @return 场景实例
     */
    public BatchScenario getScenario(String scenarioName) {
        return scenarioMap.get(scenarioName);
    }
    
    /**
     * 获取所有启用的场景
     * @return 场景列表
     */
    public List<BatchScenario> getAllEnabledScenarios() {
        return scenarioMap.values().stream()
                .filter(BatchScenario::isEnabled)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有场景名称
     * @return 场景名称列表
     */
    public List<String> getAllScenarioNames() {
        return scenarioMap.keySet().stream()
                .collect(Collectors.toList());
    }
    
    /**
     * 检查场景是否存在
     * @param scenarioName 场景名称
     * @return 是否存在
     */
    public boolean hasScenario(String scenarioName) {
        return scenarioMap.containsKey(scenarioName);
    }
    
    /**
     * 获取场景数量
     * @return 场景数量
     */
    public int getScenarioCount() {
        return scenarioMap.size();
    }
    
    /**
     * 注册新场景
     * @param scenario 场景实例
     */
    public void registerScenario(BatchScenario scenario) {
        if (scenario != null && scenario.isEnabled()) {
            scenarioMap.put(scenario.getScenarioName(), scenario);
        }
    }
    
    /**
     * 移除场景
     * @param scenarioName 场景名称
     */
    public void removeScenario(String scenarioName) {
        scenarioMap.remove(scenarioName);
    }
}
