package com.javazx.batch.config;

import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.BatchScenario;
import com.javazx.batch.scenario.BatchScenarioManager;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.support.transaction.ResourcelessTransactionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态批处理配置类
 * 根据场景动态创建批处理作业
 * 
 * <AUTHOR>
 * @date 2024
 */
@Configuration
@Order(1)
public class DynamicBatchConfig {

    private static final Logger logger = LoggerFactory.getLogger(DynamicBatchConfig.class);

    @Autowired
    private JobBuilderFactory jobBuilderFactory;
    
    @Autowired
    private StepBuilderFactory stepBuilderFactory;
    
    @Autowired
    private BatchScenarioManager scenarioManager;

    @Autowired
    private JobRegistry jobRegistry;

    @Qualifier("taskExecutor")
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    
    private Map<String, Job> jobMap = new HashMap<>();
    private Map<String, Step> stepMap = new HashMap<>();
    
    @PostConstruct
    public void initDynamicJobs() {
        logger.info("开始初始化动态批处理作业...");
        logger.info("BatchScenarioManager中的场景数量: {}", scenarioManager.getScenarioCount());

        for (BatchScenario scenario : scenarioManager.getAllEnabledScenarios()) {
            logger.info("创建场景作业: {} -> Job名称: {}", scenario.getScenarioName(), scenario.getJobName());
            createJobForScenario(scenario);
        }
        logger.info("动态批处理作业初始化完成，共创建 {} 个作业", jobMap.size());
        logger.info("JobMap中的作业: {}", jobMap.keySet());

        // 检查JobRegistry中的所有Job
        try {
            logger.info("JobRegistry中的作业: {}", jobRegistry.getJobNames());
        } catch (Exception e) {
            logger.error("获取JobRegistry中的作业名称失败", e);
        }
    }
    
    /**
     * 为指定场景创建Job
     * @param scenario 场景
     */
    @SuppressWarnings("unchecked")
    private void createJobForScenario(BatchScenario scenario) {
        if (!(scenario instanceof AbstractBatchScenario)) {
            return;
        }
        
        AbstractBatchScenario<?, ?> abstractScenario = (AbstractBatchScenario<?, ?>) scenario;
        
        // 创建Step
        Step step = createStepForScenario(abstractScenario);
        stepMap.put(scenario.getStepName(), step);
        
        // 创建Job
        Job job = jobBuilderFactory.get(scenario.getJobName())
                .incrementer(new RunIdIncrementer())
                .preventRestart()
                .start(step)
                .build();

        jobMap.put(scenario.getJobName(), job);

        // 将Job注册到JobRegistry，使JobLocator能够找到它
        try {
            ReferenceJobFactory jobFactory = new ReferenceJobFactory(job);
            jobRegistry.register(jobFactory);
            logger.info("成功注册Job到JobRegistry: {}", scenario.getJobName());
        } catch (Exception e) {
            logger.error("注册Job到JobRegistry失败: {}", scenario.getJobName(), e);
            throw new IllegalStateException("Failed to register job: " + scenario.getJobName(), e);
        }
    }
    
    /**
     * 为指定场景创建Step
     * @param scenario 场景
     * @return Step实例
     */
    @SuppressWarnings("unchecked")
    private <I, O> Step createStepForScenario(AbstractBatchScenario<I, O> scenario) {
        ItemReader<I> reader = scenario.createReader();
        ItemProcessor<I, O> processor = scenario.createProcessor();
        ItemWriter<O> writer = scenario.createWriter();
        
        return stepBuilderFactory.get(scenario.getStepName())
                .<I, O>chunk(scenario.getCommitInterval())
                .reader(reader)
                .processor(processor)
                .writer(writer)
                .transactionManager(new ResourcelessTransactionManager())
                .allowStartIfComplete(true)
                .taskExecutor(taskExecutor)
                .throttleLimit(5)
                .build();
    }
    
    /**
     * 获取指定名称的Job
     * @param jobName Job名称
     * @return Job实例
     */
    public Job getJob(String jobName) {
        return jobMap.get(jobName);
    }
    
    /**
     * 获取指定名称的Step
     * @param stepName Step名称
     * @return Step实例
     */
    public Step getStep(String stepName) {
        return stepMap.get(stepName);
    }
    
    /**
     * 获取所有Job
     * @return Job映射
     */
    public Map<String, Job> getAllJobs() {
        return new HashMap<>(jobMap);
    }
    
    /**
     * 动态添加新的场景Job
     * @param scenario 场景
     */
    public void addScenarioJob(BatchScenario scenario) {
        createJobForScenario(scenario);
    }
}
