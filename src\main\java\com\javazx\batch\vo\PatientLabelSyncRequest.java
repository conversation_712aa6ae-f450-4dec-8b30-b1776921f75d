package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 患者标签同步请求对象
 * 对应API: /sync/patient_label/sync_patient_label
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientLabelSyncRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 所有标签编码列表
     */
    private List<String> allLabelCodeList;

    /**
     * 患者标签请求列表
     */
    private List<PatientWithLabelReq> patientWithLabelReqList;
}
