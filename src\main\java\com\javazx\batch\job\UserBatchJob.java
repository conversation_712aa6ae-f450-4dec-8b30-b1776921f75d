package com.javazx.batch.job;

import cn.hutool.core.lang.UUID;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobLocator;
import org.springframework.batch.core.launch.JobLauncher;

@Slf4j
@Data
public class UserBatchJob {
	private String jobName;

	private JobLocator jobLocator;

	private JobLauncher jobLauncher;

	public void performJob() {
		try {
			JobExecution result = jobLauncher.run(jobLocator.getJob(jobName),
					new JobParametersBuilder()
							.addString("jobKey", UUID.randomUUID().toString())
							.toJobParameters());

			//waitForJobCompletion(result);

			log.info("批处理任务执行完成: {}, 状态: {}", jobName, result.getStatus());
		} catch (Exception ex) {
			log.error("执行批处理任务失败: {}", jobName, ex);
			throw new RuntimeException("Job execution failed: " + jobName, ex);
		}
	}

	/**
	 * 等待Job真正完成，确保所有异步操作都已结束
	 *
	 * @param jobExecution   Job执行实例
	 */
	private void waitForJobCompletion(JobExecution jobExecution) {
		int maxWaitSeconds = 300;
		int waitInterval = 1000; // 每秒检查一次
		int waitedSeconds = 0;

		while (!jobExecution.getStatus().isUnsuccessful() &&
			   !jobExecution.getStatus().equals(org.springframework.batch.core.BatchStatus.COMPLETED) &&
			   waitedSeconds < maxWaitSeconds) {
			try {
				Thread.sleep(waitInterval);
				waitedSeconds++;

				if (waitedSeconds % 10 == 0) { // 每10秒记录一次
					log.info("等待任务 {} 完成中... 已等待 {} 秒, 当前状态: {}",
						jobName, waitedSeconds, jobExecution.getStatus());
				}
			} catch (InterruptedException e) {
				Thread.currentThread().interrupt();
				log.error("等待任务完成时被中断: {}", jobName);
				throw new RuntimeException("Job execution interrupted", e);
			}
		}

		if (waitedSeconds >= maxWaitSeconds) {
			log.error("等待任务 {} 完成超时，已等待 {} 秒", jobName, waitedSeconds);
			throw new RuntimeException("Job execution timeout after " + waitedSeconds + " seconds");
		}

		log.info("任务 {} 等待完成，总耗时: {} 秒, 最终状态: {}", jobName, waitedSeconds, jobExecution.getStatus());
	}

}
