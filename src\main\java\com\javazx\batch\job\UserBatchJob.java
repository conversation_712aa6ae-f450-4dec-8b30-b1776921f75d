package com.javazx.batch.job;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionException;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobLocator;
import org.springframework.batch.core.launch.JobLauncher;

@Slf4j
@Data
public class UserBatchJob {
	private String jobName;

	private JobLocator jobLocator;

	private JobLauncher jobLauncher;

	public void performJob() {
		try {
			JobExecution result = jobLauncher.run(jobLocator.getJob(jobName),
					new JobParametersBuilder()
							.addLong("timestamp", System.currentTimeMillis())
							.toJobParameters());
			log.info("批处理任务执行完成: {}, 状态: {}", jobName, result.getStatus());
		} catch (JobExecutionException ex) {
			log.error("执行批处理任务失败: {}", jobName, ex);
		} catch (Exception ex) {
			log.error("查找或执行批处理任务时发生错误: {}", jobName, ex);
		}
	}

}
