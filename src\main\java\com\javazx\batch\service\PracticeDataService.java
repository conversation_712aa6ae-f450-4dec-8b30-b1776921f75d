package com.javazx.batch.service;

import com.javazx.batch.mapper.YzBingrenyzMapper;
import com.javazx.batch.mapper.ZyBingrenxxMapper;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 诊疗数据服务类
 * 专门处理诊疗相关数据访问
 */
@Service
public class PracticeDataService {

    private static final Logger log = LoggerFactory.getLogger(PracticeDataService.class);

    @Autowired
    private YzBingrenyzMapper yzBingrenyzMapper;

    @Autowired
    private ZyBingrenxxMapper zyBingrenxxMapper;

    /**
     * 分页查询医嘱数据（按病区）
     */
    public List<YzBingrenyz> selectDiagnosisByPage(String bingquid, int offset, int limit) {
        try {
            log.debug("查询医嘱数据 - 病区: {}, 偏移: {}, 限制: {}", bingquid, offset, limit);
            return yzBingrenyzMapper.selectDoctorAdviceByPage(bingquid, offset, limit);
        } catch (Exception e) {
            log.error("查询医嘱数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计医嘱总数（按病区）
     */
    public int countDiagnosis(String bingquid) {
        try {
            return yzBingrenyzMapper.countDoctorAdvice(bingquid);
        } catch (Exception e) {
            log.error("统计医嘱总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据病人住院ID查询医嘱列表
     */
    public List<YzBingrenyz> selectDiagnosisByPatientId(String bingrenzyid) {
        try {
            return yzBingrenyzMapper.selectDoctorAdviceByPatientId(bingrenzyid);
        } catch (Exception e) {
            log.error("根据病人ID查询医嘱失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据病人住院ID查询患者信息
     */
    public ZyBingrenxx selectPatientById(String bingrenzyid) {
        try {
            return zyBingrenxxMapper.selectById(bingrenzyid);
        } catch (Exception e) {
            log.error("根据病人ID查询患者信息失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
