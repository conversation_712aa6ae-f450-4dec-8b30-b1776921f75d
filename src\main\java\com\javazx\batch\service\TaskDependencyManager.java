package com.javazx.batch.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 任务依赖管理器
 * 负责管理任务间的依赖关系，确保医护同步在患者同步之前完成
 */
@Slf4j
@Service
public class TaskDependencyManager {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private static final String TASK_COMPLETION_PREFIX = "task:completion:";
    private static final String TASK_EXECUTION_PREFIX = "task:execution:";
    private static final String CURRENT_EXECUTION_PREFIX = "task:current:";
    private static final String TASK_COUNT_PREFIX = "task:count:";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 标记任务开始执行
     * @param taskName 任务名称
     * @return 返回本次执行的唯一标识符（格式：executionCount:uuid）
     */
    public String markTaskStarted(String taskName) {
        String uuid = UUID.randomUUID().toString();
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);

        // 获取并递增执行次数
        long executionCount = getAndIncrementExecutionCount(taskName);

        // 组合执行标识符：次数:UUID
        String executionId = executionCount + ":" + uuid;

        // 使用执行次数和UUID作为唯一标识
        String executionKey = TASK_EXECUTION_PREFIX + taskName + ":" + executionCount + ":" + uuid;
        String currentKey = CURRENT_EXECUTION_PREFIX + taskName;

        // 设置执行状态，15分钟后自动过期
        redisTemplate.opsForValue().set(executionKey, "STARTED", 15, TimeUnit.MINUTES);
        // 设置当前执行信息，15分钟后自动过期
        redisTemplate.opsForValue().set(currentKey, executionId, 15, TimeUnit.MINUTES);

        log.info("任务 {} 开始执行，执行次数: {}, UUID: {}, 时间: {}", taskName, executionCount, uuid, currentTime);
        return executionId;
    }

    /**
     * 标记任务完成
     * @param taskName 任务名称
     * @param executionId 本次执行的唯一标识符（格式：executionCount:uuid）
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, String executionId, boolean success) {
        String currentTime = LocalDateTime.now().format(TIME_FORMATTER);
        String status = success ? "SUCCESS" : "FAILED";

        String[] parts = executionId.split(":", 2);
        if (parts.length != 2) {
            log.error("无效的执行ID格式: {}", executionId);
            return;
        }

        String executionCount = parts[0];
        String uuid = parts[1];

        String completionKey = TASK_COMPLETION_PREFIX + taskName + ":" + executionCount + ":" + uuid;

        redisTemplate.opsForValue().set(completionKey, status, 15, TimeUnit.MINUTES);

        log.info("任务 {} 执行完成，执行次数: {}, UUID: {}, 状态: {}, 时间: {}",
                taskName, executionCount, uuid, status, currentTime);
    }

    /**
     * 标记任务完成（向后兼容方法）
     * @param taskName 任务名称
     * @param success 是否成功
     */
    public void markTaskCompleted(String taskName, boolean success) {
        // 生成一个临时的执行ID用于兼容性
        String tempExecutionId = "manual-" + UUID.randomUUID().toString();
        markTaskCompleted(taskName, tempExecutionId, success);
    }

    /**
     * 检查依赖任务是否完成
     * @param dependencyTaskName 依赖的任务名称
     * @param currentTaskName 当前任务名称（用于获取执行次数）
     * @return 是否完成
     */
    public boolean isDependencyTaskCompleted(String dependencyTaskName, String currentTaskName) {
        try {
            long currentExecutionCount = getCurrentExecutionCount(currentTaskName);

            String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
            String dependencyExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (dependencyExecutionId == null) {
                log.debug("依赖任务 {} 当前执行ID不存在", dependencyTaskName);
                return false;
            }

            String[] parts = dependencyExecutionId.split(":", 2);
            if (parts.length != 2) {
                log.error("依赖任务 {} 执行ID格式无效: {}", dependencyTaskName, dependencyExecutionId);
                return false;
            }

            long dependencyExecutionCount = Long.parseLong(parts[0]);
            String dependencyUuid = parts[1];

            if (dependencyExecutionCount != currentExecutionCount) {
                log.debug("依赖任务 {} 执行次数不匹配，依赖任务次数: {}, 当前任务次数: {}",
                         dependencyTaskName, dependencyExecutionCount, currentExecutionCount);
                return false;
            }

            String completionKey = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + dependencyExecutionCount + ":" + dependencyUuid;
            String completionStatus = redisTemplate.opsForValue().get(completionKey);

            boolean isCompleted = "SUCCESS".equals(completionStatus);
            log.debug("检查依赖任务 {} 完成状态，执行次数: {}, UUID: {}, 状态: {}, 结果: {}",
                     dependencyTaskName, dependencyExecutionCount, dependencyUuid, completionStatus, isCompleted);

            return isCompleted;
        } catch (Exception e) {
            log.error("检查依赖任务 {} 完成状态时发生错误: {}", dependencyTaskName, e.getMessage());
            return false;
        }
    }

    /**
     * 检查依赖任务是否完成
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否完成
     */
    public boolean isDependencyTaskCompleted(String dependencyTaskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
            String currentExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (currentExecutionId != null) {
                // 解析执行ID
                String[] parts = currentExecutionId.split(":", 2);
                if (parts.length == 2) {
                    String executionCount = parts[0];
                    String uuid = parts[1];

                    // 检查当前执行ID对应的完成状态
                    String completionKey = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + executionCount + ":" + uuid;
                    String status = redisTemplate.opsForValue().get(completionKey);

                    if ("SUCCESS".equals(status)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查依赖任务 {} 完成状态时发生错误: {}", dependencyTaskName, e.getMessage());
        }

        return false;
    }

    /**
     * 检查依赖任务是否正在执行（基于当前执行ID）
     * @param dependencyTaskName 依赖的任务名称
     * @return 是否正在执行
     */
    public boolean isDependencyTaskRunning(String dependencyTaskName) {
        // 检查是否有当前执行的任务
        String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
        String currentExecutionId = redisTemplate.opsForValue().get(currentKey);

        if (currentExecutionId != null) {
            // 检查对应的执行状态
            String executionKey = TASK_EXECUTION_PREFIX + dependencyTaskName + ":" + currentExecutionId;
            String status = redisTemplate.opsForValue().get(executionKey);

            if ("STARTED".equals(status)) {
                return true;
            }
        }

        log.info("检查依赖任务 {} 运行状态: false", dependencyTaskName);
        return false;
    }

    /**
     * 等待依赖任务完成
     * @param dependencyTaskName 依赖的任务名称
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 是否在等待时间内完成
     */
    public boolean waitForDependencyTask(String dependencyTaskName, int maxWaitMinutes) {
        log.info("开始等待依赖任务 {} 完成，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        
        long startTime = System.currentTimeMillis();
        long maxWaitTime = maxWaitMinutes * 60 * 1000; // 转换为毫秒
        
        while (System.currentTimeMillis() - startTime < maxWaitTime) {
            // 检查是否已完成
            if (isDependencyTaskCompleted(dependencyTaskName)) {
                log.info("依赖任务 {} 已完成", dependencyTaskName);
                return true;
            }
            
            // 检查是否正在运行
            if (isDependencyTaskRunning(dependencyTaskName)) {
                log.info("依赖任务 {} 正在运行，继续等待...", dependencyTaskName);
            } else {
                log.warn("依赖任务 {} 未在运行且未完成，可能存在问题", dependencyTaskName);
            }
            
            try {
                Thread.sleep(10000); // 等待10秒后再次检查
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待依赖任务时被中断", e);
                return false;
            }
        }
        
        log.warn("等待依赖任务 {} 超时，最大等待时间: {} 分钟", dependencyTaskName, maxWaitMinutes);
        return false;
    }

    /**
     * 清理过期的任务状态
     */
    public void cleanupExpiredTaskStatus() {
        log.info("清理过期的任务状态");
    }

    /**
     * 清理依赖任务的完成状态，确保执行周期同步
     * @param dependencyTaskName 依赖任务名称
     */
    public void clearDependencyCompletionStatus(String dependencyTaskName) {
        try {
            String currentKey = CURRENT_EXECUTION_PREFIX + dependencyTaskName;
            String previousExecutionId = redisTemplate.opsForValue().get(currentKey);

            if (previousExecutionId != null) {
                // 解析执行ID
                String[] parts = previousExecutionId.split(":", 2);
                if (parts.length == 2) {
                    String executionCount = parts[0];
                    String uuid = parts[1];

                    // 清理依赖任务的完成状态
                    String previousCompletionKey = TASK_COMPLETION_PREFIX + dependencyTaskName + ":" + executionCount + ":" + uuid;
                    redisTemplate.delete(previousCompletionKey);

                    log.info("清理依赖任务 {} 的完成状态，确保执行周期同步，执行次数: {}, UUID: {}",
                            dependencyTaskName, executionCount, uuid);
                }
            }
        } catch (Exception e) {
            log.error("清理依赖任务 {} 完成状态时发生错误: {}", dependencyTaskName, e.getMessage());
        }
    }

    /**
     * 获取并递增任务执行次数
     * @param taskName 任务名称
     * @return 当前执行次数
     */
    private long getAndIncrementExecutionCount(String taskName) {
        String countKey = TASK_COUNT_PREFIX + taskName;
        Long count = redisTemplate.opsForValue().increment(countKey);
        return count != null ? count : 1L;
    }

    /**
     * 获取当前任务执行次数
     * @param taskName 任务名称
     * @return 当前执行次数
     */
    private long getCurrentExecutionCount(String taskName) {
        String countKey = TASK_COUNT_PREFIX + taskName;
        String count = redisTemplate.opsForValue().get(countKey);
        return count != null ? Long.parseLong(count) : 0;
    }
}
