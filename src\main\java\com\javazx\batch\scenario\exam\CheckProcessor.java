package com.javazx.batch.scenario.exam;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.mapper.resp.YjShenqingdanResp;
import com.javazx.batch.po.YjShenqingdan;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.CheckDataService;
import com.javazx.batch.util.PatientInfoUtil;
import com.javazx.batch.vo.PatientCheckInfoReq;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientWithCheckReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 检查数据处理器
 * 将YjShenqingdan对象转换为PatientWithCheckReq对象用于同步
 */
@Component
public class CheckProcessor implements ItemProcessor<YjShenqingdanResp, PatientWithCheckReq> {

    private static final Logger log = LoggerFactory.getLogger(CheckProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final CheckDataService checkDataService;
    private final PatientInfoUtil patientInfoUtil;

    public CheckProcessor(CheckDataService checkDataService, PatientInfoUtil patientInfoUtil) {
        this.checkDataService = checkDataService;
        this.patientInfoUtil = patientInfoUtil;
    }

    @Override
    public PatientWithCheckReq process(YjShenqingdanResp checkResp) {
        if (checkResp == null) {
            return null;
        }

        log.debug("处理检查申请单数据: {} - {}", checkResp.getZhuyuanhao(), checkResp.getYizhumc());

        try {
            PatientWithCheckReq request = convert(checkResp);

            log.debug("完成检查数据处理: {}", checkResp.getXingming());
            return request;

        } catch (Exception e) {
            log.error("处理检查申请单数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换检查数据为同步请求（新版本，直接使用响应对象）
     */
    private PatientWithCheckReq convert(YjShenqingdanResp checkResp) {
        PatientWithCheckReq request = new PatientWithCheckReq();

        // 创建患者信息（从响应对象创建简化版本）
        PatientInfoReq patientInfo = createPatientInfoFromResp(checkResp);
        request.setPatientInfo(patientInfo);

        // 创建检查信息列表
        List<PatientCheckInfoReq> checkInfoList = new ArrayList<>();
        PatientCheckInfoReq checkInfo = createCheckInfo(checkResp);
        checkInfoList.add(checkInfo);

        request.setPatientCheckList(checkInfoList);

        return request;
    }

    /**
     * 转换检查数据为同步请求（旧版本，保留兼容性）
     */
    private PatientWithCheckReq convert(ZyBingrenxx patient, List<YjShenqingdan> checks) {
        PatientWithCheckReq request = new PatientWithCheckReq();

        // 创建患者信息
        PatientInfoReq patientInfo = patientInfoUtil.createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建检查信息列表
        List<PatientCheckInfoReq> checkInfoList = new ArrayList<>();
        for (YjShenqingdan check : checks) {
            PatientCheckInfoReq checkInfo = createCheckInfoFromCheck(check, patient);
            checkInfoList.add(checkInfo);
        }

        request.setPatientCheckList(checkInfoList);

        return request;
    }

    /**
     * 从响应对象创建简化的患者信息
     */
    private PatientInfoReq createPatientInfoFromResp(YjShenqingdanResp checkResp) {
        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(checkResp.getXingming());
        patientInfo.setGender(0L); // 响应对象中没有性别信息，设为默认值

        // 住院信息
        patientInfo.setZyh(checkResp.getZhuyuanhao());
        patientInfo.setZyhm(checkResp.getBinganghao());
        patientInfo.setBedNo(checkResp.getDangqiancw());
        patientInfo.setWardCode(checkResp.getDangqianbq());

        // 时间信息
        if (checkResp.getYuyuesqrq() != null) {
            patientInfo.setAdmissionTime(checkResp.getYuyuesqrq().format(dateFormatter));
        }

        // 状态信息
        patientInfo.setPatientStatus(1L); // 1-在院

        return patientInfo;
    }





    /**
     * 创建检查信息对象（从响应对象）
     */
    private PatientCheckInfoReq createCheckInfo(YjShenqingdanResp checkResp) {
        PatientCheckInfoReq checkInfo = new PatientCheckInfoReq();

        // 基本信息
        checkInfo.setPatientName(checkResp.getXingming());
        checkInfo.setZyh(checkResp.getZhuyuanhao());
        checkInfo.setBedNo(checkResp.getDangqiancw());

        // 检查信息
        checkInfo.setCheckName(checkResp.getYizhumc());
        checkInfo.setCheckLocation(checkResp.getJianchadz()); // 检查地址

        // 时间信息
        if (checkResp.getYuyuesqrq() != null) {
            checkInfo.setCheckStartTime(checkResp.getYuyuesqrq().format(dateFormatter));
            checkInfo.setCheckEndTime(checkResp.getYuyuesqrq().format(dateFormatter));
        }

        // 状态信息
        checkInfo.setAppointmentStatus(convertAppointmentStatusFromResp(checkResp.getJianchazt()));
        checkInfo.setIsDisplay(1L); // 默认显示
        checkInfo.setIsFinish(convertFinishStatusFromResp(checkResp.getJianchazt()));

        // 特殊检查判断（根据检查名称判断）
        checkInfo.setIsSpecialCheck(isSpecialCheck(checkResp.getYizhumc()) ? 1L : 0L);

        // 空腹检查判断（根据检查名称判断）
        checkInfo.setIsFasting(isFastingCheck(checkResp.getYizhumc()) ? 1L : 0L);

        // 身份证号设为默认值
        checkInfo.setIdNumber(0L);

        return checkInfo;
    }

    /**
     * 创建检查信息对象（从检查对象）
     */
    private PatientCheckInfoReq createCheckInfoFromCheck(YjShenqingdan check, ZyBingrenxx patient) {
        PatientCheckInfoReq checkInfo = new PatientCheckInfoReq();

        // 基本信息
        checkInfo.setPatientName(patient.getXINGMING());
        checkInfo.setZyh(patient.getZHUYUANHAO());
        checkInfo.setBedNo(patient.getDANGQIANCW());

        // 检查信息
        checkInfo.setCheckName(check.getYIZHUMC());
        checkInfo.setCheckLocation(check.getJIANCHAKS()); // 检查科室作为检查地点

        // 时间信息
        if (check.getQIWANGJCRQ() != null) {
            checkInfo.setCheckStartTime(check.getQIWANGJCRQ().format(dateFormatter));
        }
        if (check.getJIANCHAAPRQ() != null) {
            checkInfo.setCheckEndTime(check.getJIANCHAAPRQ().format(dateFormatter));
        }

        // 状态信息
        checkInfo.setAppointmentStatus(convertAppointmentStatus(check.getDANGQIANZT()));
        checkInfo.setIsDisplay(1L); // 默认显示
        checkInfo.setIsFinish(convertFinishStatus(check.getDANGQIANZT()));
        
        // 特殊检查判断（根据检查名称判断）
        checkInfo.setIsSpecialCheck(isSpecialCheck(check.getYIZHUMC()) ? 1L : 0L);
        
        // 空腹检查判断（根据检查名称判断）
        checkInfo.setIsFasting(isFastingCheck(check.getYIZHUMC()) ? 1L : 0L);

        // 身份证号（如果有的话）
        if (StringUtils.hasLength(patient.getSHENFENZH())) {
            try {
                checkInfo.setIdNumber(Long.parseLong(patient.getSHENFENZH()));
            } catch (NumberFormatException e) {
                log.warn("身份证号格式错误: {}", patient.getSHENFENZH());
                checkInfo.setIdNumber(0L);
            }
        } else {
            checkInfo.setIdNumber(0L);
        }

        return checkInfo;
    }



    /**
     * 转换预约状态
     * @param dangqianzt 当前状态
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatus(String dangqianzt) {
        if (dangqianzt == null) {
            return 0L;
        }

        switch (dangqianzt.trim()) {
            case "1": // 申请
                return 0L; // 未预约
            case "2": // 预约
                return 1L; // 已预约
            case "3": // 完成
                return 2L; // 已完成
            case "4": // 取消
                return 3L; // 已取消
            default:
                return 0L;
        }
    }

    /**
     * 转换完成状态
     */
    private Long convertFinishStatus(String dangqianzt) {
        return "3".equals(dangqianzt) ? 1L : 0L;
    }

    /**
     * 转换预约状态（从响应对象的检查状态）
     * @param jianchazt 检查状态 (1-已完成, 0-未完成)
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatusFromResp(Integer jianchazt) {
        if (jianchazt == null) {
            return 0L;
        }
        return jianchazt == 1 ? 2L : 1L; // 1表示已完成，0表示已预约
    }

    /**
     * 转换完成状态（从响应对象的检查状态）
     */
    private Long convertFinishStatusFromResp(Integer jianchazt) {
        return jianchazt != null && jianchazt == 1 ? 1L : 0L;
    }

    /**
     * 判断是否为特殊检查
     */
    private boolean isSpecialCheck(String checkName) {
        if (checkName == null) {
            return false;
        }
        
        String name = checkName.toLowerCase();
        return name.contains("增强") || name.contains("造影") || 
               name.contains("介入") || name.contains("活检") ||
               name.contains("穿刺") || name.contains("内镜");
    }

    /**
     * 判断是否需要空腹
     */
    private boolean isFastingCheck(String checkName) {
        if (checkName == null) {
            return false;
        }

        String name = checkName.toLowerCase();
        return name.contains("腹部") || name.contains("胆囊") ||
               name.contains("肝脏") || name.contains("胰腺") ||
               name.contains("上腹部") || name.contains("全腹");
    }

    /**
     * 转换病区代码为Long类型
     */
    private Long convertWardCodeToLong(String wardCode) {
        if (StringUtils.hasLength(wardCode)) {
            try {
                return Long.parseLong(wardCode);
            } catch (NumberFormatException e) {
                log.warn("病区代码格式错误: {}", wardCode);
                return 0L;
            }
        }
        return 0L;
    }
}
