package com.javazx.batch.scenario.exam;

import com.javazx.batch.mapper.resp.YjShenqingdanResp;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.CheckDataService;
import com.javazx.batch.util.PatientInfoUtil;
import com.javazx.batch.vo.PatientCheckInfoReq;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientWithCheckReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 检查数据处理器
 * 将YjShenqingdan对象转换为PatientWithCheckReq对象用于同步
 */
@Component
public class CheckProcessor implements ItemProcessor<ZyBingrenxx, PatientWithCheckReq> {

    private static final Logger log = LoggerFactory.getLogger(CheckProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final CheckDataService checkDataService;
    private final PatientInfoUtil patientInfoUtil;

    public CheckProcessor(CheckDataService checkDataService, PatientInfoUtil patientInfoUtil) {
        this.checkDataService = checkDataService;
        this.patientInfoUtil = patientInfoUtil;
    }

    @Override
    public PatientWithCheckReq process(ZyBingrenxx zyBingrenxx) {
        if (zyBingrenxx == null) {
            return null;
        }

        try {
            List<YjShenqingdanResp> checkRespList = checkDataService.selectChecksByPatientId(zyBingrenxx.getBINGRENZYID());

            PatientWithCheckReq request = convert(zyBingrenxx,checkRespList);

            log.debug("完成检查数据处理: {}", zyBingrenxx.getXINGMING());
            return request;
        } catch (Exception e) {
            log.error("处理检查申请单数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换检查数据为同步请求
     */
    private PatientWithCheckReq convert(ZyBingrenxx patient, List<YjShenqingdanResp> checkRespList) {
        PatientWithCheckReq request = new PatientWithCheckReq();

        PatientInfoReq patientInfo = patientInfoUtil.createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建检查信息列表
        List<PatientCheckInfoReq> checkInfoList = new ArrayList<>();
        for (YjShenqingdanResp check : checkRespList) {
            PatientCheckInfoReq checkInfo = createCheck(check, patient);
            checkInfoList.add(checkInfo);
        }

        request.setPatientCheckList(checkInfoList);

        return request;
    }

    /**
     * 创建检查信息对象（从检查对象）
     */
    private PatientCheckInfoReq createCheck(YjShenqingdanResp check, ZyBingrenxx patient) {
        PatientCheckInfoReq checkInfo = new PatientCheckInfoReq();

        // 基本信息
        checkInfo.setPatientName(patient.getXINGMING());
        checkInfo.setZyh(patient.getZHUYUANHAO());
        checkInfo.setBedNo(patient.getDANGQIANCW());

        // 检查信息
        checkInfo.setCheckName(check.getYIZHUMC());
        checkInfo.setCheckLocation(check.getJIANCHADZ());

        // 时间信息
        if (check.getYUYUESQRQ() != null) {
            checkInfo.setAppointStartTime(check.getYUYUESQRQ().format(dateFormatter));
            checkInfo.setAppointEndTime(check.getYUYUESQRQ().format(dateFormatter));
        }

        // 状态信息
        checkInfo.setAppointmentStatus(convertAppointmentStatus(check.getDANGQIANZT()));
        checkInfo.setIsDisplay(1L);
        checkInfo.setIsFinish(convertFinishStatus(check.getDANGQIANZT()));
        
        // 特殊检查判断
        checkInfo.setIsSpecialCheck(isSpecialCheck(check.getYIZHUMC()) ? 1L : 0L);
        
        // 空腹检查判断
        checkInfo.setIsFasting(isFastingCheck(check.getYIZHUMC()) ? 1L : 0L);

        return checkInfo;
    }



    /**
     * 转换预约状态
     * @param dangqianzt 当前状态
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatus(String dangqianzt) {
        if (dangqianzt == null) {
            return 0L;
        }

        switch (dangqianzt.trim()) {
            case "1": // 申请
            case "2":
            case "3":
                return 0L; // 未预约
            case "4": // 预约
                return 1L; // 已预约
            case "9": // 取消
            case "10":
            case "11":
                return 3L; // 已取消
            default:
                return 0L;
        }
    }

    /**
     * 转换完成状态
     */
    private Long convertFinishStatus(String dangqianzt) {
        return "6".equals(dangqianzt) || "7".equals(dangqianzt) || "8".equals(dangqianzt) ? 1L : 0L;
    }

    /**
     * 转换预约状态（从响应对象的检查状态）
     * @param jianchazt 检查状态 (1-已完成, 0-未完成)
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatusFromResp(Integer jianchazt) {
        if (jianchazt == null) {
            return 0L;
        }
        return jianchazt == 1 ? 2L : 1L; // 1表示已完成，0表示已预约
    }

    /**
     * 转换完成状态（从响应对象的检查状态）
     */
    private Long convertFinishStatusFromResp(Integer jianchazt) {
        return jianchazt != null && jianchazt == 1 ? 1L : 0L;
    }

    /**
     * 判断是否为特殊检查
     */
    private boolean isSpecialCheck(String checkName) {
        if (checkName == null) {
            return false;
        }
        
        String name = checkName.toLowerCase();
        //筛选出所有胃肠镜相关的检查，包括胃镜、肠镜、无痛胃镜、无痛肠镜、胃肠联合
        return name.contains("胃肠镜") || name.contains("胃镜") ||
               name.contains("肠镜") || name.contains("无痛胃镜") ||
               name.contains("无痛肠镜") || name.contains("胃肠联合");
    }

    /**
     * 判断是否需要空腹
     */
    private boolean isFastingCheck(String checkName) {
        if (checkName == null) {
            return false;
        }

        String name = checkName.toUpperCase();
        //*腹部*，*肝*胆*脾*胰*、*增强磁共振*、*增强CT*
        return name.contains("腹部") || name.contains("肝") ||
               name.contains("胆") || name.contains("脾") ||
               name.contains("胰") || name.contains("增强磁共振") || name.contains("增强CT");
    }

    /**
     * 转换病区代码为Long类型
     */
    private Long convertWardCodeToLong(String wardCode) {
        if (StringUtils.hasLength(wardCode)) {
            try {
                return Long.parseLong(wardCode);
            } catch (NumberFormatException e) {
                log.warn("病区代码格式错误: {}", wardCode);
                return 0L;
            }
        }
        return 0L;
    }
}
