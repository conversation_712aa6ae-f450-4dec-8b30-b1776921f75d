package com.javazx.batch.scenario.exam;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.mapper.resp.YjShenqingdanResp;
import com.javazx.batch.po.YjShenqingdan;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.CheckDataService;
import com.javazx.batch.vo.PatientCheckInfoReq;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientWithCheckReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 检查数据处理器
 * 将YjShenqingdan对象转换为PatientWithCheckReq对象用于同步
 */
@Component
public class CheckProcessor implements ItemProcessor<YjShenqingdanResp, PatientWithCheckReq> {

    private static final Logger log = LoggerFactory.getLogger(CheckProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final CheckDataService checkDataService;

    public CheckProcessor(CheckDataService checkDataService) {
        this.checkDataService = checkDataService;
    }

    @Override
    public PatientWithCheckReq process(YjShenqingdanResp checkResp) {
        if (checkResp == null) {
            return null;
        }

        log.debug("处理检查申请单数据: {} - {}", checkResp.getZhuyuanhao(), checkResp.getYizhumc());

        try {
            PatientWithCheckReq request = convert(checkResp);

            log.debug("完成检查数据处理: {}", checkResp.getXingming());
            return request;

        } catch (Exception e) {
            log.error("处理检查申请单数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换检查数据为同步请求（新版本，直接使用响应对象）
     */
    private PatientWithCheckReq convert(YjShenqingdanResp checkResp) {
        PatientWithCheckReq request = new PatientWithCheckReq();

        // 创建患者信息
        PatientInfoReq patientInfo = createPatientInfo(checkResp);
        request.setPatientInfo(patientInfo);

        // 创建检查信息列表
        List<PatientCheckInfoReq> checkInfoList = new ArrayList<>();
        PatientCheckInfoReq checkInfo = createCheckInfo(checkResp);
        checkInfoList.add(checkInfo);

        request.setPatientCheckList(checkInfoList);

        return request;
    }

    /**
     * 转换检查数据为同步请求（旧版本，保留兼容性）
     */
    private PatientWithCheckReq convert(ZyBingrenxx patient, List<YjShenqingdan> checks) {
        PatientWithCheckReq request = new PatientWithCheckReq();

        // 创建患者信息
        PatientInfoReq patientInfo = createPatientInfoFromPatient(patient);
        request.setPatientInfo(patientInfo);

        // 创建检查信息列表
        List<PatientCheckInfoReq> checkInfoList = new ArrayList<>();
        for (YjShenqingdan check : checks) {
            PatientCheckInfoReq checkInfo = createCheckInfoFromCheck(check, patient);
            checkInfoList.add(checkInfo);
        }

        request.setPatientCheckList(checkInfoList);

        return request;
    }

    /**
     * 创建患者信息对象（从响应对象）
     */
    private PatientInfoReq createPatientInfo(ZyBingrenxx zyBingrenxx) {
        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(zyBingrenxx.getXINGMING());
        patientInfo.setDesensitizedName(desensitizeName(zyBingrenxx.getXINGMING()));
        patientInfo.setGender(convertGenderToLong(zyBingrenxx.getXINGBIE()));

        // 住院信息
        patientInfo.setZyh(zyBingrenxx.getBINGRENZYID());
        patientInfo.setZyhm(zyBingrenxx.getBINGANHAO());
        patientInfo.setBedNo(zyBingrenxx.getDANGQIANCW());
        patientInfo.setWardCode(zyBingrenxx.getDANGQIANBQ());

        // 时间信息
        if (zyBingrenxx.getRUYUANRQ() != null) {
            patientInfo.setAdmissionTime(zyBingrenxx.getRUYUANRQ().format(dateFormatter));
        }

        patientInfo.setDischargeTime("");

        // 诊断信息
        patientInfo.setDiagnosis(zyBingrenxx.getRUYUANZDMC());

        // 医护人员信息
        patientInfo.setDoctorJobNumber(zyBingrenxx.getZHUYUANYS() != null ? Long.valueOf(zyBingrenxx.getZHUYUANYS()) : null);
        patientInfo.setDoctorTitleShow("住院医师");
        patientInfo.setNurseJobNumber(zyBingrenxx.getZERENHS() != null ? Long.valueOf(zyBingrenxx.getZERENHS()) : null);
        patientInfo.setNurseTitleShow("责任护士");

        // 状态信息
        patientInfo.setPatientStatus(1L); // 1-在院

        // 身份证号码
        patientInfo.setIdNumber(zyBingrenxx.getSHENFENZH());
        // 生日信息
        if (zyBingrenxx.getCHUSHENGRQ() != null) {
            patientInfo.setBirthday(zyBingrenxx.getCHUSHENGRQ().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            //从身份证号码获取出生日期 格式 1981-03-22
            patientInfo.setBirthday(
                    Optional.ofNullable(zyBingrenxx.getSHENFENZH())
                            .filter(id -> id.length() >= 14)
                            .map(id -> String.format("%s-%s-%s", id.substring(6, 10), id.substring(10, 12), id.substring(12, 14)))
                            .orElse(null)
            );
        }
        // 医疗性质
        patientInfo.setMedicalNature(zyBingrenxx.getFEIYONGXZ());
        // 护理等级
        String nursingLevel = getNursingLevelFromOrder(zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
        if (!nursingLevel.trim().isEmpty()) {
            patientInfo.setNurseGrade(nursingLevel);
        }
        // 转科时间
        if (zyBingrenxx.getZHUANBINGQBZ() != null && zyBingrenxx.getZHUANBINGQBZ() > 0
                && zyBingrenxx.getRUKERQ() != null) {
            patientInfo.setTransferTime(zyBingrenxx.getRUKERQ().format(dateFormatter));
        }
        // 出院时间
        if (zyBingrenxx.getCHUYUANRQ() != null) {
            patientInfo.setDischargeTime(zyBingrenxx.getCHUYUANRQ().format(dateFormatter));
        } else if (zyBingrenxx.getYUCHUYRQ() != null) {
            // 如果没有实际出院时间，使用预出院时间
            patientInfo.setDischargeTime(zyBingrenxx.getYUCHUYRQ().format(dateFormatter));
        } else {
            patientInfo.setDischargeTime(null);
        }

        patientInfo.setIsWriteToAdvice(null);

        return patientInfo;
    }

    /**
     * 创建患者信息对象（从患者对象）
     */
    private PatientInfoReq createPatientInfoFromPatient(ZyBingrenxx patient) {
        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(patient.getXINGMING());
        patientInfo.setDesensitizedName(desensitizeName(patient.getXINGMING()));
        patientInfo.setGender(convertGenderToLong(patient.getXINGBIE()));

        // 住院信息
        patientInfo.setZyh(patient.getZHUYUANHAO());
        patientInfo.setZyhm(patient.getBINGANHAO());
        patientInfo.setBedNo(patient.getDANGQIANCW());
        patientInfo.setWardCode(patient.getDANGQIANBQ());

        // 时间信息
        if (patient.getRUYUANRQ() != null) {
            patientInfo.setAdmissionTime(patient.getRUYUANRQ().format(dateFormatter));
        }
        if (patient.getCHUYUANRQ() != null) {
            patientInfo.setDischargeTime(patient.getCHUYUANRQ().format(dateFormatter));
        }

        // 诊断信息
        patientInfo.setDiagnosis(patient.getRUYUANZDMC());

        // 医护人员信息
        patientInfo.setDoctorTitleShow(patient.getZHUZHIYSXM());
        patientInfo.setNurseTitleShow(patient.getZERENHSXM());

        // 状态信息
        patientInfo.setPatientStatus(1L); // 1-在院

        return patientInfo;
    }

    /**
     * 创建检查信息对象（从响应对象）
     */
    private PatientCheckInfoReq createCheckInfo(YjShenqingdanResp checkResp) {
        PatientCheckInfoReq checkInfo = new PatientCheckInfoReq();

        // 基本信息
        checkInfo.setPatientName(checkResp.getXingming());
        checkInfo.setZyh(checkResp.getZhuyuanhao());
        checkInfo.setBedNo(checkResp.getDangqiancw());

        // 检查信息
        checkInfo.setCheckName(checkResp.getYizhumc());
        checkInfo.setCheckLocation(checkResp.getJianchadz()); // 检查地址

        // 时间信息
        if (checkResp.getYuyuesqrq() != null) {
            checkInfo.setCheckStartTime(checkResp.getYuyuesqrq().format(dateFormatter));
            checkInfo.setCheckEndTime(checkResp.getYuyuesqrq().format(dateFormatter));
        }

        // 状态信息
        checkInfo.setAppointmentStatus(convertAppointmentStatusFromResp(checkResp.getJianchazt()));
        checkInfo.setIsDisplay(1L); // 默认显示
        checkInfo.setIsFinish(convertFinishStatusFromResp(checkResp.getJianchazt()));

        // 特殊检查判断（根据检查名称判断）
        checkInfo.setIsSpecialCheck(isSpecialCheck(checkResp.getYizhumc()) ? 1L : 0L);

        // 空腹检查判断（根据检查名称判断）
        checkInfo.setIsFasting(isFastingCheck(checkResp.getYizhumc()) ? 1L : 0L);

        // 身份证号设为默认值
        checkInfo.setIdNumber(0L);

        return checkInfo;
    }

    /**
     * 创建检查信息对象（从检查对象）
     */
    private PatientCheckInfoReq createCheckInfoFromCheck(YjShenqingdan check, ZyBingrenxx patient) {
        PatientCheckInfoReq checkInfo = new PatientCheckInfoReq();

        // 基本信息
        checkInfo.setPatientName(patient.getXINGMING());
        checkInfo.setZyh(patient.getZHUYUANHAO());
        checkInfo.setBedNo(patient.getDANGQIANCW());

        // 检查信息
        checkInfo.setCheckName(check.getYIZHUMC());
        checkInfo.setCheckLocation(check.getJIANCHAKS()); // 检查科室作为检查地点

        // 时间信息
        if (check.getQIWANGJCRQ() != null) {
            checkInfo.setCheckStartTime(check.getQIWANGJCRQ().format(dateFormatter));
        }
        if (check.getJIANCHAAPRQ() != null) {
            checkInfo.setCheckEndTime(check.getJIANCHAAPRQ().format(dateFormatter));
        }

        // 状态信息
        checkInfo.setAppointmentStatus(convertAppointmentStatus(check.getDANGQIANZT()));
        checkInfo.setIsDisplay(1L); // 默认显示
        checkInfo.setIsFinish(convertFinishStatus(check.getDANGQIANZT()));
        
        // 特殊检查判断（根据检查名称判断）
        checkInfo.setIsSpecialCheck(isSpecialCheck(check.getYIZHUMC()) ? 1L : 0L);
        
        // 空腹检查判断（根据检查名称判断）
        checkInfo.setIsFasting(isFastingCheck(check.getYIZHUMC()) ? 1L : 0L);

        // 身份证号（如果有的话）
        if (StringUtils.hasLength(patient.getSHENFENZH())) {
            try {
                checkInfo.setIdNumber(Long.parseLong(patient.getSHENFENZH()));
            } catch (NumberFormatException e) {
                log.warn("身份证号格式错误: {}", patient.getSHENFENZH());
                checkInfo.setIdNumber(0L);
            }
        } else {
            checkInfo.setIdNumber(0L);
        }

        return checkInfo;
    }

    /**
     * 脱敏姓名
     */
    private String desensitizeName(String name) {
        if (StringUtils.hasLength(name)) {
            var frontIndex = name.length() <= 2 ? 0 : 1;
            return DesensitizedUtil.chineseName(name);
        }
        return name;
    }

    /**
     * 转换性别为Long类型
     */
    private Long convertGenderToLong(String gender) {
        if (gender == null) {
            return 0L;
        }

        switch (gender.trim()) {
            case "男":
            case "1":
                return 1L;
            case "女":
            case "2":
                return 2L;
            default:
                return 0L;
        }
    }

    /**
     * 转换预约状态
     * @param dangqianzt 当前状态
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatus(String dangqianzt) {
        if (dangqianzt == null) {
            return 0L;
        }

        switch (dangqianzt.trim()) {
            case "1": // 申请
                return 0L; // 未预约
            case "2": // 预约
                return 1L; // 已预约
            case "3": // 完成
                return 2L; // 已完成
            case "4": // 取消
                return 3L; // 已取消
            default:
                return 0L;
        }
    }

    /**
     * 转换完成状态
     */
    private Long convertFinishStatus(String dangqianzt) {
        return "3".equals(dangqianzt) ? 1L : 0L;
    }

    /**
     * 转换预约状态（从响应对象的检查状态）
     * @param jianchazt 检查状态 (1-已完成, 0-未完成)
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatusFromResp(Integer jianchazt) {
        if (jianchazt == null) {
            return 0L;
        }
        return jianchazt == 1 ? 2L : 1L; // 1表示已完成，0表示已预约
    }

    /**
     * 转换完成状态（从响应对象的检查状态）
     */
    private Long convertFinishStatusFromResp(Integer jianchazt) {
        return jianchazt != null && jianchazt == 1 ? 1L : 0L;
    }

    /**
     * 判断是否为特殊检查
     */
    private boolean isSpecialCheck(String checkName) {
        if (checkName == null) {
            return false;
        }
        
        String name = checkName.toLowerCase();
        return name.contains("增强") || name.contains("造影") || 
               name.contains("介入") || name.contains("活检") ||
               name.contains("穿刺") || name.contains("内镜");
    }

    /**
     * 判断是否需要空腹
     */
    private boolean isFastingCheck(String checkName) {
        if (checkName == null) {
            return false;
        }

        String name = checkName.toLowerCase();
        return name.contains("腹部") || name.contains("胆囊") ||
               name.contains("肝脏") || name.contains("胰腺") ||
               name.contains("上腹部") || name.contains("全腹");
    }

    /**
     * 转换病区代码为Long类型
     */
    private Long convertWardCodeToLong(String wardCode) {
        if (StringUtils.hasLength(wardCode)) {
            try {
                return Long.parseLong(wardCode);
            } catch (NumberFormatException e) {
                log.warn("病区代码格式错误: {}", wardCode);
                return 0L;
            }
        }
        return 0L;
    }
}
