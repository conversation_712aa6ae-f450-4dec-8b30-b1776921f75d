package com.javazx.batch.scenario.exam;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.po.YjShenqingdan;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.CheckDataService;
import com.javazx.batch.vo.PatientCheckInfoReq;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientWithCheckReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 检查数据处理器
 * 将YjShenqingdan对象转换为PatientWithCheckReq对象用于同步
 */
@Component
public class CheckProcessor implements ItemProcessor<YjShenqingdan, PatientWithCheckReq> {

    private static final Logger log = LoggerFactory.getLogger(CheckProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final CheckDataService checkDataService;

    public CheckProcessor(CheckDataService checkDataService) {
        this.checkDataService = checkDataService;
    }

    @Override
    public PatientWithCheckReq process(YjShenqingdan yjShenqingdan) {
        if (yjShenqingdan == null) {
            return null;
        }

        log.debug("处理检查申请单数据: {} - {}", yjShenqingdan.getSHENQINDANID(), yjShenqingdan.getYIZHUMC());

        try {
            // 获取患者信息
            ZyBingrenxx patient = checkDataService.selectPatientById(yjShenqingdan.getBINGRENZYID());
            if (patient == null) {
                log.warn("未找到患者信息: {}", yjShenqingdan.getBINGRENZYID());
                return null;
            }

            // 获取该患者的所有检查申请单
            List<YjShenqingdan> patientChecks = checkDataService.selectChecksByPatientId(yjShenqingdan.getBINGRENZYID());

            // 转换为API请求对象
            PatientWithCheckReq request = convert(patient, patientChecks);

            log.debug("完成检查数据处理: {}", patient.getXINGMING());
            return request;
            
        } catch (Exception e) {
            log.error("处理检查申请单数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换检查数据为同步请求
     */
    private PatientWithCheckReq convert(ZyBingrenxx patient, List<YjShenqingdan> checks) {
        PatientWithCheckReq request = new PatientWithCheckReq();

        // 创建患者信息
        PatientInfoReq patientInfo = createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建检查信息列表
        List<PatientCheckInfoReq> checkInfoList = new ArrayList<>();
        for (YjShenqingdan check : checks) {
            PatientCheckInfoReq checkInfo = createCheckInfo(check, patient);
            checkInfoList.add(checkInfo);
        }
        
        request.setPatientCheckList(checkInfoList);

        return request;
    }

    /**
     * 创建患者信息对象
     */
    private PatientInfoReq createPatientInfo(ZyBingrenxx patient) {
        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(patient.getXINGMING());
        patientInfo.setDesensitizedName(desensitizeName(patient.getXINGMING()));
        patientInfo.setGender(convertGenderToLong(patient.getXINGBIE()));

        // 住院信息
        patientInfo.setZyh(patient.getZHUYUANHAO());
        patientInfo.setZyhm(patient.getBINGANHAO());
        patientInfo.setBedNo(patient.getDANGQIANCW());
        patientInfo.setWardCode(patient.getDANGQIANBQ());

        // 时间信息
        if (patient.getRUYUANRQ() != null) {
            patientInfo.setAdmissionTime(patient.getRUYUANRQ().format(dateFormatter));
        }
        if (patient.getCHUYUANRQ() != null) {
            patientInfo.setDischargeTime(patient.getCHUYUANRQ().format(dateFormatter));
        }

        // 诊断信息
        patientInfo.setDiagnosis(patient.getRUYUANZDMC());

        // 医护人员信息
        patientInfo.setDoctorTitleShow(patient.getZHUZHIYSXM());
        patientInfo.setNurseTitleShow(patient.getZERENHSXM());

        // 状态信息
        patientInfo.setPatientStatus(1L); // 1-在院

        return patientInfo;
    }

    /**
     * 创建检查信息对象
     */
    private PatientCheckInfoReq createCheckInfo(YjShenqingdan check, ZyBingrenxx patient) {
        PatientCheckInfoReq checkInfo = new PatientCheckInfoReq();

        // 基本信息
        checkInfo.setPatientName(patient.getXINGMING());
        checkInfo.setZyh(patient.getZHUYUANHAO());
        checkInfo.setBedNo(patient.getDANGQIANCW());

        // 检查信息
        checkInfo.setCheckName(check.getYIZHUMC());
        checkInfo.setCheckLocation(check.getJIANCHAKS()); // 检查科室作为检查地点

        // 时间信息
        if (check.getQIWANGJCRQ() != null) {
            checkInfo.setCheckStartTime(check.getQIWANGJCRQ().format(dateFormatter));
        }
        if (check.getJIANCHAAPRQ() != null) {
            checkInfo.setCheckEndTime(check.getJIANCHAAPRQ().format(dateFormatter));
        }

        // 状态信息
        checkInfo.setAppointmentStatus(convertAppointmentStatus(check.getDANGQIANZT()));
        checkInfo.setIsDisplay(1L); // 默认显示
        checkInfo.setIsFinish(convertFinishStatus(check.getDANGQIANZT()));
        
        // 特殊检查判断（根据检查名称判断）
        checkInfo.setIsSpecialCheck(isSpecialCheck(check.getYIZHUMC()) ? 1L : 0L);
        
        // 空腹检查判断（根据检查名称判断）
        checkInfo.setIsFasting(isFastingCheck(check.getYIZHUMC()) ? 1L : 0L);

        // 身份证号（如果有的话）
        if (StringUtils.hasLength(patient.getSHENFENZH())) {
            try {
                checkInfo.setIdNumber(Long.parseLong(patient.getSHENFENZH()));
            } catch (NumberFormatException e) {
                log.warn("身份证号格式错误: {}", patient.getSHENFENZH());
                checkInfo.setIdNumber(0L);
            }
        } else {
            checkInfo.setIdNumber(0L);
        }

        return checkInfo;
    }

    /**
     * 脱敏姓名
     */
    private String desensitizeName(String name) {
        if (StringUtils.hasLength(name)) {
            var frontIndex = name.length() <= 2 ? 0 : 1;
            return DesensitizedUtil.chineseName(name);
        }
        return name;
    }

    /**
     * 转换性别为Long类型
     */
    private Long convertGenderToLong(String gender) {
        if (gender == null) {
            return 0L;
        }

        switch (gender.trim()) {
            case "男":
            case "1":
                return 1L;
            case "女":
            case "2":
                return 2L;
            default:
                return 0L;
        }
    }

    /**
     * 转换预约状态
     * @param dangqianzt 当前状态
     * @return 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long convertAppointmentStatus(String dangqianzt) {
        if (dangqianzt == null) {
            return 0L;
        }

        switch (dangqianzt.trim()) {
            case "1": // 申请
                return 0L; // 未预约
            case "2": // 预约
                return 1L; // 已预约
            case "3": // 完成
                return 2L; // 已完成
            case "4": // 取消
                return 3L; // 已取消
            default:
                return 0L;
        }
    }

    /**
     * 转换完成状态
     */
    private Long convertFinishStatus(String dangqianzt) {
        return "3".equals(dangqianzt) ? 1L : 0L;
    }

    /**
     * 判断是否为特殊检查
     */
    private boolean isSpecialCheck(String checkName) {
        if (checkName == null) {
            return false;
        }
        
        String name = checkName.toLowerCase();
        return name.contains("增强") || name.contains("造影") || 
               name.contains("介入") || name.contains("活检") ||
               name.contains("穿刺") || name.contains("内镜");
    }

    /**
     * 判断是否需要空腹
     */
    private boolean isFastingCheck(String checkName) {
        if (checkName == null) {
            return false;
        }

        String name = checkName.toLowerCase();
        return name.contains("腹部") || name.contains("胆囊") ||
               name.contains("肝脏") || name.contains("胰腺") ||
               name.contains("上腹部") || name.contains("全腹");
    }

    /**
     * 转换病区代码为Long类型
     */
    private Long convertWardCodeToLong(String wardCode) {
        if (StringUtils.hasLength(wardCode)) {
            try {
                return Long.parseLong(wardCode);
            } catch (NumberFormatException e) {
                log.warn("病区代码格式错误: {}", wardCode);
                return 0L;
            }
        }
        return 0L;
    }
}
