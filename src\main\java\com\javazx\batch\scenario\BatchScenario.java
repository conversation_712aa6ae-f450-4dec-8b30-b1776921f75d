package com.javazx.batch.scenario;

public interface BatchScenario {
    
    /**
     * 获取场景名称
     * @return 场景名称
     */
    String getScenarioName();
    
    /**
     * 获取场景描述
     * @return 场景描述
     */
    String getScenarioDescription();
    
    /**
     * 获取读取器Bean名称
     * @return 读取器Bean名称
     */
    String getReaderBeanName();
    
    /**
     * 获取处理器Bean名称
     * @return 处理器Bean名称
     */
    String getProcessorBeanName();
    
    /**
     * 获取写入器Bean名称
     * @return 写入器Bean名称
     */
    String getWriterBeanName();
    
    /**
     * 获取作业名称
     * @return 作业名称
     */
    String getJobName();
    
    /**
     * 获取步骤名称
     * @return 步骤名称
     */
    String getStepName();
    
    /**
     * 获取提交间隔
     * @return 提交间隔
     */
    default int getCommitInterval() {
        return 1000;
    }
    
    /**
     * 获取页面大小
     * @return 页面大小
     */
    default int getPageSize() {
        return 100;
    }
    
    /**
     * 是否启用
     * @return 是否启用
     */
    default boolean isEnabled() {
        return true;
    }
}
