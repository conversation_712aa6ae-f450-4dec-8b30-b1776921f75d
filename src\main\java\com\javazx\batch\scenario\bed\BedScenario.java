package com.javazx.batch.scenario.bed;

import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.BedDataService;
import com.javazx.batch.vo.BedInfoReq;
import com.javazx.batch.vo.BedSyncRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 床位数据同步场景
 * 从数据库读取床位信息并同步到外部系统
 */
@Component
public class BedScenario extends AbstractBatchScenario<BedInfoReq, BedInfoReq> {

    private static final Logger log = LoggerFactory.getLogger(BedScenario.class);

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private BedDataService bedDataService;

    public BedScenario() {
        super("bed", "床位数据同步场景：同步床位信息数据");
        this.setCommitInterval(50);
        this.setPageSize(50);
    }

    @Override
    public ItemReader<BedInfoReq> createReader() {
        log.info("创建床位数据读取器，页面大小: {}", getPageSize());

        // 创建通用分页读取器
        GenericPagingItemReader<BedInfoReq> reader = new GenericPagingItemReader<>(
                "床位信息",
                getPageSize(),
                (offset, limit) -> bedDataService.selectBedsByPage("1006", offset, limit),
                () -> bedDataService.countBeds("1006")
        );

        // 记录总床位数量
        int totalCount = reader.getTotalCount();
        log.info("病区1006床位信息总数: {}", totalCount);

        return reader;
    }

    @Override
    public ItemProcessor<BedInfoReq, BedInfoReq> createProcessor() {
        log.info("创建床位数据处理器");
        return new BedProcessor();
    }

    @Override
    public ItemWriter<BedInfoReq> createWriter() {
        log.info("创建床位数据同步写入器");
        return items -> {
            BedSyncRequest request = new BedSyncRequest();
            request.setBedInfoList(List.copyOf(items));

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理床位数据量: {}", threadName, items.size());

            try {
                List<String> bedNos = getBedNosFromRequest(List.copyOf(items));
                String bedNosStr = String.join(", ", bedNos);
                
                smartwardWebClient
                        .post()
                        .uri("/sync/bed/sync_bed_info")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request.getBedInfoList())
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("床位 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("床位 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送床位数据: {} (线程: {})", bedNosStr, threadName);
            } catch (Exception e) {
                log.error("床位数据同步时发生异常: {}", e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理床位数据量: {}", threadName, items.size());
        };
    }

    /**
     * 从请求对象中获取床位号
     */
    private List<String> getBedNosFromRequest(List<BedInfoReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知床位");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(bedInfo -> bedInfo.getWardCode() + "-" + bedInfo.getBedNo())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
