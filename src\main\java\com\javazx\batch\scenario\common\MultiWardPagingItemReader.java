package com.javazx.batch.scenario.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStream;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 多病区分页读取器（线程安全版本）
 * 支持多个病区的数据分页读取，确保线程安全和不与其他读取器冲突
 * 实现ItemStream接口以支持Spring Batch生命周期管理
 *
 * @param <T> 数据类型
 */
public class MultiWardPagingItemReader<T> implements ItemReader<T>, ItemStream {

    private static final Logger log = LoggerFactory.getLogger(MultiWardPagingItemReader.class);

    private final String readerName;
    private final int pageSize;
    private final List<String> wardIds;
    private final BiFunction<String, Integer, List<T>> wardDataProvider; // (wardId, offset) -> List<T>
    private final Function<String, Integer> wardCountProvider; // wardId -> count

    // 使用原子变量和锁确保线程安全
    private final AtomicInteger currentWardIndex = new AtomicInteger(0);
    private final AtomicInteger currentWardOffset = new AtomicInteger(0);
    private final ReentrantLock lock = new ReentrantLock();
    private volatile boolean hasMoreData = true;
    private volatile String currentWardId = null;
    private volatile int currentWardTotalCount = 0;
    
    // 线程本地变量，每个线程维护自己的数据缓存
    private final ThreadLocal<List<T>> threadLocalData = new ThreadLocal<>();
    private final ThreadLocal<Integer> threadLocalIndex = new ThreadLocal<>();

    /**
     * 构造函数
     * 
     * @param readerName 读取器名称（用于日志）
     * @param pageSize 页面大小
     * @param wardIds 病区ID列表
     * @param wardDataProvider 病区数据提供者函数 (wardId, offset) -> List<T>
     * @param wardCountProvider 病区数据统计提供者函数 wardId -> Integer
     */
    public MultiWardPagingItemReader(String readerName, 
                                   int pageSize,
                                   List<String> wardIds,
                                   BiFunction<String, Integer, List<T>> wardDataProvider,
                                   Function<String, Integer> wardCountProvider) {
        this.readerName = readerName;
        this.pageSize = pageSize;
        this.wardIds = wardIds;
        this.wardDataProvider = wardDataProvider;
        this.wardCountProvider = wardCountProvider;
        
        if (wardIds != null && !wardIds.isEmpty()) {
            this.currentWardId = wardIds.get(0);
            try {
                this.currentWardTotalCount = wardCountProvider.apply(currentWardId);
            } catch (Exception e) {
                log.error("获取病区{}数据总数失败: {}", currentWardId, e.getMessage(), e);
                this.currentWardTotalCount = 0;
            }
        }
        
        log.info("初始化多病区{}数据读取器（线程安全版本），病区: {}, 页面大小: {}", 
                readerName, wardIds, pageSize);
    }

    @Override
    public T read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        // 获取当前线程的数据缓存
        List<T> currentPageData = threadLocalData.get();
        Integer currentIndex = threadLocalIndex.get();
        
        // 初始化线程本地变量
        if (currentIndex == null) {
            currentIndex = 0;
            threadLocalIndex.set(currentIndex);
        }
        
        // 如果当前线程的数据已读完，尝试加载下一批数据
        if (currentPageData == null || currentIndex >= currentPageData.size()) {
            if (!hasMoreData) {
                String threadName = Thread.currentThread().getName();
                log.debug("线程 {} 多病区{}数据读取完成", threadName, readerName);
                return null;
            }
            currentPageData = loadNextBatch();
            if (currentPageData == null || currentPageData.isEmpty()) {
                return null;
            }
            currentIndex = 0;
            threadLocalIndex.set(currentIndex);
        }

        // 返回当前数据项
        T item = currentPageData.get(currentIndex);
        threadLocalIndex.set(currentIndex + 1);
        
        String threadName = Thread.currentThread().getName();
        log.debug("线程 {} 读取病区{}{}数据: {}", threadName, currentWardId, readerName, item);
        return item;
    }

    /**
     * 线程安全地加载下一批数据
     * 支持多病区顺序处理，当前病区处理完毕后自动切换到下一个病区
     */
    private List<T> loadNextBatch() {
        lock.lock();
        try {
            if (!hasMoreData) {
                return null;
            }
            
            String threadName = Thread.currentThread().getName();
            
            // 检查是否需要切换到下一个病区
            while (currentWardIndex.get() < wardIds.size()) {
                String wardId = wardIds.get(currentWardIndex.get());
                
                // 如果当前病区ID发生变化，更新相关信息
                if (!wardId.equals(currentWardId)) {
                    currentWardId = wardId;
                    currentWardOffset.set(0);
                    try {
                        currentWardTotalCount = wardCountProvider.apply(currentWardId);
                        log.info("线程 {} 切换到病区: {}, 数据总数: {}", threadName, currentWardId, currentWardTotalCount);
                    } catch (Exception e) {
                        log.error("线程 {} 获取病区{}数据总数失败: {}", threadName, currentWardId, e.getMessage(), e);
                        currentWardTotalCount = 0;
                    }
                }
                
                // 尝试从当前病区加载数据
                int offset = currentWardOffset.getAndAdd(pageSize);

                log.debug("线程 {} 开始加载病区{}{}数据，偏移: {}, 限制: {}",
                         threadName, currentWardId, readerName, offset, pageSize);

                List<T> data = null;
                try {
                    data = wardDataProvider.apply(currentWardId, offset);
                } catch (Exception e) {
                    log.error("线程 {} 加载病区{}{}数据失败: {}", threadName, currentWardId, readerName, e.getMessage(), e);
                }
                
                if (data != null && !data.isEmpty()) {
                    log.debug("线程 {} 成功加载病区{}{}数据，数据量: {}", 
                             threadName, currentWardId, readerName, data.size());
                    
                    // 将数据存储到线程本地变量
                    threadLocalData.set(data);
                    return data;
                } else {
                    // 当前病区没有更多数据，切换到下一个病区
                    log.info("线程 {} 病区{}{}数据处理完毕，切换到下一个病区", threadName, currentWardId, readerName);
                    currentWardIndex.incrementAndGet();
                    currentWardOffset.set(0);
                }
            }
            
            // 所有病区都处理完毕
            hasMoreData = false;
            log.info("线程 {} 所有病区{}{}数据读取完毕", threadName, wardIds, readerName);
            return null;
            
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取所有病区的总数据量（用于监控和日志）
     */
    public int getTotalCount() {
        int totalCount = 0;
        for (String wardId : wardIds) {
            try {
                totalCount += wardCountProvider.apply(wardId);
            } catch (Exception e) {
                log.error("获取病区{}{}总数失败: {}", wardId, readerName, e.getMessage(), e);
            }
        }
        return totalCount;
    }
    
    /**
     * 获取当前病区的总数据量
     */
    public int getCurrentWardTotalCount() {
        return currentWardTotalCount;
    }
    
    /**
     * 获取当前处理的病区ID
     */
    public String getCurrentWardId() {
        return currentWardId;
    }
    
    /**
     * 获取病区列表
     */
    public List<String> getWardIds() {
        return wardIds;
    }

    /**
     * 获取当前病区的偏移量
     */
    public int getCurrentWardOffset() {
        return currentWardOffset.get();
    }
    
    /**
     * 清理线程本地变量，防止内存泄漏
     */
    public void cleanup() {
        threadLocalData.remove();
        threadLocalIndex.remove();
    }

    /**
     * 重置读取器状态（用于重新开始读取）
     */
    public void reset() {
        lock.lock();
        try {
            currentWardIndex.set(0);
            currentWardOffset.set(0);
            hasMoreData = true;
            if (wardIds != null && !wardIds.isEmpty()) {
                currentWardId = wardIds.get(0);
                try {
                    currentWardTotalCount = wardCountProvider.apply(currentWardId);
                } catch (Exception e) {
                    log.error("重置时获取病区{}数据总数失败: {}", currentWardId, e.getMessage(), e);
                    currentWardTotalCount = 0;
                }
            }
            cleanup();
            log.info("重置多病区{}数据读取器状态", readerName);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取读取器名称
     */
    public String getReaderName() {
        return readerName;
    }

    /**
     * 获取页面大小
     */
    public int getPageSize() {
        return pageSize;
    }

    // ========== ItemStream接口实现 ==========

    /**
     * 打开资源，在每次Job步骤开始时调用
     * 这里重置读取器状态，确保每次执行都从头开始
     */
    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        log.info("打开多病区{}数据读取器，重置状态", readerName);
        reset();
    }

    /**
     * 更新执行上下文，在检查点时调用
     * 保存当前读取进度到执行上下文
     */
    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        // 保存当前读取进度
        executionContext.putInt(readerName + ".currentWardIndex", currentWardIndex.get());
        executionContext.putInt(readerName + ".currentWardOffset", currentWardOffset.get());
        executionContext.putString(readerName + ".currentWardId", currentWardId);
        executionContext.putString(readerName + ".hasMoreData", String.valueOf(hasMoreData));

        log.debug("更新多病区{}数据读取器执行上下文，当前病区: {}, 病区索引: {}, 病区偏移: {}, 是否有更多数据: {}",
                 readerName, currentWardId, currentWardIndex.get(), currentWardOffset.get(), hasMoreData);
    }

    /**
     * 关闭资源，在Job步骤结束时调用
     * 清理线程本地变量，防止内存泄漏
     */
    @Override
    public void close() throws ItemStreamException {
        log.info("关闭多病区{}数据读取器，清理资源", readerName);
        cleanup();
    }
}
