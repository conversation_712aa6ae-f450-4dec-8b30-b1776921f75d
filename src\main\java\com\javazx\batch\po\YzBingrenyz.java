package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 医嘱_病人医嘱
 * @TableName YZ_BINGRENYZ
 */
@TableName(value ="YZ_BINGRENYZ")
@Data
@NoArgsConstructor
public class YzBingrenyz implements Serializable {
    /**
     * 医嘱ID
     */
    @TableId(value = "YIZHUID")
    private String YIZHUID;

    /**
     * 应用ID
     */
    @TableField(value = "YINGYONGID")
    private String YINGYONGID;

    /**
     * 院区ID
     */
    @TableField(value = "YUANQUID")
    private String YUANQUID;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 婴儿ID
     */
    @TableField(value = "YINGERID")
    private String YINGERID;

    /**
     * 病人姓名
     */
    @TableField(value = "BINGRENXM")
    private String BINGRENXM;

    /**
     * 医嘱项目ID
     */
    @TableField(value = "YIZHUXMID")
    private String YIZHUXMID;

    /**
     * 医嘱名称
     */
    @TableField(value = "YIZHUMC")
    private String YIZHUMC;

    /**
     * 开嘱时间
     */
    @TableField(value = "KAIZHUSJ")
    private LocalDateTime KAIZHUSJ;

    /**
     * 医嘱描述
     */
    @TableField(value = "YIZHUMS")
    private String YIZHUMS;

    /**
     * 输入时间
     */
    @TableField(value = "SHURUSJ")
    private LocalDateTime SHURUSJ;

    /**
     * 频次
     */
    @TableField(value = "PINCI")
    private String PINCI;

    /**
     * 输入人
     */
    @TableField(value = "SHURUREN")
    private String SHURUREN;

    /**
     * 开始时间
     */
    @TableField(value = "KAISHISJ")
    private LocalDateTime KAISHISJ;

    /**
     * 结束时间
     */
    @TableField(value = "JIESHUSJ")
    private LocalDateTime JIESHUSJ;

    /**
     * 医嘱分类:1 诊疗2西药3成药4草药7大输液
8文本9检查10检验11血液12手术13会诊14高压氧
     */
    @TableField(value = "YIZHUFL")
    private String YIZHUFL;

    /**
     * 给药方式
     */
    @TableField(value = "GEIYAOFS")
    private String GEIYAOFS;

    /**
     * 给药方式类型
     */
    @TableField(value = "GEIYAOFSLX")
    private Integer GEIYAOFSLX;

    /**
     * 给药方式名称
     */
    @TableField(value = "GEIYAOFSMC")
    private String GEIYAOFSMC;

    /**
     * 开嘱医生
     */
    @TableField(value = "KAIZHUYS")
    private String KAIZHUYS;

    /**
     * 开嘱医生姓名
     */
    @TableField(value = "KAIZHUYSXM")
    private String KAIZHUYSXM;

    /**
     * 执行时间
     */
    @TableField(value = "ZHIXINGSJ")
    private LocalDateTime ZHIXINGSJ;

    /**
     * 执行人
     */
    @TableField(value = "ZHIXINGREN")
    private String ZHIXINGREN;

    /**
     * 停嘱时间
     */
    @TableField(value = "TINGZHUSJ")
    private LocalDateTime TINGZHUSJ;

    /**
     * 复核时间
     */
    @TableField(value = "FUHESJ")
    private LocalDateTime FUHESJ;

    /**
     * 复核人
     */
    @TableField(value = "FUHEREN")
    private String FUHEREN;

    /**
     * 停嘱人
     */
    @TableField(value = "TINGZHUREN")
    private String TINGZHUREN;

    /**
     * 停嘱复核人
     */
    @TableField(value = "TINGZHUFHR")
    private String TINGZHUFHR;

    /**
     * 停嘱复核时间
     */
    @TableField(value = "TINGZHUFHSJ")
    private LocalDateTime TINGZHUFHSJ;

    /**
     * 规格ID
     */
    @TableField(value = "GUIGEID")
    private String GUIGEID;

    /**
     * 撤消人
     */
    @TableField(value = "CHEXIAOREN")
    private String CHEXIAOREN;

    /**
     * 医嘱状态:1 未确认 2 已确认 3 已执行 4 已停止 5 已撤销
     */
    @TableField(value = "YIZHUZT")
    private String YIZHUZT;

    /**
     * 当前状态:1未确认,2已确认,3新更动,4未执行,5已执行,6不执行,7已停止,8待撤销,9已撤销,10待退药,11已退药,12已发药,13缺药,14已完成 
     */
    @TableField(value = "DANGQIANZT")
    private String DANGQIANZT;

    /**
     * 剂量单位
     */
    @TableField(value = "JILIANGDW")
    private String JILIANGDW;

    /**
     * 包装单位
     */
    @TableField(value = "BAOZHUANGDW")
    private String BAOZHUANGDW;

    /**
     * 最小单位
     */
    @TableField(value = "ZUIXIAODW")
    private String ZUIXIAODW;

    /**
     * 包装量
     */
    @TableField(value = "BAOZHUANGLIANG")
    private BigDecimal BAOZHUANGLIANG;

    /**
     * 父医嘱名
     */
    @TableField(value = "FUYIZMC")
    private String FUYIZMC;

    /**
     * 药房应用ID
     */
    @TableField(value = "YAOFANGYYID")
    private String YAOFANGYYID;

    /**
     * 药品名称
     */
    @TableField(value = "YAOPINMC")
    private String YAOPINMC;

    /**
     * 价格ID
     */
    @TableField(value = "JIAGEID")
    private String JIAGEID;

    /**
     * 药品别名ID
     */
    @TableField(value = "YAOPINBMID")
    private String YAOPINBMID;

    /**
     * 药品规格
     */
    @TableField(value = "YAOPINGG")
    private String YAOPINGG;

    /**
     * 排斥时间
     */
    @TableField(value = "PAICHISJ")
    private LocalDateTime PAICHISJ;

    /**
     * 排斥医嘱ID
     */
    @TableField(value = "PAICHIYZID")
    private String PAICHIYZID;

    /**
     * 剂型0普通1大输液2针剂
     */
    @TableField(value = "JIXING")
    private Integer JIXING;

    /**
     * 皮试结果
     */
    @TableField(value = "PISHIJG")
    private String PISHIJG;

    /**
     * 领药方式
     */
    @TableField(value = "LINGYAOFS")
    private String LINGYAOFS;

    /**
     * 打印标志
     */
    @TableField(value = "DAYINBZ")
    private Integer DAYINBZ;

    /**
     * 父医嘱ID
     */
    @TableField(value = "FUYIZID")
    private String FUYIZID;

    /**
     * 医生组ID
     */
    @TableField(value = "YISHENGZID")
    private String YISHENGZID;

    /**
     * 单价
     */
    @TableField(value = "DANJIA")
    private BigDecimal DANJIA;

    /**
     * 医生嘱托
     */
    @TableField(value = "YISHENGZT")
    private String YISHENGZT;

    /**
     * 执行用量
     */
    @TableField(value = "ZHIXINGYL")
    private String ZHIXINGYL;

    /**
     * 科室ID
     */
    @TableField(value = "KESHIID")
    private String KESHIID;

    /**
     * 组号
     */
    @TableField(value = "ZUHAO")
    private String ZUHAO;

    /**
     * 排斥类型
     */
    @TableField(value = "PAICHILX")
    private Integer PAICHILX;

    /**
     * 排序编号
     */
    @TableField(value = "PAIXUBH")
    private String PAIXUBH;

    /**
     * 首日次数
     */
    @TableField(value = "SHOURICS")
    private BigDecimal SHOURICS;

    /**
     * 当日次数
     */
    @TableField(value = "DANGRICS")
    private BigDecimal DANGRICS;

    /**
     * 打印剂量
     */
    @TableField(value = "DAYINJL")
    private String DAYINJL;

    /**
     * 末日次数
     */
    @TableField(value = "MORICS")
    private BigDecimal MORICS;

    /**
     * 开嘱病区
     */
    @TableField(value = "KAIZHUBQ")
    private String KAIZHUBQ;

    /**
     * 开嘱科室
     */
    @TableField(value = "KAIZHUKS")
    private String KAIZHUKS;

    /**
     * 撤销时间
     */
    @TableField(value = "CHEXIAOSJ")
    private LocalDateTime CHEXIAOSJ;

    /**
     * 状态时间
     */
    @TableField(value = "ZHUANGTAISJ")
    private LocalDateTime ZHUANGTAISJ;

    /**
     * 产地类别
     */
    @TableField(value = "CHANDILB")
    private String CHANDILB;

    /**
     * 病区ID
     */
    @TableField(value = "BINGQUID")
    private String BINGQUID;

    /**
     * 一次剂量
     */
    @TableField(value = "YICIJL")
    private BigDecimal YICIJL;

    /**
     * 一次剂量单位
     */
    @TableField(value = "YICIJLDW")
    private String YICIJLDW;

    /**
     * 一次用量
     */
    @TableField(value = "YICIYL")
    private BigDecimal YICIYL;

    /**
     * 剂量
     */
    @TableField(value = "JILIANG")
    private BigDecimal JILIANG;

    /**
     * 强制标志
     */
    @TableField(value = "QIANGZHIBZ")
    private Integer QIANGZHIBZ;

    /**
     * 开嘱科室名称
     */
    @TableField(value = "KAIZHUKSMC")
    private String KAIZHUKSMC;

    /**
     * 开嘱病区名称
     */
    @TableField(value = "KAIZHUBQMC")
    private String KAIZHUBQMC;

    /**
     * 急诊标志
     */
    @TableField(value = "JIZHENBZ")
    private Integer JIZHENBZ;

    /**
     * 处理意见
     */
    @TableField(value = "CHULIYJ")
    private String CHULIYJ;

    /**
     * 病人ID
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 最小单位执行用量
     */
    @TableField(value = "ZUIXIAODWZXYL")
    private String ZUIXIAODWZXYL;

    /**
     * 最小单位用量
     */
    @TableField(value = "ZUIXIAODWYL")
    private BigDecimal ZUIXIAODWYL;

    /**
     * 皮试标志
     */
    @TableField(value = "PISHIBZ")
    private Integer PISHIBZ;

    /**
     * 服药顺序
     */
    @TableField(value = "FUYAOSX")
    private String FUYAOSX;

    /**
     * 他科标志
     */
    @TableField(value = "TAKEBZ")
    private Integer TAKEBZ;

    /**
     * 夜间标志
     */
    @TableField(value = "YEJIANBZ")
    private Integer YEJIANBZ;

    /**
     * 皮试类型
     */
    @TableField(value = "PISHILX")
    private String PISHILX;

    /**
     * 皮试复核人1
     */
    @TableField(value = "PISHIFHR1")
    private String PISHIFHR1;

    /**
     * 皮试复核时间1
     */
    @TableField(value = "PISHIFHSJ1")
    private LocalDateTime PISHIFHSJ1;

    /**
     * 皮试复核人2
     */
    @TableField(value = "PISHIFHR2")
    private String PISHIFHR2;

    /**
     * 皮试复核时间2
     */
    @TableField(value = "PISHIFHSJ2")
    private LocalDateTime PISHIFHSJ2;

    /**
     * 计费间隔
     */
    @TableField(value = "JIFEIJG")
    private Integer JIFEIJG;

    /**
     * 计费方式
     */
    @TableField(value = "JIFEIFS")
    private String JIFEIFS;

    /**
     * 执行时间类型
     */
    @TableField(value = "ZHIXINGSJLX")
    private String ZHIXINGSJLX;

    /**
     * 费用性质转换标志
     */
    @TableField(value = "FEIYONGXZZHBZ")
    private Integer FEIYONGXZZHBZ;

    /**
     * 静脉配标志
     */
    @TableField(value = "JINGMAIPBZ")
    private Integer JINGMAIPBZ;

    /**
     * 发药状态
     */
    @TableField(value = "FAYAOZT")
    private String FAYAOZT;

    /**
     * 原药房应用ID
     */
    @TableField(value = "YUANYAOFYYID")
    private String YUANYAOFYYID;

    /**
     * 自付比例
     */
    @TableField(value = "ZIFUBL")
    private String ZIFUBL;

    /**
     * 草药标志
     */
    @TableField(value = "CAOYAOBZ")
    private Integer CAOYAOBZ;

    /**
     *  代煎帖数
     */
    @TableField(value = "DAIJIANTS")
    private BigDecimal DAIJIANTS;

    /**
     * 停嘱病区
     */
    @TableField(value = "TINGZHUBQ")
    private String TINGZHUBQ;

    /**
     * 排序组号
     */
    @TableField(value = "PAIXUZH")
    private String PAIXUZH;

    /**
     * 欠费审核标志
     */
    @TableField(value = "QIANFEISHBZ")
    private Integer QIANFEISHBZ;

    /**
     * 预约日期
     */
    @TableField(value = "YUYUERQ")
    private LocalDateTime YUYUERQ;

    /**
     * 执行科室计费标志
     */
    @TableField(value = "ZHIXINGKSJFBZ")
    private Integer ZHIXINGKSJFBZ;

    /**
     * 执行科室
     */
    @TableField(value = "ZHIXINGKS")
    private String ZHIXINGKS;

    /**
     * 分区日期
     */
    @TableField(value = "FENQURQ")
    private LocalDateTime FENQURQ;

    /**
     * 频次执行时间
     */
    @TableField(value = "PINCIZXSJ")
    private String PINCIZXSJ;

    /**
     * 医院审批标志
     */
    @TableField(value = "YIYUANSPBZ")
    private Integer YIYUANSPBZ;

    /**
     * 医院审批类型
     */
    @TableField(value = "YIYUANSPLX")
    private Integer YIYUANSPLX;

    /**
     * 医院审批说明
     */
    @TableField(value = "YIYUANSPSM")
    private String YIYUANSPSM;

    /**
     * 医院审批比例
     */
    @TableField(value = "YIYUANSPBL")
    private String YIYUANSPBL;

    /**
     * 交班核对标志
     */
    @TableField(value = "JIAOBANHDBZ")
    private String JIAOBANHDBZ;

    /**
     * 总核对序号
     */
    @TableField(value = "ZONGHEDXH")
    private String ZONGHEDXH;

    /**
     * 交班核对时间
     */
    @TableField(value = "JIAOBANHDSJ")
    private LocalDateTime JIAOBANHDSJ;

    /**
     * 交班核对人
     */
    @TableField(value = "JIAOBANHDR")
    private String JIAOBANHDR;

    /**
     * 附加费用计费方式
     */
    @TableField(value = "FUJIAFYJFFS")
    private String FUJIAFYJFFS;

    /**
     * 原当前状态
     */
    @TableField(value = "YUANDANGQZT")
    private String YUANDANGQZT;

    /**
     * 原医嘱状态
     */
    @TableField(value = "YUANYIZZT")
    private String YUANYIZZT;

    /**
     * 冻结标志
     */
    @TableField(value = "DONGJIEBZ")
    private Integer DONGJIEBZ;

    /**
     * 解冻日期
     */
    @TableField(value = "JIEDONGRQ")
    private LocalDateTime JIEDONGRQ;

    /**
     * 退药审批标志
     */
    @TableField(value = "TUIYAOSPBZ")
    private Integer TUIYAOSPBZ;

    /**
     * 药品剂型
     */
    @TableField(value = "YAOPINJX")
    private String YAOPINJX;

    /**
     * 执行日期
     */
    @TableField(value = "ZHIXINGRQ")
    private String ZHIXINGRQ;

    /**
     * 执行状态
     */
    @TableField(value = "ZHIXINGZT")
    private Integer ZHIXINGZT;

    /**
     * 卡片打印标志
     */
    @TableField(value = "KAPIANDYBZ")
    private String KAPIANDYBZ;

    /**
     * 药品取整类型：1-单次取整；2-单日累计；3-逐日累计
     */
    @TableField(value = "QUZHENGLX")
    private String QUZHENGLX;

    /**
     * 冻结日期
     */
    @TableField(value = "DONGJIERQ")
    private LocalDateTime DONGJIERQ;

    /**
     * 请假标志
     */
    @TableField(value = "QINGJIABZ")
    private Integer QINGJIABZ;

    /**
     * 医院审批日期
     */
    @TableField(value = "YIYUANSPRQ")
    private LocalDateTime YIYUANSPRQ;

    /**
     * 变更单打印标志
     */
    @TableField(value = "BIANGENGDDYBZ")
    private String BIANGENGDDYBZ;

    /**
     * 撤销类型:1医生撤销2病人撤销3
     */
    @TableField(value = "CHEXIAOLX")
    private String CHEXIAOLX;

    /**
     * 撤销原因
     */
    @TableField(value = "CHEXIAOYY")
    private String CHEXIAOYY;

    /**
     * 原执行时间
     */
    @TableField(value = "YUANZHIXSJ")
    private LocalDateTime YUANZHIXSJ;

    /**
     * 出院带药标志
     */
    @TableField(value = "CHUYUANDYBZ")
    private Integer CHUYUANDYBZ;

    /**
     * 长期临时标志:1长期 2临时null临时
     */
    @TableField(value = "CHANGQILSBZ")
    private Integer CHANGQILSBZ;

    /**
     * 执行标志
     */
    @TableField(value = "ZHIXINGBZ")
    private Integer ZHIXINGBZ;

    /**
     * 交班核对人2
     */
    @TableField(value = "JIAOBANHDR2")
    private String JIAOBANHDR2;

    /**
     * 工作站ID
     */
    @TableField(value = "GONGZUOZID")
    private String GONGZUOZID;

    /**
     * 草药特殊用法
     */
    @TableField(value = "CAOYAOTSYF")
    private String CAOYAOTSYF;

    /**
     * 抗生素使用说明HR3-9277(136526)用药时机-I类切口越级原因的组合
     */
    @TableField(value = "KANGSHENGSSYSM")
    private String KANGSHENGSSYSM;

    /**
     * 审批医生HR3-11879(157299)
     */
    @TableField(value = "SHENPIYS")
    private String SHENPIYS;

    /**
     * 审批内容HR3-11879(157299)
     */
    @TableField(value = "SHENPINR")
    private String SHENPINR;

    /**
     * 滴速HR3-13043(169274)
     */
    @TableField(value = "DISHU")
    private String DISHU;

    /**
     * 避光标志HR3-13043(169274)
     */
    @TableField(value = "BIGUANGBZ")
    private Integer BIGUANGBZ;

    /**
     * 中药配方颗粒标志HR3-13370(172230)
     */
    @TableField(value = "ZHONGYAOPFKLBZ")
    private Integer ZHONGYAOPFKLBZ;

    /**
     * 库存不足标志HR3-12233(160538)
     */
    @TableField(value = "KUCUNBZBZ")
    private Integer KUCUNBZBZ;

    /**
     * 变更汇总打印标志HR3-14286(181628)
     */
    @TableField(value = "BIANGENGHZDYBZ")
    private String BIANGENGHZDYBZ;

    /**
     * 成组医嘱IDHR3-14588(184999)
     */
    @TableField(value = "CHENGZUYZID")
    private String CHENGZUYZID;

    /**
     * 药品联动IDHR3-14947(188666)
     */
    @TableField(value = "YAOPINLDID")
    private String YAOPINLDID;

    /**
     * 中药颗粒转换数量HR3-15404(193499)
     */
    @TableField(value = "ZHONGYAOKLZHSL")
    private BigDecimal ZHONGYAOKLZHSL;

    /**
     * 抗菌药物使用1HR3-15537(194445)用药时机
     */
    @TableField(value = "KANGJUNYWSY1")
    private String KANGJUNYWSY1;

    /**
     * 抗菌药物使用2HR3-15537(194445)用药目的
     */
    @TableField(value = "KANGJUNYWSY2")
    private String KANGJUNYWSY2;

    /**
     * 抗菌药物使用3HR3-15537(194445)疗程
     */
    @TableField(value = "KANGJUNYWSY3")
    private String KANGJUNYWSY3;

    /**
     * 抗菌药物使用4HR3-15537(194445)使用依据
     */
    @TableField(value = "KANGJUNYWSY4")
    private String KANGJUNYWSY4;

    /**
     * 抗菌药物使用5HR3-15537(194445)抗菌药限级
     */
    @TableField(value = "KANGJUNYWSY5")
    private String KANGJUNYWSY5;

    /**
     * 抗菌药物使用6HR3-15537(194445)I类切口越级原因
     */
    @TableField(value = "KANGJUNYWSY6")
    private String KANGJUNYWSY6;

    /**
     * 抗菌药物使用7HR3-15537(194445)紧急程度
     */
    @TableField(value = "KANGJUNYWSY7")
    private String KANGJUNYWSY7;

    /**
     * 抗菌药物使用8HR3-15537(194445)
     */
    @TableField(value = "KANGJUNYWSY8")
    private String KANGJUNYWSY8;

    /**
     * 抗菌药物使用9HR3-15537(194445)
     */
    @TableField(value = "KANGJUNYWSY9")
    private String KANGJUNYWSY9;

    /**
     * 毒理分类HR3-15742(196171)
     */
    @TableField(value = "DULIFL")
    private String DULIFL;

    /**
     * 其他属性HR3-15898(197227)
     */
    @TableField(value = "QITASX")
    private String QITASX;

    /**
     * 会诊医嘱审批标志HR3-16607(202304)
     */
    @TableField(value = "HUIZHENYZSPBZ")
    private Integer HUIZHENYZSPBZ;

    /**
     * 会诊医嘱审批医生HR3-16607(202304)
     */
    @TableField(value = "HUIZHENYZSPYS")
    private String HUIZHENYZSPYS;

    /**
     * 浓煎剂标志HR3-16746(203169)
     */
    @TableField(value = "NONGJIANJBZ")
    private Integer NONGJIANJBZ;

    /**
     * 临床路径导入标志HR3-17082(205576)
     */
    @TableField(value = "LINCHUANGLJDRBZ")
    private Integer LINCHUANGLJDRBZ;

    /**
     * 药物属性HR3-18045(211225)
     */
    @TableField(value = "YAOWUSX")
    private Integer YAOWUSX;

    /**
     * 膏方标志HR3-18607(215078)
     */
    @TableField(value = "GAOFANGBZ")
    private Integer GAOFANGBZ;

    /**
     * 领药人姓名HR3-18663(215386)
     */
    @TableField(value = "LINGYAORXM")
    private String LINGYAORXM;

    /**
     * 领药人身份证号HR3-18663(215386)
     */
    @TableField(value = "LINGYAORSFZH")
    private String LINGYAORSFZH;

    /**
     * 毒麻卡号HR3-18663(215386)
     */
    @TableField(value = "DUMAKH")
    private String DUMAKH;

    /**
     * 药品产地HR3-18682(215554)
     */
    @TableField(value = "CHANDI")
    private String CHANDI;

    /**
     * 产地名称【医保用】
     */
    @TableField(value = "CHANDIMC")
    private String CHANDIMC;

    /**
     * 免费药品标志HR3-19254(220103)
     */
    @TableField(value = "MIANFEIYPBZ")
    private Integer MIANFEIYPBZ;

    /**
     * 原结束时间 HR3-19552(223368) 
     */
    @TableField(value = "YUANJIESSJ")
    private LocalDateTime YUANJIESSJ;

    /**
     * 原停嘱人 HR3-19552(223368) 
     */
    @TableField(value = "YUANTINGZR")
    private String YUANTINGZR;

    /**
     * 原停嘱病区 HR3-19552(223368) 
     */
    @TableField(value = "YUANTINGZBQ")
    private String YUANTINGZBQ;

    /**
     * 原停嘱时间 HR3-19552(223368) 
     */
    @TableField(value = "YUANTINGZSJ")
    private LocalDateTime YUANTINGZSJ;

    /**
     * 医嘱接口IDMDReg-142(213260)
     */
    @TableField(value = "YIZHUJKID")
    private String YIZHUJKID;

    /**
     * 审方状态MDReg-178(224150)
     */
    @TableField(value = "SHENFANGZT")
    private String SHENFANGZT;

    /**
     * 审方结果MDReg-178(224150)
     */
    @TableField(value = "SHENFANGJG")
    private String SHENFANGJG;

    /**
     * 审方人MDReg-178(224150)
     */
    @TableField(value = "SHENFANGREN")
    private String SHENFANGREN;

    /**
     * 审方时间MDReg-178(224150)
     */
    @TableField(value = "SHENFANGSJ")
    private LocalDateTime SHENFANGSJ;

    /**
     * 抗菌药物使用10HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY10")
    private String KANGJUNYWSY10;

    /**
     * 抗菌药物使用11HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY11")
    private String KANGJUNYWSY11;

    /**
     * 抗菌药物使用12HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY12")
    private String KANGJUNYWSY12;

    /**
     * 抗菌药物使用13HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY13")
    private String KANGJUNYWSY13;

    /**
     * 抗菌药物使用14HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY14")
    private String KANGJUNYWSY14;

    /**
     * 抗菌药物使用15HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY15")
    private String KANGJUNYWSY15;

    /**
     * 抗菌药物使用16HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY16")
    private String KANGJUNYWSY16;

    /**
     * 抗菌药物使用17HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY17")
    private String KANGJUNYWSY17;

    /**
     * 抗菌药物使用18HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY18")
    private String KANGJUNYWSY18;

    /**
     * 抗菌药物使用19HR3-19868(227300)
     */
    @TableField(value = "KANGJUNYWSY19")
    private String KANGJUNYWSY19;

    /**
     * 抗菌药物使用20HR3-19868(227300)关联电子病历序号
     */
    @TableField(value = "KANGJUNYWSY20")
    private String KANGJUNYWSY20;

    /**
     * 主治医生HR3-20445(237395)
     */
    @TableField(value = "ZHUZHIYS")
    private String ZHUZHIYS;

    /**
     * 调配剂量HR3-20837(241097)
     */
    @TableField(value = "TIAOPEIJL")
    private String TIAOPEIJL;

    /**
     * 草药分次服用HR3-20837(241097)
     */
    @TableField(value = "CAOYAOFCFY")
    private String CAOYAOFCFY;

    /**
     * 精麻诊断HR3-21637(246608)
     */
    @TableField(value = "JINGMAZD")
    private String JINGMAZD;

    /**
     * 异常结果处理意见HR3-21404(245103)
     */
    @TableField(value = "YICHANGJGCLYJ")
    private Integer YICHANGJGCLYJ;

    /**
     * 异常结果处理时间HR3-21404(245103)
     */
    @TableField(value = "YICHANGJGCLSJ")
    private LocalDateTime YICHANGJGCLSJ;

    /**
     * 异常结果处理说明HR3-21404(245103)
     */
    @TableField(value = "YICHANGJGCLSM")
    private String YICHANGJGCLSM;

    /**
     * 异常结果处理人HR3-21404(245103)
     */
    @TableField(value = "YICHANGJGCLR")
    private String YICHANGJGCLR;

    /**
     * 警示颜色HR3-21418(245191)
     */
    @TableField(value = "JINGSHIYS")
    private Integer JINGSHIYS;

    /**
     * 不显示医嘱单标志HR3-23218(258104)0默认，1不显示在医嘱单上
     */
    @TableField(value = "BXSYZDBZ")
    private Integer BXSYZDBZ;

    /**
     * 已查看报告(267268)
     */
    @TableField(value = "YICHAKBG")
    private String YICHAKBG;

    /**
     * 临床路径IDHR3-26717(280501)
     */
    @TableField(value = "LINCHUANGLJID")
    private String LINCHUANGLJID;

    /**
     * 病人套餐明细ID
     */
    @TableField(value = "TAOCANXSBRMXID")
    private String TAOCANXSBRMXID;

    /**
     * 病人套餐标志
     */
    @TableField(value = "TAOCANXSBZ")
    private Integer TAOCANXSBZ;

    /**
     * 
     */
    @TableField(value = "XIEDINGBZ")
    private Integer XIEDINGBZ;

    /**
     * 
     */
    @TableField(value = "XIEDINGCFID")
    private String XIEDINGCFID;

    /**
     * 毒麻处方签名HR3-33031(321191)
     */
    @TableField(value = "DUMACFQM")
    private String DUMACFQM;

    /**
     * 毒麻处方签名时间HR3-33031(321191)
     */
    @TableField(value = "DUMACFQMSJ")
    private LocalDateTime DUMACFQMSJ;

    /**
     * 科室发放标志HR3-32461(317796)
     */
    @TableField(value = "KESHIFFBZ")
    private Integer KESHIFFBZ;

    /**
     * 滴速单位HB3-22452(335967)
     */
    @TableField(value = "DISHUDW")
    private String DISHUDW;

    /**
     * 用药主诊断代码HR3-36578(340794)
     */
    @TableField(value = "KANGJUNYW_LCZDDM")
    private String KANGJUNYW_LCZDDM;

    /**
     * 用药主诊断名称HR3-36578(340794)
     */
    @TableField(value = "KANGJUNYW_LCZDMC")
    private String KANGJUNYW_LCZDMC;

    /**
     * 感染性疾病科是否会诊HR3-36578(340794)
     */
    @TableField(value = "KANGJUNYW_SFHZ")
    private Integer KANGJUNYW_SFHZ;

    /**
     * 使用前是否微生物送检HR3-36578(340794)
     */
    @TableField(value = "KANGJUNYW_WSWSJ")
    private Integer KANGJUNYW_WSWSJ;

    /**
     * 是否紧急情况下使用HR3-36578(340794)
     */
    @TableField(value = "KANGJUNYW_JJSY")
    private Integer KANGJUNYW_JJSY;

    /**
     * 撤销护士工号HR3-37786(347674)
     */
    @TableField(value = "CHEXIAOHSID")
    private String CHEXIAOHSID;

    /**
     * 导入导管记录表标志HR3-36918(342673)
     */
    @TableField(value = "DAORUDGJLBBZ")
    private Integer DAORUDGJLBBZ;

    /**
     * 皮试补入批号(363041)
     */
    @TableField(value = "PISHIPH")
    private String PISHIPH;

    /**
     * 撤销医嘱审核标志HR3-40897(368287) 1申请2确认3拒绝
     */
    @TableField(value = "CHEXIAOSHBZ")
    private Integer CHEXIAOSHBZ;

    /**
     * 撤销医嘱审核人HR3-40897(368287)
     */
    @TableField(value = "CHEXIAOSHR")
    private String CHEXIAOSHR;

    /**
     * 撤销医嘱审核时间HR3-40897(368287)
     */
    @TableField(value = "CHEXIAOSHSJ")
    private LocalDateTime CHEXIAOSHSJ;

    /**
     * 双签名标志HR3-42121(375017)
     */
    @TableField(value = "SHUANGQIANMBZ")
    private Integer SHUANGQIANMBZ;

    /**
     * PPD标志HR3-42121(375017)
     */
    @TableField(value = "PPDBZ")
    private Integer PPDBZ;

    /**
     * PPD结果HR3-42121(375017)
     */
    @TableField(value = "PPDJG")
    private String PPDJG;

    /**
     * PPD部位备注HR3-42800(378607)
     */
    @TableField(value = "PPDBWBZ")
    private String PPDBWBZ;

    /**
     * 
     */
    @TableField(value = "HESUANKS")
    private String HESUANKS;

    /**
     * 
     */
    @TableField(value = "HESUANKSMC")
    private String HESUANKSMC;

    /**
     * 医嘱提交标志HR3-43604(382610)
     */
    @TableField(value = "YIZHUTJBZ")
    private Integer YIZHUTJBZ;

    /**
     * 医嘱提交人HR3-43604(382610)
     */
    @TableField(value = "YIZHUTJR")
    private String YIZHUTJR;

    /**
     * 医嘱提交时间HR3-43604(382610)
     */
    @TableField(value = "YIZHUTJSJ")
    private LocalDateTime YIZHUTJSJ;

    /**
     * 撤销申请人HR3-43512(382114)
     */
    @TableField(value = "CHEXIAOSQR")
    private String CHEXIAOSQR;

    /**
     * 撤销申请时间HR3-43512(382114)
     */
    @TableField(value = "CHEXIAOSQSJ")
    private LocalDateTime CHEXIAOSQSJ;

    /**
     * 
     */
    @TableField(value = "KUOZHANXX")
    private String KUOZHANXX;

    /**
     * 病人医疗组HR3-44546(387839)
     */
    @TableField(value = "BINGRENYLZ")
    private String BINGRENYLZ;

    /**
     * 医嘱锁定标志HR3-46092(395964)0解锁1锁定2申请解锁
     */
    @TableField(value = "YIZHUSDBZ")
    private Integer YIZHUSDBZ;

    /**
     * 院前申请单IDHR3-51453(427766)
     */
    @TableField(value = "YUANQIANSQDID")
    private String YUANQIANSQDID;

    /**
     * 导管结束标志HR3-52476(433718)
     */
    @TableField(value = "DAOGUANJSBZ")
    private Integer DAOGUANJSBZ;

    /**
     * 导管结束人HR3-52476(433718)
     */
    @TableField(value = "DAOGUANJSR")
    private String DAOGUANJSR;

    /**
     * 导管结束时间HR3-52476(433718)
     */
    @TableField(value = "DAOGUANJSSJ")
    private LocalDateTime DAOGUANJSSJ;

    /**
     * 用血备注HR3-53511(439340)
     */
    @TableField(value = "YONGXUEBZ")
    private String YONGXUEBZ;

    /**
     * 院前导入标志HR3-51453(427766)
     */
    @TableField(value = "YUANQIANDRBZ")
    private Integer YUANQIANDRBZ;

    /**
     * 转科换成标志
     */
    @TableField(value = "ZHUANKEHCBZ")
    private Integer ZHUANKEHCBZ;

    /**
     * 管道部位IDHR6-700(487195)
     */
    @TableField(value = "GUANDAOBWID")
    private String GUANDAOBWID;

    /**
     * 管道部位名称HR6-700(487195)
     */
    @TableField(value = "GUANDAOBWMC")
    private String GUANDAOBWMC;

    /**
     * 管道方位IDHR6-700(487195)
     */
    @TableField(value = "GUANDAOFWID")
    private String GUANDAOFWID;

    /**
     * 管道方位名称HR6-700(487195)
     */
    @TableField(value = "GUANDAOFWMC")
    private String GUANDAOFWMC;

    /**
     * 加急标志HR6-700(487195)
     */
    @TableField(value = "JIAJIBZ")
    private Integer JIAJIBZ;

    /**
     * 自备标志HR6-700(487195)
     */
    @TableField(value = "ZIBEIBZ")
    private Integer ZIBEIBZ;

    /**
     * 多重耐药标志HR6-700(487195)
     */
    @TableField(value = "DUOCHONGNYBZ")
    private Integer DUOCHONGNYBZ;

    /**
     * 换床床号HR6-700(487195)
     */
    @TableField(value = "HUANCHUANGHAO")
    private String HUANCHUANGHAO;

    /**
     * 转入科室HR6-700(487195)
     */
    @TableField(value = "ZHUANRUKS")
    private String ZHUANRUKS;

    /**
     * 转入科室名称HR6-700(487195)
     */
    @TableField(value = "ZHUANRUKSMC")
    private String ZHUANRUKSMC;

    /**
     * 管道来源IDIDHR6-700(487195)，1：置入，2：外院带入，3：术后带回，4：拔管重置
     */
    @TableField(value = "GUANDAOLYID")
    private String GUANDAOLYID;

    /**
     * 管道来源名称HR6-700(487195)
     */
    @TableField(value = "GUANDAOLYMC")
    private String GUANDAOLYMC;

    /**
     * 皮试观察时间：单位分钟491223
     */
    @TableField(value = "PISHIGCSJ")
    private Long PISHIGCSJ;

    /**
     * 皮试开始时间491223
     */
    @TableField(value = "PISHIKSSJ")
    private LocalDateTime PISHIKSSJ;

    /**
     * 手术医嘱ID
     */
    @TableField(value = "SHOUSHUYZID")
    private String SHOUSHUYZID;

    /**
     * 
     */
    @TableField(value = "ZHILIAOFA")
    private String ZHILIAOFA;

    /**
     * 
     */
    @TableField(value = "ZHILIAOLY")
    private String ZHILIAOLY;

    /**
     * 血糖医嘱标志HR6-2037(538661)
     */
    @TableField(value = "XUETANGYZBZ")
    private Integer XUETANGYZBZ;

    /**
     * 需审批标志(544340)
     */
    @TableField(value = "XUSHENPIBZ")
    private Integer XUSHENPIBZ;

    /**
     * 审批标志(544340) 0:未审批改为无需审批，1:审批通过，2:审批不通过  3需要审批
     */
    @TableField(value = "SHENPIBZ")
    private Integer SHENPIBZ;

    /**
     * 审批人(544340)
     */
    @TableField(value = "SHENPIREN")
    private String SHENPIREN;

    /**
     * 审批时间(544340)
     */
    @TableField(value = "SHENPISJ")
    private LocalDateTime SHENPISJ;

    /**
     * 备血申请单IDHR6-2804(563780)
     */
    @TableField(value = "BEIXUESQDID")
    private String BEIXUESQDID;

    /**
     * 备血医嘱IDHR6-2804(563780)
     */
    @TableField(value = "BEIXUEYZID")
    private String BEIXUEYZID;

    /**
     * 
     */
    @TableField(value = "HUSHIBZ")
    private String HUSHIBZ;

    /**
     * 转入病区 HR6-4147(597930)
     */
    @TableField(value = "ZHUANRUBQ")
    private String ZHUANRUBQ;

    /**
     * 转入病区名称 HR6-4147(597930)
     */
    @TableField(value = "ZHUANRUBQMC")
    private String ZHUANRUBQMC;

    /**
     * 管道IDHOCB2710 管道评估功能优化
     */
    @TableField(value = "GUANDAOID")
    private String GUANDAOID;

    /**
     * 特殊医嘱标志1:TPN医嘱 ,2:化疗医嘱HR6-4124(597348)
     */
    @TableField(value = "TESHUYZBZ")
    private Integer TESHUYZBZ;

    /**
     * HR6-3574(583839)草方有书写顺序
     */
    @TableField(value = "SHUNXUHAO")
    private Integer SHUNXUHAO;

    /**
     * 知情同意书状态：1已打印，2已签名，0未处理
     */
    @TableField(value = "ZHIQINGTYSZT")
    private Integer ZHIQINGTYSZT;

    /**
     * 药品批号HB6-15151(615071)
     */
    @TableField(value = "YAOPINPH")
    private String YAOPINPH;

    /**
     * GCP标志HR6-5561(627456)
     */
    @TableField(value = "GCPBZ")
    private Integer GCPBZ;

    /**
     * 外配标志
     */
    @TableField(value = "WAIPEIBZ")
    private Integer WAIPEIBZ;

    /**
     * 赠品标志
     */
    @TableField(value = "ZENGPINBZ")
    private Integer ZENGPINBZ;

    /**
     * TPN医嘱标志
     */
    @TableField(value = "TPNYZBZ")
    private Integer TPNYZBZ;

    /**
     * PPD医嘱IDHOCR2837
     */
    @TableField(value = "PPDYIZHUID")
    private String PPDYIZHUID;

    /**
     * 术前医嘱标志HOCR2699 
     */
    @TableField(value = "SHUQIANYZBZ")
    private Integer SHUQIANYZBZ;

    /**
     * PDA执行医嘱后.将签名标志置为1
     */
    @TableField(value = "QIANMINGBZ")
    private Integer QIANMINGBZ;

    /**
     * 审方双签医生签名
     */
    @TableField(value = "SHENFANGSQM")
    private String SHENFANGSQM;

    /**
     * 审方双签医生双签名时间
     */
    @TableField(value = "SHENFANGSQMSJ")
    private LocalDateTime SHENFANGSQMSJ;

    /**
     * 审方双签用药原因
     */
    @TableField(value = "YONGYAOYY")
    private String YONGYAOYY;

    /**
     * 术前医嘱手术名称 HOCR6107
     */
    @TableField(value = "SHUQIANYZSSMC")
    private String SHUQIANYZSSMC;

    /**
     * 抗菌药物审核状态(0为不需要审核,1为需要审核)
     */
    @TableField(value = "KANGJUNYWSHBZ")
    private String KANGJUNYWSHBZ;

    /**
     * 麻醉标志 KHCR15656
     */
    @TableField(value = "MAZUIBZ")
    private Integer MAZUIBZ;

    /**
     * 术前医嘱手术ID HOCR6107
     */
    @TableField(value = "SHUQIANYZSSID")
    private String SHUQIANYZSSID;

    /**
     * 医嘱类型HOCT20274
     */
    @TableField(value = "YIZHULX")
    private Integer YIZHULX;

    /**
     * 药品加急标志(HOCR9804)
     */
    @TableField(value = "YAOPINJJBZ")
    private Integer YAOPINJJBZ;

    /**
     * 抗菌药物签名人HR6-6447(646269)
     */
    @TableField(value = "KANGJUNYWQM")
    private String KANGJUNYWQM;

    /**
     * 抗菌药物签名时间HR6-6447(646269)
     */
    @TableField(value = "KANGJUNYWQMSJ")
    private LocalDateTime KANGJUNYWQMSJ;

    /**
     * 管道名称HOCB2710 管道评估功能优化
     */
    @TableField(value = "GUANDAOMC")
    private String GUANDAOMC;

    /**
     * 过敏源分类HR6-1512(518757)
     */
    @TableField(value = "GUOMINYFL")
    private String GUOMINYFL;

    /**
     * HOCR8355 抗菌药物预停时间
     */
    @TableField(value = "YUTINGSJ")
    private LocalDateTime YUTINGSJ;

    /**
     * 出院诊断ID 逗号分隔
     */
    @TableField(value = "CHUYUANZDID")
    private String CHUYUANZDID;

    /**
     * 出院诊断名称 
     */
    @TableField(value = "CHUYUANZDMC")
    private String CHUYUANZDMC;

    /**
     * 中医诊断
     */
    @TableField(value = "ZHONGYIZD")
    private String ZHONGYIZD;

    /**
     * 转入医疗组HOCB42305
     */
    @TableField(value = "ZHUANRUYLZ")
    private String ZHUANRUYLZ;

    /**
     * 
     */
    @TableField(value = "ZHONGYIZHID")
    private String ZHONGYIZHID;

    /**
     * 访视周期HOCR59888
     */
    @TableField(value = "FANGSHIZQ")
    private String FANGSHIZQ;

    /**
     * 适用范围中药协定方根据使用范围来控制是否可修改
     */
    @TableField(value = "SHIYONGFW")
    private String SHIYONGFW;

    /**
     * 医惠复核人HOCR19272
     */
    @TableField(value = "FUHESJYH")
    private String FUHESJYH;

    /**
     * 医惠复核时间HOCR19272
     */
    @TableField(value = "FUHERENYH")
    private LocalDateTime FUHERENYH;

    /**
     * 靶点检测内容HOCR64123
     */
    @TableField(value = "BADIANJCNR")
    private String BADIANJCNR;

    /**
     * 靶点检测结果HOCR64123
     */
    @TableField(value = "BADIANJCJG")
    private String BADIANJCJG;

    /**
     * 手术台
     */
    @TableField(value = "SHOUSHUT")
    private String SHOUSHUT;

    /**
     * 卡片类型
     */
    @TableField(value = "KAPIANLX")
    private String KAPIANLX;

    /**
     * 大规格产地 【医保用】
     */
    @TableField(value = "DAGUIGCD")
    private String DAGUIGCD;

    /**
     * 大规格产地名称 【医保用】
     */
    @TableField(value = "DAGUIGCDMC")
    private String DAGUIGCDMC;

    /**
     * 大规格包装量  【医保用】
     */
    @TableField(value = "DAGUIGBZL")
    private BigDecimal DAGUIGBZL;

    /**
     * 包装单位【医保用】
     */
    @TableField(value = "BAIZHUANGDW")
    private String BAIZHUANGDW;

    /**
     * 体积单位【医保用 转换最小单位用】
     */
    @TableField(value = "TIJIDW")
    private String TIJIDW;

    /**
     * 体积 【医保用 转换最小单位用】
     */
    @TableField(value = "TIJI")
    private String TIJI;

    /**
     * 大规格价格ID 【医保用】
     */
    @TableField(value = "DAGUIGJGID")
    private String DAGUIGJGID;

    /**
     * 大规格idHR6-61(405893)不上传费用改造
     */
    @TableField(value = "DAGUIGID")
    private String DAGUIGID;

    /**
     * 医嘱项目类型
     */
    @TableField(value = "YIZHUXMLX")
    private String YIZHUXMLX;

    /**
     * 
     */
    @TableField(value = "JIEDUANID")
    private String JIEDUANID;

    /**
     * 
     */
    @TableField(value = "BINGRENLJID")
    private String BINGRENLJID;

    /**
     * 抢救标志HOCT130515
     */
    @TableField(value = "QIANGJIUBZ")
    private Integer QIANGJIUBZ;

    /**
     * 阿片类型
     */
    @TableField(value = "APIANLX")
    private String APIANLX;

    /**
     * 审方双签名原因
     */
    @TableField(value = "SHENGFANGSQMYY")
    private String SHENGFANGSQMYY;

    /**
     * 审方双签名时间
     */
    @TableField(value = "SHENGFANGSQMSJ")
    private String SHENGFANGSQMSJ;

    /**
     * 审方双签名名称
     */
    @TableField(value = "SHENGFANGSQMMC")
    private String SHENGFANGSQMMC;

    /**
     * 审方双签名
     */
    @TableField(value = "SHENGFANGSQM")
    private String SHENGFANGSQM;

    /**
     * 费用控制标志：0：已调用费用控制，1：未调用费用控制
     */
    @TableField(value = "FEIYONGKZBZ")
    private Integer FEIYONGKZBZ;

    /**
     * 医保外配编号
     */
    @TableField(value = "WAIPEIBABH")
    private String WAIPEIBABH;

    /**
     * 膏方剂型HOCB343865（0切片，1流浸方，2传统方）
     */
    @TableField(value = "GAOFANGJX")
    private String GAOFANGJX;

    /**
     * 膏方辅料信息HOCB343865
     */
    @TableField(value = "GAOFANGFLXX")
    private String GAOFANGFLXX;

    /**
     * 每日剂量HOCR345269
     */
    @TableField(value = "MEIRIJL")
    private String MEIRIJL;

    /**
     * 煎药方法HOCR345269
     */
    @TableField(value = "JIANYAOFF")
    private String JIANYAOFF;

    /**
     * 服药次数HOCR345269
     */
    @TableField(value = "FUYAOCS")
    private String FUYAOCS;

    /**
     * 服药方法HOCR345269
     */
    @TableField(value = "FUYAOFF")
    private String FUYAOFF;

    /**
     * 
     */
    @TableField(value = "TONGSHUOYZID")
    private String TONGSHUOYZID;

    /**
     * 医保等级
     */
    @TableField(value = "YIBAODJ")
    private String YIBAODJ;

    /**
     * 外配审方人
     */
    @TableField(value = "WAIPEISFR")
    private String WAIPEISFR;

    /**
     * 外配审方时间
     */
    @TableField(value = "WAIPEISFSJ")
    private LocalDateTime WAIPEISFSJ;

    /**
     * 
     */
    @TableField(value = "SHENGLIZQ")
    private String SHENGLIZQ;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}