package com.javazx.batch.vo;

import lombok.Data;

/**
 * 患者手术信息请求对象
 * 对应 ApifoxModel 中的 PatientSurgeryInfoReq 结构
 */
@Data
public class PatientSurgeryInfoReq {
    /**
     * 床位号
     */
    private String bedNo;
    
    /**
     * 病人身份证号
     */
    private Long idNumber;
    
    /**
     * 显示状态(0:不显示,1:显示)
     */
    private Long isDisplay;
    
    /**
     * 是否完成(0:否,1:是)
     */
    private Long isFinish;
    
    /**
     * 病人名称
     */
    private String patientName;
    
    /**
     * 携带药品
     */
    private BringMedicineBO surgeryBringMedicine;
    
    /**
     * 手术名称
     */
    private String surgeryName;
    
    /**
     * 术前准备
     */
    private String surgeryPreparation;
    
    /**
     * 手术进程
     */
    private String surgeryProcess;
    
    /**
     * 手术时间
     */
    private String surgeryTime;
    
    /**
     * 住院号
     */
    private String zyh;
}
