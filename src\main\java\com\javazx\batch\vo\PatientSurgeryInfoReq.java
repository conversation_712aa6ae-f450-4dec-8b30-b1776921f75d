package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 患者手术信息请求对象
 * 对应 ApifoxModel 中的 PatientSurgeryInfoReq 结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientSurgeryInfoReq {
    /**
     * 手术同步id
     */
    private String syncId;
    /**
     * 床位号
     */
    private String bedNo;
    
    /**
     * 显示状态(0:不显示,1:显示)
     */
    private Long isDisplay;
    
    /**
     * 是否完成(0:否,1:是)
     */
    private Long isFinish;
    
    /**
     * 病人名称
     */
    private String patientName;
    
    /**
     * 携带药品
     */
    private List<BringMedicineBO> surgeryBringMedicine;
    
    /**
     * 手术名称
     */
    private String surgeryName;
    
    /**
     * 术前准备
     */
    private List<String> surgeryPreparationList;
    
    /**
     * 手术进程
     */
    private String surgeryProcess;
    
    /**
     * 手术时间
     */
    private String surgeryTime;
    
    /**
     * 住院号
     */
    private String zyh;
}
