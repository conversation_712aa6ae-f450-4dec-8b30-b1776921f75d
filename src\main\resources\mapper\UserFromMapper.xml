<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.javazx.batch.mapper.UserFromMapper">
    <resultMap id="BaseResultMap" type="com.javazx.batch.po.UserFrom">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="sex" property="sex" jdbcType="INTEGER"/>
        <result column="age" property="age" jdbcType="INTEGER"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="DATE"/>
        <result column="update_time" property="updateTime" jdbcType="DATE"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, user_name, sex,
        age, address,
        create_time, update_time, status, remark
    </sql>
    <!-- 读取数据 -->
    <select id="selectAllInfo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from user_from  limit #{_skiprows,jdbcType=INTEGER},#{_pagesize,jdbcType=INTEGER}
    </select>
</mapper>