package com.javazx.batch.mapper.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 护理任务医嘱查询结果VO
 * 用于承载医嘱表和患者表联查的结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NursingTaskOrderResp {

    /**
     * 医嘱ID
     */
    private String yizhuid;

    /**
     * 患者住院ID
     */
    private String bingrenzyid;

    /**
     * 医嘱名称
     */
    private String yizhumc;

    /**
     * 频次
     */
    private String pinci;

    /**
     * 给药方式
     */
    private String geiyaofs;

    /**
     * 医嘱状态
     */
    private String yizhuzt;

    /**
     * 开始时间
     */
    private LocalDateTime kaishisj;

    /**
     * 结束时间
     */
    private LocalDateTime jieshusj;

    /**
     * 开嘱时间
     */
    private LocalDateTime kaizhusj;

    /**
     * 开嘱医生
     */
    private String kaizhuys;

    /**
     * 开嘱医生姓名
     */
    private String kaizhuysxm;

    /**
     * 当前床位号
     */
    private String dangqiancw;

    /**
     * 病区ID
     */
    private String bingquid;
}
