package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.GyZhigongxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GY_ZHIGONGXX(公用_职工信息)】的数据库操作Mapper
* @createDate 2025-06-17 15:14:56
* @Entity com.javazx.batch.po.GyZhigongxx
*/
@Mapper
@DS("hzzyy")
public interface GyZhigongxxMapper extends BaseMapper<GyZhigongxx> {

    int deleteByPrimaryKey(Long id);

    int insert(GyZhigongxx record);

    int insertSelective(GyZhigongxx record);

    GyZhigongxx selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GyZhigongxx record);

    int updateByPrimaryKey(GyZhigongxx record);

    /**
     * 分页查询医护人员列表（通过科室病区关联）
     * @param offset 偏移量
     * @param limit 限制数量
     * @param wardId 病区ID，如果为null则查询所有病区
     * @return 医护人员列表
     */
    @Select("""
              SELECT *
              FROM (
                  SELECT b.ZHIGONGID, b.ZHIGONGGH, b.ZHIGONGXM, b.XINGBIE, b.DIANZIYJ, b.DIANHUA,
                         b.ZHIWU, b.ZHICHENG, b.ZHIGONGLB, b.KESHIID, a.KESHIBQID AS BINGQUID, b.ZUOFEIBZ,
                         ROW_NUMBER() OVER (ORDER BY b.ZHIGONGID) AS ROW_NUM
                  FROM HIS6.GY_ZHIGONGKS a
                  LEFT JOIN HIS6.GY_ZHIGONGXX b ON a.ZHIGONGID = b.ZHIGONGID
                  WHERE a.KESHIBQBZ = 2
                    AND b.ZUOFEIBZ = 0
                    AND (#{wardId} IS NULL OR a.KESHIBQID = #{wardId})
              )
              WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<GyZhigongxx> selectDoctorsByPage(@Param("offset") int offset, @Param("limit") int limit, @Param("wardId") String wardId);

    /**
     * 分页查询指定病区的医护人员列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @param wardId 病区ID
     * @return 医护人员列表
     */
    @Select("""
              SELECT *
              FROM (
                  SELECT b.ZHIGONGID, b.ZHIGONGGH, b.ZHIGONGXM, b.XINGBIE, b.DIANZIYJ, b.DIANHUA,
                         b.ZHIWU, b.ZHICHENG, b.ZHIGONGLB, b.KESHIID, a.KESHIBQID AS BINGQUID, b.ZUOFEIBZ,
                         ROW_NUMBER() OVER (ORDER BY b.ZHIGONGID) AS ROW_NUM
                  FROM HIS6.GY_ZHIGONGKS a
                  LEFT JOIN HIS6.GY_ZHIGONGXX b ON a.ZHIGONGID = b.ZHIGONGID
                  WHERE a.KESHIBQBZ = 2
                    AND a.KESHIBQID = #{wardId}
                    AND b.ZUOFEIBZ = 0
              )
              WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<GyZhigongxx> selectDoctorsByPageAndWard(@Param("offset") int offset, @Param("limit") int limit, @Param("wardId") String wardId);

    /**
     * 统计医护人员总数（通过科室病区关联）
     * @param wardId 病区ID，如果为null则统计所有病区
     * @return 医护人员总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM HIS6.GY_ZHIGONGKS a
            LEFT JOIN HIS6.GY_ZHIGONGXX b ON a.ZHIGONGID = b.ZHIGONGID
            WHERE a.KESHIBQBZ = 2
              AND b.ZUOFEIBZ = 0
              AND (#{wardId} IS NULL OR a.KESHIBQID = #{wardId})
            """)
    int countDoctors(@Param("wardId") String wardId);

    /**
     * 统计指定病区的医护人员总数
     * @param wardId 病区ID
     * @return 医护人员总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM HIS6.GY_ZHIGONGKS a
            LEFT JOIN HIS6.GY_ZHIGONGXX b ON a.ZHIGONGID = b.ZHIGONGID
            WHERE a.KESHIBQBZ = 2
              AND a.KESHIBQID = #{wardId}
              AND b.ZUOFEIBZ = 0
            """)
    int countDoctorsByWard(@Param("wardId") String wardId);

}
