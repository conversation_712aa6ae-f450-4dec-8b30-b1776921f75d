package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.GyZhigongxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GY_ZHIGONGXX(公用_职工信息)】的数据库操作Mapper
* @createDate 2025-06-17 15:14:56
* @Entity com.javazx.batch.po.GyZhigongxx
*/
@Mapper
@DS("hzzyy")
public interface GyZhigongxxMapper extends BaseMapper<GyZhigongxx> {

    int deleteByPrimaryKey(Long id);

    int insert(GyZhigongxx record);

    int insertSelective(GyZhigongxx record);

    GyZhigongxx selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GyZhigongxx record);

    int updateByPrimaryKey(GyZhigongxx record);

    /**
     * 分页查询医护人员列表
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 医护人员列表
     */
    @Select("""
              SELECT *
              FROM (
                  SELECT ZHIGONGID, ZHIGONGGH, ZHIGONGXM, XINGBIE, DIANZIYJ, DIANHUA,
                         ZHIWU, ZHICHENG, ZHIGONGLB, KESHIID, BINGQUID, ZUOFEIBZ,
                         ROW_NUMBER() OVER (ORDER BY ZHIGONGID) AS ROW_NUM
                  FROM HIS6.GY_ZHIGONGXX
                  WHERE ZUOFEIBZ = 0 AND DANGQIANZT = '1'
              )
              WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<GyZhigongxx> selectDoctorsByPage(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 统计医护人员总数
     * @return 医护人员总数
     */
    @Select("""
            SELECT COUNT(*) FROM HIS6.GY_ZHIGONGXX
            WHERE ZUOFEIBZ = 0 AND DANGQIANZT = '1'
            """)
    int countDoctors();

}
