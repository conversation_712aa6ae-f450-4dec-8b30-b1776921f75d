package com.javazx.batch.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.YjShenqingdan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【YJ_SHENQINGDAN(医技_申请单)】的数据库操作Mapper
* @createDate 2025-06-12 15:25:56
* @Entity generator.domain.YjShenqingdan
*/
@Mapper
@DS("hzzyy")
public interface YjShenqingdanMapper extends BaseMapper<YjShenqingdan> {

    int deleteByPrimaryKey(Long id);

    int insert(YjShenqingdan record);

    int insertSelective(YjShenqingdan record);

    YjShenqingdan selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(YjShenqingdan record);

    int updateByPrimaryKey(YjShenqingdan record);

    /**
     * 分页查询检查申请单列表（按病区）- 基于医嘱和申请单关联查询
     * @param dangqianbq 当前病区
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 检查申请单列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT b.*, a.YIZHUMC, a.YIZHUFL, b.YUYUESQRQ,
                       decode(b.dangqianzt, '6', 1, '7', 1, '8', 1, 0) as jianchazt,
                       (SELECT dysm.daoyism5
                        FROM his6.gy_duoyuanqdysm dysm
                        WHERE dysm.xiangmuid = b.yizhuxmid
                          AND dysm.zuofeibz = 0
                          AND dysm.jiajisy = 0) as jianchadz,
                       z.DANGQIANBQ, z.DANGQIANCW, z.XINGMING, z.ZHUYUANHAO, z.BINGANGHAO,
                       ROW_NUMBER() OVER (ORDER BY b.SHENQINDANID) AS ROW_NUM
                FROM his6.yz_bingrenyz a
                JOIN his6.yj_shenqingdan b ON a.yizhuid = b.yizhuid
                JOIN his6.zy_bingrenxx z ON b.BINGRENZYID = z.BINGRENZYID
                WHERE a.KAISHISJ > SYSDATE - 7
                  AND a.BINGQUID = #{dangqianbq}
                  AND a.YIZHUZT IN (2, 3, 4)
                  AND b.DANGQIANZT NOT IN (9, 10)
                  AND a.KAISHISJ > SYSDATE - 7
                  AND z.ZAIYUANZT = 0
                  AND z.YINGERBZ = 0
                  AND z.RUKEBZ = 1
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<YjShenqingdan> selectChecksByPage(@Param("dangqianbq") String dangqianbq,
                                           @Param("offset") int offset,
                                           @Param("limit") int limit);

    /**
     * 统计检查申请单总数（按病区）
     * @param dangqianbq 当前病区
     * @return 检查申请单总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.yz_bingrenyz a
            JOIN his6.yj_shenqingdan b ON a.yizhuid = b.yizhuid
            JOIN his6.zy_bingrenxx z ON b.BINGRENZYID = z.BINGRENZYID
            WHERE a.KAISHISJ > SYSDATE - 7
              AND a.BINGQUID = #{dangqianbq}
              AND a.YIZHUZT IN (2, 3, 4)
              AND b.DANGQIANZT NOT IN (9, 10)
              AND a.KAISHISJ > SYSDATE - 7
              AND z.ZAIYUANZT = 0
              AND z.YINGERBZ = 0
              AND z.RUKEBZ = 1
            """)
    int countChecks(@Param("dangqianbq") String dangqianbq);

    /**
     * 根据病人住院ID查询检查申请单列表
     * @param bingrenzyid 病人住院ID
     * @return 检查申请单列表
     */
    @Select("""
            SELECT s.*
            FROM HIS6.YJ_SHENQINGDAN s
            WHERE s.BINGRENZYID = #{bingrenzyid}
              AND s.DANGQIANZT IN ('1', '2', '3')
              AND s.MENZHENZYBZ = 1
            ORDER BY s.KAIDANRQ DESC
            """)
    List<YjShenqingdan> selectChecksByPatientId(@Param("bingrenzyid") String bingrenzyid);

    /**
     * 分页查询胃肠镜检查申请单列表（按病区）
     * @param dangqianbq 当前病区
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 胃肠镜检查申请单列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT b.*, a.YIZHUMC, a.YIZHUFL, b.YUYUESQRQ,
                       decode(b.dangqianzt, '6', 1, '7', 1, '8', 1, 0) as jianchazt,
                       (SELECT dysm.daoyism5
                        FROM his6.gy_duoyuanqdysm dysm
                        WHERE dysm.xiangmuid = b.yizhuxmid
                          AND dysm.zuofeibz = 0
                          AND dysm.jiajisy = 0) as jianchadz,
                       z.DANGQIANBQ, z.DANGQIANCW, z.XINGMING, z.ZHUYUANHAO, z.BINGANGHAO,
                       ROW_NUMBER() OVER (ORDER BY b.SHENQINDANID) AS ROW_NUM
                FROM his6.yz_bingrenyz a
                JOIN his6.yj_shenqingdan b ON a.yizhuid = b.yizhuid
                JOIN his6.zy_bingrenxx z ON b.BINGRENZYID = z.BINGRENZYID
                WHERE a.KAISHISJ > SYSDATE - 7
                  AND a.BINGQUID = #{dangqianbq}
                  AND a.YIZHUZT IN (2, 3, 4)
                  AND b.DANGQIANZT NOT IN (9, 10)
                  AND a.KAISHISJ > SYSDATE - 7
                  AND z.ZAIYUANZT = 0
                  AND z.YINGERBZ = 0
                  AND z.RUKEBZ = 1
                  AND b.jianchalx = 12
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<YjShenqingdan> selectGastroscopeChecksByPage(@Param("dangqianbq") String dangqianbq,
                                                      @Param("offset") int offset,
                                                      @Param("limit") int limit);

    /**
     * 统计胃肠镜检查申请单总数（按病区）
     * @param dangqianbq 当前病区
     * @return 胃肠镜检查申请单总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.yz_bingrenyz a
            JOIN his6.yj_shenqingdan b ON a.yizhuid = b.yizhuid
            JOIN his6.zy_bingrenxx z ON b.BINGRENZYID = z.BINGRENZYID
            WHERE a.KAISHISJ > SYSDATE - 7
              AND a.BINGQUID = #{dangqianbq}
              AND a.YIZHUZT IN (2, 3, 4)
              AND b.DANGQIANZT NOT IN (9, 10)
              AND a.KAISHISJ > SYSDATE - 7
              AND z.ZAIYUANZT = 0
              AND z.YINGERBZ = 0
              AND z.RUKEBZ = 1
              AND b.jianchalx = 12
            """)
    int countGastroscopeChecks(@Param("dangqianbq") String dangqianbq);

}
