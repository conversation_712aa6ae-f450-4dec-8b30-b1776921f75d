package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.ZyhlZhongyihljl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description 针对表【ZYHL_ZHONGYIHLJL(住院护理_中医护理记录)】的数据库操作Mapper
 * @createDate 2025-06-17 16:41:00
 * @Entity generator.domain.ZyhlZhongyihljl
 */
@Mapper
@DS("hzzyy")
public interface ZyhlZhongyihljlMapper extends BaseMapper<ZyhlZhongyihljl> {

    /**
     * 查询中医护理方案记录数量
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return 中医护理方案记录数量
     */
    @Select("""
            SELECT count(1) 
            FROM HIS6.zyhl_zhongyihljl a, HIS6.zy_bingrenxx b
            WHERE a.bingrenzyid = b.bingrenzyid 
              AND b.zaiyuanzt = 0 
              AND nvl(a.zuofeibz,0) = 0 
              AND a.tichuyy is null 
              AND a.bingrenzyid = #{bingrenzyid}
              AND b.dangqianbq = #{dangqianbq}
            """)
    Integer getChineseMedicineNursingCount(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);
}
