package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公用_职工信息
 * @TableName GY_ZHIGONGXX
 */
@TableName(value ="GY_ZHIGONGXX")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GyZhigongxx implements Serializable {
    /**
     * 职工ID
     */
    @TableId(value = "ZHIGONGID")
    private String ZHIGONGID;

    /**
     * 职工工号
     */
    @TableField(value = "ZHIGONGGH")
    private String ZHIGONGGH;

    /**
     * 职工姓名
     */
    @TableField(value = "ZHIGONGXM")
    private String ZHIGONGXM;

    /**
     * 性别
     */
    @TableField(value = "XINGBIE")
    private String XINGBIE;

    /**
     * 出生日期
     */
    @TableField(value = "CHUSHENGRQ")
    private LocalDateTime CHUSHENGRQ;

    /**
     * 身份证号
     */
    @TableField(value = "SHENFENZH")
    private String SHENFENZH;

    /**
     * 家庭地址
     */
    @TableField(value = "JIATINGDZ")
    private String JIATINGDZ;

    /**
     * 家庭邮编
     */
    @TableField(value = "JIATINGYB")
    private String JIATINGYB;

    /**
     * 电子邮件
     */
    @TableField(value = "DIANZIYJ")
    private String DIANZIYJ;

    /**
     * 电话
     */
    @TableField(value = "DIANHUA")
    private String DIANHUA;

    /**
     * 职务
     */
    @TableField(value = "ZHIWU")
    private String ZHIWU;

    /**
     * 职称
     */
    @TableField(value = "ZHICHENG")
    private String ZHICHENG;

    /**
     * 参加工作时间
     */
    @TableField(value = "CANJIAGZSJ")
    private LocalDateTime CANJIAGZSJ;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private LocalDateTime XIUGAISJ;

    /**
     * 输入码1
     */
    @TableField(value = "SHURUMA1")
    private String SHURUMA1;

    /**
     * 输入码3
     */
    @TableField(value = "SHURUMA3")
    private String SHURUMA3;

    /**
     * 输入码2
     */
    @TableField(value = "SHURUMA2")
    private String SHURUMA2;

    /**
     * 当前状态
     */
    @TableField(value = "DANGQIANZT")
    private String DANGQIANZT;

    /**
     * 权限
     */
    @TableField(value = "QUANXIAN")
    private String QUANXIAN;

    /**
     * 门诊类别
     */
    @TableField(value = "MENZHENLB")
    private String MENZHENLB;

    /**
     * 职工类别
     */
    @TableField(value = "ZHIGONGLB")
    private String ZHIGONGLB;

    /**
     * 密码
     */
    @TableField(value = "MIMA")
    private String MIMA;

    /**
     * 人事科室
     */
    @TableField(value = "RENSHIKS")
    private String RENSHIKS;

    /**
     * 挂号费项目
     */
    @TableField(value = "GUAHAOFXM")
    private String GUAHAOFXM;

    /**
     * 诊疗费项目
     */
    @TableField(value = "ZHENLIAOFXM")
    private String ZHENLIAOFXM;

    /**
     * 科室ID
     */
    @TableField(value = "KESHIID")
    private String KESHIID;

    /**
     * 医生等级
     */
    @TableField(value = "YISHENGDJ")
    private String YISHENGDJ;

    /**
     * 英文名
     */
    @TableField(value = "YINGWENMING")
    private String YINGWENMING;

    /**
     * 是否签名标志
     */
    @TableField(value = "QIANMINGBZ")
    private Integer QIANMINGBZ;

    /**
     * 图章号
     */
    @TableField(value = "TUZHANGHAO")
    private String TUZHANGHAO;

    /**
     * 职工资料
     */
    @TableField(value = "ZHIGONGZL")
    private String ZHIGONGZL;

    /**
     * 核算科室
     */
    @TableField(value = "HESUANKS")
    private String HESUANKS;

    /**
     * 医师资格证书编码HR3-16977
     */
    @TableField(value = "YISHIZGZS")
    private String YISHIZGZS;

    /**
     * 职业开始日期HR3-16977
     */
    @TableField(value = "ZHIYEKSRQ")
    private LocalDateTime ZHIYEKSRQ;

    /**
     * 职业结束日期HR3-16977
     */
    @TableField(value = "ZHIYEJSRQ")
    private LocalDateTime ZHIYEJSRQ;

    /**
     * 医师执业证书编码HR3-17394
     */
    @TableField(value = "YISHIZYZS")
    private String YISHIZYZS;

    /**
     * 在职状态HR3-16975
     */
    @TableField(value = "ZAIZHIZT")
    private String ZAIZHIZT;

    /**
     * 职业资格HR3-16975
     */
    @TableField(value = "ZHIYEZG")
    private String ZHIYEZG;

    /**
     * 人员编制类别HR3-16975
     */
    @TableField(value = "RENYUANBZLB")
    private String RENYUANBZLB;

    /**
     * 职业顺序号HR3-18523
     */
    @TableField(value = "ZHIYESXH")
    private String ZHIYESXH;

    /**
     * 执业范围HR3-18523
     */
    @TableField(value = "ZHIYEFW")
    private String ZHIYEFW;

    /**
     * 执业科别HR3-18523
     */
    @TableField(value = "ZHIYEKB")
    private String ZHIYEKB;

    /**
     * 康复资质HR3-18523
     */
    @TableField(value = "KANGFUZZ")
    private String KANGFUZZ;

    /**
     * 是否技术交流HR3-18523
     */
    @TableField(value = "SHIFOUJSJL")
    private String SHIFOUJSJL;

    /**
     * 医保医师服务编码HR3-18523
     */
    @TableField(value = "YIBAOYSFWBM")
    private String YIBAOYSFWBM;

    /**
     * 医师级别HR3-18523
     */
    @TableField(value = "YISHIJB")
    private String YISHIJB;

    /**
     * 执业地点HR3-19704
     */
    @TableField(value = "ZHIYEDD")
    private String ZHIYEDD;

    /**
     * 执业类别HR3-19710
     */
    @TableField(value = "ZHIYELB")
    private String ZHIYELB;

    /**
     * 电话1(虚拟网号码)HR3-22399
     */
    @TableField(value = "DIANHUA1")
    private String DIANHUA1;

    /**
     * 电话2HR3-22399
     */
    @TableField(value = "DIANHUA2")
    private String DIANHUA2;

    /**
     * 人员序号FOR HR3-23877(262362)
     */
    @TableField(value = "RENYUANXH")
    private String RENYUANXH;

    /**
     * 挂号网IDHR3-22355(252587)
     */
    @TableField(value = "GUAHAOWID")
    private String GUAHAOWID;

    /**
     * 学历HR3-24900(268712)
     */
    @TableField(value = "XUELI")
    private String XUELI;

    /**
     * 门诊电子病历启用标志
     */
    @TableField(value = "MENZHENDZBLQYBZ")
    private Integer MENZHENDZBLQYBZ;

    /**
     * 社区工号HR3-26659(280001)
     */
    @TableField(value = "SHEQUGH")
    private String SHEQUGH;

    /**
     * 社区密码HR3-26659(280001)
     */
    @TableField(value = "SHEQUMM")
    private String SHEQUMM;

    /**
     * 社区角色HR3-26659(280001)
     */
    @TableField(value = "SHEQUJS")
    private String SHEQUJS;

    /**
     * 技术职称HR3-30427(305519)
     */
    @TableField(value = "JISHUZC")
    private String JISHUZC;

    /**
     * 
     */
    @TableField(value = "CABZ")
    private Integer CABZ;

    /**
     * 推荐属性HR3-33183(322051)
     */
    @TableField(value = "TUIJIANSX")
    private String TUIJIANSX;

    /**
     * 诊断界面右侧工具栏启用标志
     */
    @TableField(value = "ZHENDUANJMYCGJLQYBZ")
    private Integer ZHENDUANJMYCGJLQYBZ;

    /**
     * 住院CA标志
     */
    @TableField(value = "ZYCABZ")
    private Integer ZYCABZ;

    /**
     * 电子发票代码
     */
    @TableField(value = "DIANZIFPDM")
    private String DIANZIFPDM;

    /**
     * 宁帆签名标志HR5-11684(638027)

     */
    @TableField(value = "NINGFANQMBZ")
    private Integer NINGFANQMBZ;

    /**
     * 职工专长HR5-12367(643628)
     */
    @TableField(value = "ZHIGONGZC")
    private String ZHIGONGZC;

    /**
     * 是否上传照片HR5-12367(643628)
     */
    @TableField(value = "ZHAOPIANBZ")
    private Integer ZHAOPIANBZ;

    /**
     * 病区ID HOCR6600
     */
    @TableField(value = "BINGQUID")
    private String BINGQUID;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 医保医生国家代码HOCR28648
     */
    @TableField(value = "YIBAOYSGJDM")
    private String YIBAOYSGJDM;

    /**
     * 医保医生国家代码
     */
    @TableField(value = "YIBAOGJDM")
    private String YIBAOGJDM;

    /**
     * 
     */
    @TableField(value = "ZHONGYISSFXM")
    private String ZHONGYISSFXM;

    /**
     * 中医师标志
     */
    @TableField(value = "ZHONGYISBZ")
    private String ZHONGYISBZ;

    /**
     * 特殊会诊权限
     */
    @TableField(value = "TESHUHZQX")
    private String TESHUHZQX;

    /**
     * 病案首页tab的text 
     */
    @TableField(value = "PINRENSJ")
    private LocalDateTime PINRENSJ;

    /**
     * 中医辩证费项目
     */
    @TableField(value = "ZHONGYIBZFXM")
    private String ZHONGYIBZFXM;

    /**
     * 病程展开标志
     */
    @TableField(value = "BINGCHENGZKBZ")
    private Integer BINGCHENGZKBZ;

    /**
     * 院区使用-嘉善第一人民医院HOCR291390
     */
    @TableField(value = "YUANQUSY")
    private String YUANQUSY;

    /**
     * 门诊颗粒剂药房应用ID
     */
    @TableField(value = "MENZHENKLJYF")
    private String MENZHENKLJYF;

    /**
     * 住院颗粒剂药房应用ID
     */
    @TableField(value = "ZHUYUANKLJYF")
    private String ZHUYUANKLJYF;

    /**
     * CDSS启用标志，湖州市中
     */
    @TableField(value = "CDSSQYBZ")
    private Integer CDSSQYBZ;

    /**
     * 医保平台权限管理
     */
    @TableField(value = "MENUDATA")
    private byte[] MENUDATA;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}