package com.javazx.batch.scenario.patientInfor;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.PatientDataService;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientLabelReq;
import com.javazx.batch.vo.PatientWithLabelReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * 患者数据处理器
 * 将ZyBingrenxx对象转换为PatientLabelSyncRequest对象用于同步
 */
@Component
public class PatientProcessor implements ItemProcessor<ZyBingrenxx, PatientWithLabelReq> {

    private static final Logger log = LoggerFactory.getLogger(PatientProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final PatientDataService patientDataService;

    public PatientProcessor(PatientDataService patientDataService) {
        this.patientDataService = patientDataService;
    }

    @Override
    public PatientWithLabelReq process(ZyBingrenxx zyBingrenxx) {
        if (zyBingrenxx == null) {
            return null;
        }

        if (zyBingrenxx.getDANGQIANCW() == null || zyBingrenxx.getDANGQIANCW().trim().isEmpty()) {
            log.info("过滤床位号为空的患者数据: {} - {}", zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getXINGMING());
            return null;
        }

        log.debug("处理患者数据: {} - {}", zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getXINGMING());

        // 转换为API请求对象
        PatientWithLabelReq request = convert(zyBingrenxx);

        log.debug("完成患者数据处理: {}", zyBingrenxx.getXINGMING());
        return request;
    }

    /**
     * 转换患者数据为标签同步请求
     */
    private PatientWithLabelReq convert(ZyBingrenxx zyBingrenxx) {
        // 生成患者标签列表
        List<PatientLabelReq> patientLabels = generatePatientLabels(zyBingrenxx);

        // 创建患者信息
        PatientInfoReq patientInfo = createPatientInfo(zyBingrenxx);

        // 创建患者标签请求
        PatientWithLabelReq patientWithLabel = new PatientWithLabelReq();
        patientWithLabel.setPatientInfo(patientInfo);
        patientWithLabel.setLabelPatientOwnList(patientLabels);

        return patientWithLabel;
    }

    /**
     * 创建患者信息对象
     */
    private PatientInfoReq createPatientInfo(ZyBingrenxx zyBingrenxx) {
        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(zyBingrenxx.getXINGMING());
        patientInfo.setDesensitizedName(desensitizeName(zyBingrenxx.getXINGMING()));
        patientInfo.setGender(convertGenderToLong(zyBingrenxx.getXINGBIE()));

        // 住院信息
        patientInfo.setZyh(zyBingrenxx.getBINGRENZYID());
        patientInfo.setZyhm(zyBingrenxx.getBINGANHAO());
        patientInfo.setBedNo(zyBingrenxx.getDANGQIANCW());
        patientInfo.setWardCode(zyBingrenxx.getDANGQIANBQ());

        // 时间信息
        if (zyBingrenxx.getRUYUANRQ() != null) {
            patientInfo.setAdmissionTime(zyBingrenxx.getRUYUANRQ().format(dateFormatter));
        }

        patientInfo.setDischargeTime("");

        // 诊断信息
        patientInfo.setDiagnosis(zyBingrenxx.getRUYUANZDMC());

        // 医护人员信息
        patientInfo.setDoctorJobNumber(zyBingrenxx.getZHUYUANYS() != null ? Long.valueOf(zyBingrenxx.getZHUYUANYS()) : null);
        patientInfo.setDoctorTitleShow("住院医师");
        patientInfo.setNurseJobNumber(zyBingrenxx.getZERENHS() != null ? Long.valueOf(zyBingrenxx.getZERENHS()) : null);
        patientInfo.setNurseTitleShow("责任护士");

        // 状态信息
        patientInfo.setPatientStatus(1L); // 1-在院

        // 身份证号码
        patientInfo.setIdNumber(zyBingrenxx.getSHENFENZH());
        // 生日信息
        if (zyBingrenxx.getCHUSHENGRQ() != null) {
            patientInfo.setBirthday(zyBingrenxx.getCHUSHENGRQ().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            //从身份证号码获取出生日期 格式 1981-03-22
            patientInfo.setBirthday(
                    Optional.ofNullable(zyBingrenxx.getSHENFENZH())
                            .filter(id -> id.length() >= 14)
                            .map(id -> String.format("%s-%s-%s", id.substring(6, 10), id.substring(10, 12), id.substring(12, 14)))
                            .orElse(null)
            );
        }
        // 医疗性质
        patientInfo.setMedicalNature(zyBingrenxx.getFEIYONGXZ());
        // 护理等级
        String nursingLevel = getNursingLevelFromOrder(zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
        if (nursingLevel != null && !nursingLevel.trim().isEmpty()) {
            patientInfo.setNurseGrade(nursingLevel);
        }
        // 转科时间
        if (zyBingrenxx.getZHUANBINGQBZ() != null && zyBingrenxx.getZHUANBINGQBZ() > 0
            && zyBingrenxx.getRUKERQ() != null) {
            patientInfo.setTransferTime(zyBingrenxx.getRUKERQ().format(dateFormatter));
        }
        // 出院时间
        if (zyBingrenxx.getCHUYUANRQ() != null) {
            patientInfo.setDischargeTime(zyBingrenxx.getCHUYUANRQ().format(dateFormatter));
        } else if (zyBingrenxx.getYUCHUYRQ() != null) {
            // 如果没有实际出院时间，使用预出院时间
            patientInfo.setDischargeTime(zyBingrenxx.getYUCHUYRQ().format(dateFormatter));
        } else {
            patientInfo.setDischargeTime(null);
        }

        patientInfo.setIsWriteToAdvice(null);

        return patientInfo;
    }

    /**
     * 脱敏姓名
     */
    private String desensitizeName(String name) {
        if (StringUtils.hasLength(name)) {
            var frontIndex = name.length() <= 2 ? 0 : 1;
            var desensitizedInpatientName = DesensitizedUtil.idCardNum(name, frontIndex, 1);
            return desensitizedInpatientName;
        }
        return name;
    }

    /**
     * 转换性别为Long类型
     */
    private Long convertGenderToLong(String gender) {
        if (gender == null) {
            return 0L;
        }

        switch (gender.trim()) {
            case "男":
            case "1":
                return 1L;
            case "女":
            case "2":
                return 2L;
            default:
                return 0L;
        }
    }

    /**
     * 转换病区代码为Long类型
     */
    private Long convertWardCodeToLong(String wardCode) {
        if (wardCode == null) {
            return null;
        }
        try {
            return Long.parseLong(wardCode);
        } catch (NumberFormatException e) {
            log.warn("无法转换病区代码为数字: {}", wardCode);
            return null;
        }
    }

    /**
     * 生成患者标签
     */
    private List<PatientLabelReq> generatePatientLabels(ZyBingrenxx zyBingrenxx) {
        List<PatientLabelReq> labels = new ArrayList<>();
        Long patientId = parsePatientId(zyBingrenxx.getBINGRENZYID());
        String bingquid = zyBingrenxx.getDANGQIANBQ(); // 病区ID

        labels.add(createLabel(patientId, "FL01", 1L));
        // FL02 - 在院（所有患者默认都是在院状态）
        labels.add(createLabel(patientId, "FL02", 1L));

        // FL16 - 过敏（根据过敏史判断）
        if (hasAllergy(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL16", 1L));
        } else {
            labels.add(createLabel(patientId, "FL16", 0L));
        }

        // FL09 - 今日入院（根据入院日期判断）
        if (zyBingrenxx.getRUYUANRQ() != null && isToday(zyBingrenxx.getRUYUANRQ())) {
            labels.add(createLabel(patientId, "FL09", 1L));
        }else {
            labels.add(createLabel(patientId, "FL09", 0L));
        }

        // FL05/FL06/FL08 - 护理等级（根据医嘱判断）
        String nursingLevel = getNursingLevelFromOrder(zyBingrenxx.getBINGRENZYID(), bingquid);
       /* if ("特级护理".equals(nursingLevel) || "特级护理(ICU)".equals(nursingLevel)) {
            labels.add(createLabel(patientId, "FL05", 1L)); // 特级护理归为一级护理
        } */
        if ("一级护理".equals(nursingLevel)) {
            labels.add(createLabel(patientId, "FL05", 1L));
        }else {
            labels.add(createLabel(patientId, "FL05", 0L));
        }
        if ("二级护理".equals(nursingLevel)) {
            labels.add(createLabel(patientId, "FL06", 1L));
        }else {
            labels.add(createLabel(patientId, "FL06", 0L));
        }
        if ("三级护理".equals(nursingLevel)) {
            labels.add(createLabel(patientId, "FL08", 1L));
        }else {
            labels.add(createLabel(patientId, "FL08", 0L));
        }

        // FL07 - 危重（根据特定医嘱判断）
        if (isCriticalPatient(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL07", 1L));
        } else {
            labels.add(createLabel(patientId, "FL07", 0L));
        }

        // FL10 - 今日手术（根据手术安排判断）
        if (hasSurgeryToday(zyBingrenxx.getBINGRENZYID(), bingquid)) {
            labels.add(createLabel(patientId, "FL10", 1L));
        }else {
            labels.add(createLabel(patientId, "FL10", 0L));
        }

        // FL11 - 今日出院（需要根据出院安排判断）
        if (hasDischargeToday(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL11", 1L));
        }else {
            labels.add(createLabel(patientId, "FL11", 0L));
        }

        // FL12 - 转入（根据床位变动记录判断）
        if (isTransferIn(zyBingrenxx.getBINGRENZYID())) {
            labels.add(createLabel(patientId, "FL12", 1L));
        }else {
            labels.add(createLabel(patientId, "FL12", 0L));
        }

        // FL13 - 转出（根据床位变动记录判断）
        if (isTransferOut(zyBingrenxx.getBINGRENZYID())) {
            labels.add(createLabel(patientId, "FL13", 1L));
        }else {
            labels.add(createLabel(patientId, "FL13", 0L));
        }

        // FL14 - 借床（根据床位变动记录判断）
        if (isBorrowedBed(zyBingrenxx.getBINGRENZYID())) {
            labels.add(createLabel(patientId, "FL14", 1L));
        }else {
            labels.add(createLabel(patientId, "FL14", 0L));
        }

        // FL15 - 多重耐药（需要根据医嘱"接触隔离"判断）
        if (hasContactIsolation(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL15", 1L));
        }else {
            labels.add(createLabel(patientId, "FL15", 0L));
        }

        // FL17 - 高危VTE（需要根据Caprini量表评分≥5分判断）
        if (isHighRiskVTE(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL17", 1L));
        }else {
            labels.add(createLabel(patientId, "FL17", 0L));
        }

        // FL18 - 高危跌倒（需要根据约翰霍普金斯量表≥14分判断）
        if (isHighRiskFall(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL18", 1L));
        }else {
            labels.add(createLabel(patientId, "FL18", 0L));
        }

        // FL19 - 高危压力性损伤（需要根据Braden评估表≤12分判断）
        if (isHighRiskPressureInjury(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL19", 1L));
        }else {
            labels.add(createLabel(patientId, "FL19", 0L));
        }

        // FL20 - 疼痛（需要根据NRS评估表≥1分判断）
        if (hasPain(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL20", 1L));
        }else {
            labels.add(createLabel(patientId, "FL20", 0L));
        }

        // FL21 - 中医护理方案（需要根据护理系统"中"字判断）
        if (hasChineseMedicineNursing(zyBingrenxx)) {
            labels.add(createLabel(patientId, "FL21", 1L));
        }else {
            labels.add(createLabel(patientId, "FL21", 0L));
        }

        log.debug("为患者{}生成了{}个标签", zyBingrenxx.getXINGMING(), labels.size());
        return labels;
    }

    /**
     * 创建标签对象
     */
    private PatientLabelReq createLabel(Long patientId, String labelCode, Long isDisplay) {
        PatientLabelReq label = new PatientLabelReq();
        label.setPatientid(patientId);
        label.setLabelCode(labelCode);
        label.setIsDisplay(isDisplay);
        return label;
    }

    /**
     * 获取默认标签编码列表
     */
    private List<String> getDefaultLabelCodes() {
        List<String> defaultCodes = new ArrayList<>();
        defaultCodes.add("FL01"); // 全部
        defaultCodes.add("FL02"); // 在院
        defaultCodes.add("FL05"); // 一级护理
        defaultCodes.add("FL06"); // 二级护理
        defaultCodes.add("FL07"); // 危重
        defaultCodes.add("FL08"); // 三级护理
        defaultCodes.add("FL09"); // 今日入院
        defaultCodes.add("FL10"); // 今日手术
        defaultCodes.add("FL11"); // 今日出院
        defaultCodes.add("FL12"); // 转入
        defaultCodes.add("FL13"); // 转出
        defaultCodes.add("FL14"); // 借床
        defaultCodes.add("FL15"); // 多重耐药
        defaultCodes.add("FL16"); // 过敏
        defaultCodes.add("FL17"); // 高危VTE
        defaultCodes.add("FL18"); // 高危跌倒
        defaultCodes.add("FL19"); // 高危压力性损伤
        defaultCodes.add("FL20"); // 疼痛
        defaultCodes.add("FL21"); // 中医护理方案
        return defaultCodes;
    }

    /**
     * 判断是否为今天
     */
    private boolean isToday(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        LocalDateTime today = LocalDateTime.now();

        return dateTime.toLocalDate().equals(today.toLocalDate());
    }



    /**
     * 判断是否为危重患者（根据特定医嘱判断）
     */
    private boolean isCriticalPatient(ZyBingrenxx zyBingrenxx) {
        try {
            Integer criticalCount = patientDataService.getCriticalPatientCount(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return criticalCount != null && criticalCount > 0;
        } catch (Exception e) {
            log.warn("查询危重患者记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断今日是否有手术
     */
    private boolean hasSurgeryToday(String bingrenzyid, String bingquid) {
        try {
            Integer surgeryCount = patientDataService.getTodaySurgeryCount(bingrenzyid, bingquid);
            return surgeryCount != null && surgeryCount > 0;
        } catch (Exception e) {
            log.warn("查询今日手术失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断今日是否出院（需要根据实际出院安排判断）
     */
    private boolean hasDischargeToday(ZyBingrenxx zyBingrenxx) {
        // 这里需要根据实际的出院安排来判断
        // 暂时返回false，需要从出院安排表获取数据
        return false;
    }

    /**
     * 判断是否为转入患者
     */
    private boolean isTransferIn(String bingrenzyid) {
        try {
            Integer transferInCount = patientDataService.getTransferInCount(bingrenzyid);
            return transferInCount != null && transferInCount > 0;
        } catch (Exception e) {
            log.warn("查询转入记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否为转出患者
     */
    private boolean isTransferOut(String bingrenzyid) {
        try {
            Integer transferOutCount = patientDataService.getTransferOutCount(bingrenzyid);
            return transferOutCount != null && transferOutCount > 0;
        } catch (Exception e) {
            log.warn("查询转出记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否为借床患者
     */
    private boolean isBorrowedBed(String bingrenzyid) {
        try {
            Integer borrowedBedCount = patientDataService.getBorrowedBedCount(bingrenzyid);
            return borrowedBedCount != null && borrowedBedCount > 0;
        } catch (Exception e) {
            log.warn("查询借床记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否有过敏史
     */
    private boolean hasAllergy(ZyBingrenxx zyBingrenxx) {
        // 检查过敏史表中的药品过敏记录
        try {
            Integer allergyCount = patientDataService.getAllergyCount(zyBingrenxx.getBINGRENID());
            return allergyCount != null && allergyCount > 0;
        } catch (Exception e) {
            log.warn("查询过敏史记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否有接触隔离医嘱（多重耐药）
     */
    private boolean hasContactIsolation(ZyBingrenxx zyBingrenxx) {
        try {
            Integer isolationCount = patientDataService.getContactIsolationCount(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return isolationCount != null && isolationCount > 0;
        } catch (Exception e) {
            log.warn("查询接触隔离记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否为高危VTE（根据Caprini量表评分="高危"）
     */
    private boolean isHighRiskVTE(ZyBingrenxx zyBingrenxx) {
        try {
            String vteRiskLevel = patientDataService.getVTERiskLevel(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return vteRiskLevel != null;
        } catch (Exception e) {
            log.warn("查询VTE风险等级失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否为高危跌倒（根据约翰霍普金斯量表>13分）
     */
    private boolean isHighRiskFall(ZyBingrenxx zyBingrenxx) {
        try {
            Integer fallScore = patientDataService.getFallRiskScore(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return fallScore != null && fallScore > 13;
        } catch (Exception e) {
            log.warn("查询跌倒风险评分失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否为高危压力性损伤（根据Braden评估表≤12分）
     */
    private boolean isHighRiskPressureInjury(ZyBingrenxx zyBingrenxx) {
        try {
            Integer pressureScore = patientDataService.getPressureInjuryScore(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return pressureScore != null && pressureScore <= 12;
        } catch (Exception e) {
            log.warn("查询压力性损伤评分失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否有疼痛（根据NRS评估表≥1分）
     */
    private boolean hasPain(ZyBingrenxx zyBingrenxx) {
        try {
            Integer painScore = patientDataService.getPainScore(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return painScore != null && painScore >= 1;
        } catch (Exception e) {
            log.warn("查询疼痛评分失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 判断是否有中医护理方案
     */
    private boolean hasChineseMedicineNursing(ZyBingrenxx zyBingrenxx) {
        try {
            Integer nursingCount = patientDataService.getChineseMedicineNursingCount(
                zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            return nursingCount != null && nursingCount > 0;
        } catch (Exception e) {
            log.warn("查询中医护理方案记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据医嘱获取护理等级
     * @param bingrenzyid 病人住院ID
     * @param bingquid 病区ID
     * @return 护理等级
     */
    private String getNursingLevelFromOrder(String bingrenzyid, String bingquid) {
        if (bingrenzyid == null || bingquid == null) {
            return "";
        }

        try {
            String orderItemId = patientDataService.getNursingLevelOrderId(bingrenzyid, bingquid);
            if (orderItemId != null && !orderItemId.trim().isEmpty()) {
                // 根据医嘱项目ID转换为护理等级名称
                return convertOrderIdToNursingLevel(orderItemId);
            }
        } catch (Exception e) {
            log.warn("查询护理等级失败，病人ID: {}, 病区: {}, 错误: {}", bingrenzyid, bingquid, e.getMessage());
        }

        return "";
    }

    /**
     * 将医嘱项目ID转换为护理等级名称
     * @param orderItemId 医嘱项目ID
     * @return 护理等级名称
     */
    private String convertOrderIdToNursingLevel(String orderItemId) {
        if (orderItemId == null || orderItemId.trim().isEmpty()) {
            return "";
        }


        switch (orderItemId.trim()) {
            case "15150":
            case "**********":
            case "**********":
                return "特级护理";
            case "**********":
                return "一级护理";
            case "**********":
                return "二级护理";
            case "**********":
            case "**********":
                return "三级护理";
            default:
                log.debug("未知的护理等级医嘱项目ID: {}", orderItemId);
                return "";
        }
    }

    /**
     * 解析患者ID为Long类型
     */
    private Long parsePatientId(String patientId) {
        if (patientId == null) {
            return null;
        }
        try {
            return Long.parseLong(patientId);
        } catch (NumberFormatException e) {
            log.warn("无法转换患者ID为数字: {}", patientId);
            return null;
        }
    }
}
