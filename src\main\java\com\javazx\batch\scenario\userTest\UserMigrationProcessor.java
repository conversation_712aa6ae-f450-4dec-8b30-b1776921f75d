package com.javazx.batch.scenario.userTest;

import com.javazx.batch.po.UserFrom;
import com.javazx.batch.po.UserTo;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 用户数据迁移处理器
 * 将UserFrom对象转换为UserTo对象
 *
 */
@Component
public class UserMigrationProcessor implements ItemProcessor<UserFrom, UserTo> {
    
    @Override
    public UserTo process(UserFrom userFrom) {
        if (userFrom == null) {
            return null;
        }
        
        // 创建目标对象
        UserTo userTo = new UserTo();
        
        // 基本字段映射
        userTo.setId(userFrom.getId());
        userTo.setUserName(userFrom.getUserName());
        userTo.setSex(userFrom.getSex());
        userTo.setAge(userFrom.getAge());
        userTo.setAddress(userFrom.getAddress());
        userTo.setCreateTime(userFrom.getCreateTime());
        userTo.setStatus(userFrom.getStatus());
        userTo.setRemark(userFrom.getRemark());
        
        // 设置更新时间为当前时间
        userTo.setUpdateTime(new Date());
        
        // 可以在这里添加更复杂的业务逻辑
        // 例如：数据验证、字段转换、业务规则处理等
        
        // 示例：如果年龄小于0，设置为0
        if (userTo.getAge() != null && userTo.getAge() < 0) {
            userTo.setAge(0);
        }
        
        // 示例：如果地址为空，设置默认值
        if (userTo.getAddress() == null || userTo.getAddress().trim().isEmpty()) {
            userTo.setAddress("未知地址");
        }
        
        return userTo;
    }
}
