package com.javazx.batch.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Set;

/**
 * Redis配置类
 * 配置RedisTemplate用于任务依赖管理
 */
@Slf4j
@Configuration
public class RedisConfig implements ApplicationRunner {

    // 任务相关的Redis键前缀
    private static final String TASK_COMPLETION_PREFIX = "task:completion:";
    private static final String TASK_EXECUTION_PREFIX = "task:execution:";
    private static final String CURRENT_EXECUTION_PREFIX = "task:current:";
    private static final String TASK_COUNT_PREFIX = "task:count:";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 配置RedisTemplate
     * @param connectionFactory Redis连接工厂
     * @return RedisTemplate实例
     */
    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置key和value的序列化器为StringRedisSerializer
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setValueSerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setHashValueSerializer(stringSerializer);
        
        // 启用事务支持
        template.setEnableTransactionSupport(true);
        
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 应用启动时执行，清理所有任务相关的Redis键
     */
    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("应用启动，开始清理任务相关的Redis键...");

        try {
            clearTaskRelatedKeys();
            log.info("任务相关的Redis键清理完成");
        } catch (Exception e) {
            log.error("清理任务相关的Redis键时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 清理所有任务相关的Redis键
     */
    private void clearTaskRelatedKeys() {
        // 清理任务完成状态键
        clearKeysByPattern(TASK_COMPLETION_PREFIX + "*");

        // 清理任务执行状态键
        clearKeysByPattern(TASK_EXECUTION_PREFIX + "*");

        // 清理当前执行ID键
        clearKeysByPattern(CURRENT_EXECUTION_PREFIX + "*");

        // 清理任务执行次数计数器
        clearKeysByPattern(TASK_COUNT_PREFIX + "*");
    }

    /**
     * 根据模式清理Redis键
     * @param pattern 键模式
     */
    private void clearKeysByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清理Redis键，模式: {}, 数量: {}", pattern, keys.size());
            } else {
                log.debug("未找到匹配的Redis键，模式: {}", pattern);
            }
        } catch (Exception e) {
            log.error("清理Redis键时发生错误，模式: {}, 错误: {}", pattern, e.getMessage());
        }
    }
}
