<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.YzBingrenyzMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.YzBingrenyz">
            <id property="YIZHUID" column="YIZHUID" jdbcType="VARCHAR"/>
            <result property="YINGYONGID" column="YINGYONGID" jdbcType="VARCHAR"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="VARCHAR"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="YINGERID" column="YINGERID" jdbcType="VARCHAR"/>
            <result property="BINGRENXM" column="BINGRENXM" jdbcType="VARCHAR"/>
            <result property="YIZHUXMID" column="YIZHUXMID" jdbcType="VARCHAR"/>
            <result property="YIZHUMC" column="YIZHUMC" jdbcType="VARCHAR"/>
            <result property="KAIZHUSJ" column="KAIZHUSJ" jdbcType="TIMESTAMP"/>
            <result property="YIZHUMS" column="YIZHUMS" jdbcType="VARCHAR"/>
            <result property="SHURUSJ" column="SHURUSJ" jdbcType="TIMESTAMP"/>
            <result property="PINCI" column="PINCI" jdbcType="VARCHAR"/>
            <result property="SHURUREN" column="SHURUREN" jdbcType="VARCHAR"/>
            <result property="KAISHISJ" column="KAISHISJ" jdbcType="TIMESTAMP"/>
            <result property="JIESHUSJ" column="JIESHUSJ" jdbcType="TIMESTAMP"/>
            <result property="YIZHUFL" column="YIZHUFL" jdbcType="VARCHAR"/>
            <result property="GEIYAOFS" column="GEIYAOFS" jdbcType="VARCHAR"/>
            <result property="GEIYAOFSLX" column="GEIYAOFSLX" jdbcType="DECIMAL"/>
            <result property="GEIYAOFSMC" column="GEIYAOFSMC" jdbcType="VARCHAR"/>
            <result property="KAIZHUYS" column="KAIZHUYS" jdbcType="VARCHAR"/>
            <result property="KAIZHUYSXM" column="KAIZHUYSXM" jdbcType="VARCHAR"/>
            <result property="ZHIXINGSJ" column="ZHIXINGSJ" jdbcType="TIMESTAMP"/>
            <result property="ZHIXINGREN" column="ZHIXINGREN" jdbcType="VARCHAR"/>
            <result property="TINGZHUSJ" column="TINGZHUSJ" jdbcType="TIMESTAMP"/>
            <result property="FUHESJ" column="FUHESJ" jdbcType="TIMESTAMP"/>
            <result property="FUHEREN" column="FUHEREN" jdbcType="VARCHAR"/>
            <result property="TINGZHUREN" column="TINGZHUREN" jdbcType="VARCHAR"/>
            <result property="TINGZHUFHR" column="TINGZHUFHR" jdbcType="VARCHAR"/>
            <result property="TINGZHUFHSJ" column="TINGZHUFHSJ" jdbcType="TIMESTAMP"/>
            <result property="GUIGEID" column="GUIGEID" jdbcType="VARCHAR"/>
            <result property="CHEXIAOREN" column="CHEXIAOREN" jdbcType="VARCHAR"/>
            <result property="YIZHUZT" column="YIZHUZT" jdbcType="VARCHAR"/>
            <result property="DANGQIANZT" column="DANGQIANZT" jdbcType="VARCHAR"/>
            <result property="JILIANGDW" column="JILIANGDW" jdbcType="VARCHAR"/>
            <result property="BAOZHUANGDW" column="BAOZHUANGDW" jdbcType="VARCHAR"/>
            <result property="ZUIXIAODW" column="ZUIXIAODW" jdbcType="VARCHAR"/>
            <result property="BAOZHUANGLIANG" column="BAOZHUANGLIANG" jdbcType="DECIMAL"/>
            <result property="FUYIZMC" column="FUYIZMC" jdbcType="VARCHAR"/>
            <result property="YAOFANGYYID" column="YAOFANGYYID" jdbcType="VARCHAR"/>
            <result property="YAOPINMC" column="YAOPINMC" jdbcType="VARCHAR"/>
            <result property="JIAGEID" column="JIAGEID" jdbcType="VARCHAR"/>
            <result property="YAOPINBMID" column="YAOPINBMID" jdbcType="VARCHAR"/>
            <result property="YAOPINGG" column="YAOPINGG" jdbcType="VARCHAR"/>
            <result property="PAICHISJ" column="PAICHISJ" jdbcType="TIMESTAMP"/>
            <result property="PAICHIYZID" column="PAICHIYZID" jdbcType="VARCHAR"/>
            <result property="JIXING" column="JIXING" jdbcType="DECIMAL"/>
            <result property="PISHIJG" column="PISHIJG" jdbcType="VARCHAR"/>
            <result property="LINGYAOFS" column="LINGYAOFS" jdbcType="VARCHAR"/>
            <result property="DAYINBZ" column="DAYINBZ" jdbcType="DECIMAL"/>
            <result property="FUYIZID" column="FUYIZID" jdbcType="VARCHAR"/>
            <result property="YISHENGZID" column="YISHENGZID" jdbcType="VARCHAR"/>
            <result property="DANJIA" column="DANJIA" jdbcType="DECIMAL"/>
            <result property="YISHENGZT" column="YISHENGZT" jdbcType="VARCHAR"/>
            <result property="ZHIXINGYL" column="ZHIXINGYL" jdbcType="VARCHAR"/>
            <result property="KESHIID" column="KESHIID" jdbcType="VARCHAR"/>
            <result property="ZUHAO" column="ZUHAO" jdbcType="VARCHAR"/>
            <result property="PAICHILX" column="PAICHILX" jdbcType="DECIMAL"/>
            <result property="PAIXUBH" column="PAIXUBH" jdbcType="VARCHAR"/>
            <result property="SHOURICS" column="SHOURICS" jdbcType="DECIMAL"/>
            <result property="DANGRICS" column="DANGRICS" jdbcType="DECIMAL"/>
            <result property="DAYINJL" column="DAYINJL" jdbcType="VARCHAR"/>
            <result property="MORICS" column="MORICS" jdbcType="DECIMAL"/>
            <result property="KAIZHUBQ" column="KAIZHUBQ" jdbcType="VARCHAR"/>
            <result property="KAIZHUKS" column="KAIZHUKS" jdbcType="VARCHAR"/>
            <result property="CHEXIAOSJ" column="CHEXIAOSJ" jdbcType="TIMESTAMP"/>
            <result property="ZHUANGTAISJ" column="ZHUANGTAISJ" jdbcType="TIMESTAMP"/>
            <result property="CHANDILB" column="CHANDILB" jdbcType="VARCHAR"/>
            <result property="BINGQUID" column="BINGQUID" jdbcType="VARCHAR"/>
            <result property="YICIJL" column="YICIJL" jdbcType="DECIMAL"/>
            <result property="YICIJLDW" column="YICIJLDW" jdbcType="VARCHAR"/>
            <result property="YICIYL" column="YICIYL" jdbcType="DECIMAL"/>
            <result property="JILIANG" column="JILIANG" jdbcType="DECIMAL"/>
            <result property="QIANGZHIBZ" column="QIANGZHIBZ" jdbcType="DECIMAL"/>
            <result property="KAIZHUKSMC" column="KAIZHUKSMC" jdbcType="VARCHAR"/>
            <result property="KAIZHUBQMC" column="KAIZHUBQMC" jdbcType="VARCHAR"/>
            <result property="JIZHENBZ" column="JIZHENBZ" jdbcType="DECIMAL"/>
            <result property="CHULIYJ" column="CHULIYJ" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="ZUIXIAODWZXYL" column="ZUIXIAODWZXYL" jdbcType="VARCHAR"/>
            <result property="ZUIXIAODWYL" column="ZUIXIAODWYL" jdbcType="DECIMAL"/>
            <result property="PISHIBZ" column="PISHIBZ" jdbcType="DECIMAL"/>
            <result property="FUYAOSX" column="FUYAOSX" jdbcType="VARCHAR"/>
            <result property="TAKEBZ" column="TAKEBZ" jdbcType="DECIMAL"/>
            <result property="YEJIANBZ" column="YEJIANBZ" jdbcType="DECIMAL"/>
            <result property="PISHILX" column="PISHILX" jdbcType="VARCHAR"/>
            <result property="PISHIFHR1" column="PISHIFHR1" jdbcType="VARCHAR"/>
            <result property="PISHIFHSJ1" column="PISHIFHSJ1" jdbcType="TIMESTAMP"/>
            <result property="PISHIFHR2" column="PISHIFHR2" jdbcType="VARCHAR"/>
            <result property="PISHIFHSJ2" column="PISHIFHSJ2" jdbcType="TIMESTAMP"/>
            <result property="JIFEIJG" column="JIFEIJG" jdbcType="DECIMAL"/>
            <result property="JIFEIFS" column="JIFEIFS" jdbcType="VARCHAR"/>
            <result property="ZHIXINGSJLX" column="ZHIXINGSJLX" jdbcType="VARCHAR"/>
            <result property="FEIYONGXZZHBZ" column="FEIYONGXZZHBZ" jdbcType="DECIMAL"/>
            <result property="JINGMAIPBZ" column="JINGMAIPBZ" jdbcType="DECIMAL"/>
            <result property="FAYAOZT" column="FAYAOZT" jdbcType="VARCHAR"/>
            <result property="YUANYAOFYYID" column="YUANYAOFYYID" jdbcType="VARCHAR"/>
            <result property="ZIFUBL" column="ZIFUBL" jdbcType="VARCHAR"/>
            <result property="CAOYAOBZ" column="CAOYAOBZ" jdbcType="DECIMAL"/>
            <result property="DAIJIANTS" column="DAIJIANTS" jdbcType="DECIMAL"/>
            <result property="TINGZHUBQ" column="TINGZHUBQ" jdbcType="VARCHAR"/>
            <result property="PAIXUZH" column="PAIXUZH" jdbcType="VARCHAR"/>
            <result property="QIANFEISHBZ" column="QIANFEISHBZ" jdbcType="DECIMAL"/>
            <result property="YUYUERQ" column="YUYUERQ" jdbcType="TIMESTAMP"/>
            <result property="ZHIXINGKSJFBZ" column="ZHIXINGKSJFBZ" jdbcType="DECIMAL"/>
            <result property="ZHIXINGKS" column="ZHIXINGKS" jdbcType="VARCHAR"/>
            <result property="FENQURQ" column="FENQURQ" jdbcType="TIMESTAMP"/>
            <result property="PINCIZXSJ" column="PINCIZXSJ" jdbcType="VARCHAR"/>
            <result property="YIYUANSPBZ" column="YIYUANSPBZ" jdbcType="DECIMAL"/>
            <result property="YIYUANSPLX" column="YIYUANSPLX" jdbcType="DECIMAL"/>
            <result property="YIYUANSPSM" column="YIYUANSPSM" jdbcType="VARCHAR"/>
            <result property="YIYUANSPBL" column="YIYUANSPBL" jdbcType="VARCHAR"/>
            <result property="JIAOBANHDBZ" column="JIAOBANHDBZ" jdbcType="VARCHAR"/>
            <result property="ZONGHEDXH" column="ZONGHEDXH" jdbcType="VARCHAR"/>
            <result property="JIAOBANHDSJ" column="JIAOBANHDSJ" jdbcType="TIMESTAMP"/>
            <result property="JIAOBANHDR" column="JIAOBANHDR" jdbcType="VARCHAR"/>
            <result property="FUJIAFYJFFS" column="FUJIAFYJFFS" jdbcType="VARCHAR"/>
            <result property="YUANDANGQZT" column="YUANDANGQZT" jdbcType="VARCHAR"/>
            <result property="YUANYIZZT" column="YUANYIZZT" jdbcType="VARCHAR"/>
            <result property="DONGJIEBZ" column="DONGJIEBZ" jdbcType="DECIMAL"/>
            <result property="JIEDONGRQ" column="JIEDONGRQ" jdbcType="TIMESTAMP"/>
            <result property="TUIYAOSPBZ" column="TUIYAOSPBZ" jdbcType="DECIMAL"/>
            <result property="YAOPINJX" column="YAOPINJX" jdbcType="VARCHAR"/>
            <result property="ZHIXINGRQ" column="ZHIXINGRQ" jdbcType="VARCHAR"/>
            <result property="ZHIXINGZT" column="ZHIXINGZT" jdbcType="DECIMAL"/>
            <result property="KAPIANDYBZ" column="KAPIANDYBZ" jdbcType="VARCHAR"/>
            <result property="QUZHENGLX" column="QUZHENGLX" jdbcType="VARCHAR"/>
            <result property="DONGJIERQ" column="DONGJIERQ" jdbcType="TIMESTAMP"/>
            <result property="QINGJIABZ" column="QINGJIABZ" jdbcType="DECIMAL"/>
            <result property="YIYUANSPRQ" column="YIYUANSPRQ" jdbcType="TIMESTAMP"/>
            <result property="BIANGENGDDYBZ" column="BIANGENGDDYBZ" jdbcType="VARCHAR"/>
            <result property="CHEXIAOLX" column="CHEXIAOLX" jdbcType="VARCHAR"/>
            <result property="CHEXIAOYY" column="CHEXIAOYY" jdbcType="VARCHAR"/>
            <result property="YUANZHIXSJ" column="YUANZHIXSJ" jdbcType="TIMESTAMP"/>
            <result property="CHUYUANDYBZ" column="CHUYUANDYBZ" jdbcType="DECIMAL"/>
            <result property="CHANGQILSBZ" column="CHANGQILSBZ" jdbcType="DECIMAL"/>
            <result property="ZHIXINGBZ" column="ZHIXINGBZ" jdbcType="DECIMAL"/>
            <result property="JIAOBANHDR2" column="JIAOBANHDR2" jdbcType="VARCHAR"/>
            <result property="GONGZUOZID" column="GONGZUOZID" jdbcType="VARCHAR"/>
            <result property="CAOYAOTSYF" column="CAOYAOTSYF" jdbcType="VARCHAR"/>
            <result property="KANGSHENGSSYSM" column="KANGSHENGSSYSM" jdbcType="VARCHAR"/>
            <result property="SHENPIYS" column="SHENPIYS" jdbcType="VARCHAR"/>
            <result property="SHENPINR" column="SHENPINR" jdbcType="VARCHAR"/>
            <result property="DISHU" column="DISHU" jdbcType="VARCHAR"/>
            <result property="BIGUANGBZ" column="BIGUANGBZ" jdbcType="DECIMAL"/>
            <result property="ZHONGYAOPFKLBZ" column="ZHONGYAOPFKLBZ" jdbcType="DECIMAL"/>
            <result property="KUCUNBZBZ" column="KUCUNBZBZ" jdbcType="DECIMAL"/>
            <result property="BIANGENGHZDYBZ" column="BIANGENGHZDYBZ" jdbcType="VARCHAR"/>
            <result property="CHENGZUYZID" column="CHENGZUYZID" jdbcType="VARCHAR"/>
            <result property="YAOPINLDID" column="YAOPINLDID" jdbcType="VARCHAR"/>
            <result property="ZHONGYAOKLZHSL" column="ZHONGYAOKLZHSL" jdbcType="DECIMAL"/>
            <result property="KANGJUNYWSY1" column="KANGJUNYWSY1" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY2" column="KANGJUNYWSY2" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY3" column="KANGJUNYWSY3" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY4" column="KANGJUNYWSY4" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY5" column="KANGJUNYWSY5" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY6" column="KANGJUNYWSY6" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY7" column="KANGJUNYWSY7" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY8" column="KANGJUNYWSY8" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY9" column="KANGJUNYWSY9" jdbcType="VARCHAR"/>
            <result property="DULIFL" column="DULIFL" jdbcType="VARCHAR"/>
            <result property="QITASX" column="QITASX" jdbcType="VARCHAR"/>
            <result property="HUIZHENYZSPBZ" column="HUIZHENYZSPBZ" jdbcType="DECIMAL"/>
            <result property="HUIZHENYZSPYS" column="HUIZHENYZSPYS" jdbcType="VARCHAR"/>
            <result property="NONGJIANJBZ" column="NONGJIANJBZ" jdbcType="DECIMAL"/>
            <result property="LINCHUANGLJDRBZ" column="LINCHUANGLJDRBZ" jdbcType="DECIMAL"/>
            <result property="YAOWUSX" column="YAOWUSX" jdbcType="DECIMAL"/>
            <result property="GAOFANGBZ" column="GAOFANGBZ" jdbcType="DECIMAL"/>
            <result property="LINGYAORXM" column="LINGYAORXM" jdbcType="VARCHAR"/>
            <result property="LINGYAORSFZH" column="LINGYAORSFZH" jdbcType="VARCHAR"/>
            <result property="DUMAKH" column="DUMAKH" jdbcType="VARCHAR"/>
            <result property="CHANDI" column="CHANDI" jdbcType="VARCHAR"/>
            <result property="CHANDIMC" column="CHANDIMC" jdbcType="VARCHAR"/>
            <result property="MIANFEIYPBZ" column="MIANFEIYPBZ" jdbcType="DECIMAL"/>
            <result property="YUANJIESSJ" column="YUANJIESSJ" jdbcType="TIMESTAMP"/>
            <result property="YUANTINGZR" column="YUANTINGZR" jdbcType="VARCHAR"/>
            <result property="YUANTINGZBQ" column="YUANTINGZBQ" jdbcType="VARCHAR"/>
            <result property="YUANTINGZSJ" column="YUANTINGZSJ" jdbcType="TIMESTAMP"/>
            <result property="YIZHUJKID" column="YIZHUJKID" jdbcType="VARCHAR"/>
            <result property="SHENFANGZT" column="SHENFANGZT" jdbcType="VARCHAR"/>
            <result property="SHENFANGJG" column="SHENFANGJG" jdbcType="VARCHAR"/>
            <result property="SHENFANGREN" column="SHENFANGREN" jdbcType="VARCHAR"/>
            <result property="SHENFANGSJ" column="SHENFANGSJ" jdbcType="TIMESTAMP"/>
            <result property="KANGJUNYWSY10" column="KANGJUNYWSY10" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY11" column="KANGJUNYWSY11" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY12" column="KANGJUNYWSY12" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY13" column="KANGJUNYWSY13" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY14" column="KANGJUNYWSY14" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY15" column="KANGJUNYWSY15" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY16" column="KANGJUNYWSY16" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY17" column="KANGJUNYWSY17" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY18" column="KANGJUNYWSY18" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY19" column="KANGJUNYWSY19" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSY20" column="KANGJUNYWSY20" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYS" column="ZHUZHIYS" jdbcType="VARCHAR"/>
            <result property="TIAOPEIJL" column="TIAOPEIJL" jdbcType="VARCHAR"/>
            <result property="CAOYAOFCFY" column="CAOYAOFCFY" jdbcType="VARCHAR"/>
            <result property="JINGMAZD" column="JINGMAZD" jdbcType="VARCHAR"/>
            <result property="YICHANGJGCLYJ" column="YICHANGJGCLYJ" jdbcType="DECIMAL"/>
            <result property="YICHANGJGCLSJ" column="YICHANGJGCLSJ" jdbcType="TIMESTAMP"/>
            <result property="YICHANGJGCLSM" column="YICHANGJGCLSM" jdbcType="VARCHAR"/>
            <result property="YICHANGJGCLR" column="YICHANGJGCLR" jdbcType="VARCHAR"/>
            <result property="JINGSHIYS" column="JINGSHIYS" jdbcType="DECIMAL"/>
            <result property="BXSYZDBZ" column="BXSYZDBZ" jdbcType="DECIMAL"/>
            <result property="YICHAKBG" column="YICHAKBG" jdbcType="VARCHAR"/>
            <result property="LINCHUANGLJID" column="LINCHUANGLJID" jdbcType="VARCHAR"/>
            <result property="TAOCANXSBRMXID" column="TAOCANXSBRMXID" jdbcType="VARCHAR"/>
            <result property="TAOCANXSBZ" column="TAOCANXSBZ" jdbcType="DECIMAL"/>
            <result property="XIEDINGBZ" column="XIEDINGBZ" jdbcType="DECIMAL"/>
            <result property="XIEDINGCFID" column="XIEDINGCFID" jdbcType="VARCHAR"/>
            <result property="DUMACFQM" column="DUMACFQM" jdbcType="VARCHAR"/>
            <result property="DUMACFQMSJ" column="DUMACFQMSJ" jdbcType="TIMESTAMP"/>
            <result property="KESHIFFBZ" column="KESHIFFBZ" jdbcType="DECIMAL"/>
            <result property="DISHUDW" column="DISHUDW" jdbcType="VARCHAR"/>
            <result property="KANGJUNYW_LCZDDM" column="KANGJUNYW_LCZDDM" jdbcType="VARCHAR"/>
            <result property="KANGJUNYW_LCZDMC" column="KANGJUNYW_LCZDMC" jdbcType="VARCHAR"/>
            <result property="KANGJUNYW_SFHZ" column="KANGJUNYW_SFHZ" jdbcType="DECIMAL"/>
            <result property="KANGJUNYW_WSWSJ" column="KANGJUNYW_WSWSJ" jdbcType="DECIMAL"/>
            <result property="KANGJUNYW_JJSY" column="KANGJUNYW_JJSY" jdbcType="DECIMAL"/>
            <result property="CHEXIAOHSID" column="CHEXIAOHSID" jdbcType="VARCHAR"/>
            <result property="DAORUDGJLBBZ" column="DAORUDGJLBBZ" jdbcType="DECIMAL"/>
            <result property="PISHIPH" column="PISHIPH" jdbcType="VARCHAR"/>
            <result property="CHEXIAOSHBZ" column="CHEXIAOSHBZ" jdbcType="DECIMAL"/>
            <result property="CHEXIAOSHR" column="CHEXIAOSHR" jdbcType="VARCHAR"/>
            <result property="CHEXIAOSHSJ" column="CHEXIAOSHSJ" jdbcType="TIMESTAMP"/>
            <result property="SHUANGQIANMBZ" column="SHUANGQIANMBZ" jdbcType="DECIMAL"/>
            <result property="PPDBZ" column="PPDBZ" jdbcType="DECIMAL"/>
            <result property="PPDJG" column="PPDJG" jdbcType="VARCHAR"/>
            <result property="PPDBWBZ" column="PPDBWBZ" jdbcType="VARCHAR"/>
            <result property="HESUANKS" column="HESUANKS" jdbcType="VARCHAR"/>
            <result property="HESUANKSMC" column="HESUANKSMC" jdbcType="VARCHAR"/>
            <result property="YIZHUTJBZ" column="YIZHUTJBZ" jdbcType="DECIMAL"/>
            <result property="YIZHUTJR" column="YIZHUTJR" jdbcType="VARCHAR"/>
            <result property="YIZHUTJSJ" column="YIZHUTJSJ" jdbcType="TIMESTAMP"/>
            <result property="CHEXIAOSQR" column="CHEXIAOSQR" jdbcType="VARCHAR"/>
            <result property="CHEXIAOSQSJ" column="CHEXIAOSQSJ" jdbcType="TIMESTAMP"/>
            <result property="KUOZHANXX" column="KUOZHANXX" jdbcType="VARCHAR"/>
            <result property="BINGRENYLZ" column="BINGRENYLZ" jdbcType="VARCHAR"/>
            <result property="YIZHUSDBZ" column="YIZHUSDBZ" jdbcType="DECIMAL"/>
            <result property="YUANQIANSQDID" column="YUANQIANSQDID" jdbcType="VARCHAR"/>
            <result property="DAOGUANJSBZ" column="DAOGUANJSBZ" jdbcType="DECIMAL"/>
            <result property="DAOGUANJSR" column="DAOGUANJSR" jdbcType="VARCHAR"/>
            <result property="DAOGUANJSSJ" column="DAOGUANJSSJ" jdbcType="TIMESTAMP"/>
            <result property="YONGXUEBZ" column="YONGXUEBZ" jdbcType="VARCHAR"/>
            <result property="YUANQIANDRBZ" column="YUANQIANDRBZ" jdbcType="DECIMAL"/>
            <result property="ZHUANKEHCBZ" column="ZHUANKEHCBZ" jdbcType="DECIMAL"/>
            <result property="GUANDAOBWID" column="GUANDAOBWID" jdbcType="VARCHAR"/>
            <result property="GUANDAOBWMC" column="GUANDAOBWMC" jdbcType="VARCHAR"/>
            <result property="GUANDAOFWID" column="GUANDAOFWID" jdbcType="VARCHAR"/>
            <result property="GUANDAOFWMC" column="GUANDAOFWMC" jdbcType="VARCHAR"/>
            <result property="JIAJIBZ" column="JIAJIBZ" jdbcType="DECIMAL"/>
            <result property="ZIBEIBZ" column="ZIBEIBZ" jdbcType="DECIMAL"/>
            <result property="DUOCHONGNYBZ" column="DUOCHONGNYBZ" jdbcType="DECIMAL"/>
            <result property="HUANCHUANGHAO" column="HUANCHUANGHAO" jdbcType="VARCHAR"/>
            <result property="ZHUANRUKS" column="ZHUANRUKS" jdbcType="VARCHAR"/>
            <result property="ZHUANRUKSMC" column="ZHUANRUKSMC" jdbcType="VARCHAR"/>
            <result property="GUANDAOLYID" column="GUANDAOLYID" jdbcType="VARCHAR"/>
            <result property="GUANDAOLYMC" column="GUANDAOLYMC" jdbcType="VARCHAR"/>
            <result property="PISHIGCSJ" column="PISHIGCSJ" jdbcType="DECIMAL"/>
            <result property="PISHIKSSJ" column="PISHIKSSJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHUYZID" column="SHOUSHUYZID" jdbcType="VARCHAR"/>
            <result property="ZHILIAOFA" column="ZHILIAOFA" jdbcType="VARCHAR"/>
            <result property="ZHILIAOLY" column="ZHILIAOLY" jdbcType="VARCHAR"/>
            <result property="XUETANGYZBZ" column="XUETANGYZBZ" jdbcType="DECIMAL"/>
            <result property="XUSHENPIBZ" column="XUSHENPIBZ" jdbcType="DECIMAL"/>
            <result property="SHENPIBZ" column="SHENPIBZ" jdbcType="DECIMAL"/>
            <result property="SHENPIREN" column="SHENPIREN" jdbcType="VARCHAR"/>
            <result property="SHENPISJ" column="SHENPISJ" jdbcType="TIMESTAMP"/>
            <result property="BEIXUESQDID" column="BEIXUESQDID" jdbcType="VARCHAR"/>
            <result property="BEIXUEYZID" column="BEIXUEYZID" jdbcType="VARCHAR"/>
            <result property="HUSHIBZ" column="HUSHIBZ" jdbcType="VARCHAR"/>
            <result property="ZHUANRUBQ" column="ZHUANRUBQ" jdbcType="VARCHAR"/>
            <result property="ZHUANRUBQMC" column="ZHUANRUBQMC" jdbcType="VARCHAR"/>
            <result property="GUANDAOID" column="GUANDAOID" jdbcType="VARCHAR"/>
            <result property="TESHUYZBZ" column="TESHUYZBZ" jdbcType="DECIMAL"/>
            <result property="SHUNXUHAO" column="SHUNXUHAO" jdbcType="DECIMAL"/>
            <result property="ZHIQINGTYSZT" column="ZHIQINGTYSZT" jdbcType="DECIMAL"/>
            <result property="YAOPINPH" column="YAOPINPH" jdbcType="VARCHAR"/>
            <result property="GCPBZ" column="GCPBZ" jdbcType="DECIMAL"/>
            <result property="WAIPEIBZ" column="WAIPEIBZ" jdbcType="DECIMAL"/>
            <result property="ZENGPINBZ" column="ZENGPINBZ" jdbcType="DECIMAL"/>
            <result property="TPNYZBZ" column="TPNYZBZ" jdbcType="DECIMAL"/>
            <result property="PPDYIZHUID" column="PPDYIZHUID" jdbcType="VARCHAR"/>
            <result property="SHUQIANYZBZ" column="SHUQIANYZBZ" jdbcType="DECIMAL"/>
            <result property="QIANMINGBZ" column="QIANMINGBZ" jdbcType="DECIMAL"/>
            <result property="SHENFANGSQM" column="SHENFANGSQM" jdbcType="VARCHAR"/>
            <result property="SHENFANGSQMSJ" column="SHENFANGSQMSJ" jdbcType="TIMESTAMP"/>
            <result property="YONGYAOYY" column="YONGYAOYY" jdbcType="VARCHAR"/>
            <result property="SHUQIANYZSSMC" column="SHUQIANYZSSMC" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWSHBZ" column="KANGJUNYWSHBZ" jdbcType="VARCHAR"/>
            <result property="MAZUIBZ" column="MAZUIBZ" jdbcType="DECIMAL"/>
            <result property="SHUQIANYZSSID" column="SHUQIANYZSSID" jdbcType="VARCHAR"/>
            <result property="YIZHULX" column="YIZHULX" jdbcType="DECIMAL"/>
            <result property="YAOPINJJBZ" column="YAOPINJJBZ" jdbcType="DECIMAL"/>
            <result property="KANGJUNYWQM" column="KANGJUNYWQM" jdbcType="VARCHAR"/>
            <result property="KANGJUNYWQMSJ" column="KANGJUNYWQMSJ" jdbcType="TIMESTAMP"/>
            <result property="GUANDAOMC" column="GUANDAOMC" jdbcType="VARCHAR"/>
            <result property="GUOMINYFL" column="GUOMINYFL" jdbcType="VARCHAR"/>
            <result property="YUTINGSJ" column="YUTINGSJ" jdbcType="TIMESTAMP"/>
            <result property="CHUYUANZDID" column="CHUYUANZDID" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDMC" column="CHUYUANZDMC" jdbcType="VARCHAR"/>
            <result property="ZHONGYIZD" column="ZHONGYIZD" jdbcType="VARCHAR"/>
            <result property="ZHUANRUYLZ" column="ZHUANRUYLZ" jdbcType="VARCHAR"/>
            <result property="ZHONGYIZHID" column="ZHONGYIZHID" jdbcType="VARCHAR"/>
            <result property="FANGSHIZQ" column="FANGSHIZQ" jdbcType="VARCHAR"/>
            <result property="SHIYONGFW" column="SHIYONGFW" jdbcType="VARCHAR"/>
            <result property="FUHESJYH" column="FUHESJYH" jdbcType="VARCHAR"/>
            <result property="FUHERENYH" column="FUHERENYH" jdbcType="TIMESTAMP"/>
            <result property="BADIANJCNR" column="BADIANJCNR" jdbcType="VARCHAR"/>
            <result property="BADIANJCJG" column="BADIANJCJG" jdbcType="VARCHAR"/>
            <result property="SHOUSHUT" column="SHOUSHUT" jdbcType="VARCHAR"/>
            <result property="KAPIANLX" column="KAPIANLX" jdbcType="VARCHAR"/>
            <result property="DAGUIGCD" column="DAGUIGCD" jdbcType="VARCHAR"/>
            <result property="DAGUIGCDMC" column="DAGUIGCDMC" jdbcType="VARCHAR"/>
            <result property="DAGUIGBZL" column="DAGUIGBZL" jdbcType="DECIMAL"/>
            <result property="BAIZHUANGDW" column="BAIZHUANGDW" jdbcType="VARCHAR"/>
            <result property="TIJIDW" column="TIJIDW" jdbcType="VARCHAR"/>
            <result property="TIJI" column="TIJI" jdbcType="VARCHAR"/>
            <result property="DAGUIGJGID" column="DAGUIGJGID" jdbcType="VARCHAR"/>
            <result property="DAGUIGID" column="DAGUIGID" jdbcType="VARCHAR"/>
            <result property="YIZHUXMLX" column="YIZHUXMLX" jdbcType="VARCHAR"/>
            <result property="JIEDUANID" column="JIEDUANID" jdbcType="VARCHAR"/>
            <result property="BINGRENLJID" column="BINGRENLJID" jdbcType="VARCHAR"/>
            <result property="QIANGJIUBZ" column="QIANGJIUBZ" jdbcType="DECIMAL"/>
            <result property="APIANLX" column="APIANLX" jdbcType="VARCHAR"/>
            <result property="SHENGFANGSQMYY" column="SHENGFANGSQMYY" jdbcType="VARCHAR"/>
            <result property="SHENGFANGSQMSJ" column="SHENGFANGSQMSJ" jdbcType="VARCHAR"/>
            <result property="SHENGFANGSQMMC" column="SHENGFANGSQMMC" jdbcType="VARCHAR"/>
            <result property="SHENGFANGSQM" column="SHENGFANGSQM" jdbcType="VARCHAR"/>
            <result property="FEIYONGKZBZ" column="FEIYONGKZBZ" jdbcType="DECIMAL"/>
            <result property="WAIPEIBABH" column="WAIPEIBABH" jdbcType="VARCHAR"/>
            <result property="GAOFANGJX" column="GAOFANGJX" jdbcType="VARCHAR"/>
            <result property="GAOFANGFLXX" column="GAOFANGFLXX" jdbcType="VARCHAR"/>
            <result property="MEIRIJL" column="MEIRIJL" jdbcType="VARCHAR"/>
            <result property="JIANYAOFF" column="JIANYAOFF" jdbcType="VARCHAR"/>
            <result property="FUYAOCS" column="FUYAOCS" jdbcType="VARCHAR"/>
            <result property="FUYAOFF" column="FUYAOFF" jdbcType="VARCHAR"/>
            <result property="TONGSHUOYZID" column="TONGSHUOYZID" jdbcType="VARCHAR"/>
            <result property="YIBAODJ" column="YIBAODJ" jdbcType="VARCHAR"/>
            <result property="WAIPEISFR" column="WAIPEISFR" jdbcType="VARCHAR"/>
            <result property="WAIPEISFSJ" column="WAIPEISFSJ" jdbcType="TIMESTAMP"/>
            <result property="SHENGLIZQ" column="SHENGLIZQ" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        YIZHUID,YINGYONGID,YUANQUID,
        BINGRENZYID,YINGERID,BINGRENXM,
        YIZHUXMID,YIZHUMC,KAIZHUSJ,
        YIZHUMS,SHURUSJ,PINCI,
        SHURUREN,KAISHISJ,JIESHUSJ,
        YIZHUFL,GEIYAOFS,GEIYAOFSLX,
        GEIYAOFSMC,KAIZHUYS,KAIZHUYSXM,
        ZHIXINGSJ,ZHIXINGREN,TINGZHUSJ,
        FUHESJ,FUHEREN,TINGZHUREN,
        TINGZHUFHR,TINGZHUFHSJ,GUIGEID,
        CHEXIAOREN,YIZHUZT,DANGQIANZT,
        JILIANGDW,BAOZHUANGDW,ZUIXIAODW,
        BAOZHUANGLIANG,FUYIZMC,YAOFANGYYID,
        YAOPINMC,JIAGEID,YAOPINBMID,
        YAOPINGG,PAICHISJ,PAICHIYZID,
        JIXING,PISHIJG,LINGYAOFS,
        DAYINBZ,FUYIZID,YISHENGZID,
        DANJIA,YISHENGZT,ZHIXINGYL,
        KESHIID,ZUHAO,PAICHILX,
        PAIXUBH,SHOURICS,DANGRICS,
        DAYINJL,MORICS,KAIZHUBQ,
        KAIZHUKS,CHEXIAOSJ,ZHUANGTAISJ,
        CHANDILB,BINGQUID,YICIJL,
        YICIJLDW,YICIYL,JILIANG,
        QIANGZHIBZ,KAIZHUKSMC,KAIZHUBQMC,
        JIZHENBZ,CHULIYJ,BINGRENID,
        ZUIXIAODWZXYL,ZUIXIAODWYL,PISHIBZ,
        FUYAOSX,TAKEBZ,YEJIANBZ,
        PISHILX,PISHIFHR1,PISHIFHSJ1,
        PISHIFHR2,PISHIFHSJ2,JIFEIJG,
        JIFEIFS,ZHIXINGSJLX,FEIYONGXZZHBZ,
        JINGMAIPBZ,FAYAOZT,YUANYAOFYYID,
        ZIFUBL,CAOYAOBZ,DAIJIANTS,
        TINGZHUBQ,PAIXUZH,QIANFEISHBZ,
        YUYUERQ,ZHIXINGKSJFBZ,ZHIXINGKS,
        FENQURQ,PINCIZXSJ,YIYUANSPBZ,
        YIYUANSPLX,YIYUANSPSM,YIYUANSPBL,
        JIAOBANHDBZ,ZONGHEDXH,JIAOBANHDSJ,
        JIAOBANHDR,FUJIAFYJFFS,YUANDANGQZT,
        YUANYIZZT,DONGJIEBZ,JIEDONGRQ,
        TUIYAOSPBZ,YAOPINJX,ZHIXINGRQ,
        ZHIXINGZT,KAPIANDYBZ,QUZHENGLX,
        DONGJIERQ,QINGJIABZ,YIYUANSPRQ,
        BIANGENGDDYBZ,CHEXIAOLX,CHEXIAOYY,
        YUANZHIXSJ,CHUYUANDYBZ,CHANGQILSBZ,
        ZHIXINGBZ,JIAOBANHDR2,GONGZUOZID,
        CAOYAOTSYF,KANGSHENGSSYSM,SHENPIYS,
        SHENPINR,DISHU,BIGUANGBZ,
        ZHONGYAOPFKLBZ,KUCUNBZBZ,BIANGENGHZDYBZ,
        CHENGZUYZID,YAOPINLDID,ZHONGYAOKLZHSL,
        KANGJUNYWSY1,KANGJUNYWSY2,KANGJUNYWSY3,
        KANGJUNYWSY4,KANGJUNYWSY5,KANGJUNYWSY6,
        KANGJUNYWSY7,KANGJUNYWSY8,KANGJUNYWSY9,
        DULIFL,QITASX,HUIZHENYZSPBZ,
        HUIZHENYZSPYS,NONGJIANJBZ,LINCHUANGLJDRBZ,
        YAOWUSX,GAOFANGBZ,LINGYAORXM,
        LINGYAORSFZH,DUMAKH,CHANDI,
        CHANDIMC,MIANFEIYPBZ,YUANJIESSJ,
        YUANTINGZR,YUANTINGZBQ,YUANTINGZSJ,
        YIZHUJKID,SHENFANGZT,SHENFANGJG,
        SHENFANGREN,SHENFANGSJ,KANGJUNYWSY10,
        KANGJUNYWSY11,KANGJUNYWSY12,KANGJUNYWSY13,
        KANGJUNYWSY14,KANGJUNYWSY15,KANGJUNYWSY16,
        KANGJUNYWSY17,KANGJUNYWSY18,KANGJUNYWSY19,
        KANGJUNYWSY20,ZHUZHIYS,TIAOPEIJL,
        CAOYAOFCFY,JINGMAZD,YICHANGJGCLYJ,
        YICHANGJGCLSJ,YICHANGJGCLSM,YICHANGJGCLR,
        JINGSHIYS,BXSYZDBZ,YICHAKBG,
        LINCHUANGLJID,TAOCANXSBRMXID,TAOCANXSBZ,
        XIEDINGBZ,XIEDINGCFID,DUMACFQM,
        DUMACFQMSJ,KESHIFFBZ,DISHUDW,
        KANGJUNYW_LCZDDM,KANGJUNYW_LCZDMC,KANGJUNYW_SFHZ,
        KANGJUNYW_WSWSJ,KANGJUNYW_JJSY,CHEXIAOHSID,
        DAORUDGJLBBZ,PISHIPH,CHEXIAOSHBZ,
        CHEXIAOSHR,CHEXIAOSHSJ,SHUANGQIANMBZ,
        PPDBZ,PPDJG,PPDBWBZ,
        HESUANKS,HESUANKSMC,YIZHUTJBZ,
        YIZHUTJR,YIZHUTJSJ,CHEXIAOSQR,
        CHEXIAOSQSJ,KUOZHANXX,BINGRENYLZ,
        YIZHUSDBZ,YUANQIANSQDID,DAOGUANJSBZ,
        DAOGUANJSR,DAOGUANJSSJ,YONGXUEBZ,
        YUANQIANDRBZ,ZHUANKEHCBZ,GUANDAOBWID,
        GUANDAOBWMC,GUANDAOFWID,GUANDAOFWMC,
        JIAJIBZ,ZIBEIBZ,DUOCHONGNYBZ,
        HUANCHUANGHAO,ZHUANRUKS,ZHUANRUKSMC,
        GUANDAOLYID,GUANDAOLYMC,PISHIGCSJ,
        PISHIKSSJ,SHOUSHUYZID,ZHILIAOFA,
        ZHILIAOLY,XUETANGYZBZ,XUSHENPIBZ,
        SHENPIBZ,SHENPIREN,SHENPISJ,
        BEIXUESQDID,BEIXUEYZID,HUSHIBZ,
        ZHUANRUBQ,ZHUANRUBQMC,GUANDAOID,
        TESHUYZBZ,SHUNXUHAO,ZHIQINGTYSZT,
        YAOPINPH,GCPBZ,WAIPEIBZ,
        ZENGPINBZ,TPNYZBZ,PPDYIZHUID,
        SHUQIANYZBZ,QIANMINGBZ,SHENFANGSQM,
        SHENFANGSQMSJ,YONGYAOYY,SHUQIANYZSSMC,
        KANGJUNYWSHBZ,MAZUIBZ,SHUQIANYZSSID,
        YIZHULX,YAOPINJJBZ,KANGJUNYWQM,
        KANGJUNYWQMSJ,GUANDAOMC,GUOMINYFL,
        YUTINGSJ,CHUYUANZDID,CHUYUANZDMC,
        ZHONGYIZD,ZHUANRUYLZ,ZHONGYIZHID,
        FANGSHIZQ,SHIYONGFW,FUHESJYH,
        FUHERENYH,BADIANJCNR,BADIANJCJG,
        SHOUSHUT,KAPIANLX,DAGUIGCD,
        DAGUIGCDMC,DAGUIGBZL,BAIZHUANGDW,
        TIJIDW,TIJI,DAGUIGJGID,
        DAGUIGID,YIZHUXMLX,JIEDUANID,
        BINGRENLJID,QIANGJIUBZ,APIANLX,
        SHENGFANGSQMYY,SHENGFANGSQMSJ,SHENGFANGSQMMC,
        SHENGFANGSQM,FEIYONGKZBZ,WAIPEIBABH,
        GAOFANGJX,GAOFANGFLXX,MEIRIJL,
        JIANYAOFF,FUYAOCS,FUYAOFF,
        TONGSHUOYZID,YIBAODJ,WAIPEISFR,
        WAIPEISFSJ,SHENGLIZQ
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from YZ_BINGRENYZ
        where  YIZHUID = #{YIZHUID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from YZ_BINGRENYZ
        where  YIZHUID = #{YIZHUID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="YIZHUID" keyProperty="YIZHUID" parameterType="com.javazx.batch.po.YzBingrenyz" useGeneratedKeys="true">
        insert into YZ_BINGRENYZ
        ( YIZHUID,YINGYONGID,YUANQUID
        ,BINGRENZYID,YINGERID,BINGRENXM
        ,YIZHUXMID,YIZHUMC,KAIZHUSJ
        ,YIZHUMS,SHURUSJ,PINCI
        ,SHURUREN,KAISHISJ,JIESHUSJ
        ,YIZHUFL,GEIYAOFS,GEIYAOFSLX
        ,GEIYAOFSMC,KAIZHUYS,KAIZHUYSXM
        ,ZHIXINGSJ,ZHIXINGREN,TINGZHUSJ
        ,FUHESJ,FUHEREN,TINGZHUREN
        ,TINGZHUFHR,TINGZHUFHSJ,GUIGEID
        ,CHEXIAOREN,YIZHUZT,DANGQIANZT
        ,JILIANGDW,BAOZHUANGDW,ZUIXIAODW
        ,BAOZHUANGLIANG,FUYIZMC,YAOFANGYYID
        ,YAOPINMC,JIAGEID,YAOPINBMID
        ,YAOPINGG,PAICHISJ,PAICHIYZID
        ,JIXING,PISHIJG,LINGYAOFS
        ,DAYINBZ,FUYIZID,YISHENGZID
        ,DANJIA,YISHENGZT,ZHIXINGYL
        ,KESHIID,ZUHAO,PAICHILX
        ,PAIXUBH,SHOURICS,DANGRICS
        ,DAYINJL,MORICS,KAIZHUBQ
        ,KAIZHUKS,CHEXIAOSJ,ZHUANGTAISJ
        ,CHANDILB,BINGQUID,YICIJL
        ,YICIJLDW,YICIYL,JILIANG
        ,QIANGZHIBZ,KAIZHUKSMC,KAIZHUBQMC
        ,JIZHENBZ,CHULIYJ,BINGRENID
        ,ZUIXIAODWZXYL,ZUIXIAODWYL,PISHIBZ
        ,FUYAOSX,TAKEBZ,YEJIANBZ
        ,PISHILX,PISHIFHR1,PISHIFHSJ1
        ,PISHIFHR2,PISHIFHSJ2,JIFEIJG
        ,JIFEIFS,ZHIXINGSJLX,FEIYONGXZZHBZ
        ,JINGMAIPBZ,FAYAOZT,YUANYAOFYYID
        ,ZIFUBL,CAOYAOBZ,DAIJIANTS
        ,TINGZHUBQ,PAIXUZH,QIANFEISHBZ
        ,YUYUERQ,ZHIXINGKSJFBZ,ZHIXINGKS
        ,FENQURQ,PINCIZXSJ,YIYUANSPBZ
        ,YIYUANSPLX,YIYUANSPSM,YIYUANSPBL
        ,JIAOBANHDBZ,ZONGHEDXH,JIAOBANHDSJ
        ,JIAOBANHDR,FUJIAFYJFFS,YUANDANGQZT
        ,YUANYIZZT,DONGJIEBZ,JIEDONGRQ
        ,TUIYAOSPBZ,YAOPINJX,ZHIXINGRQ
        ,ZHIXINGZT,KAPIANDYBZ,QUZHENGLX
        ,DONGJIERQ,QINGJIABZ,YIYUANSPRQ
        ,BIANGENGDDYBZ,CHEXIAOLX,CHEXIAOYY
        ,YUANZHIXSJ,CHUYUANDYBZ,CHANGQILSBZ
        ,ZHIXINGBZ,JIAOBANHDR2,GONGZUOZID
        ,CAOYAOTSYF,KANGSHENGSSYSM,SHENPIYS
        ,SHENPINR,DISHU,BIGUANGBZ
        ,ZHONGYAOPFKLBZ,KUCUNBZBZ,BIANGENGHZDYBZ
        ,CHENGZUYZID,YAOPINLDID,ZHONGYAOKLZHSL
        ,KANGJUNYWSY1,KANGJUNYWSY2,KANGJUNYWSY3
        ,KANGJUNYWSY4,KANGJUNYWSY5,KANGJUNYWSY6
        ,KANGJUNYWSY7,KANGJUNYWSY8,KANGJUNYWSY9
        ,DULIFL,QITASX,HUIZHENYZSPBZ
        ,HUIZHENYZSPYS,NONGJIANJBZ,LINCHUANGLJDRBZ
        ,YAOWUSX,GAOFANGBZ,LINGYAORXM
        ,LINGYAORSFZH,DUMAKH,CHANDI
        ,CHANDIMC,MIANFEIYPBZ,YUANJIESSJ
        ,YUANTINGZR,YUANTINGZBQ,YUANTINGZSJ
        ,YIZHUJKID,SHENFANGZT,SHENFANGJG
        ,SHENFANGREN,SHENFANGSJ,KANGJUNYWSY10
        ,KANGJUNYWSY11,KANGJUNYWSY12,KANGJUNYWSY13
        ,KANGJUNYWSY14,KANGJUNYWSY15,KANGJUNYWSY16
        ,KANGJUNYWSY17,KANGJUNYWSY18,KANGJUNYWSY19
        ,KANGJUNYWSY20,ZHUZHIYS,TIAOPEIJL
        ,CAOYAOFCFY,JINGMAZD,YICHANGJGCLYJ
        ,YICHANGJGCLSJ,YICHANGJGCLSM,YICHANGJGCLR
        ,JINGSHIYS,BXSYZDBZ,YICHAKBG
        ,LINCHUANGLJID,TAOCANXSBRMXID,TAOCANXSBZ
        ,XIEDINGBZ,XIEDINGCFID,DUMACFQM
        ,DUMACFQMSJ,KESHIFFBZ,DISHUDW
        ,KANGJUNYW_LCZDDM,KANGJUNYW_LCZDMC,KANGJUNYW_SFHZ
        ,KANGJUNYW_WSWSJ,KANGJUNYW_JJSY,CHEXIAOHSID
        ,DAORUDGJLBBZ,PISHIPH,CHEXIAOSHBZ
        ,CHEXIAOSHR,CHEXIAOSHSJ,SHUANGQIANMBZ
        ,PPDBZ,PPDJG,PPDBWBZ
        ,HESUANKS,HESUANKSMC,YIZHUTJBZ
        ,YIZHUTJR,YIZHUTJSJ,CHEXIAOSQR
        ,CHEXIAOSQSJ,KUOZHANXX,BINGRENYLZ
        ,YIZHUSDBZ,YUANQIANSQDID,DAOGUANJSBZ
        ,DAOGUANJSR,DAOGUANJSSJ,YONGXUEBZ
        ,YUANQIANDRBZ,ZHUANKEHCBZ,GUANDAOBWID
        ,GUANDAOBWMC,GUANDAOFWID,GUANDAOFWMC
        ,JIAJIBZ,ZIBEIBZ,DUOCHONGNYBZ
        ,HUANCHUANGHAO,ZHUANRUKS,ZHUANRUKSMC
        ,GUANDAOLYID,GUANDAOLYMC,PISHIGCSJ
        ,PISHIKSSJ,SHOUSHUYZID,ZHILIAOFA
        ,ZHILIAOLY,XUETANGYZBZ,XUSHENPIBZ
        ,SHENPIBZ,SHENPIREN,SHENPISJ
        ,BEIXUESQDID,BEIXUEYZID,HUSHIBZ
        ,ZHUANRUBQ,ZHUANRUBQMC,GUANDAOID
        ,TESHUYZBZ,SHUNXUHAO,ZHIQINGTYSZT
        ,YAOPINPH,GCPBZ,WAIPEIBZ
        ,ZENGPINBZ,TPNYZBZ,PPDYIZHUID
        ,SHUQIANYZBZ,QIANMINGBZ,SHENFANGSQM
        ,SHENFANGSQMSJ,YONGYAOYY,SHUQIANYZSSMC
        ,KANGJUNYWSHBZ,MAZUIBZ,SHUQIANYZSSID
        ,YIZHULX,YAOPINJJBZ,KANGJUNYWQM
        ,KANGJUNYWQMSJ,GUANDAOMC,GUOMINYFL
        ,YUTINGSJ,CHUYUANZDID,CHUYUANZDMC
        ,ZHONGYIZD,ZHUANRUYLZ,ZHONGYIZHID
        ,FANGSHIZQ,SHIYONGFW,FUHESJYH
        ,FUHERENYH,BADIANJCNR,BADIANJCJG
        ,SHOUSHUT,KAPIANLX,DAGUIGCD
        ,DAGUIGCDMC,DAGUIGBZL,BAIZHUANGDW
        ,TIJIDW,TIJI,DAGUIGJGID
        ,DAGUIGID,YIZHUXMLX,JIEDUANID
        ,BINGRENLJID,QIANGJIUBZ,APIANLX
        ,SHENGFANGSQMYY,SHENGFANGSQMSJ,SHENGFANGSQMMC
        ,SHENGFANGSQM,FEIYONGKZBZ,WAIPEIBABH
        ,GAOFANGJX,GAOFANGFLXX,MEIRIJL
        ,JIANYAOFF,FUYAOCS,FUYAOFF
        ,TONGSHUOYZID,YIBAODJ,WAIPEISFR
        ,WAIPEISFSJ,SHENGLIZQ)
        values (#{YIZHUID,jdbcType=VARCHAR},#{YINGYONGID,jdbcType=VARCHAR},#{YUANQUID,jdbcType=VARCHAR}
        ,#{BINGRENZYID,jdbcType=VARCHAR},#{YINGERID,jdbcType=VARCHAR},#{BINGRENXM,jdbcType=VARCHAR}
        ,#{YIZHUXMID,jdbcType=VARCHAR},#{YIZHUMC,jdbcType=VARCHAR},#{KAIZHUSJ,jdbcType=TIMESTAMP}
        ,#{YIZHUMS,jdbcType=VARCHAR},#{SHURUSJ,jdbcType=TIMESTAMP},#{PINCI,jdbcType=VARCHAR}
        ,#{SHURUREN,jdbcType=VARCHAR},#{KAISHISJ,jdbcType=TIMESTAMP},#{JIESHUSJ,jdbcType=TIMESTAMP}
        ,#{YIZHUFL,jdbcType=VARCHAR},#{GEIYAOFS,jdbcType=VARCHAR},#{GEIYAOFSLX,jdbcType=DECIMAL}
        ,#{GEIYAOFSMC,jdbcType=VARCHAR},#{KAIZHUYS,jdbcType=VARCHAR},#{KAIZHUYSXM,jdbcType=VARCHAR}
        ,#{ZHIXINGSJ,jdbcType=TIMESTAMP},#{ZHIXINGREN,jdbcType=VARCHAR},#{TINGZHUSJ,jdbcType=TIMESTAMP}
        ,#{FUHESJ,jdbcType=TIMESTAMP},#{FUHEREN,jdbcType=VARCHAR},#{TINGZHUREN,jdbcType=VARCHAR}
        ,#{TINGZHUFHR,jdbcType=VARCHAR},#{TINGZHUFHSJ,jdbcType=TIMESTAMP},#{GUIGEID,jdbcType=VARCHAR}
        ,#{CHEXIAOREN,jdbcType=VARCHAR},#{YIZHUZT,jdbcType=VARCHAR},#{DANGQIANZT,jdbcType=VARCHAR}
        ,#{JILIANGDW,jdbcType=VARCHAR},#{BAOZHUANGDW,jdbcType=VARCHAR},#{ZUIXIAODW,jdbcType=VARCHAR}
        ,#{BAOZHUANGLIANG,jdbcType=DECIMAL},#{FUYIZMC,jdbcType=VARCHAR},#{YAOFANGYYID,jdbcType=VARCHAR}
        ,#{YAOPINMC,jdbcType=VARCHAR},#{JIAGEID,jdbcType=VARCHAR},#{YAOPINBMID,jdbcType=VARCHAR}
        ,#{YAOPINGG,jdbcType=VARCHAR},#{PAICHISJ,jdbcType=TIMESTAMP},#{PAICHIYZID,jdbcType=VARCHAR}
        ,#{JIXING,jdbcType=DECIMAL},#{PISHIJG,jdbcType=VARCHAR},#{LINGYAOFS,jdbcType=VARCHAR}
        ,#{DAYINBZ,jdbcType=DECIMAL},#{FUYIZID,jdbcType=VARCHAR},#{YISHENGZID,jdbcType=VARCHAR}
        ,#{DANJIA,jdbcType=DECIMAL},#{YISHENGZT,jdbcType=VARCHAR},#{ZHIXINGYL,jdbcType=VARCHAR}
        ,#{KESHIID,jdbcType=VARCHAR},#{ZUHAO,jdbcType=VARCHAR},#{PAICHILX,jdbcType=DECIMAL}
        ,#{PAIXUBH,jdbcType=VARCHAR},#{SHOURICS,jdbcType=DECIMAL},#{DANGRICS,jdbcType=DECIMAL}
        ,#{DAYINJL,jdbcType=VARCHAR},#{MORICS,jdbcType=DECIMAL},#{KAIZHUBQ,jdbcType=VARCHAR}
        ,#{KAIZHUKS,jdbcType=VARCHAR},#{CHEXIAOSJ,jdbcType=TIMESTAMP},#{ZHUANGTAISJ,jdbcType=TIMESTAMP}
        ,#{CHANDILB,jdbcType=VARCHAR},#{BINGQUID,jdbcType=VARCHAR},#{YICIJL,jdbcType=DECIMAL}
        ,#{YICIJLDW,jdbcType=VARCHAR},#{YICIYL,jdbcType=DECIMAL},#{JILIANG,jdbcType=DECIMAL}
        ,#{QIANGZHIBZ,jdbcType=DECIMAL},#{KAIZHUKSMC,jdbcType=VARCHAR},#{KAIZHUBQMC,jdbcType=VARCHAR}
        ,#{JIZHENBZ,jdbcType=DECIMAL},#{CHULIYJ,jdbcType=VARCHAR},#{BINGRENID,jdbcType=VARCHAR}
        ,#{ZUIXIAODWZXYL,jdbcType=VARCHAR},#{ZUIXIAODWYL,jdbcType=DECIMAL},#{PISHIBZ,jdbcType=DECIMAL}
        ,#{FUYAOSX,jdbcType=VARCHAR},#{TAKEBZ,jdbcType=DECIMAL},#{YEJIANBZ,jdbcType=DECIMAL}
        ,#{PISHILX,jdbcType=VARCHAR},#{PISHIFHR1,jdbcType=VARCHAR},#{PISHIFHSJ1,jdbcType=TIMESTAMP}
        ,#{PISHIFHR2,jdbcType=VARCHAR},#{PISHIFHSJ2,jdbcType=TIMESTAMP},#{JIFEIJG,jdbcType=DECIMAL}
        ,#{JIFEIFS,jdbcType=VARCHAR},#{ZHIXINGSJLX,jdbcType=VARCHAR},#{FEIYONGXZZHBZ,jdbcType=DECIMAL}
        ,#{JINGMAIPBZ,jdbcType=DECIMAL},#{FAYAOZT,jdbcType=VARCHAR},#{YUANYAOFYYID,jdbcType=VARCHAR}
        ,#{ZIFUBL,jdbcType=VARCHAR},#{CAOYAOBZ,jdbcType=DECIMAL},#{DAIJIANTS,jdbcType=DECIMAL}
        ,#{TINGZHUBQ,jdbcType=VARCHAR},#{PAIXUZH,jdbcType=VARCHAR},#{QIANFEISHBZ,jdbcType=DECIMAL}
        ,#{YUYUERQ,jdbcType=TIMESTAMP},#{ZHIXINGKSJFBZ,jdbcType=DECIMAL},#{ZHIXINGKS,jdbcType=VARCHAR}
        ,#{FENQURQ,jdbcType=TIMESTAMP},#{PINCIZXSJ,jdbcType=VARCHAR},#{YIYUANSPBZ,jdbcType=DECIMAL}
        ,#{YIYUANSPLX,jdbcType=DECIMAL},#{YIYUANSPSM,jdbcType=VARCHAR},#{YIYUANSPBL,jdbcType=VARCHAR}
        ,#{JIAOBANHDBZ,jdbcType=VARCHAR},#{ZONGHEDXH,jdbcType=VARCHAR},#{JIAOBANHDSJ,jdbcType=TIMESTAMP}
        ,#{JIAOBANHDR,jdbcType=VARCHAR},#{FUJIAFYJFFS,jdbcType=VARCHAR},#{YUANDANGQZT,jdbcType=VARCHAR}
        ,#{YUANYIZZT,jdbcType=VARCHAR},#{DONGJIEBZ,jdbcType=DECIMAL},#{JIEDONGRQ,jdbcType=TIMESTAMP}
        ,#{TUIYAOSPBZ,jdbcType=DECIMAL},#{YAOPINJX,jdbcType=VARCHAR},#{ZHIXINGRQ,jdbcType=VARCHAR}
        ,#{ZHIXINGZT,jdbcType=DECIMAL},#{KAPIANDYBZ,jdbcType=VARCHAR},#{QUZHENGLX,jdbcType=VARCHAR}
        ,#{DONGJIERQ,jdbcType=TIMESTAMP},#{QINGJIABZ,jdbcType=DECIMAL},#{YIYUANSPRQ,jdbcType=TIMESTAMP}
        ,#{BIANGENGDDYBZ,jdbcType=VARCHAR},#{CHEXIAOLX,jdbcType=VARCHAR},#{CHEXIAOYY,jdbcType=VARCHAR}
        ,#{YUANZHIXSJ,jdbcType=TIMESTAMP},#{CHUYUANDYBZ,jdbcType=DECIMAL},#{CHANGQILSBZ,jdbcType=DECIMAL}
        ,#{ZHIXINGBZ,jdbcType=DECIMAL},#{JIAOBANHDR2,jdbcType=VARCHAR},#{GONGZUOZID,jdbcType=VARCHAR}
        ,#{CAOYAOTSYF,jdbcType=VARCHAR},#{KANGSHENGSSYSM,jdbcType=VARCHAR},#{SHENPIYS,jdbcType=VARCHAR}
        ,#{SHENPINR,jdbcType=VARCHAR},#{DISHU,jdbcType=VARCHAR},#{BIGUANGBZ,jdbcType=DECIMAL}
        ,#{ZHONGYAOPFKLBZ,jdbcType=DECIMAL},#{KUCUNBZBZ,jdbcType=DECIMAL},#{BIANGENGHZDYBZ,jdbcType=VARCHAR}
        ,#{CHENGZUYZID,jdbcType=VARCHAR},#{YAOPINLDID,jdbcType=VARCHAR},#{ZHONGYAOKLZHSL,jdbcType=DECIMAL}
        ,#{KANGJUNYWSY1,jdbcType=VARCHAR},#{KANGJUNYWSY2,jdbcType=VARCHAR},#{KANGJUNYWSY3,jdbcType=VARCHAR}
        ,#{KANGJUNYWSY4,jdbcType=VARCHAR},#{KANGJUNYWSY5,jdbcType=VARCHAR},#{KANGJUNYWSY6,jdbcType=VARCHAR}
        ,#{KANGJUNYWSY7,jdbcType=VARCHAR},#{KANGJUNYWSY8,jdbcType=VARCHAR},#{KANGJUNYWSY9,jdbcType=VARCHAR}
        ,#{DULIFL,jdbcType=VARCHAR},#{QITASX,jdbcType=VARCHAR},#{HUIZHENYZSPBZ,jdbcType=DECIMAL}
        ,#{HUIZHENYZSPYS,jdbcType=VARCHAR},#{NONGJIANJBZ,jdbcType=DECIMAL},#{LINCHUANGLJDRBZ,jdbcType=DECIMAL}
        ,#{YAOWUSX,jdbcType=DECIMAL},#{GAOFANGBZ,jdbcType=DECIMAL},#{LINGYAORXM,jdbcType=VARCHAR}
        ,#{LINGYAORSFZH,jdbcType=VARCHAR},#{DUMAKH,jdbcType=VARCHAR},#{CHANDI,jdbcType=VARCHAR}
        ,#{CHANDIMC,jdbcType=VARCHAR},#{MIANFEIYPBZ,jdbcType=DECIMAL},#{YUANJIESSJ,jdbcType=TIMESTAMP}
        ,#{YUANTINGZR,jdbcType=VARCHAR},#{YUANTINGZBQ,jdbcType=VARCHAR},#{YUANTINGZSJ,jdbcType=TIMESTAMP}
        ,#{YIZHUJKID,jdbcType=VARCHAR},#{SHENFANGZT,jdbcType=VARCHAR},#{SHENFANGJG,jdbcType=VARCHAR}
        ,#{SHENFANGREN,jdbcType=VARCHAR},#{SHENFANGSJ,jdbcType=TIMESTAMP},#{KANGJUNYWSY10,jdbcType=VARCHAR}
        ,#{KANGJUNYWSY11,jdbcType=VARCHAR},#{KANGJUNYWSY12,jdbcType=VARCHAR},#{KANGJUNYWSY13,jdbcType=VARCHAR}
        ,#{KANGJUNYWSY14,jdbcType=VARCHAR},#{KANGJUNYWSY15,jdbcType=VARCHAR},#{KANGJUNYWSY16,jdbcType=VARCHAR}
        ,#{KANGJUNYWSY17,jdbcType=VARCHAR},#{KANGJUNYWSY18,jdbcType=VARCHAR},#{KANGJUNYWSY19,jdbcType=VARCHAR}
        ,#{KANGJUNYWSY20,jdbcType=VARCHAR},#{ZHUZHIYS,jdbcType=VARCHAR},#{TIAOPEIJL,jdbcType=VARCHAR}
        ,#{CAOYAOFCFY,jdbcType=VARCHAR},#{JINGMAZD,jdbcType=VARCHAR},#{YICHANGJGCLYJ,jdbcType=DECIMAL}
        ,#{YICHANGJGCLSJ,jdbcType=TIMESTAMP},#{YICHANGJGCLSM,jdbcType=VARCHAR},#{YICHANGJGCLR,jdbcType=VARCHAR}
        ,#{JINGSHIYS,jdbcType=DECIMAL},#{BXSYZDBZ,jdbcType=DECIMAL},#{YICHAKBG,jdbcType=VARCHAR}
        ,#{LINCHUANGLJID,jdbcType=VARCHAR},#{TAOCANXSBRMXID,jdbcType=VARCHAR},#{TAOCANXSBZ,jdbcType=DECIMAL}
        ,#{XIEDINGBZ,jdbcType=DECIMAL},#{XIEDINGCFID,jdbcType=VARCHAR},#{DUMACFQM,jdbcType=VARCHAR}
        ,#{DUMACFQMSJ,jdbcType=TIMESTAMP},#{KESHIFFBZ,jdbcType=DECIMAL},#{DISHUDW,jdbcType=VARCHAR}
        ,#{KANGJUNYW_LCZDDM,jdbcType=VARCHAR},#{KANGJUNYW_LCZDMC,jdbcType=VARCHAR},#{KANGJUNYW_SFHZ,jdbcType=DECIMAL}
        ,#{KANGJUNYW_WSWSJ,jdbcType=DECIMAL},#{KANGJUNYW_JJSY,jdbcType=DECIMAL},#{CHEXIAOHSID,jdbcType=VARCHAR}
        ,#{DAORUDGJLBBZ,jdbcType=DECIMAL},#{PISHIPH,jdbcType=VARCHAR},#{CHEXIAOSHBZ,jdbcType=DECIMAL}
        ,#{CHEXIAOSHR,jdbcType=VARCHAR},#{CHEXIAOSHSJ,jdbcType=TIMESTAMP},#{SHUANGQIANMBZ,jdbcType=DECIMAL}
        ,#{PPDBZ,jdbcType=DECIMAL},#{PPDJG,jdbcType=VARCHAR},#{PPDBWBZ,jdbcType=VARCHAR}
        ,#{HESUANKS,jdbcType=VARCHAR},#{HESUANKSMC,jdbcType=VARCHAR},#{YIZHUTJBZ,jdbcType=DECIMAL}
        ,#{YIZHUTJR,jdbcType=VARCHAR},#{YIZHUTJSJ,jdbcType=TIMESTAMP},#{CHEXIAOSQR,jdbcType=VARCHAR}
        ,#{CHEXIAOSQSJ,jdbcType=TIMESTAMP},#{KUOZHANXX,jdbcType=VARCHAR},#{BINGRENYLZ,jdbcType=VARCHAR}
        ,#{YIZHUSDBZ,jdbcType=DECIMAL},#{YUANQIANSQDID,jdbcType=VARCHAR},#{DAOGUANJSBZ,jdbcType=DECIMAL}
        ,#{DAOGUANJSR,jdbcType=VARCHAR},#{DAOGUANJSSJ,jdbcType=TIMESTAMP},#{YONGXUEBZ,jdbcType=VARCHAR}
        ,#{YUANQIANDRBZ,jdbcType=DECIMAL},#{ZHUANKEHCBZ,jdbcType=DECIMAL},#{GUANDAOBWID,jdbcType=VARCHAR}
        ,#{GUANDAOBWMC,jdbcType=VARCHAR},#{GUANDAOFWID,jdbcType=VARCHAR},#{GUANDAOFWMC,jdbcType=VARCHAR}
        ,#{JIAJIBZ,jdbcType=DECIMAL},#{ZIBEIBZ,jdbcType=DECIMAL},#{DUOCHONGNYBZ,jdbcType=DECIMAL}
        ,#{HUANCHUANGHAO,jdbcType=VARCHAR},#{ZHUANRUKS,jdbcType=VARCHAR},#{ZHUANRUKSMC,jdbcType=VARCHAR}
        ,#{GUANDAOLYID,jdbcType=VARCHAR},#{GUANDAOLYMC,jdbcType=VARCHAR},#{PISHIGCSJ,jdbcType=DECIMAL}
        ,#{PISHIKSSJ,jdbcType=TIMESTAMP},#{SHOUSHUYZID,jdbcType=VARCHAR},#{ZHILIAOFA,jdbcType=VARCHAR}
        ,#{ZHILIAOLY,jdbcType=VARCHAR},#{XUETANGYZBZ,jdbcType=DECIMAL},#{XUSHENPIBZ,jdbcType=DECIMAL}
        ,#{SHENPIBZ,jdbcType=DECIMAL},#{SHENPIREN,jdbcType=VARCHAR},#{SHENPISJ,jdbcType=TIMESTAMP}
        ,#{BEIXUESQDID,jdbcType=VARCHAR},#{BEIXUEYZID,jdbcType=VARCHAR},#{HUSHIBZ,jdbcType=VARCHAR}
        ,#{ZHUANRUBQ,jdbcType=VARCHAR},#{ZHUANRUBQMC,jdbcType=VARCHAR},#{GUANDAOID,jdbcType=VARCHAR}
        ,#{TESHUYZBZ,jdbcType=DECIMAL},#{SHUNXUHAO,jdbcType=DECIMAL},#{ZHIQINGTYSZT,jdbcType=DECIMAL}
        ,#{YAOPINPH,jdbcType=VARCHAR},#{GCPBZ,jdbcType=DECIMAL},#{WAIPEIBZ,jdbcType=DECIMAL}
        ,#{ZENGPINBZ,jdbcType=DECIMAL},#{TPNYZBZ,jdbcType=DECIMAL},#{PPDYIZHUID,jdbcType=VARCHAR}
        ,#{SHUQIANYZBZ,jdbcType=DECIMAL},#{QIANMINGBZ,jdbcType=DECIMAL},#{SHENFANGSQM,jdbcType=VARCHAR}
        ,#{SHENFANGSQMSJ,jdbcType=TIMESTAMP},#{YONGYAOYY,jdbcType=VARCHAR},#{SHUQIANYZSSMC,jdbcType=VARCHAR}
        ,#{KANGJUNYWSHBZ,jdbcType=VARCHAR},#{MAZUIBZ,jdbcType=DECIMAL},#{SHUQIANYZSSID,jdbcType=VARCHAR}
        ,#{YIZHULX,jdbcType=DECIMAL},#{YAOPINJJBZ,jdbcType=DECIMAL},#{KANGJUNYWQM,jdbcType=VARCHAR}
        ,#{KANGJUNYWQMSJ,jdbcType=TIMESTAMP},#{GUANDAOMC,jdbcType=VARCHAR},#{GUOMINYFL,jdbcType=VARCHAR}
        ,#{YUTINGSJ,jdbcType=TIMESTAMP},#{CHUYUANZDID,jdbcType=VARCHAR},#{CHUYUANZDMC,jdbcType=VARCHAR}
        ,#{ZHONGYIZD,jdbcType=VARCHAR},#{ZHUANRUYLZ,jdbcType=VARCHAR},#{ZHONGYIZHID,jdbcType=VARCHAR}
        ,#{FANGSHIZQ,jdbcType=VARCHAR},#{SHIYONGFW,jdbcType=VARCHAR},#{FUHESJYH,jdbcType=VARCHAR}
        ,#{FUHERENYH,jdbcType=TIMESTAMP},#{BADIANJCNR,jdbcType=VARCHAR},#{BADIANJCJG,jdbcType=VARCHAR}
        ,#{SHOUSHUT,jdbcType=VARCHAR},#{KAPIANLX,jdbcType=VARCHAR},#{DAGUIGCD,jdbcType=VARCHAR}
        ,#{DAGUIGCDMC,jdbcType=VARCHAR},#{DAGUIGBZL,jdbcType=DECIMAL},#{BAIZHUANGDW,jdbcType=VARCHAR}
        ,#{TIJIDW,jdbcType=VARCHAR},#{TIJI,jdbcType=VARCHAR},#{DAGUIGJGID,jdbcType=VARCHAR}
        ,#{DAGUIGID,jdbcType=VARCHAR},#{YIZHUXMLX,jdbcType=VARCHAR},#{JIEDUANID,jdbcType=VARCHAR}
        ,#{BINGRENLJID,jdbcType=VARCHAR},#{QIANGJIUBZ,jdbcType=DECIMAL},#{APIANLX,jdbcType=VARCHAR}
        ,#{SHENGFANGSQMYY,jdbcType=VARCHAR},#{SHENGFANGSQMSJ,jdbcType=VARCHAR},#{SHENGFANGSQMMC,jdbcType=VARCHAR}
        ,#{SHENGFANGSQM,jdbcType=VARCHAR},#{FEIYONGKZBZ,jdbcType=DECIMAL},#{WAIPEIBABH,jdbcType=VARCHAR}
        ,#{GAOFANGJX,jdbcType=VARCHAR},#{GAOFANGFLXX,jdbcType=VARCHAR},#{MEIRIJL,jdbcType=VARCHAR}
        ,#{JIANYAOFF,jdbcType=VARCHAR},#{FUYAOCS,jdbcType=VARCHAR},#{FUYAOFF,jdbcType=VARCHAR}
        ,#{TONGSHUOYZID,jdbcType=VARCHAR},#{YIBAODJ,jdbcType=VARCHAR},#{WAIPEISFR,jdbcType=VARCHAR}
        ,#{WAIPEISFSJ,jdbcType=TIMESTAMP},#{SHENGLIZQ,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="YIZHUID" keyProperty="YIZHUID" parameterType="com.javazx.batch.po.YzBingrenyz" useGeneratedKeys="true">
        insert into YZ_BINGRENYZ
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="YIZHUID != null">YIZHUID,</if>
                <if test="YINGYONGID != null">YINGYONGID,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="YINGERID != null">YINGERID,</if>
                <if test="BINGRENXM != null">BINGRENXM,</if>
                <if test="YIZHUXMID != null">YIZHUXMID,</if>
                <if test="YIZHUMC != null">YIZHUMC,</if>
                <if test="KAIZHUSJ != null">KAIZHUSJ,</if>
                <if test="YIZHUMS != null">YIZHUMS,</if>
                <if test="SHURUSJ != null">SHURUSJ,</if>
                <if test="PINCI != null">PINCI,</if>
                <if test="SHURUREN != null">SHURUREN,</if>
                <if test="KAISHISJ != null">KAISHISJ,</if>
                <if test="JIESHUSJ != null">JIESHUSJ,</if>
                <if test="YIZHUFL != null">YIZHUFL,</if>
                <if test="GEIYAOFS != null">GEIYAOFS,</if>
                <if test="GEIYAOFSLX != null">GEIYAOFSLX,</if>
                <if test="GEIYAOFSMC != null">GEIYAOFSMC,</if>
                <if test="KAIZHUYS != null">KAIZHUYS,</if>
                <if test="KAIZHUYSXM != null">KAIZHUYSXM,</if>
                <if test="ZHIXINGSJ != null">ZHIXINGSJ,</if>
                <if test="ZHIXINGREN != null">ZHIXINGREN,</if>
                <if test="TINGZHUSJ != null">TINGZHUSJ,</if>
                <if test="FUHESJ != null">FUHESJ,</if>
                <if test="FUHEREN != null">FUHEREN,</if>
                <if test="TINGZHUREN != null">TINGZHUREN,</if>
                <if test="TINGZHUFHR != null">TINGZHUFHR,</if>
                <if test="TINGZHUFHSJ != null">TINGZHUFHSJ,</if>
                <if test="GUIGEID != null">GUIGEID,</if>
                <if test="CHEXIAOREN != null">CHEXIAOREN,</if>
                <if test="YIZHUZT != null">YIZHUZT,</if>
                <if test="DANGQIANZT != null">DANGQIANZT,</if>
                <if test="JILIANGDW != null">JILIANGDW,</if>
                <if test="BAOZHUANGDW != null">BAOZHUANGDW,</if>
                <if test="ZUIXIAODW != null">ZUIXIAODW,</if>
                <if test="BAOZHUANGLIANG != null">BAOZHUANGLIANG,</if>
                <if test="FUYIZMC != null">FUYIZMC,</if>
                <if test="YAOFANGYYID != null">YAOFANGYYID,</if>
                <if test="YAOPINMC != null">YAOPINMC,</if>
                <if test="JIAGEID != null">JIAGEID,</if>
                <if test="YAOPINBMID != null">YAOPINBMID,</if>
                <if test="YAOPINGG != null">YAOPINGG,</if>
                <if test="PAICHISJ != null">PAICHISJ,</if>
                <if test="PAICHIYZID != null">PAICHIYZID,</if>
                <if test="JIXING != null">JIXING,</if>
                <if test="PISHIJG != null">PISHIJG,</if>
                <if test="LINGYAOFS != null">LINGYAOFS,</if>
                <if test="DAYINBZ != null">DAYINBZ,</if>
                <if test="FUYIZID != null">FUYIZID,</if>
                <if test="YISHENGZID != null">YISHENGZID,</if>
                <if test="DANJIA != null">DANJIA,</if>
                <if test="YISHENGZT != null">YISHENGZT,</if>
                <if test="ZHIXINGYL != null">ZHIXINGYL,</if>
                <if test="KESHIID != null">KESHIID,</if>
                <if test="ZUHAO != null">ZUHAO,</if>
                <if test="PAICHILX != null">PAICHILX,</if>
                <if test="PAIXUBH != null">PAIXUBH,</if>
                <if test="SHOURICS != null">SHOURICS,</if>
                <if test="DANGRICS != null">DANGRICS,</if>
                <if test="DAYINJL != null">DAYINJL,</if>
                <if test="MORICS != null">MORICS,</if>
                <if test="KAIZHUBQ != null">KAIZHUBQ,</if>
                <if test="KAIZHUKS != null">KAIZHUKS,</if>
                <if test="CHEXIAOSJ != null">CHEXIAOSJ,</if>
                <if test="ZHUANGTAISJ != null">ZHUANGTAISJ,</if>
                <if test="CHANDILB != null">CHANDILB,</if>
                <if test="BINGQUID != null">BINGQUID,</if>
                <if test="YICIJL != null">YICIJL,</if>
                <if test="YICIJLDW != null">YICIJLDW,</if>
                <if test="YICIYL != null">YICIYL,</if>
                <if test="JILIANG != null">JILIANG,</if>
                <if test="QIANGZHIBZ != null">QIANGZHIBZ,</if>
                <if test="KAIZHUKSMC != null">KAIZHUKSMC,</if>
                <if test="KAIZHUBQMC != null">KAIZHUBQMC,</if>
                <if test="JIZHENBZ != null">JIZHENBZ,</if>
                <if test="CHULIYJ != null">CHULIYJ,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="ZUIXIAODWZXYL != null">ZUIXIAODWZXYL,</if>
                <if test="ZUIXIAODWYL != null">ZUIXIAODWYL,</if>
                <if test="PISHIBZ != null">PISHIBZ,</if>
                <if test="FUYAOSX != null">FUYAOSX,</if>
                <if test="TAKEBZ != null">TAKEBZ,</if>
                <if test="YEJIANBZ != null">YEJIANBZ,</if>
                <if test="PISHILX != null">PISHILX,</if>
                <if test="PISHIFHR1 != null">PISHIFHR1,</if>
                <if test="PISHIFHSJ1 != null">PISHIFHSJ1,</if>
                <if test="PISHIFHR2 != null">PISHIFHR2,</if>
                <if test="PISHIFHSJ2 != null">PISHIFHSJ2,</if>
                <if test="JIFEIJG != null">JIFEIJG,</if>
                <if test="JIFEIFS != null">JIFEIFS,</if>
                <if test="ZHIXINGSJLX != null">ZHIXINGSJLX,</if>
                <if test="FEIYONGXZZHBZ != null">FEIYONGXZZHBZ,</if>
                <if test="JINGMAIPBZ != null">JINGMAIPBZ,</if>
                <if test="FAYAOZT != null">FAYAOZT,</if>
                <if test="YUANYAOFYYID != null">YUANYAOFYYID,</if>
                <if test="ZIFUBL != null">ZIFUBL,</if>
                <if test="CAOYAOBZ != null">CAOYAOBZ,</if>
                <if test="DAIJIANTS != null">DAIJIANTS,</if>
                <if test="TINGZHUBQ != null">TINGZHUBQ,</if>
                <if test="PAIXUZH != null">PAIXUZH,</if>
                <if test="QIANFEISHBZ != null">QIANFEISHBZ,</if>
                <if test="YUYUERQ != null">YUYUERQ,</if>
                <if test="ZHIXINGKSJFBZ != null">ZHIXINGKSJFBZ,</if>
                <if test="ZHIXINGKS != null">ZHIXINGKS,</if>
                <if test="FENQURQ != null">FENQURQ,</if>
                <if test="PINCIZXSJ != null">PINCIZXSJ,</if>
                <if test="YIYUANSPBZ != null">YIYUANSPBZ,</if>
                <if test="YIYUANSPLX != null">YIYUANSPLX,</if>
                <if test="YIYUANSPSM != null">YIYUANSPSM,</if>
                <if test="YIYUANSPBL != null">YIYUANSPBL,</if>
                <if test="JIAOBANHDBZ != null">JIAOBANHDBZ,</if>
                <if test="ZONGHEDXH != null">ZONGHEDXH,</if>
                <if test="JIAOBANHDSJ != null">JIAOBANHDSJ,</if>
                <if test="JIAOBANHDR != null">JIAOBANHDR,</if>
                <if test="FUJIAFYJFFS != null">FUJIAFYJFFS,</if>
                <if test="YUANDANGQZT != null">YUANDANGQZT,</if>
                <if test="YUANYIZZT != null">YUANYIZZT,</if>
                <if test="DONGJIEBZ != null">DONGJIEBZ,</if>
                <if test="JIEDONGRQ != null">JIEDONGRQ,</if>
                <if test="TUIYAOSPBZ != null">TUIYAOSPBZ,</if>
                <if test="YAOPINJX != null">YAOPINJX,</if>
                <if test="ZHIXINGRQ != null">ZHIXINGRQ,</if>
                <if test="ZHIXINGZT != null">ZHIXINGZT,</if>
                <if test="KAPIANDYBZ != null">KAPIANDYBZ,</if>
                <if test="QUZHENGLX != null">QUZHENGLX,</if>
                <if test="DONGJIERQ != null">DONGJIERQ,</if>
                <if test="QINGJIABZ != null">QINGJIABZ,</if>
                <if test="YIYUANSPRQ != null">YIYUANSPRQ,</if>
                <if test="BIANGENGDDYBZ != null">BIANGENGDDYBZ,</if>
                <if test="CHEXIAOLX != null">CHEXIAOLX,</if>
                <if test="CHEXIAOYY != null">CHEXIAOYY,</if>
                <if test="YUANZHIXSJ != null">YUANZHIXSJ,</if>
                <if test="CHUYUANDYBZ != null">CHUYUANDYBZ,</if>
                <if test="CHANGQILSBZ != null">CHANGQILSBZ,</if>
                <if test="ZHIXINGBZ != null">ZHIXINGBZ,</if>
                <if test="JIAOBANHDR2 != null">JIAOBANHDR2,</if>
                <if test="GONGZUOZID != null">GONGZUOZID,</if>
                <if test="CAOYAOTSYF != null">CAOYAOTSYF,</if>
                <if test="KANGSHENGSSYSM != null">KANGSHENGSSYSM,</if>
                <if test="SHENPIYS != null">SHENPIYS,</if>
                <if test="SHENPINR != null">SHENPINR,</if>
                <if test="DISHU != null">DISHU,</if>
                <if test="BIGUANGBZ != null">BIGUANGBZ,</if>
                <if test="ZHONGYAOPFKLBZ != null">ZHONGYAOPFKLBZ,</if>
                <if test="KUCUNBZBZ != null">KUCUNBZBZ,</if>
                <if test="BIANGENGHZDYBZ != null">BIANGENGHZDYBZ,</if>
                <if test="CHENGZUYZID != null">CHENGZUYZID,</if>
                <if test="YAOPINLDID != null">YAOPINLDID,</if>
                <if test="ZHONGYAOKLZHSL != null">ZHONGYAOKLZHSL,</if>
                <if test="KANGJUNYWSY1 != null">KANGJUNYWSY1,</if>
                <if test="KANGJUNYWSY2 != null">KANGJUNYWSY2,</if>
                <if test="KANGJUNYWSY3 != null">KANGJUNYWSY3,</if>
                <if test="KANGJUNYWSY4 != null">KANGJUNYWSY4,</if>
                <if test="KANGJUNYWSY5 != null">KANGJUNYWSY5,</if>
                <if test="KANGJUNYWSY6 != null">KANGJUNYWSY6,</if>
                <if test="KANGJUNYWSY7 != null">KANGJUNYWSY7,</if>
                <if test="KANGJUNYWSY8 != null">KANGJUNYWSY8,</if>
                <if test="KANGJUNYWSY9 != null">KANGJUNYWSY9,</if>
                <if test="DULIFL != null">DULIFL,</if>
                <if test="QITASX != null">QITASX,</if>
                <if test="HUIZHENYZSPBZ != null">HUIZHENYZSPBZ,</if>
                <if test="HUIZHENYZSPYS != null">HUIZHENYZSPYS,</if>
                <if test="NONGJIANJBZ != null">NONGJIANJBZ,</if>
                <if test="LINCHUANGLJDRBZ != null">LINCHUANGLJDRBZ,</if>
                <if test="YAOWUSX != null">YAOWUSX,</if>
                <if test="GAOFANGBZ != null">GAOFANGBZ,</if>
                <if test="LINGYAORXM != null">LINGYAORXM,</if>
                <if test="LINGYAORSFZH != null">LINGYAORSFZH,</if>
                <if test="DUMAKH != null">DUMAKH,</if>
                <if test="CHANDI != null">CHANDI,</if>
                <if test="CHANDIMC != null">CHANDIMC,</if>
                <if test="MIANFEIYPBZ != null">MIANFEIYPBZ,</if>
                <if test="YUANJIESSJ != null">YUANJIESSJ,</if>
                <if test="YUANTINGZR != null">YUANTINGZR,</if>
                <if test="YUANTINGZBQ != null">YUANTINGZBQ,</if>
                <if test="YUANTINGZSJ != null">YUANTINGZSJ,</if>
                <if test="YIZHUJKID != null">YIZHUJKID,</if>
                <if test="SHENFANGZT != null">SHENFANGZT,</if>
                <if test="SHENFANGJG != null">SHENFANGJG,</if>
                <if test="SHENFANGREN != null">SHENFANGREN,</if>
                <if test="SHENFANGSJ != null">SHENFANGSJ,</if>
                <if test="KANGJUNYWSY10 != null">KANGJUNYWSY10,</if>
                <if test="KANGJUNYWSY11 != null">KANGJUNYWSY11,</if>
                <if test="KANGJUNYWSY12 != null">KANGJUNYWSY12,</if>
                <if test="KANGJUNYWSY13 != null">KANGJUNYWSY13,</if>
                <if test="KANGJUNYWSY14 != null">KANGJUNYWSY14,</if>
                <if test="KANGJUNYWSY15 != null">KANGJUNYWSY15,</if>
                <if test="KANGJUNYWSY16 != null">KANGJUNYWSY16,</if>
                <if test="KANGJUNYWSY17 != null">KANGJUNYWSY17,</if>
                <if test="KANGJUNYWSY18 != null">KANGJUNYWSY18,</if>
                <if test="KANGJUNYWSY19 != null">KANGJUNYWSY19,</if>
                <if test="KANGJUNYWSY20 != null">KANGJUNYWSY20,</if>
                <if test="ZHUZHIYS != null">ZHUZHIYS,</if>
                <if test="TIAOPEIJL != null">TIAOPEIJL,</if>
                <if test="CAOYAOFCFY != null">CAOYAOFCFY,</if>
                <if test="JINGMAZD != null">JINGMAZD,</if>
                <if test="YICHANGJGCLYJ != null">YICHANGJGCLYJ,</if>
                <if test="YICHANGJGCLSJ != null">YICHANGJGCLSJ,</if>
                <if test="YICHANGJGCLSM != null">YICHANGJGCLSM,</if>
                <if test="YICHANGJGCLR != null">YICHANGJGCLR,</if>
                <if test="JINGSHIYS != null">JINGSHIYS,</if>
                <if test="BXSYZDBZ != null">BXSYZDBZ,</if>
                <if test="YICHAKBG != null">YICHAKBG,</if>
                <if test="LINCHUANGLJID != null">LINCHUANGLJID,</if>
                <if test="TAOCANXSBRMXID != null">TAOCANXSBRMXID,</if>
                <if test="TAOCANXSBZ != null">TAOCANXSBZ,</if>
                <if test="XIEDINGBZ != null">XIEDINGBZ,</if>
                <if test="XIEDINGCFID != null">XIEDINGCFID,</if>
                <if test="DUMACFQM != null">DUMACFQM,</if>
                <if test="DUMACFQMSJ != null">DUMACFQMSJ,</if>
                <if test="KESHIFFBZ != null">KESHIFFBZ,</if>
                <if test="DISHUDW != null">DISHUDW,</if>
                <if test="KANGJUNYW_LCZDDM != null">KANGJUNYW_LCZDDM,</if>
                <if test="KANGJUNYW_LCZDMC != null">KANGJUNYW_LCZDMC,</if>
                <if test="KANGJUNYW_SFHZ != null">KANGJUNYW_SFHZ,</if>
                <if test="KANGJUNYW_WSWSJ != null">KANGJUNYW_WSWSJ,</if>
                <if test="KANGJUNYW_JJSY != null">KANGJUNYW_JJSY,</if>
                <if test="CHEXIAOHSID != null">CHEXIAOHSID,</if>
                <if test="DAORUDGJLBBZ != null">DAORUDGJLBBZ,</if>
                <if test="PISHIPH != null">PISHIPH,</if>
                <if test="CHEXIAOSHBZ != null">CHEXIAOSHBZ,</if>
                <if test="CHEXIAOSHR != null">CHEXIAOSHR,</if>
                <if test="CHEXIAOSHSJ != null">CHEXIAOSHSJ,</if>
                <if test="SHUANGQIANMBZ != null">SHUANGQIANMBZ,</if>
                <if test="PPDBZ != null">PPDBZ,</if>
                <if test="PPDJG != null">PPDJG,</if>
                <if test="PPDBWBZ != null">PPDBWBZ,</if>
                <if test="HESUANKS != null">HESUANKS,</if>
                <if test="HESUANKSMC != null">HESUANKSMC,</if>
                <if test="YIZHUTJBZ != null">YIZHUTJBZ,</if>
                <if test="YIZHUTJR != null">YIZHUTJR,</if>
                <if test="YIZHUTJSJ != null">YIZHUTJSJ,</if>
                <if test="CHEXIAOSQR != null">CHEXIAOSQR,</if>
                <if test="CHEXIAOSQSJ != null">CHEXIAOSQSJ,</if>
                <if test="KUOZHANXX != null">KUOZHANXX,</if>
                <if test="BINGRENYLZ != null">BINGRENYLZ,</if>
                <if test="YIZHUSDBZ != null">YIZHUSDBZ,</if>
                <if test="YUANQIANSQDID != null">YUANQIANSQDID,</if>
                <if test="DAOGUANJSBZ != null">DAOGUANJSBZ,</if>
                <if test="DAOGUANJSR != null">DAOGUANJSR,</if>
                <if test="DAOGUANJSSJ != null">DAOGUANJSSJ,</if>
                <if test="YONGXUEBZ != null">YONGXUEBZ,</if>
                <if test="YUANQIANDRBZ != null">YUANQIANDRBZ,</if>
                <if test="ZHUANKEHCBZ != null">ZHUANKEHCBZ,</if>
                <if test="GUANDAOBWID != null">GUANDAOBWID,</if>
                <if test="GUANDAOBWMC != null">GUANDAOBWMC,</if>
                <if test="GUANDAOFWID != null">GUANDAOFWID,</if>
                <if test="GUANDAOFWMC != null">GUANDAOFWMC,</if>
                <if test="JIAJIBZ != null">JIAJIBZ,</if>
                <if test="ZIBEIBZ != null">ZIBEIBZ,</if>
                <if test="DUOCHONGNYBZ != null">DUOCHONGNYBZ,</if>
                <if test="HUANCHUANGHAO != null">HUANCHUANGHAO,</if>
                <if test="ZHUANRUKS != null">ZHUANRUKS,</if>
                <if test="ZHUANRUKSMC != null">ZHUANRUKSMC,</if>
                <if test="GUANDAOLYID != null">GUANDAOLYID,</if>
                <if test="GUANDAOLYMC != null">GUANDAOLYMC,</if>
                <if test="PISHIGCSJ != null">PISHIGCSJ,</if>
                <if test="PISHIKSSJ != null">PISHIKSSJ,</if>
                <if test="SHOUSHUYZID != null">SHOUSHUYZID,</if>
                <if test="ZHILIAOFA != null">ZHILIAOFA,</if>
                <if test="ZHILIAOLY != null">ZHILIAOLY,</if>
                <if test="XUETANGYZBZ != null">XUETANGYZBZ,</if>
                <if test="XUSHENPIBZ != null">XUSHENPIBZ,</if>
                <if test="SHENPIBZ != null">SHENPIBZ,</if>
                <if test="SHENPIREN != null">SHENPIREN,</if>
                <if test="SHENPISJ != null">SHENPISJ,</if>
                <if test="BEIXUESQDID != null">BEIXUESQDID,</if>
                <if test="BEIXUEYZID != null">BEIXUEYZID,</if>
                <if test="HUSHIBZ != null">HUSHIBZ,</if>
                <if test="ZHUANRUBQ != null">ZHUANRUBQ,</if>
                <if test="ZHUANRUBQMC != null">ZHUANRUBQMC,</if>
                <if test="GUANDAOID != null">GUANDAOID,</if>
                <if test="TESHUYZBZ != null">TESHUYZBZ,</if>
                <if test="SHUNXUHAO != null">SHUNXUHAO,</if>
                <if test="ZHIQINGTYSZT != null">ZHIQINGTYSZT,</if>
                <if test="YAOPINPH != null">YAOPINPH,</if>
                <if test="GCPBZ != null">GCPBZ,</if>
                <if test="WAIPEIBZ != null">WAIPEIBZ,</if>
                <if test="ZENGPINBZ != null">ZENGPINBZ,</if>
                <if test="TPNYZBZ != null">TPNYZBZ,</if>
                <if test="PPDYIZHUID != null">PPDYIZHUID,</if>
                <if test="SHUQIANYZBZ != null">SHUQIANYZBZ,</if>
                <if test="QIANMINGBZ != null">QIANMINGBZ,</if>
                <if test="SHENFANGSQM != null">SHENFANGSQM,</if>
                <if test="SHENFANGSQMSJ != null">SHENFANGSQMSJ,</if>
                <if test="YONGYAOYY != null">YONGYAOYY,</if>
                <if test="SHUQIANYZSSMC != null">SHUQIANYZSSMC,</if>
                <if test="KANGJUNYWSHBZ != null">KANGJUNYWSHBZ,</if>
                <if test="MAZUIBZ != null">MAZUIBZ,</if>
                <if test="SHUQIANYZSSID != null">SHUQIANYZSSID,</if>
                <if test="YIZHULX != null">YIZHULX,</if>
                <if test="YAOPINJJBZ != null">YAOPINJJBZ,</if>
                <if test="KANGJUNYWQM != null">KANGJUNYWQM,</if>
                <if test="KANGJUNYWQMSJ != null">KANGJUNYWQMSJ,</if>
                <if test="GUANDAOMC != null">GUANDAOMC,</if>
                <if test="GUOMINYFL != null">GUOMINYFL,</if>
                <if test="YUTINGSJ != null">YUTINGSJ,</if>
                <if test="CHUYUANZDID != null">CHUYUANZDID,</if>
                <if test="CHUYUANZDMC != null">CHUYUANZDMC,</if>
                <if test="ZHONGYIZD != null">ZHONGYIZD,</if>
                <if test="ZHUANRUYLZ != null">ZHUANRUYLZ,</if>
                <if test="ZHONGYIZHID != null">ZHONGYIZHID,</if>
                <if test="FANGSHIZQ != null">FANGSHIZQ,</if>
                <if test="SHIYONGFW != null">SHIYONGFW,</if>
                <if test="FUHESJYH != null">FUHESJYH,</if>
                <if test="FUHERENYH != null">FUHERENYH,</if>
                <if test="BADIANJCNR != null">BADIANJCNR,</if>
                <if test="BADIANJCJG != null">BADIANJCJG,</if>
                <if test="SHOUSHUT != null">SHOUSHUT,</if>
                <if test="KAPIANLX != null">KAPIANLX,</if>
                <if test="DAGUIGCD != null">DAGUIGCD,</if>
                <if test="DAGUIGCDMC != null">DAGUIGCDMC,</if>
                <if test="DAGUIGBZL != null">DAGUIGBZL,</if>
                <if test="BAIZHUANGDW != null">BAIZHUANGDW,</if>
                <if test="TIJIDW != null">TIJIDW,</if>
                <if test="TIJI != null">TIJI,</if>
                <if test="DAGUIGJGID != null">DAGUIGJGID,</if>
                <if test="DAGUIGID != null">DAGUIGID,</if>
                <if test="YIZHUXMLX != null">YIZHUXMLX,</if>
                <if test="JIEDUANID != null">JIEDUANID,</if>
                <if test="BINGRENLJID != null">BINGRENLJID,</if>
                <if test="QIANGJIUBZ != null">QIANGJIUBZ,</if>
                <if test="APIANLX != null">APIANLX,</if>
                <if test="SHENGFANGSQMYY != null">SHENGFANGSQMYY,</if>
                <if test="SHENGFANGSQMSJ != null">SHENGFANGSQMSJ,</if>
                <if test="SHENGFANGSQMMC != null">SHENGFANGSQMMC,</if>
                <if test="SHENGFANGSQM != null">SHENGFANGSQM,</if>
                <if test="FEIYONGKZBZ != null">FEIYONGKZBZ,</if>
                <if test="WAIPEIBABH != null">WAIPEIBABH,</if>
                <if test="GAOFANGJX != null">GAOFANGJX,</if>
                <if test="GAOFANGFLXX != null">GAOFANGFLXX,</if>
                <if test="MEIRIJL != null">MEIRIJL,</if>
                <if test="JIANYAOFF != null">JIANYAOFF,</if>
                <if test="FUYAOCS != null">FUYAOCS,</if>
                <if test="FUYAOFF != null">FUYAOFF,</if>
                <if test="TONGSHUOYZID != null">TONGSHUOYZID,</if>
                <if test="YIBAODJ != null">YIBAODJ,</if>
                <if test="WAIPEISFR != null">WAIPEISFR,</if>
                <if test="WAIPEISFSJ != null">WAIPEISFSJ,</if>
                <if test="SHENGLIZQ != null">SHENGLIZQ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="YIZHUID != null">#{YIZHUID,jdbcType=VARCHAR},</if>
                <if test="YINGYONGID != null">#{YINGYONGID,jdbcType=VARCHAR},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=VARCHAR},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="YINGERID != null">#{YINGERID,jdbcType=VARCHAR},</if>
                <if test="BINGRENXM != null">#{BINGRENXM,jdbcType=VARCHAR},</if>
                <if test="YIZHUXMID != null">#{YIZHUXMID,jdbcType=VARCHAR},</if>
                <if test="YIZHUMC != null">#{YIZHUMC,jdbcType=VARCHAR},</if>
                <if test="KAIZHUSJ != null">#{KAIZHUSJ,jdbcType=TIMESTAMP},</if>
                <if test="YIZHUMS != null">#{YIZHUMS,jdbcType=VARCHAR},</if>
                <if test="SHURUSJ != null">#{SHURUSJ,jdbcType=TIMESTAMP},</if>
                <if test="PINCI != null">#{PINCI,jdbcType=VARCHAR},</if>
                <if test="SHURUREN != null">#{SHURUREN,jdbcType=VARCHAR},</if>
                <if test="KAISHISJ != null">#{KAISHISJ,jdbcType=TIMESTAMP},</if>
                <if test="JIESHUSJ != null">#{JIESHUSJ,jdbcType=TIMESTAMP},</if>
                <if test="YIZHUFL != null">#{YIZHUFL,jdbcType=VARCHAR},</if>
                <if test="GEIYAOFS != null">#{GEIYAOFS,jdbcType=VARCHAR},</if>
                <if test="GEIYAOFSLX != null">#{GEIYAOFSLX,jdbcType=DECIMAL},</if>
                <if test="GEIYAOFSMC != null">#{GEIYAOFSMC,jdbcType=VARCHAR},</if>
                <if test="KAIZHUYS != null">#{KAIZHUYS,jdbcType=VARCHAR},</if>
                <if test="KAIZHUYSXM != null">#{KAIZHUYSXM,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGSJ != null">#{ZHIXINGSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHIXINGREN != null">#{ZHIXINGREN,jdbcType=VARCHAR},</if>
                <if test="TINGZHUSJ != null">#{TINGZHUSJ,jdbcType=TIMESTAMP},</if>
                <if test="FUHESJ != null">#{FUHESJ,jdbcType=TIMESTAMP},</if>
                <if test="FUHEREN != null">#{FUHEREN,jdbcType=VARCHAR},</if>
                <if test="TINGZHUREN != null">#{TINGZHUREN,jdbcType=VARCHAR},</if>
                <if test="TINGZHUFHR != null">#{TINGZHUFHR,jdbcType=VARCHAR},</if>
                <if test="TINGZHUFHSJ != null">#{TINGZHUFHSJ,jdbcType=TIMESTAMP},</if>
                <if test="GUIGEID != null">#{GUIGEID,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOREN != null">#{CHEXIAOREN,jdbcType=VARCHAR},</if>
                <if test="YIZHUZT != null">#{YIZHUZT,jdbcType=VARCHAR},</if>
                <if test="DANGQIANZT != null">#{DANGQIANZT,jdbcType=VARCHAR},</if>
                <if test="JILIANGDW != null">#{JILIANGDW,jdbcType=VARCHAR},</if>
                <if test="BAOZHUANGDW != null">#{BAOZHUANGDW,jdbcType=VARCHAR},</if>
                <if test="ZUIXIAODW != null">#{ZUIXIAODW,jdbcType=VARCHAR},</if>
                <if test="BAOZHUANGLIANG != null">#{BAOZHUANGLIANG,jdbcType=DECIMAL},</if>
                <if test="FUYIZMC != null">#{FUYIZMC,jdbcType=VARCHAR},</if>
                <if test="YAOFANGYYID != null">#{YAOFANGYYID,jdbcType=VARCHAR},</if>
                <if test="YAOPINMC != null">#{YAOPINMC,jdbcType=VARCHAR},</if>
                <if test="JIAGEID != null">#{JIAGEID,jdbcType=VARCHAR},</if>
                <if test="YAOPINBMID != null">#{YAOPINBMID,jdbcType=VARCHAR},</if>
                <if test="YAOPINGG != null">#{YAOPINGG,jdbcType=VARCHAR},</if>
                <if test="PAICHISJ != null">#{PAICHISJ,jdbcType=TIMESTAMP},</if>
                <if test="PAICHIYZID != null">#{PAICHIYZID,jdbcType=VARCHAR},</if>
                <if test="JIXING != null">#{JIXING,jdbcType=DECIMAL},</if>
                <if test="PISHIJG != null">#{PISHIJG,jdbcType=VARCHAR},</if>
                <if test="LINGYAOFS != null">#{LINGYAOFS,jdbcType=VARCHAR},</if>
                <if test="DAYINBZ != null">#{DAYINBZ,jdbcType=DECIMAL},</if>
                <if test="FUYIZID != null">#{FUYIZID,jdbcType=VARCHAR},</if>
                <if test="YISHENGZID != null">#{YISHENGZID,jdbcType=VARCHAR},</if>
                <if test="DANJIA != null">#{DANJIA,jdbcType=DECIMAL},</if>
                <if test="YISHENGZT != null">#{YISHENGZT,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGYL != null">#{ZHIXINGYL,jdbcType=VARCHAR},</if>
                <if test="KESHIID != null">#{KESHIID,jdbcType=VARCHAR},</if>
                <if test="ZUHAO != null">#{ZUHAO,jdbcType=VARCHAR},</if>
                <if test="PAICHILX != null">#{PAICHILX,jdbcType=DECIMAL},</if>
                <if test="PAIXUBH != null">#{PAIXUBH,jdbcType=VARCHAR},</if>
                <if test="SHOURICS != null">#{SHOURICS,jdbcType=DECIMAL},</if>
                <if test="DANGRICS != null">#{DANGRICS,jdbcType=DECIMAL},</if>
                <if test="DAYINJL != null">#{DAYINJL,jdbcType=VARCHAR},</if>
                <if test="MORICS != null">#{MORICS,jdbcType=DECIMAL},</if>
                <if test="KAIZHUBQ != null">#{KAIZHUBQ,jdbcType=VARCHAR},</if>
                <if test="KAIZHUKS != null">#{KAIZHUKS,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOSJ != null">#{CHEXIAOSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHUANGTAISJ != null">#{ZHUANGTAISJ,jdbcType=TIMESTAMP},</if>
                <if test="CHANDILB != null">#{CHANDILB,jdbcType=VARCHAR},</if>
                <if test="BINGQUID != null">#{BINGQUID,jdbcType=VARCHAR},</if>
                <if test="YICIJL != null">#{YICIJL,jdbcType=DECIMAL},</if>
                <if test="YICIJLDW != null">#{YICIJLDW,jdbcType=VARCHAR},</if>
                <if test="YICIYL != null">#{YICIYL,jdbcType=DECIMAL},</if>
                <if test="JILIANG != null">#{JILIANG,jdbcType=DECIMAL},</if>
                <if test="QIANGZHIBZ != null">#{QIANGZHIBZ,jdbcType=DECIMAL},</if>
                <if test="KAIZHUKSMC != null">#{KAIZHUKSMC,jdbcType=VARCHAR},</if>
                <if test="KAIZHUBQMC != null">#{KAIZHUBQMC,jdbcType=VARCHAR},</if>
                <if test="JIZHENBZ != null">#{JIZHENBZ,jdbcType=DECIMAL},</if>
                <if test="CHULIYJ != null">#{CHULIYJ,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="ZUIXIAODWZXYL != null">#{ZUIXIAODWZXYL,jdbcType=VARCHAR},</if>
                <if test="ZUIXIAODWYL != null">#{ZUIXIAODWYL,jdbcType=DECIMAL},</if>
                <if test="PISHIBZ != null">#{PISHIBZ,jdbcType=DECIMAL},</if>
                <if test="FUYAOSX != null">#{FUYAOSX,jdbcType=VARCHAR},</if>
                <if test="TAKEBZ != null">#{TAKEBZ,jdbcType=DECIMAL},</if>
                <if test="YEJIANBZ != null">#{YEJIANBZ,jdbcType=DECIMAL},</if>
                <if test="PISHILX != null">#{PISHILX,jdbcType=VARCHAR},</if>
                <if test="PISHIFHR1 != null">#{PISHIFHR1,jdbcType=VARCHAR},</if>
                <if test="PISHIFHSJ1 != null">#{PISHIFHSJ1,jdbcType=TIMESTAMP},</if>
                <if test="PISHIFHR2 != null">#{PISHIFHR2,jdbcType=VARCHAR},</if>
                <if test="PISHIFHSJ2 != null">#{PISHIFHSJ2,jdbcType=TIMESTAMP},</if>
                <if test="JIFEIJG != null">#{JIFEIJG,jdbcType=DECIMAL},</if>
                <if test="JIFEIFS != null">#{JIFEIFS,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGSJLX != null">#{ZHIXINGSJLX,jdbcType=VARCHAR},</if>
                <if test="FEIYONGXZZHBZ != null">#{FEIYONGXZZHBZ,jdbcType=DECIMAL},</if>
                <if test="JINGMAIPBZ != null">#{JINGMAIPBZ,jdbcType=DECIMAL},</if>
                <if test="FAYAOZT != null">#{FAYAOZT,jdbcType=VARCHAR},</if>
                <if test="YUANYAOFYYID != null">#{YUANYAOFYYID,jdbcType=VARCHAR},</if>
                <if test="ZIFUBL != null">#{ZIFUBL,jdbcType=VARCHAR},</if>
                <if test="CAOYAOBZ != null">#{CAOYAOBZ,jdbcType=DECIMAL},</if>
                <if test="DAIJIANTS != null">#{DAIJIANTS,jdbcType=DECIMAL},</if>
                <if test="TINGZHUBQ != null">#{TINGZHUBQ,jdbcType=VARCHAR},</if>
                <if test="PAIXUZH != null">#{PAIXUZH,jdbcType=VARCHAR},</if>
                <if test="QIANFEISHBZ != null">#{QIANFEISHBZ,jdbcType=DECIMAL},</if>
                <if test="YUYUERQ != null">#{YUYUERQ,jdbcType=TIMESTAMP},</if>
                <if test="ZHIXINGKSJFBZ != null">#{ZHIXINGKSJFBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIXINGKS != null">#{ZHIXINGKS,jdbcType=VARCHAR},</if>
                <if test="FENQURQ != null">#{FENQURQ,jdbcType=TIMESTAMP},</if>
                <if test="PINCIZXSJ != null">#{PINCIZXSJ,jdbcType=VARCHAR},</if>
                <if test="YIYUANSPBZ != null">#{YIYUANSPBZ,jdbcType=DECIMAL},</if>
                <if test="YIYUANSPLX != null">#{YIYUANSPLX,jdbcType=DECIMAL},</if>
                <if test="YIYUANSPSM != null">#{YIYUANSPSM,jdbcType=VARCHAR},</if>
                <if test="YIYUANSPBL != null">#{YIYUANSPBL,jdbcType=VARCHAR},</if>
                <if test="JIAOBANHDBZ != null">#{JIAOBANHDBZ,jdbcType=VARCHAR},</if>
                <if test="ZONGHEDXH != null">#{ZONGHEDXH,jdbcType=VARCHAR},</if>
                <if test="JIAOBANHDSJ != null">#{JIAOBANHDSJ,jdbcType=TIMESTAMP},</if>
                <if test="JIAOBANHDR != null">#{JIAOBANHDR,jdbcType=VARCHAR},</if>
                <if test="FUJIAFYJFFS != null">#{FUJIAFYJFFS,jdbcType=VARCHAR},</if>
                <if test="YUANDANGQZT != null">#{YUANDANGQZT,jdbcType=VARCHAR},</if>
                <if test="YUANYIZZT != null">#{YUANYIZZT,jdbcType=VARCHAR},</if>
                <if test="DONGJIEBZ != null">#{DONGJIEBZ,jdbcType=DECIMAL},</if>
                <if test="JIEDONGRQ != null">#{JIEDONGRQ,jdbcType=TIMESTAMP},</if>
                <if test="TUIYAOSPBZ != null">#{TUIYAOSPBZ,jdbcType=DECIMAL},</if>
                <if test="YAOPINJX != null">#{YAOPINJX,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGRQ != null">#{ZHIXINGRQ,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGZT != null">#{ZHIXINGZT,jdbcType=DECIMAL},</if>
                <if test="KAPIANDYBZ != null">#{KAPIANDYBZ,jdbcType=VARCHAR},</if>
                <if test="QUZHENGLX != null">#{QUZHENGLX,jdbcType=VARCHAR},</if>
                <if test="DONGJIERQ != null">#{DONGJIERQ,jdbcType=TIMESTAMP},</if>
                <if test="QINGJIABZ != null">#{QINGJIABZ,jdbcType=DECIMAL},</if>
                <if test="YIYUANSPRQ != null">#{YIYUANSPRQ,jdbcType=TIMESTAMP},</if>
                <if test="BIANGENGDDYBZ != null">#{BIANGENGDDYBZ,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOLX != null">#{CHEXIAOLX,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOYY != null">#{CHEXIAOYY,jdbcType=VARCHAR},</if>
                <if test="YUANZHIXSJ != null">#{YUANZHIXSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUYUANDYBZ != null">#{CHUYUANDYBZ,jdbcType=DECIMAL},</if>
                <if test="CHANGQILSBZ != null">#{CHANGQILSBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIXINGBZ != null">#{ZHIXINGBZ,jdbcType=DECIMAL},</if>
                <if test="JIAOBANHDR2 != null">#{JIAOBANHDR2,jdbcType=VARCHAR},</if>
                <if test="GONGZUOZID != null">#{GONGZUOZID,jdbcType=VARCHAR},</if>
                <if test="CAOYAOTSYF != null">#{CAOYAOTSYF,jdbcType=VARCHAR},</if>
                <if test="KANGSHENGSSYSM != null">#{KANGSHENGSSYSM,jdbcType=VARCHAR},</if>
                <if test="SHENPIYS != null">#{SHENPIYS,jdbcType=VARCHAR},</if>
                <if test="SHENPINR != null">#{SHENPINR,jdbcType=VARCHAR},</if>
                <if test="DISHU != null">#{DISHU,jdbcType=VARCHAR},</if>
                <if test="BIGUANGBZ != null">#{BIGUANGBZ,jdbcType=DECIMAL},</if>
                <if test="ZHONGYAOPFKLBZ != null">#{ZHONGYAOPFKLBZ,jdbcType=DECIMAL},</if>
                <if test="KUCUNBZBZ != null">#{KUCUNBZBZ,jdbcType=DECIMAL},</if>
                <if test="BIANGENGHZDYBZ != null">#{BIANGENGHZDYBZ,jdbcType=VARCHAR},</if>
                <if test="CHENGZUYZID != null">#{CHENGZUYZID,jdbcType=VARCHAR},</if>
                <if test="YAOPINLDID != null">#{YAOPINLDID,jdbcType=VARCHAR},</if>
                <if test="ZHONGYAOKLZHSL != null">#{ZHONGYAOKLZHSL,jdbcType=DECIMAL},</if>
                <if test="KANGJUNYWSY1 != null">#{KANGJUNYWSY1,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY2 != null">#{KANGJUNYWSY2,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY3 != null">#{KANGJUNYWSY3,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY4 != null">#{KANGJUNYWSY4,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY5 != null">#{KANGJUNYWSY5,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY6 != null">#{KANGJUNYWSY6,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY7 != null">#{KANGJUNYWSY7,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY8 != null">#{KANGJUNYWSY8,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY9 != null">#{KANGJUNYWSY9,jdbcType=VARCHAR},</if>
                <if test="DULIFL != null">#{DULIFL,jdbcType=VARCHAR},</if>
                <if test="QITASX != null">#{QITASX,jdbcType=VARCHAR},</if>
                <if test="HUIZHENYZSPBZ != null">#{HUIZHENYZSPBZ,jdbcType=DECIMAL},</if>
                <if test="HUIZHENYZSPYS != null">#{HUIZHENYZSPYS,jdbcType=VARCHAR},</if>
                <if test="NONGJIANJBZ != null">#{NONGJIANJBZ,jdbcType=DECIMAL},</if>
                <if test="LINCHUANGLJDRBZ != null">#{LINCHUANGLJDRBZ,jdbcType=DECIMAL},</if>
                <if test="YAOWUSX != null">#{YAOWUSX,jdbcType=DECIMAL},</if>
                <if test="GAOFANGBZ != null">#{GAOFANGBZ,jdbcType=DECIMAL},</if>
                <if test="LINGYAORXM != null">#{LINGYAORXM,jdbcType=VARCHAR},</if>
                <if test="LINGYAORSFZH != null">#{LINGYAORSFZH,jdbcType=VARCHAR},</if>
                <if test="DUMAKH != null">#{DUMAKH,jdbcType=VARCHAR},</if>
                <if test="CHANDI != null">#{CHANDI,jdbcType=VARCHAR},</if>
                <if test="CHANDIMC != null">#{CHANDIMC,jdbcType=VARCHAR},</if>
                <if test="MIANFEIYPBZ != null">#{MIANFEIYPBZ,jdbcType=DECIMAL},</if>
                <if test="YUANJIESSJ != null">#{YUANJIESSJ,jdbcType=TIMESTAMP},</if>
                <if test="YUANTINGZR != null">#{YUANTINGZR,jdbcType=VARCHAR},</if>
                <if test="YUANTINGZBQ != null">#{YUANTINGZBQ,jdbcType=VARCHAR},</if>
                <if test="YUANTINGZSJ != null">#{YUANTINGZSJ,jdbcType=TIMESTAMP},</if>
                <if test="YIZHUJKID != null">#{YIZHUJKID,jdbcType=VARCHAR},</if>
                <if test="SHENFANGZT != null">#{SHENFANGZT,jdbcType=VARCHAR},</if>
                <if test="SHENFANGJG != null">#{SHENFANGJG,jdbcType=VARCHAR},</if>
                <if test="SHENFANGREN != null">#{SHENFANGREN,jdbcType=VARCHAR},</if>
                <if test="SHENFANGSJ != null">#{SHENFANGSJ,jdbcType=TIMESTAMP},</if>
                <if test="KANGJUNYWSY10 != null">#{KANGJUNYWSY10,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY11 != null">#{KANGJUNYWSY11,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY12 != null">#{KANGJUNYWSY12,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY13 != null">#{KANGJUNYWSY13,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY14 != null">#{KANGJUNYWSY14,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY15 != null">#{KANGJUNYWSY15,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY16 != null">#{KANGJUNYWSY16,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY17 != null">#{KANGJUNYWSY17,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY18 != null">#{KANGJUNYWSY18,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY19 != null">#{KANGJUNYWSY19,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSY20 != null">#{KANGJUNYWSY20,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYS != null">#{ZHUZHIYS,jdbcType=VARCHAR},</if>
                <if test="TIAOPEIJL != null">#{TIAOPEIJL,jdbcType=VARCHAR},</if>
                <if test="CAOYAOFCFY != null">#{CAOYAOFCFY,jdbcType=VARCHAR},</if>
                <if test="JINGMAZD != null">#{JINGMAZD,jdbcType=VARCHAR},</if>
                <if test="YICHANGJGCLYJ != null">#{YICHANGJGCLYJ,jdbcType=DECIMAL},</if>
                <if test="YICHANGJGCLSJ != null">#{YICHANGJGCLSJ,jdbcType=TIMESTAMP},</if>
                <if test="YICHANGJGCLSM != null">#{YICHANGJGCLSM,jdbcType=VARCHAR},</if>
                <if test="YICHANGJGCLR != null">#{YICHANGJGCLR,jdbcType=VARCHAR},</if>
                <if test="JINGSHIYS != null">#{JINGSHIYS,jdbcType=DECIMAL},</if>
                <if test="BXSYZDBZ != null">#{BXSYZDBZ,jdbcType=DECIMAL},</if>
                <if test="YICHAKBG != null">#{YICHAKBG,jdbcType=VARCHAR},</if>
                <if test="LINCHUANGLJID != null">#{LINCHUANGLJID,jdbcType=VARCHAR},</if>
                <if test="TAOCANXSBRMXID != null">#{TAOCANXSBRMXID,jdbcType=VARCHAR},</if>
                <if test="TAOCANXSBZ != null">#{TAOCANXSBZ,jdbcType=DECIMAL},</if>
                <if test="XIEDINGBZ != null">#{XIEDINGBZ,jdbcType=DECIMAL},</if>
                <if test="XIEDINGCFID != null">#{XIEDINGCFID,jdbcType=VARCHAR},</if>
                <if test="DUMACFQM != null">#{DUMACFQM,jdbcType=VARCHAR},</if>
                <if test="DUMACFQMSJ != null">#{DUMACFQMSJ,jdbcType=TIMESTAMP},</if>
                <if test="KESHIFFBZ != null">#{KESHIFFBZ,jdbcType=DECIMAL},</if>
                <if test="DISHUDW != null">#{DISHUDW,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYW_LCZDDM != null">#{KANGJUNYW_LCZDDM,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYW_LCZDMC != null">#{KANGJUNYW_LCZDMC,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYW_SFHZ != null">#{KANGJUNYW_SFHZ,jdbcType=DECIMAL},</if>
                <if test="KANGJUNYW_WSWSJ != null">#{KANGJUNYW_WSWSJ,jdbcType=DECIMAL},</if>
                <if test="KANGJUNYW_JJSY != null">#{KANGJUNYW_JJSY,jdbcType=DECIMAL},</if>
                <if test="CHEXIAOHSID != null">#{CHEXIAOHSID,jdbcType=VARCHAR},</if>
                <if test="DAORUDGJLBBZ != null">#{DAORUDGJLBBZ,jdbcType=DECIMAL},</if>
                <if test="PISHIPH != null">#{PISHIPH,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOSHBZ != null">#{CHEXIAOSHBZ,jdbcType=DECIMAL},</if>
                <if test="CHEXIAOSHR != null">#{CHEXIAOSHR,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOSHSJ != null">#{CHEXIAOSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHUANGQIANMBZ != null">#{SHUANGQIANMBZ,jdbcType=DECIMAL},</if>
                <if test="PPDBZ != null">#{PPDBZ,jdbcType=DECIMAL},</if>
                <if test="PPDJG != null">#{PPDJG,jdbcType=VARCHAR},</if>
                <if test="PPDBWBZ != null">#{PPDBWBZ,jdbcType=VARCHAR},</if>
                <if test="HESUANKS != null">#{HESUANKS,jdbcType=VARCHAR},</if>
                <if test="HESUANKSMC != null">#{HESUANKSMC,jdbcType=VARCHAR},</if>
                <if test="YIZHUTJBZ != null">#{YIZHUTJBZ,jdbcType=DECIMAL},</if>
                <if test="YIZHUTJR != null">#{YIZHUTJR,jdbcType=VARCHAR},</if>
                <if test="YIZHUTJSJ != null">#{YIZHUTJSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHEXIAOSQR != null">#{CHEXIAOSQR,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOSQSJ != null">#{CHEXIAOSQSJ,jdbcType=TIMESTAMP},</if>
                <if test="KUOZHANXX != null">#{KUOZHANXX,jdbcType=VARCHAR},</if>
                <if test="BINGRENYLZ != null">#{BINGRENYLZ,jdbcType=VARCHAR},</if>
                <if test="YIZHUSDBZ != null">#{YIZHUSDBZ,jdbcType=DECIMAL},</if>
                <if test="YUANQIANSQDID != null">#{YUANQIANSQDID,jdbcType=VARCHAR},</if>
                <if test="DAOGUANJSBZ != null">#{DAOGUANJSBZ,jdbcType=DECIMAL},</if>
                <if test="DAOGUANJSR != null">#{DAOGUANJSR,jdbcType=VARCHAR},</if>
                <if test="DAOGUANJSSJ != null">#{DAOGUANJSSJ,jdbcType=TIMESTAMP},</if>
                <if test="YONGXUEBZ != null">#{YONGXUEBZ,jdbcType=VARCHAR},</if>
                <if test="YUANQIANDRBZ != null">#{YUANQIANDRBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUANKEHCBZ != null">#{ZHUANKEHCBZ,jdbcType=DECIMAL},</if>
                <if test="GUANDAOBWID != null">#{GUANDAOBWID,jdbcType=VARCHAR},</if>
                <if test="GUANDAOBWMC != null">#{GUANDAOBWMC,jdbcType=VARCHAR},</if>
                <if test="GUANDAOFWID != null">#{GUANDAOFWID,jdbcType=VARCHAR},</if>
                <if test="GUANDAOFWMC != null">#{GUANDAOFWMC,jdbcType=VARCHAR},</if>
                <if test="JIAJIBZ != null">#{JIAJIBZ,jdbcType=DECIMAL},</if>
                <if test="ZIBEIBZ != null">#{ZIBEIBZ,jdbcType=DECIMAL},</if>
                <if test="DUOCHONGNYBZ != null">#{DUOCHONGNYBZ,jdbcType=DECIMAL},</if>
                <if test="HUANCHUANGHAO != null">#{HUANCHUANGHAO,jdbcType=VARCHAR},</if>
                <if test="ZHUANRUKS != null">#{ZHUANRUKS,jdbcType=VARCHAR},</if>
                <if test="ZHUANRUKSMC != null">#{ZHUANRUKSMC,jdbcType=VARCHAR},</if>
                <if test="GUANDAOLYID != null">#{GUANDAOLYID,jdbcType=VARCHAR},</if>
                <if test="GUANDAOLYMC != null">#{GUANDAOLYMC,jdbcType=VARCHAR},</if>
                <if test="PISHIGCSJ != null">#{PISHIGCSJ,jdbcType=DECIMAL},</if>
                <if test="PISHIKSSJ != null">#{PISHIKSSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHUYZID != null">#{SHOUSHUYZID,jdbcType=VARCHAR},</if>
                <if test="ZHILIAOFA != null">#{ZHILIAOFA,jdbcType=VARCHAR},</if>
                <if test="ZHILIAOLY != null">#{ZHILIAOLY,jdbcType=VARCHAR},</if>
                <if test="XUETANGYZBZ != null">#{XUETANGYZBZ,jdbcType=DECIMAL},</if>
                <if test="XUSHENPIBZ != null">#{XUSHENPIBZ,jdbcType=DECIMAL},</if>
                <if test="SHENPIBZ != null">#{SHENPIBZ,jdbcType=DECIMAL},</if>
                <if test="SHENPIREN != null">#{SHENPIREN,jdbcType=VARCHAR},</if>
                <if test="SHENPISJ != null">#{SHENPISJ,jdbcType=TIMESTAMP},</if>
                <if test="BEIXUESQDID != null">#{BEIXUESQDID,jdbcType=VARCHAR},</if>
                <if test="BEIXUEYZID != null">#{BEIXUEYZID,jdbcType=VARCHAR},</if>
                <if test="HUSHIBZ != null">#{HUSHIBZ,jdbcType=VARCHAR},</if>
                <if test="ZHUANRUBQ != null">#{ZHUANRUBQ,jdbcType=VARCHAR},</if>
                <if test="ZHUANRUBQMC != null">#{ZHUANRUBQMC,jdbcType=VARCHAR},</if>
                <if test="GUANDAOID != null">#{GUANDAOID,jdbcType=VARCHAR},</if>
                <if test="TESHUYZBZ != null">#{TESHUYZBZ,jdbcType=DECIMAL},</if>
                <if test="SHUNXUHAO != null">#{SHUNXUHAO,jdbcType=DECIMAL},</if>
                <if test="ZHIQINGTYSZT != null">#{ZHIQINGTYSZT,jdbcType=DECIMAL},</if>
                <if test="YAOPINPH != null">#{YAOPINPH,jdbcType=VARCHAR},</if>
                <if test="GCPBZ != null">#{GCPBZ,jdbcType=DECIMAL},</if>
                <if test="WAIPEIBZ != null">#{WAIPEIBZ,jdbcType=DECIMAL},</if>
                <if test="ZENGPINBZ != null">#{ZENGPINBZ,jdbcType=DECIMAL},</if>
                <if test="TPNYZBZ != null">#{TPNYZBZ,jdbcType=DECIMAL},</if>
                <if test="PPDYIZHUID != null">#{PPDYIZHUID,jdbcType=VARCHAR},</if>
                <if test="SHUQIANYZBZ != null">#{SHUQIANYZBZ,jdbcType=DECIMAL},</if>
                <if test="QIANMINGBZ != null">#{QIANMINGBZ,jdbcType=DECIMAL},</if>
                <if test="SHENFANGSQM != null">#{SHENFANGSQM,jdbcType=VARCHAR},</if>
                <if test="SHENFANGSQMSJ != null">#{SHENFANGSQMSJ,jdbcType=TIMESTAMP},</if>
                <if test="YONGYAOYY != null">#{YONGYAOYY,jdbcType=VARCHAR},</if>
                <if test="SHUQIANYZSSMC != null">#{SHUQIANYZSSMC,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWSHBZ != null">#{KANGJUNYWSHBZ,jdbcType=VARCHAR},</if>
                <if test="MAZUIBZ != null">#{MAZUIBZ,jdbcType=DECIMAL},</if>
                <if test="SHUQIANYZSSID != null">#{SHUQIANYZSSID,jdbcType=VARCHAR},</if>
                <if test="YIZHULX != null">#{YIZHULX,jdbcType=DECIMAL},</if>
                <if test="YAOPINJJBZ != null">#{YAOPINJJBZ,jdbcType=DECIMAL},</if>
                <if test="KANGJUNYWQM != null">#{KANGJUNYWQM,jdbcType=VARCHAR},</if>
                <if test="KANGJUNYWQMSJ != null">#{KANGJUNYWQMSJ,jdbcType=TIMESTAMP},</if>
                <if test="GUANDAOMC != null">#{GUANDAOMC,jdbcType=VARCHAR},</if>
                <if test="GUOMINYFL != null">#{GUOMINYFL,jdbcType=VARCHAR},</if>
                <if test="YUTINGSJ != null">#{YUTINGSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUYUANZDID != null">#{CHUYUANZDID,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDMC != null">#{CHUYUANZDMC,jdbcType=VARCHAR},</if>
                <if test="ZHONGYIZD != null">#{ZHONGYIZD,jdbcType=VARCHAR},</if>
                <if test="ZHUANRUYLZ != null">#{ZHUANRUYLZ,jdbcType=VARCHAR},</if>
                <if test="ZHONGYIZHID != null">#{ZHONGYIZHID,jdbcType=VARCHAR},</if>
                <if test="FANGSHIZQ != null">#{FANGSHIZQ,jdbcType=VARCHAR},</if>
                <if test="SHIYONGFW != null">#{SHIYONGFW,jdbcType=VARCHAR},</if>
                <if test="FUHESJYH != null">#{FUHESJYH,jdbcType=VARCHAR},</if>
                <if test="FUHERENYH != null">#{FUHERENYH,jdbcType=TIMESTAMP},</if>
                <if test="BADIANJCNR != null">#{BADIANJCNR,jdbcType=VARCHAR},</if>
                <if test="BADIANJCJG != null">#{BADIANJCJG,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUT != null">#{SHOUSHUT,jdbcType=VARCHAR},</if>
                <if test="KAPIANLX != null">#{KAPIANLX,jdbcType=VARCHAR},</if>
                <if test="DAGUIGCD != null">#{DAGUIGCD,jdbcType=VARCHAR},</if>
                <if test="DAGUIGCDMC != null">#{DAGUIGCDMC,jdbcType=VARCHAR},</if>
                <if test="DAGUIGBZL != null">#{DAGUIGBZL,jdbcType=DECIMAL},</if>
                <if test="BAIZHUANGDW != null">#{BAIZHUANGDW,jdbcType=VARCHAR},</if>
                <if test="TIJIDW != null">#{TIJIDW,jdbcType=VARCHAR},</if>
                <if test="TIJI != null">#{TIJI,jdbcType=VARCHAR},</if>
                <if test="DAGUIGJGID != null">#{DAGUIGJGID,jdbcType=VARCHAR},</if>
                <if test="DAGUIGID != null">#{DAGUIGID,jdbcType=VARCHAR},</if>
                <if test="YIZHUXMLX != null">#{YIZHUXMLX,jdbcType=VARCHAR},</if>
                <if test="JIEDUANID != null">#{JIEDUANID,jdbcType=VARCHAR},</if>
                <if test="BINGRENLJID != null">#{BINGRENLJID,jdbcType=VARCHAR},</if>
                <if test="QIANGJIUBZ != null">#{QIANGJIUBZ,jdbcType=DECIMAL},</if>
                <if test="APIANLX != null">#{APIANLX,jdbcType=VARCHAR},</if>
                <if test="SHENGFANGSQMYY != null">#{SHENGFANGSQMYY,jdbcType=VARCHAR},</if>
                <if test="SHENGFANGSQMSJ != null">#{SHENGFANGSQMSJ,jdbcType=VARCHAR},</if>
                <if test="SHENGFANGSQMMC != null">#{SHENGFANGSQMMC,jdbcType=VARCHAR},</if>
                <if test="SHENGFANGSQM != null">#{SHENGFANGSQM,jdbcType=VARCHAR},</if>
                <if test="FEIYONGKZBZ != null">#{FEIYONGKZBZ,jdbcType=DECIMAL},</if>
                <if test="WAIPEIBABH != null">#{WAIPEIBABH,jdbcType=VARCHAR},</if>
                <if test="GAOFANGJX != null">#{GAOFANGJX,jdbcType=VARCHAR},</if>
                <if test="GAOFANGFLXX != null">#{GAOFANGFLXX,jdbcType=VARCHAR},</if>
                <if test="MEIRIJL != null">#{MEIRIJL,jdbcType=VARCHAR},</if>
                <if test="JIANYAOFF != null">#{JIANYAOFF,jdbcType=VARCHAR},</if>
                <if test="FUYAOCS != null">#{FUYAOCS,jdbcType=VARCHAR},</if>
                <if test="FUYAOFF != null">#{FUYAOFF,jdbcType=VARCHAR},</if>
                <if test="TONGSHUOYZID != null">#{TONGSHUOYZID,jdbcType=VARCHAR},</if>
                <if test="YIBAODJ != null">#{YIBAODJ,jdbcType=VARCHAR},</if>
                <if test="WAIPEISFR != null">#{WAIPEISFR,jdbcType=VARCHAR},</if>
                <if test="WAIPEISFSJ != null">#{WAIPEISFSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHENGLIZQ != null">#{SHENGLIZQ,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.YzBingrenyz">
        update YZ_BINGRENYZ
        <set>
                <if test="YINGYONGID != null">
                    YINGYONGID = #{YINGYONGID,jdbcType=VARCHAR},
                </if>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="YINGERID != null">
                    YINGERID = #{YINGERID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENXM != null">
                    BINGRENXM = #{BINGRENXM,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUXMID != null">
                    YIZHUXMID = #{YIZHUXMID,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUMC != null">
                    YIZHUMC = #{YIZHUMC,jdbcType=VARCHAR},
                </if>
                <if test="KAIZHUSJ != null">
                    KAIZHUSJ = #{KAIZHUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YIZHUMS != null">
                    YIZHUMS = #{YIZHUMS,jdbcType=VARCHAR},
                </if>
                <if test="SHURUSJ != null">
                    SHURUSJ = #{SHURUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="PINCI != null">
                    PINCI = #{PINCI,jdbcType=VARCHAR},
                </if>
                <if test="SHURUREN != null">
                    SHURUREN = #{SHURUREN,jdbcType=VARCHAR},
                </if>
                <if test="KAISHISJ != null">
                    KAISHISJ = #{KAISHISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIESHUSJ != null">
                    JIESHUSJ = #{JIESHUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YIZHUFL != null">
                    YIZHUFL = #{YIZHUFL,jdbcType=VARCHAR},
                </if>
                <if test="GEIYAOFS != null">
                    GEIYAOFS = #{GEIYAOFS,jdbcType=VARCHAR},
                </if>
                <if test="GEIYAOFSLX != null">
                    GEIYAOFSLX = #{GEIYAOFSLX,jdbcType=DECIMAL},
                </if>
                <if test="GEIYAOFSMC != null">
                    GEIYAOFSMC = #{GEIYAOFSMC,jdbcType=VARCHAR},
                </if>
                <if test="KAIZHUYS != null">
                    KAIZHUYS = #{KAIZHUYS,jdbcType=VARCHAR},
                </if>
                <if test="KAIZHUYSXM != null">
                    KAIZHUYSXM = #{KAIZHUYSXM,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGSJ != null">
                    ZHIXINGSJ = #{ZHIXINGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHIXINGREN != null">
                    ZHIXINGREN = #{ZHIXINGREN,jdbcType=VARCHAR},
                </if>
                <if test="TINGZHUSJ != null">
                    TINGZHUSJ = #{TINGZHUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="FUHESJ != null">
                    FUHESJ = #{FUHESJ,jdbcType=TIMESTAMP},
                </if>
                <if test="FUHEREN != null">
                    FUHEREN = #{FUHEREN,jdbcType=VARCHAR},
                </if>
                <if test="TINGZHUREN != null">
                    TINGZHUREN = #{TINGZHUREN,jdbcType=VARCHAR},
                </if>
                <if test="TINGZHUFHR != null">
                    TINGZHUFHR = #{TINGZHUFHR,jdbcType=VARCHAR},
                </if>
                <if test="TINGZHUFHSJ != null">
                    TINGZHUFHSJ = #{TINGZHUFHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="GUIGEID != null">
                    GUIGEID = #{GUIGEID,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOREN != null">
                    CHEXIAOREN = #{CHEXIAOREN,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUZT != null">
                    YIZHUZT = #{YIZHUZT,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANZT != null">
                    DANGQIANZT = #{DANGQIANZT,jdbcType=VARCHAR},
                </if>
                <if test="JILIANGDW != null">
                    JILIANGDW = #{JILIANGDW,jdbcType=VARCHAR},
                </if>
                <if test="BAOZHUANGDW != null">
                    BAOZHUANGDW = #{BAOZHUANGDW,jdbcType=VARCHAR},
                </if>
                <if test="ZUIXIAODW != null">
                    ZUIXIAODW = #{ZUIXIAODW,jdbcType=VARCHAR},
                </if>
                <if test="BAOZHUANGLIANG != null">
                    BAOZHUANGLIANG = #{BAOZHUANGLIANG,jdbcType=DECIMAL},
                </if>
                <if test="FUYIZMC != null">
                    FUYIZMC = #{FUYIZMC,jdbcType=VARCHAR},
                </if>
                <if test="YAOFANGYYID != null">
                    YAOFANGYYID = #{YAOFANGYYID,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINMC != null">
                    YAOPINMC = #{YAOPINMC,jdbcType=VARCHAR},
                </if>
                <if test="JIAGEID != null">
                    JIAGEID = #{JIAGEID,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINBMID != null">
                    YAOPINBMID = #{YAOPINBMID,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINGG != null">
                    YAOPINGG = #{YAOPINGG,jdbcType=VARCHAR},
                </if>
                <if test="PAICHISJ != null">
                    PAICHISJ = #{PAICHISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="PAICHIYZID != null">
                    PAICHIYZID = #{PAICHIYZID,jdbcType=VARCHAR},
                </if>
                <if test="JIXING != null">
                    JIXING = #{JIXING,jdbcType=DECIMAL},
                </if>
                <if test="PISHIJG != null">
                    PISHIJG = #{PISHIJG,jdbcType=VARCHAR},
                </if>
                <if test="LINGYAOFS != null">
                    LINGYAOFS = #{LINGYAOFS,jdbcType=VARCHAR},
                </if>
                <if test="DAYINBZ != null">
                    DAYINBZ = #{DAYINBZ,jdbcType=DECIMAL},
                </if>
                <if test="FUYIZID != null">
                    FUYIZID = #{FUYIZID,jdbcType=VARCHAR},
                </if>
                <if test="YISHENGZID != null">
                    YISHENGZID = #{YISHENGZID,jdbcType=VARCHAR},
                </if>
                <if test="DANJIA != null">
                    DANJIA = #{DANJIA,jdbcType=DECIMAL},
                </if>
                <if test="YISHENGZT != null">
                    YISHENGZT = #{YISHENGZT,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGYL != null">
                    ZHIXINGYL = #{ZHIXINGYL,jdbcType=VARCHAR},
                </if>
                <if test="KESHIID != null">
                    KESHIID = #{KESHIID,jdbcType=VARCHAR},
                </if>
                <if test="ZUHAO != null">
                    ZUHAO = #{ZUHAO,jdbcType=VARCHAR},
                </if>
                <if test="PAICHILX != null">
                    PAICHILX = #{PAICHILX,jdbcType=DECIMAL},
                </if>
                <if test="PAIXUBH != null">
                    PAIXUBH = #{PAIXUBH,jdbcType=VARCHAR},
                </if>
                <if test="SHOURICS != null">
                    SHOURICS = #{SHOURICS,jdbcType=DECIMAL},
                </if>
                <if test="DANGRICS != null">
                    DANGRICS = #{DANGRICS,jdbcType=DECIMAL},
                </if>
                <if test="DAYINJL != null">
                    DAYINJL = #{DAYINJL,jdbcType=VARCHAR},
                </if>
                <if test="MORICS != null">
                    MORICS = #{MORICS,jdbcType=DECIMAL},
                </if>
                <if test="KAIZHUBQ != null">
                    KAIZHUBQ = #{KAIZHUBQ,jdbcType=VARCHAR},
                </if>
                <if test="KAIZHUKS != null">
                    KAIZHUKS = #{KAIZHUKS,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOSJ != null">
                    CHEXIAOSJ = #{CHEXIAOSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHUANGTAISJ != null">
                    ZHUANGTAISJ = #{ZHUANGTAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHANDILB != null">
                    CHANDILB = #{CHANDILB,jdbcType=VARCHAR},
                </if>
                <if test="BINGQUID != null">
                    BINGQUID = #{BINGQUID,jdbcType=VARCHAR},
                </if>
                <if test="YICIJL != null">
                    YICIJL = #{YICIJL,jdbcType=DECIMAL},
                </if>
                <if test="YICIJLDW != null">
                    YICIJLDW = #{YICIJLDW,jdbcType=VARCHAR},
                </if>
                <if test="YICIYL != null">
                    YICIYL = #{YICIYL,jdbcType=DECIMAL},
                </if>
                <if test="JILIANG != null">
                    JILIANG = #{JILIANG,jdbcType=DECIMAL},
                </if>
                <if test="QIANGZHIBZ != null">
                    QIANGZHIBZ = #{QIANGZHIBZ,jdbcType=DECIMAL},
                </if>
                <if test="KAIZHUKSMC != null">
                    KAIZHUKSMC = #{KAIZHUKSMC,jdbcType=VARCHAR},
                </if>
                <if test="KAIZHUBQMC != null">
                    KAIZHUBQMC = #{KAIZHUBQMC,jdbcType=VARCHAR},
                </if>
                <if test="JIZHENBZ != null">
                    JIZHENBZ = #{JIZHENBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHULIYJ != null">
                    CHULIYJ = #{CHULIYJ,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="ZUIXIAODWZXYL != null">
                    ZUIXIAODWZXYL = #{ZUIXIAODWZXYL,jdbcType=VARCHAR},
                </if>
                <if test="ZUIXIAODWYL != null">
                    ZUIXIAODWYL = #{ZUIXIAODWYL,jdbcType=DECIMAL},
                </if>
                <if test="PISHIBZ != null">
                    PISHIBZ = #{PISHIBZ,jdbcType=DECIMAL},
                </if>
                <if test="FUYAOSX != null">
                    FUYAOSX = #{FUYAOSX,jdbcType=VARCHAR},
                </if>
                <if test="TAKEBZ != null">
                    TAKEBZ = #{TAKEBZ,jdbcType=DECIMAL},
                </if>
                <if test="YEJIANBZ != null">
                    YEJIANBZ = #{YEJIANBZ,jdbcType=DECIMAL},
                </if>
                <if test="PISHILX != null">
                    PISHILX = #{PISHILX,jdbcType=VARCHAR},
                </if>
                <if test="PISHIFHR1 != null">
                    PISHIFHR1 = #{PISHIFHR1,jdbcType=VARCHAR},
                </if>
                <if test="PISHIFHSJ1 != null">
                    PISHIFHSJ1 = #{PISHIFHSJ1,jdbcType=TIMESTAMP},
                </if>
                <if test="PISHIFHR2 != null">
                    PISHIFHR2 = #{PISHIFHR2,jdbcType=VARCHAR},
                </if>
                <if test="PISHIFHSJ2 != null">
                    PISHIFHSJ2 = #{PISHIFHSJ2,jdbcType=TIMESTAMP},
                </if>
                <if test="JIFEIJG != null">
                    JIFEIJG = #{JIFEIJG,jdbcType=DECIMAL},
                </if>
                <if test="JIFEIFS != null">
                    JIFEIFS = #{JIFEIFS,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGSJLX != null">
                    ZHIXINGSJLX = #{ZHIXINGSJLX,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGXZZHBZ != null">
                    FEIYONGXZZHBZ = #{FEIYONGXZZHBZ,jdbcType=DECIMAL},
                </if>
                <if test="JINGMAIPBZ != null">
                    JINGMAIPBZ = #{JINGMAIPBZ,jdbcType=DECIMAL},
                </if>
                <if test="FAYAOZT != null">
                    FAYAOZT = #{FAYAOZT,jdbcType=VARCHAR},
                </if>
                <if test="YUANYAOFYYID != null">
                    YUANYAOFYYID = #{YUANYAOFYYID,jdbcType=VARCHAR},
                </if>
                <if test="ZIFUBL != null">
                    ZIFUBL = #{ZIFUBL,jdbcType=VARCHAR},
                </if>
                <if test="CAOYAOBZ != null">
                    CAOYAOBZ = #{CAOYAOBZ,jdbcType=DECIMAL},
                </if>
                <if test="DAIJIANTS != null">
                    DAIJIANTS = #{DAIJIANTS,jdbcType=DECIMAL},
                </if>
                <if test="TINGZHUBQ != null">
                    TINGZHUBQ = #{TINGZHUBQ,jdbcType=VARCHAR},
                </if>
                <if test="PAIXUZH != null">
                    PAIXUZH = #{PAIXUZH,jdbcType=VARCHAR},
                </if>
                <if test="QIANFEISHBZ != null">
                    QIANFEISHBZ = #{QIANFEISHBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUYUERQ != null">
                    YUYUERQ = #{YUYUERQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHIXINGKSJFBZ != null">
                    ZHIXINGKSJFBZ = #{ZHIXINGKSJFBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIXINGKS != null">
                    ZHIXINGKS = #{ZHIXINGKS,jdbcType=VARCHAR},
                </if>
                <if test="FENQURQ != null">
                    FENQURQ = #{FENQURQ,jdbcType=TIMESTAMP},
                </if>
                <if test="PINCIZXSJ != null">
                    PINCIZXSJ = #{PINCIZXSJ,jdbcType=VARCHAR},
                </if>
                <if test="YIYUANSPBZ != null">
                    YIYUANSPBZ = #{YIYUANSPBZ,jdbcType=DECIMAL},
                </if>
                <if test="YIYUANSPLX != null">
                    YIYUANSPLX = #{YIYUANSPLX,jdbcType=DECIMAL},
                </if>
                <if test="YIYUANSPSM != null">
                    YIYUANSPSM = #{YIYUANSPSM,jdbcType=VARCHAR},
                </if>
                <if test="YIYUANSPBL != null">
                    YIYUANSPBL = #{YIYUANSPBL,jdbcType=VARCHAR},
                </if>
                <if test="JIAOBANHDBZ != null">
                    JIAOBANHDBZ = #{JIAOBANHDBZ,jdbcType=VARCHAR},
                </if>
                <if test="ZONGHEDXH != null">
                    ZONGHEDXH = #{ZONGHEDXH,jdbcType=VARCHAR},
                </if>
                <if test="JIAOBANHDSJ != null">
                    JIAOBANHDSJ = #{JIAOBANHDSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIAOBANHDR != null">
                    JIAOBANHDR = #{JIAOBANHDR,jdbcType=VARCHAR},
                </if>
                <if test="FUJIAFYJFFS != null">
                    FUJIAFYJFFS = #{FUJIAFYJFFS,jdbcType=VARCHAR},
                </if>
                <if test="YUANDANGQZT != null">
                    YUANDANGQZT = #{YUANDANGQZT,jdbcType=VARCHAR},
                </if>
                <if test="YUANYIZZT != null">
                    YUANYIZZT = #{YUANYIZZT,jdbcType=VARCHAR},
                </if>
                <if test="DONGJIEBZ != null">
                    DONGJIEBZ = #{DONGJIEBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIEDONGRQ != null">
                    JIEDONGRQ = #{JIEDONGRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="TUIYAOSPBZ != null">
                    TUIYAOSPBZ = #{TUIYAOSPBZ,jdbcType=DECIMAL},
                </if>
                <if test="YAOPINJX != null">
                    YAOPINJX = #{YAOPINJX,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGRQ != null">
                    ZHIXINGRQ = #{ZHIXINGRQ,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGZT != null">
                    ZHIXINGZT = #{ZHIXINGZT,jdbcType=DECIMAL},
                </if>
                <if test="KAPIANDYBZ != null">
                    KAPIANDYBZ = #{KAPIANDYBZ,jdbcType=VARCHAR},
                </if>
                <if test="QUZHENGLX != null">
                    QUZHENGLX = #{QUZHENGLX,jdbcType=VARCHAR},
                </if>
                <if test="DONGJIERQ != null">
                    DONGJIERQ = #{DONGJIERQ,jdbcType=TIMESTAMP},
                </if>
                <if test="QINGJIABZ != null">
                    QINGJIABZ = #{QINGJIABZ,jdbcType=DECIMAL},
                </if>
                <if test="YIYUANSPRQ != null">
                    YIYUANSPRQ = #{YIYUANSPRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="BIANGENGDDYBZ != null">
                    BIANGENGDDYBZ = #{BIANGENGDDYBZ,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOLX != null">
                    CHEXIAOLX = #{CHEXIAOLX,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOYY != null">
                    CHEXIAOYY = #{CHEXIAOYY,jdbcType=VARCHAR},
                </if>
                <if test="YUANZHIXSJ != null">
                    YUANZHIXSJ = #{YUANZHIXSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUYUANDYBZ != null">
                    CHUYUANDYBZ = #{CHUYUANDYBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHANGQILSBZ != null">
                    CHANGQILSBZ = #{CHANGQILSBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIXINGBZ != null">
                    ZHIXINGBZ = #{ZHIXINGBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIAOBANHDR2 != null">
                    JIAOBANHDR2 = #{JIAOBANHDR2,jdbcType=VARCHAR},
                </if>
                <if test="GONGZUOZID != null">
                    GONGZUOZID = #{GONGZUOZID,jdbcType=VARCHAR},
                </if>
                <if test="CAOYAOTSYF != null">
                    CAOYAOTSYF = #{CAOYAOTSYF,jdbcType=VARCHAR},
                </if>
                <if test="KANGSHENGSSYSM != null">
                    KANGSHENGSSYSM = #{KANGSHENGSSYSM,jdbcType=VARCHAR},
                </if>
                <if test="SHENPIYS != null">
                    SHENPIYS = #{SHENPIYS,jdbcType=VARCHAR},
                </if>
                <if test="SHENPINR != null">
                    SHENPINR = #{SHENPINR,jdbcType=VARCHAR},
                </if>
                <if test="DISHU != null">
                    DISHU = #{DISHU,jdbcType=VARCHAR},
                </if>
                <if test="BIGUANGBZ != null">
                    BIGUANGBZ = #{BIGUANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHONGYAOPFKLBZ != null">
                    ZHONGYAOPFKLBZ = #{ZHONGYAOPFKLBZ,jdbcType=DECIMAL},
                </if>
                <if test="KUCUNBZBZ != null">
                    KUCUNBZBZ = #{KUCUNBZBZ,jdbcType=DECIMAL},
                </if>
                <if test="BIANGENGHZDYBZ != null">
                    BIANGENGHZDYBZ = #{BIANGENGHZDYBZ,jdbcType=VARCHAR},
                </if>
                <if test="CHENGZUYZID != null">
                    CHENGZUYZID = #{CHENGZUYZID,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINLDID != null">
                    YAOPINLDID = #{YAOPINLDID,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYAOKLZHSL != null">
                    ZHONGYAOKLZHSL = #{ZHONGYAOKLZHSL,jdbcType=DECIMAL},
                </if>
                <if test="KANGJUNYWSY1 != null">
                    KANGJUNYWSY1 = #{KANGJUNYWSY1,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY2 != null">
                    KANGJUNYWSY2 = #{KANGJUNYWSY2,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY3 != null">
                    KANGJUNYWSY3 = #{KANGJUNYWSY3,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY4 != null">
                    KANGJUNYWSY4 = #{KANGJUNYWSY4,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY5 != null">
                    KANGJUNYWSY5 = #{KANGJUNYWSY5,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY6 != null">
                    KANGJUNYWSY6 = #{KANGJUNYWSY6,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY7 != null">
                    KANGJUNYWSY7 = #{KANGJUNYWSY7,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY8 != null">
                    KANGJUNYWSY8 = #{KANGJUNYWSY8,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY9 != null">
                    KANGJUNYWSY9 = #{KANGJUNYWSY9,jdbcType=VARCHAR},
                </if>
                <if test="DULIFL != null">
                    DULIFL = #{DULIFL,jdbcType=VARCHAR},
                </if>
                <if test="QITASX != null">
                    QITASX = #{QITASX,jdbcType=VARCHAR},
                </if>
                <if test="HUIZHENYZSPBZ != null">
                    HUIZHENYZSPBZ = #{HUIZHENYZSPBZ,jdbcType=DECIMAL},
                </if>
                <if test="HUIZHENYZSPYS != null">
                    HUIZHENYZSPYS = #{HUIZHENYZSPYS,jdbcType=VARCHAR},
                </if>
                <if test="NONGJIANJBZ != null">
                    NONGJIANJBZ = #{NONGJIANJBZ,jdbcType=DECIMAL},
                </if>
                <if test="LINCHUANGLJDRBZ != null">
                    LINCHUANGLJDRBZ = #{LINCHUANGLJDRBZ,jdbcType=DECIMAL},
                </if>
                <if test="YAOWUSX != null">
                    YAOWUSX = #{YAOWUSX,jdbcType=DECIMAL},
                </if>
                <if test="GAOFANGBZ != null">
                    GAOFANGBZ = #{GAOFANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="LINGYAORXM != null">
                    LINGYAORXM = #{LINGYAORXM,jdbcType=VARCHAR},
                </if>
                <if test="LINGYAORSFZH != null">
                    LINGYAORSFZH = #{LINGYAORSFZH,jdbcType=VARCHAR},
                </if>
                <if test="DUMAKH != null">
                    DUMAKH = #{DUMAKH,jdbcType=VARCHAR},
                </if>
                <if test="CHANDI != null">
                    CHANDI = #{CHANDI,jdbcType=VARCHAR},
                </if>
                <if test="CHANDIMC != null">
                    CHANDIMC = #{CHANDIMC,jdbcType=VARCHAR},
                </if>
                <if test="MIANFEIYPBZ != null">
                    MIANFEIYPBZ = #{MIANFEIYPBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUANJIESSJ != null">
                    YUANJIESSJ = #{YUANJIESSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUANTINGZR != null">
                    YUANTINGZR = #{YUANTINGZR,jdbcType=VARCHAR},
                </if>
                <if test="YUANTINGZBQ != null">
                    YUANTINGZBQ = #{YUANTINGZBQ,jdbcType=VARCHAR},
                </if>
                <if test="YUANTINGZSJ != null">
                    YUANTINGZSJ = #{YUANTINGZSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YIZHUJKID != null">
                    YIZHUJKID = #{YIZHUJKID,jdbcType=VARCHAR},
                </if>
                <if test="SHENFANGZT != null">
                    SHENFANGZT = #{SHENFANGZT,jdbcType=VARCHAR},
                </if>
                <if test="SHENFANGJG != null">
                    SHENFANGJG = #{SHENFANGJG,jdbcType=VARCHAR},
                </if>
                <if test="SHENFANGREN != null">
                    SHENFANGREN = #{SHENFANGREN,jdbcType=VARCHAR},
                </if>
                <if test="SHENFANGSJ != null">
                    SHENFANGSJ = #{SHENFANGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="KANGJUNYWSY10 != null">
                    KANGJUNYWSY10 = #{KANGJUNYWSY10,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY11 != null">
                    KANGJUNYWSY11 = #{KANGJUNYWSY11,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY12 != null">
                    KANGJUNYWSY12 = #{KANGJUNYWSY12,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY13 != null">
                    KANGJUNYWSY13 = #{KANGJUNYWSY13,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY14 != null">
                    KANGJUNYWSY14 = #{KANGJUNYWSY14,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY15 != null">
                    KANGJUNYWSY15 = #{KANGJUNYWSY15,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY16 != null">
                    KANGJUNYWSY16 = #{KANGJUNYWSY16,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY17 != null">
                    KANGJUNYWSY17 = #{KANGJUNYWSY17,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY18 != null">
                    KANGJUNYWSY18 = #{KANGJUNYWSY18,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY19 != null">
                    KANGJUNYWSY19 = #{KANGJUNYWSY19,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSY20 != null">
                    KANGJUNYWSY20 = #{KANGJUNYWSY20,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYS != null">
                    ZHUZHIYS = #{ZHUZHIYS,jdbcType=VARCHAR},
                </if>
                <if test="TIAOPEIJL != null">
                    TIAOPEIJL = #{TIAOPEIJL,jdbcType=VARCHAR},
                </if>
                <if test="CAOYAOFCFY != null">
                    CAOYAOFCFY = #{CAOYAOFCFY,jdbcType=VARCHAR},
                </if>
                <if test="JINGMAZD != null">
                    JINGMAZD = #{JINGMAZD,jdbcType=VARCHAR},
                </if>
                <if test="YICHANGJGCLYJ != null">
                    YICHANGJGCLYJ = #{YICHANGJGCLYJ,jdbcType=DECIMAL},
                </if>
                <if test="YICHANGJGCLSJ != null">
                    YICHANGJGCLSJ = #{YICHANGJGCLSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YICHANGJGCLSM != null">
                    YICHANGJGCLSM = #{YICHANGJGCLSM,jdbcType=VARCHAR},
                </if>
                <if test="YICHANGJGCLR != null">
                    YICHANGJGCLR = #{YICHANGJGCLR,jdbcType=VARCHAR},
                </if>
                <if test="JINGSHIYS != null">
                    JINGSHIYS = #{JINGSHIYS,jdbcType=DECIMAL},
                </if>
                <if test="BXSYZDBZ != null">
                    BXSYZDBZ = #{BXSYZDBZ,jdbcType=DECIMAL},
                </if>
                <if test="YICHAKBG != null">
                    YICHAKBG = #{YICHAKBG,jdbcType=VARCHAR},
                </if>
                <if test="LINCHUANGLJID != null">
                    LINCHUANGLJID = #{LINCHUANGLJID,jdbcType=VARCHAR},
                </if>
                <if test="TAOCANXSBRMXID != null">
                    TAOCANXSBRMXID = #{TAOCANXSBRMXID,jdbcType=VARCHAR},
                </if>
                <if test="TAOCANXSBZ != null">
                    TAOCANXSBZ = #{TAOCANXSBZ,jdbcType=DECIMAL},
                </if>
                <if test="XIEDINGBZ != null">
                    XIEDINGBZ = #{XIEDINGBZ,jdbcType=DECIMAL},
                </if>
                <if test="XIEDINGCFID != null">
                    XIEDINGCFID = #{XIEDINGCFID,jdbcType=VARCHAR},
                </if>
                <if test="DUMACFQM != null">
                    DUMACFQM = #{DUMACFQM,jdbcType=VARCHAR},
                </if>
                <if test="DUMACFQMSJ != null">
                    DUMACFQMSJ = #{DUMACFQMSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="KESHIFFBZ != null">
                    KESHIFFBZ = #{KESHIFFBZ,jdbcType=DECIMAL},
                </if>
                <if test="DISHUDW != null">
                    DISHUDW = #{DISHUDW,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYW_LCZDDM != null">
                    KANGJUNYW_LCZDDM = #{KANGJUNYW_LCZDDM,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYW_LCZDMC != null">
                    KANGJUNYW_LCZDMC = #{KANGJUNYW_LCZDMC,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYW_SFHZ != null">
                    KANGJUNYW_SFHZ = #{KANGJUNYW_SFHZ,jdbcType=DECIMAL},
                </if>
                <if test="KANGJUNYW_WSWSJ != null">
                    KANGJUNYW_WSWSJ = #{KANGJUNYW_WSWSJ,jdbcType=DECIMAL},
                </if>
                <if test="KANGJUNYW_JJSY != null">
                    KANGJUNYW_JJSY = #{KANGJUNYW_JJSY,jdbcType=DECIMAL},
                </if>
                <if test="CHEXIAOHSID != null">
                    CHEXIAOHSID = #{CHEXIAOHSID,jdbcType=VARCHAR},
                </if>
                <if test="DAORUDGJLBBZ != null">
                    DAORUDGJLBBZ = #{DAORUDGJLBBZ,jdbcType=DECIMAL},
                </if>
                <if test="PISHIPH != null">
                    PISHIPH = #{PISHIPH,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOSHBZ != null">
                    CHEXIAOSHBZ = #{CHEXIAOSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHEXIAOSHR != null">
                    CHEXIAOSHR = #{CHEXIAOSHR,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOSHSJ != null">
                    CHEXIAOSHSJ = #{CHEXIAOSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHUANGQIANMBZ != null">
                    SHUANGQIANMBZ = #{SHUANGQIANMBZ,jdbcType=DECIMAL},
                </if>
                <if test="PPDBZ != null">
                    PPDBZ = #{PPDBZ,jdbcType=DECIMAL},
                </if>
                <if test="PPDJG != null">
                    PPDJG = #{PPDJG,jdbcType=VARCHAR},
                </if>
                <if test="PPDBWBZ != null">
                    PPDBWBZ = #{PPDBWBZ,jdbcType=VARCHAR},
                </if>
                <if test="HESUANKS != null">
                    HESUANKS = #{HESUANKS,jdbcType=VARCHAR},
                </if>
                <if test="HESUANKSMC != null">
                    HESUANKSMC = #{HESUANKSMC,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUTJBZ != null">
                    YIZHUTJBZ = #{YIZHUTJBZ,jdbcType=DECIMAL},
                </if>
                <if test="YIZHUTJR != null">
                    YIZHUTJR = #{YIZHUTJR,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUTJSJ != null">
                    YIZHUTJSJ = #{YIZHUTJSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHEXIAOSQR != null">
                    CHEXIAOSQR = #{CHEXIAOSQR,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOSQSJ != null">
                    CHEXIAOSQSJ = #{CHEXIAOSQSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="KUOZHANXX != null">
                    KUOZHANXX = #{KUOZHANXX,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENYLZ != null">
                    BINGRENYLZ = #{BINGRENYLZ,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUSDBZ != null">
                    YIZHUSDBZ = #{YIZHUSDBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUANQIANSQDID != null">
                    YUANQIANSQDID = #{YUANQIANSQDID,jdbcType=VARCHAR},
                </if>
                <if test="DAOGUANJSBZ != null">
                    DAOGUANJSBZ = #{DAOGUANJSBZ,jdbcType=DECIMAL},
                </if>
                <if test="DAOGUANJSR != null">
                    DAOGUANJSR = #{DAOGUANJSR,jdbcType=VARCHAR},
                </if>
                <if test="DAOGUANJSSJ != null">
                    DAOGUANJSSJ = #{DAOGUANJSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YONGXUEBZ != null">
                    YONGXUEBZ = #{YONGXUEBZ,jdbcType=VARCHAR},
                </if>
                <if test="YUANQIANDRBZ != null">
                    YUANQIANDRBZ = #{YUANQIANDRBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUANKEHCBZ != null">
                    ZHUANKEHCBZ = #{ZHUANKEHCBZ,jdbcType=DECIMAL},
                </if>
                <if test="GUANDAOBWID != null">
                    GUANDAOBWID = #{GUANDAOBWID,jdbcType=VARCHAR},
                </if>
                <if test="GUANDAOBWMC != null">
                    GUANDAOBWMC = #{GUANDAOBWMC,jdbcType=VARCHAR},
                </if>
                <if test="GUANDAOFWID != null">
                    GUANDAOFWID = #{GUANDAOFWID,jdbcType=VARCHAR},
                </if>
                <if test="GUANDAOFWMC != null">
                    GUANDAOFWMC = #{GUANDAOFWMC,jdbcType=VARCHAR},
                </if>
                <if test="JIAJIBZ != null">
                    JIAJIBZ = #{JIAJIBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZIBEIBZ != null">
                    ZIBEIBZ = #{ZIBEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="DUOCHONGNYBZ != null">
                    DUOCHONGNYBZ = #{DUOCHONGNYBZ,jdbcType=DECIMAL},
                </if>
                <if test="HUANCHUANGHAO != null">
                    HUANCHUANGHAO = #{HUANCHUANGHAO,jdbcType=VARCHAR},
                </if>
                <if test="ZHUANRUKS != null">
                    ZHUANRUKS = #{ZHUANRUKS,jdbcType=VARCHAR},
                </if>
                <if test="ZHUANRUKSMC != null">
                    ZHUANRUKSMC = #{ZHUANRUKSMC,jdbcType=VARCHAR},
                </if>
                <if test="GUANDAOLYID != null">
                    GUANDAOLYID = #{GUANDAOLYID,jdbcType=VARCHAR},
                </if>
                <if test="GUANDAOLYMC != null">
                    GUANDAOLYMC = #{GUANDAOLYMC,jdbcType=VARCHAR},
                </if>
                <if test="PISHIGCSJ != null">
                    PISHIGCSJ = #{PISHIGCSJ,jdbcType=DECIMAL},
                </if>
                <if test="PISHIKSSJ != null">
                    PISHIKSSJ = #{PISHIKSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHUYZID != null">
                    SHOUSHUYZID = #{SHOUSHUYZID,jdbcType=VARCHAR},
                </if>
                <if test="ZHILIAOFA != null">
                    ZHILIAOFA = #{ZHILIAOFA,jdbcType=VARCHAR},
                </if>
                <if test="ZHILIAOLY != null">
                    ZHILIAOLY = #{ZHILIAOLY,jdbcType=VARCHAR},
                </if>
                <if test="XUETANGYZBZ != null">
                    XUETANGYZBZ = #{XUETANGYZBZ,jdbcType=DECIMAL},
                </if>
                <if test="XUSHENPIBZ != null">
                    XUSHENPIBZ = #{XUSHENPIBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENPIBZ != null">
                    SHENPIBZ = #{SHENPIBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENPIREN != null">
                    SHENPIREN = #{SHENPIREN,jdbcType=VARCHAR},
                </if>
                <if test="SHENPISJ != null">
                    SHENPISJ = #{SHENPISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="BEIXUESQDID != null">
                    BEIXUESQDID = #{BEIXUESQDID,jdbcType=VARCHAR},
                </if>
                <if test="BEIXUEYZID != null">
                    BEIXUEYZID = #{BEIXUEYZID,jdbcType=VARCHAR},
                </if>
                <if test="HUSHIBZ != null">
                    HUSHIBZ = #{HUSHIBZ,jdbcType=VARCHAR},
                </if>
                <if test="ZHUANRUBQ != null">
                    ZHUANRUBQ = #{ZHUANRUBQ,jdbcType=VARCHAR},
                </if>
                <if test="ZHUANRUBQMC != null">
                    ZHUANRUBQMC = #{ZHUANRUBQMC,jdbcType=VARCHAR},
                </if>
                <if test="GUANDAOID != null">
                    GUANDAOID = #{GUANDAOID,jdbcType=VARCHAR},
                </if>
                <if test="TESHUYZBZ != null">
                    TESHUYZBZ = #{TESHUYZBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUNXUHAO != null">
                    SHUNXUHAO = #{SHUNXUHAO,jdbcType=DECIMAL},
                </if>
                <if test="ZHIQINGTYSZT != null">
                    ZHIQINGTYSZT = #{ZHIQINGTYSZT,jdbcType=DECIMAL},
                </if>
                <if test="YAOPINPH != null">
                    YAOPINPH = #{YAOPINPH,jdbcType=VARCHAR},
                </if>
                <if test="GCPBZ != null">
                    GCPBZ = #{GCPBZ,jdbcType=DECIMAL},
                </if>
                <if test="WAIPEIBZ != null">
                    WAIPEIBZ = #{WAIPEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZENGPINBZ != null">
                    ZENGPINBZ = #{ZENGPINBZ,jdbcType=DECIMAL},
                </if>
                <if test="TPNYZBZ != null">
                    TPNYZBZ = #{TPNYZBZ,jdbcType=DECIMAL},
                </if>
                <if test="PPDYIZHUID != null">
                    PPDYIZHUID = #{PPDYIZHUID,jdbcType=VARCHAR},
                </if>
                <if test="SHUQIANYZBZ != null">
                    SHUQIANYZBZ = #{SHUQIANYZBZ,jdbcType=DECIMAL},
                </if>
                <if test="QIANMINGBZ != null">
                    QIANMINGBZ = #{QIANMINGBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENFANGSQM != null">
                    SHENFANGSQM = #{SHENFANGSQM,jdbcType=VARCHAR},
                </if>
                <if test="SHENFANGSQMSJ != null">
                    SHENFANGSQMSJ = #{SHENFANGSQMSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YONGYAOYY != null">
                    YONGYAOYY = #{YONGYAOYY,jdbcType=VARCHAR},
                </if>
                <if test="SHUQIANYZSSMC != null">
                    SHUQIANYZSSMC = #{SHUQIANYZSSMC,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWSHBZ != null">
                    KANGJUNYWSHBZ = #{KANGJUNYWSHBZ,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIBZ != null">
                    MAZUIBZ = #{MAZUIBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUQIANYZSSID != null">
                    SHUQIANYZSSID = #{SHUQIANYZSSID,jdbcType=VARCHAR},
                </if>
                <if test="YIZHULX != null">
                    YIZHULX = #{YIZHULX,jdbcType=DECIMAL},
                </if>
                <if test="YAOPINJJBZ != null">
                    YAOPINJJBZ = #{YAOPINJJBZ,jdbcType=DECIMAL},
                </if>
                <if test="KANGJUNYWQM != null">
                    KANGJUNYWQM = #{KANGJUNYWQM,jdbcType=VARCHAR},
                </if>
                <if test="KANGJUNYWQMSJ != null">
                    KANGJUNYWQMSJ = #{KANGJUNYWQMSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="GUANDAOMC != null">
                    GUANDAOMC = #{GUANDAOMC,jdbcType=VARCHAR},
                </if>
                <if test="GUOMINYFL != null">
                    GUOMINYFL = #{GUOMINYFL,jdbcType=VARCHAR},
                </if>
                <if test="YUTINGSJ != null">
                    YUTINGSJ = #{YUTINGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUYUANZDID != null">
                    CHUYUANZDID = #{CHUYUANZDID,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDMC != null">
                    CHUYUANZDMC = #{CHUYUANZDMC,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYIZD != null">
                    ZHONGYIZD = #{ZHONGYIZD,jdbcType=VARCHAR},
                </if>
                <if test="ZHUANRUYLZ != null">
                    ZHUANRUYLZ = #{ZHUANRUYLZ,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYIZHID != null">
                    ZHONGYIZHID = #{ZHONGYIZHID,jdbcType=VARCHAR},
                </if>
                <if test="FANGSHIZQ != null">
                    FANGSHIZQ = #{FANGSHIZQ,jdbcType=VARCHAR},
                </if>
                <if test="SHIYONGFW != null">
                    SHIYONGFW = #{SHIYONGFW,jdbcType=VARCHAR},
                </if>
                <if test="FUHESJYH != null">
                    FUHESJYH = #{FUHESJYH,jdbcType=VARCHAR},
                </if>
                <if test="FUHERENYH != null">
                    FUHERENYH = #{FUHERENYH,jdbcType=TIMESTAMP},
                </if>
                <if test="BADIANJCNR != null">
                    BADIANJCNR = #{BADIANJCNR,jdbcType=VARCHAR},
                </if>
                <if test="BADIANJCJG != null">
                    BADIANJCJG = #{BADIANJCJG,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUT != null">
                    SHOUSHUT = #{SHOUSHUT,jdbcType=VARCHAR},
                </if>
                <if test="KAPIANLX != null">
                    KAPIANLX = #{KAPIANLX,jdbcType=VARCHAR},
                </if>
                <if test="DAGUIGCD != null">
                    DAGUIGCD = #{DAGUIGCD,jdbcType=VARCHAR},
                </if>
                <if test="DAGUIGCDMC != null">
                    DAGUIGCDMC = #{DAGUIGCDMC,jdbcType=VARCHAR},
                </if>
                <if test="DAGUIGBZL != null">
                    DAGUIGBZL = #{DAGUIGBZL,jdbcType=DECIMAL},
                </if>
                <if test="BAIZHUANGDW != null">
                    BAIZHUANGDW = #{BAIZHUANGDW,jdbcType=VARCHAR},
                </if>
                <if test="TIJIDW != null">
                    TIJIDW = #{TIJIDW,jdbcType=VARCHAR},
                </if>
                <if test="TIJI != null">
                    TIJI = #{TIJI,jdbcType=VARCHAR},
                </if>
                <if test="DAGUIGJGID != null">
                    DAGUIGJGID = #{DAGUIGJGID,jdbcType=VARCHAR},
                </if>
                <if test="DAGUIGID != null">
                    DAGUIGID = #{DAGUIGID,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUXMLX != null">
                    YIZHUXMLX = #{YIZHUXMLX,jdbcType=VARCHAR},
                </if>
                <if test="JIEDUANID != null">
                    JIEDUANID = #{JIEDUANID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENLJID != null">
                    BINGRENLJID = #{BINGRENLJID,jdbcType=VARCHAR},
                </if>
                <if test="QIANGJIUBZ != null">
                    QIANGJIUBZ = #{QIANGJIUBZ,jdbcType=DECIMAL},
                </if>
                <if test="APIANLX != null">
                    APIANLX = #{APIANLX,jdbcType=VARCHAR},
                </if>
                <if test="SHENGFANGSQMYY != null">
                    SHENGFANGSQMYY = #{SHENGFANGSQMYY,jdbcType=VARCHAR},
                </if>
                <if test="SHENGFANGSQMSJ != null">
                    SHENGFANGSQMSJ = #{SHENGFANGSQMSJ,jdbcType=VARCHAR},
                </if>
                <if test="SHENGFANGSQMMC != null">
                    SHENGFANGSQMMC = #{SHENGFANGSQMMC,jdbcType=VARCHAR},
                </if>
                <if test="SHENGFANGSQM != null">
                    SHENGFANGSQM = #{SHENGFANGSQM,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGKZBZ != null">
                    FEIYONGKZBZ = #{FEIYONGKZBZ,jdbcType=DECIMAL},
                </if>
                <if test="WAIPEIBABH != null">
                    WAIPEIBABH = #{WAIPEIBABH,jdbcType=VARCHAR},
                </if>
                <if test="GAOFANGJX != null">
                    GAOFANGJX = #{GAOFANGJX,jdbcType=VARCHAR},
                </if>
                <if test="GAOFANGFLXX != null">
                    GAOFANGFLXX = #{GAOFANGFLXX,jdbcType=VARCHAR},
                </if>
                <if test="MEIRIJL != null">
                    MEIRIJL = #{MEIRIJL,jdbcType=VARCHAR},
                </if>
                <if test="JIANYAOFF != null">
                    JIANYAOFF = #{JIANYAOFF,jdbcType=VARCHAR},
                </if>
                <if test="FUYAOCS != null">
                    FUYAOCS = #{FUYAOCS,jdbcType=VARCHAR},
                </if>
                <if test="FUYAOFF != null">
                    FUYAOFF = #{FUYAOFF,jdbcType=VARCHAR},
                </if>
                <if test="TONGSHUOYZID != null">
                    TONGSHUOYZID = #{TONGSHUOYZID,jdbcType=VARCHAR},
                </if>
                <if test="YIBAODJ != null">
                    YIBAODJ = #{YIBAODJ,jdbcType=VARCHAR},
                </if>
                <if test="WAIPEISFR != null">
                    WAIPEISFR = #{WAIPEISFR,jdbcType=VARCHAR},
                </if>
                <if test="WAIPEISFSJ != null">
                    WAIPEISFSJ = #{WAIPEISFSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHENGLIZQ != null">
                    SHENGLIZQ = #{SHENGLIZQ,jdbcType=VARCHAR},
                </if>
        </set>
        where   YIZHUID = #{YIZHUID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.YzBingrenyz">
        update YZ_BINGRENYZ
        set 
            YINGYONGID =  #{YINGYONGID,jdbcType=VARCHAR},
            YUANQUID =  #{YUANQUID,jdbcType=VARCHAR},
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            YINGERID =  #{YINGERID,jdbcType=VARCHAR},
            BINGRENXM =  #{BINGRENXM,jdbcType=VARCHAR},
            YIZHUXMID =  #{YIZHUXMID,jdbcType=VARCHAR},
            YIZHUMC =  #{YIZHUMC,jdbcType=VARCHAR},
            KAIZHUSJ =  #{KAIZHUSJ,jdbcType=TIMESTAMP},
            YIZHUMS =  #{YIZHUMS,jdbcType=VARCHAR},
            SHURUSJ =  #{SHURUSJ,jdbcType=TIMESTAMP},
            PINCI =  #{PINCI,jdbcType=VARCHAR},
            SHURUREN =  #{SHURUREN,jdbcType=VARCHAR},
            KAISHISJ =  #{KAISHISJ,jdbcType=TIMESTAMP},
            JIESHUSJ =  #{JIESHUSJ,jdbcType=TIMESTAMP},
            YIZHUFL =  #{YIZHUFL,jdbcType=VARCHAR},
            GEIYAOFS =  #{GEIYAOFS,jdbcType=VARCHAR},
            GEIYAOFSLX =  #{GEIYAOFSLX,jdbcType=DECIMAL},
            GEIYAOFSMC =  #{GEIYAOFSMC,jdbcType=VARCHAR},
            KAIZHUYS =  #{KAIZHUYS,jdbcType=VARCHAR},
            KAIZHUYSXM =  #{KAIZHUYSXM,jdbcType=VARCHAR},
            ZHIXINGSJ =  #{ZHIXINGSJ,jdbcType=TIMESTAMP},
            ZHIXINGREN =  #{ZHIXINGREN,jdbcType=VARCHAR},
            TINGZHUSJ =  #{TINGZHUSJ,jdbcType=TIMESTAMP},
            FUHESJ =  #{FUHESJ,jdbcType=TIMESTAMP},
            FUHEREN =  #{FUHEREN,jdbcType=VARCHAR},
            TINGZHUREN =  #{TINGZHUREN,jdbcType=VARCHAR},
            TINGZHUFHR =  #{TINGZHUFHR,jdbcType=VARCHAR},
            TINGZHUFHSJ =  #{TINGZHUFHSJ,jdbcType=TIMESTAMP},
            GUIGEID =  #{GUIGEID,jdbcType=VARCHAR},
            CHEXIAOREN =  #{CHEXIAOREN,jdbcType=VARCHAR},
            YIZHUZT =  #{YIZHUZT,jdbcType=VARCHAR},
            DANGQIANZT =  #{DANGQIANZT,jdbcType=VARCHAR},
            JILIANGDW =  #{JILIANGDW,jdbcType=VARCHAR},
            BAOZHUANGDW =  #{BAOZHUANGDW,jdbcType=VARCHAR},
            ZUIXIAODW =  #{ZUIXIAODW,jdbcType=VARCHAR},
            BAOZHUANGLIANG =  #{BAOZHUANGLIANG,jdbcType=DECIMAL},
            FUYIZMC =  #{FUYIZMC,jdbcType=VARCHAR},
            YAOFANGYYID =  #{YAOFANGYYID,jdbcType=VARCHAR},
            YAOPINMC =  #{YAOPINMC,jdbcType=VARCHAR},
            JIAGEID =  #{JIAGEID,jdbcType=VARCHAR},
            YAOPINBMID =  #{YAOPINBMID,jdbcType=VARCHAR},
            YAOPINGG =  #{YAOPINGG,jdbcType=VARCHAR},
            PAICHISJ =  #{PAICHISJ,jdbcType=TIMESTAMP},
            PAICHIYZID =  #{PAICHIYZID,jdbcType=VARCHAR},
            JIXING =  #{JIXING,jdbcType=DECIMAL},
            PISHIJG =  #{PISHIJG,jdbcType=VARCHAR},
            LINGYAOFS =  #{LINGYAOFS,jdbcType=VARCHAR},
            DAYINBZ =  #{DAYINBZ,jdbcType=DECIMAL},
            FUYIZID =  #{FUYIZID,jdbcType=VARCHAR},
            YISHENGZID =  #{YISHENGZID,jdbcType=VARCHAR},
            DANJIA =  #{DANJIA,jdbcType=DECIMAL},
            YISHENGZT =  #{YISHENGZT,jdbcType=VARCHAR},
            ZHIXINGYL =  #{ZHIXINGYL,jdbcType=VARCHAR},
            KESHIID =  #{KESHIID,jdbcType=VARCHAR},
            ZUHAO =  #{ZUHAO,jdbcType=VARCHAR},
            PAICHILX =  #{PAICHILX,jdbcType=DECIMAL},
            PAIXUBH =  #{PAIXUBH,jdbcType=VARCHAR},
            SHOURICS =  #{SHOURICS,jdbcType=DECIMAL},
            DANGRICS =  #{DANGRICS,jdbcType=DECIMAL},
            DAYINJL =  #{DAYINJL,jdbcType=VARCHAR},
            MORICS =  #{MORICS,jdbcType=DECIMAL},
            KAIZHUBQ =  #{KAIZHUBQ,jdbcType=VARCHAR},
            KAIZHUKS =  #{KAIZHUKS,jdbcType=VARCHAR},
            CHEXIAOSJ =  #{CHEXIAOSJ,jdbcType=TIMESTAMP},
            ZHUANGTAISJ =  #{ZHUANGTAISJ,jdbcType=TIMESTAMP},
            CHANDILB =  #{CHANDILB,jdbcType=VARCHAR},
            BINGQUID =  #{BINGQUID,jdbcType=VARCHAR},
            YICIJL =  #{YICIJL,jdbcType=DECIMAL},
            YICIJLDW =  #{YICIJLDW,jdbcType=VARCHAR},
            YICIYL =  #{YICIYL,jdbcType=DECIMAL},
            JILIANG =  #{JILIANG,jdbcType=DECIMAL},
            QIANGZHIBZ =  #{QIANGZHIBZ,jdbcType=DECIMAL},
            KAIZHUKSMC =  #{KAIZHUKSMC,jdbcType=VARCHAR},
            KAIZHUBQMC =  #{KAIZHUBQMC,jdbcType=VARCHAR},
            JIZHENBZ =  #{JIZHENBZ,jdbcType=DECIMAL},
            CHULIYJ =  #{CHULIYJ,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            ZUIXIAODWZXYL =  #{ZUIXIAODWZXYL,jdbcType=VARCHAR},
            ZUIXIAODWYL =  #{ZUIXIAODWYL,jdbcType=DECIMAL},
            PISHIBZ =  #{PISHIBZ,jdbcType=DECIMAL},
            FUYAOSX =  #{FUYAOSX,jdbcType=VARCHAR},
            TAKEBZ =  #{TAKEBZ,jdbcType=DECIMAL},
            YEJIANBZ =  #{YEJIANBZ,jdbcType=DECIMAL},
            PISHILX =  #{PISHILX,jdbcType=VARCHAR},
            PISHIFHR1 =  #{PISHIFHR1,jdbcType=VARCHAR},
            PISHIFHSJ1 =  #{PISHIFHSJ1,jdbcType=TIMESTAMP},
            PISHIFHR2 =  #{PISHIFHR2,jdbcType=VARCHAR},
            PISHIFHSJ2 =  #{PISHIFHSJ2,jdbcType=TIMESTAMP},
            JIFEIJG =  #{JIFEIJG,jdbcType=DECIMAL},
            JIFEIFS =  #{JIFEIFS,jdbcType=VARCHAR},
            ZHIXINGSJLX =  #{ZHIXINGSJLX,jdbcType=VARCHAR},
            FEIYONGXZZHBZ =  #{FEIYONGXZZHBZ,jdbcType=DECIMAL},
            JINGMAIPBZ =  #{JINGMAIPBZ,jdbcType=DECIMAL},
            FAYAOZT =  #{FAYAOZT,jdbcType=VARCHAR},
            YUANYAOFYYID =  #{YUANYAOFYYID,jdbcType=VARCHAR},
            ZIFUBL =  #{ZIFUBL,jdbcType=VARCHAR},
            CAOYAOBZ =  #{CAOYAOBZ,jdbcType=DECIMAL},
            DAIJIANTS =  #{DAIJIANTS,jdbcType=DECIMAL},
            TINGZHUBQ =  #{TINGZHUBQ,jdbcType=VARCHAR},
            PAIXUZH =  #{PAIXUZH,jdbcType=VARCHAR},
            QIANFEISHBZ =  #{QIANFEISHBZ,jdbcType=DECIMAL},
            YUYUERQ =  #{YUYUERQ,jdbcType=TIMESTAMP},
            ZHIXINGKSJFBZ =  #{ZHIXINGKSJFBZ,jdbcType=DECIMAL},
            ZHIXINGKS =  #{ZHIXINGKS,jdbcType=VARCHAR},
            FENQURQ =  #{FENQURQ,jdbcType=TIMESTAMP},
            PINCIZXSJ =  #{PINCIZXSJ,jdbcType=VARCHAR},
            YIYUANSPBZ =  #{YIYUANSPBZ,jdbcType=DECIMAL},
            YIYUANSPLX =  #{YIYUANSPLX,jdbcType=DECIMAL},
            YIYUANSPSM =  #{YIYUANSPSM,jdbcType=VARCHAR},
            YIYUANSPBL =  #{YIYUANSPBL,jdbcType=VARCHAR},
            JIAOBANHDBZ =  #{JIAOBANHDBZ,jdbcType=VARCHAR},
            ZONGHEDXH =  #{ZONGHEDXH,jdbcType=VARCHAR},
            JIAOBANHDSJ =  #{JIAOBANHDSJ,jdbcType=TIMESTAMP},
            JIAOBANHDR =  #{JIAOBANHDR,jdbcType=VARCHAR},
            FUJIAFYJFFS =  #{FUJIAFYJFFS,jdbcType=VARCHAR},
            YUANDANGQZT =  #{YUANDANGQZT,jdbcType=VARCHAR},
            YUANYIZZT =  #{YUANYIZZT,jdbcType=VARCHAR},
            DONGJIEBZ =  #{DONGJIEBZ,jdbcType=DECIMAL},
            JIEDONGRQ =  #{JIEDONGRQ,jdbcType=TIMESTAMP},
            TUIYAOSPBZ =  #{TUIYAOSPBZ,jdbcType=DECIMAL},
            YAOPINJX =  #{YAOPINJX,jdbcType=VARCHAR},
            ZHIXINGRQ =  #{ZHIXINGRQ,jdbcType=VARCHAR},
            ZHIXINGZT =  #{ZHIXINGZT,jdbcType=DECIMAL},
            KAPIANDYBZ =  #{KAPIANDYBZ,jdbcType=VARCHAR},
            QUZHENGLX =  #{QUZHENGLX,jdbcType=VARCHAR},
            DONGJIERQ =  #{DONGJIERQ,jdbcType=TIMESTAMP},
            QINGJIABZ =  #{QINGJIABZ,jdbcType=DECIMAL},
            YIYUANSPRQ =  #{YIYUANSPRQ,jdbcType=TIMESTAMP},
            BIANGENGDDYBZ =  #{BIANGENGDDYBZ,jdbcType=VARCHAR},
            CHEXIAOLX =  #{CHEXIAOLX,jdbcType=VARCHAR},
            CHEXIAOYY =  #{CHEXIAOYY,jdbcType=VARCHAR},
            YUANZHIXSJ =  #{YUANZHIXSJ,jdbcType=TIMESTAMP},
            CHUYUANDYBZ =  #{CHUYUANDYBZ,jdbcType=DECIMAL},
            CHANGQILSBZ =  #{CHANGQILSBZ,jdbcType=DECIMAL},
            ZHIXINGBZ =  #{ZHIXINGBZ,jdbcType=DECIMAL},
            JIAOBANHDR2 =  #{JIAOBANHDR2,jdbcType=VARCHAR},
            GONGZUOZID =  #{GONGZUOZID,jdbcType=VARCHAR},
            CAOYAOTSYF =  #{CAOYAOTSYF,jdbcType=VARCHAR},
            KANGSHENGSSYSM =  #{KANGSHENGSSYSM,jdbcType=VARCHAR},
            SHENPIYS =  #{SHENPIYS,jdbcType=VARCHAR},
            SHENPINR =  #{SHENPINR,jdbcType=VARCHAR},
            DISHU =  #{DISHU,jdbcType=VARCHAR},
            BIGUANGBZ =  #{BIGUANGBZ,jdbcType=DECIMAL},
            ZHONGYAOPFKLBZ =  #{ZHONGYAOPFKLBZ,jdbcType=DECIMAL},
            KUCUNBZBZ =  #{KUCUNBZBZ,jdbcType=DECIMAL},
            BIANGENGHZDYBZ =  #{BIANGENGHZDYBZ,jdbcType=VARCHAR},
            CHENGZUYZID =  #{CHENGZUYZID,jdbcType=VARCHAR},
            YAOPINLDID =  #{YAOPINLDID,jdbcType=VARCHAR},
            ZHONGYAOKLZHSL =  #{ZHONGYAOKLZHSL,jdbcType=DECIMAL},
            KANGJUNYWSY1 =  #{KANGJUNYWSY1,jdbcType=VARCHAR},
            KANGJUNYWSY2 =  #{KANGJUNYWSY2,jdbcType=VARCHAR},
            KANGJUNYWSY3 =  #{KANGJUNYWSY3,jdbcType=VARCHAR},
            KANGJUNYWSY4 =  #{KANGJUNYWSY4,jdbcType=VARCHAR},
            KANGJUNYWSY5 =  #{KANGJUNYWSY5,jdbcType=VARCHAR},
            KANGJUNYWSY6 =  #{KANGJUNYWSY6,jdbcType=VARCHAR},
            KANGJUNYWSY7 =  #{KANGJUNYWSY7,jdbcType=VARCHAR},
            KANGJUNYWSY8 =  #{KANGJUNYWSY8,jdbcType=VARCHAR},
            KANGJUNYWSY9 =  #{KANGJUNYWSY9,jdbcType=VARCHAR},
            DULIFL =  #{DULIFL,jdbcType=VARCHAR},
            QITASX =  #{QITASX,jdbcType=VARCHAR},
            HUIZHENYZSPBZ =  #{HUIZHENYZSPBZ,jdbcType=DECIMAL},
            HUIZHENYZSPYS =  #{HUIZHENYZSPYS,jdbcType=VARCHAR},
            NONGJIANJBZ =  #{NONGJIANJBZ,jdbcType=DECIMAL},
            LINCHUANGLJDRBZ =  #{LINCHUANGLJDRBZ,jdbcType=DECIMAL},
            YAOWUSX =  #{YAOWUSX,jdbcType=DECIMAL},
            GAOFANGBZ =  #{GAOFANGBZ,jdbcType=DECIMAL},
            LINGYAORXM =  #{LINGYAORXM,jdbcType=VARCHAR},
            LINGYAORSFZH =  #{LINGYAORSFZH,jdbcType=VARCHAR},
            DUMAKH =  #{DUMAKH,jdbcType=VARCHAR},
            CHANDI =  #{CHANDI,jdbcType=VARCHAR},
            CHANDIMC =  #{CHANDIMC,jdbcType=VARCHAR},
            MIANFEIYPBZ =  #{MIANFEIYPBZ,jdbcType=DECIMAL},
            YUANJIESSJ =  #{YUANJIESSJ,jdbcType=TIMESTAMP},
            YUANTINGZR =  #{YUANTINGZR,jdbcType=VARCHAR},
            YUANTINGZBQ =  #{YUANTINGZBQ,jdbcType=VARCHAR},
            YUANTINGZSJ =  #{YUANTINGZSJ,jdbcType=TIMESTAMP},
            YIZHUJKID =  #{YIZHUJKID,jdbcType=VARCHAR},
            SHENFANGZT =  #{SHENFANGZT,jdbcType=VARCHAR},
            SHENFANGJG =  #{SHENFANGJG,jdbcType=VARCHAR},
            SHENFANGREN =  #{SHENFANGREN,jdbcType=VARCHAR},
            SHENFANGSJ =  #{SHENFANGSJ,jdbcType=TIMESTAMP},
            KANGJUNYWSY10 =  #{KANGJUNYWSY10,jdbcType=VARCHAR},
            KANGJUNYWSY11 =  #{KANGJUNYWSY11,jdbcType=VARCHAR},
            KANGJUNYWSY12 =  #{KANGJUNYWSY12,jdbcType=VARCHAR},
            KANGJUNYWSY13 =  #{KANGJUNYWSY13,jdbcType=VARCHAR},
            KANGJUNYWSY14 =  #{KANGJUNYWSY14,jdbcType=VARCHAR},
            KANGJUNYWSY15 =  #{KANGJUNYWSY15,jdbcType=VARCHAR},
            KANGJUNYWSY16 =  #{KANGJUNYWSY16,jdbcType=VARCHAR},
            KANGJUNYWSY17 =  #{KANGJUNYWSY17,jdbcType=VARCHAR},
            KANGJUNYWSY18 =  #{KANGJUNYWSY18,jdbcType=VARCHAR},
            KANGJUNYWSY19 =  #{KANGJUNYWSY19,jdbcType=VARCHAR},
            KANGJUNYWSY20 =  #{KANGJUNYWSY20,jdbcType=VARCHAR},
            ZHUZHIYS =  #{ZHUZHIYS,jdbcType=VARCHAR},
            TIAOPEIJL =  #{TIAOPEIJL,jdbcType=VARCHAR},
            CAOYAOFCFY =  #{CAOYAOFCFY,jdbcType=VARCHAR},
            JINGMAZD =  #{JINGMAZD,jdbcType=VARCHAR},
            YICHANGJGCLYJ =  #{YICHANGJGCLYJ,jdbcType=DECIMAL},
            YICHANGJGCLSJ =  #{YICHANGJGCLSJ,jdbcType=TIMESTAMP},
            YICHANGJGCLSM =  #{YICHANGJGCLSM,jdbcType=VARCHAR},
            YICHANGJGCLR =  #{YICHANGJGCLR,jdbcType=VARCHAR},
            JINGSHIYS =  #{JINGSHIYS,jdbcType=DECIMAL},
            BXSYZDBZ =  #{BXSYZDBZ,jdbcType=DECIMAL},
            YICHAKBG =  #{YICHAKBG,jdbcType=VARCHAR},
            LINCHUANGLJID =  #{LINCHUANGLJID,jdbcType=VARCHAR},
            TAOCANXSBRMXID =  #{TAOCANXSBRMXID,jdbcType=VARCHAR},
            TAOCANXSBZ =  #{TAOCANXSBZ,jdbcType=DECIMAL},
            XIEDINGBZ =  #{XIEDINGBZ,jdbcType=DECIMAL},
            XIEDINGCFID =  #{XIEDINGCFID,jdbcType=VARCHAR},
            DUMACFQM =  #{DUMACFQM,jdbcType=VARCHAR},
            DUMACFQMSJ =  #{DUMACFQMSJ,jdbcType=TIMESTAMP},
            KESHIFFBZ =  #{KESHIFFBZ,jdbcType=DECIMAL},
            DISHUDW =  #{DISHUDW,jdbcType=VARCHAR},
            KANGJUNYW_LCZDDM =  #{KANGJUNYW_LCZDDM,jdbcType=VARCHAR},
            KANGJUNYW_LCZDMC =  #{KANGJUNYW_LCZDMC,jdbcType=VARCHAR},
            KANGJUNYW_SFHZ =  #{KANGJUNYW_SFHZ,jdbcType=DECIMAL},
            KANGJUNYW_WSWSJ =  #{KANGJUNYW_WSWSJ,jdbcType=DECIMAL},
            KANGJUNYW_JJSY =  #{KANGJUNYW_JJSY,jdbcType=DECIMAL},
            CHEXIAOHSID =  #{CHEXIAOHSID,jdbcType=VARCHAR},
            DAORUDGJLBBZ =  #{DAORUDGJLBBZ,jdbcType=DECIMAL},
            PISHIPH =  #{PISHIPH,jdbcType=VARCHAR},
            CHEXIAOSHBZ =  #{CHEXIAOSHBZ,jdbcType=DECIMAL},
            CHEXIAOSHR =  #{CHEXIAOSHR,jdbcType=VARCHAR},
            CHEXIAOSHSJ =  #{CHEXIAOSHSJ,jdbcType=TIMESTAMP},
            SHUANGQIANMBZ =  #{SHUANGQIANMBZ,jdbcType=DECIMAL},
            PPDBZ =  #{PPDBZ,jdbcType=DECIMAL},
            PPDJG =  #{PPDJG,jdbcType=VARCHAR},
            PPDBWBZ =  #{PPDBWBZ,jdbcType=VARCHAR},
            HESUANKS =  #{HESUANKS,jdbcType=VARCHAR},
            HESUANKSMC =  #{HESUANKSMC,jdbcType=VARCHAR},
            YIZHUTJBZ =  #{YIZHUTJBZ,jdbcType=DECIMAL},
            YIZHUTJR =  #{YIZHUTJR,jdbcType=VARCHAR},
            YIZHUTJSJ =  #{YIZHUTJSJ,jdbcType=TIMESTAMP},
            CHEXIAOSQR =  #{CHEXIAOSQR,jdbcType=VARCHAR},
            CHEXIAOSQSJ =  #{CHEXIAOSQSJ,jdbcType=TIMESTAMP},
            KUOZHANXX =  #{KUOZHANXX,jdbcType=VARCHAR},
            BINGRENYLZ =  #{BINGRENYLZ,jdbcType=VARCHAR},
            YIZHUSDBZ =  #{YIZHUSDBZ,jdbcType=DECIMAL},
            YUANQIANSQDID =  #{YUANQIANSQDID,jdbcType=VARCHAR},
            DAOGUANJSBZ =  #{DAOGUANJSBZ,jdbcType=DECIMAL},
            DAOGUANJSR =  #{DAOGUANJSR,jdbcType=VARCHAR},
            DAOGUANJSSJ =  #{DAOGUANJSSJ,jdbcType=TIMESTAMP},
            YONGXUEBZ =  #{YONGXUEBZ,jdbcType=VARCHAR},
            YUANQIANDRBZ =  #{YUANQIANDRBZ,jdbcType=DECIMAL},
            ZHUANKEHCBZ =  #{ZHUANKEHCBZ,jdbcType=DECIMAL},
            GUANDAOBWID =  #{GUANDAOBWID,jdbcType=VARCHAR},
            GUANDAOBWMC =  #{GUANDAOBWMC,jdbcType=VARCHAR},
            GUANDAOFWID =  #{GUANDAOFWID,jdbcType=VARCHAR},
            GUANDAOFWMC =  #{GUANDAOFWMC,jdbcType=VARCHAR},
            JIAJIBZ =  #{JIAJIBZ,jdbcType=DECIMAL},
            ZIBEIBZ =  #{ZIBEIBZ,jdbcType=DECIMAL},
            DUOCHONGNYBZ =  #{DUOCHONGNYBZ,jdbcType=DECIMAL},
            HUANCHUANGHAO =  #{HUANCHUANGHAO,jdbcType=VARCHAR},
            ZHUANRUKS =  #{ZHUANRUKS,jdbcType=VARCHAR},
            ZHUANRUKSMC =  #{ZHUANRUKSMC,jdbcType=VARCHAR},
            GUANDAOLYID =  #{GUANDAOLYID,jdbcType=VARCHAR},
            GUANDAOLYMC =  #{GUANDAOLYMC,jdbcType=VARCHAR},
            PISHIGCSJ =  #{PISHIGCSJ,jdbcType=DECIMAL},
            PISHIKSSJ =  #{PISHIKSSJ,jdbcType=TIMESTAMP},
            SHOUSHUYZID =  #{SHOUSHUYZID,jdbcType=VARCHAR},
            ZHILIAOFA =  #{ZHILIAOFA,jdbcType=VARCHAR},
            ZHILIAOLY =  #{ZHILIAOLY,jdbcType=VARCHAR},
            XUETANGYZBZ =  #{XUETANGYZBZ,jdbcType=DECIMAL},
            XUSHENPIBZ =  #{XUSHENPIBZ,jdbcType=DECIMAL},
            SHENPIBZ =  #{SHENPIBZ,jdbcType=DECIMAL},
            SHENPIREN =  #{SHENPIREN,jdbcType=VARCHAR},
            SHENPISJ =  #{SHENPISJ,jdbcType=TIMESTAMP},
            BEIXUESQDID =  #{BEIXUESQDID,jdbcType=VARCHAR},
            BEIXUEYZID =  #{BEIXUEYZID,jdbcType=VARCHAR},
            HUSHIBZ =  #{HUSHIBZ,jdbcType=VARCHAR},
            ZHUANRUBQ =  #{ZHUANRUBQ,jdbcType=VARCHAR},
            ZHUANRUBQMC =  #{ZHUANRUBQMC,jdbcType=VARCHAR},
            GUANDAOID =  #{GUANDAOID,jdbcType=VARCHAR},
            TESHUYZBZ =  #{TESHUYZBZ,jdbcType=DECIMAL},
            SHUNXUHAO =  #{SHUNXUHAO,jdbcType=DECIMAL},
            ZHIQINGTYSZT =  #{ZHIQINGTYSZT,jdbcType=DECIMAL},
            YAOPINPH =  #{YAOPINPH,jdbcType=VARCHAR},
            GCPBZ =  #{GCPBZ,jdbcType=DECIMAL},
            WAIPEIBZ =  #{WAIPEIBZ,jdbcType=DECIMAL},
            ZENGPINBZ =  #{ZENGPINBZ,jdbcType=DECIMAL},
            TPNYZBZ =  #{TPNYZBZ,jdbcType=DECIMAL},
            PPDYIZHUID =  #{PPDYIZHUID,jdbcType=VARCHAR},
            SHUQIANYZBZ =  #{SHUQIANYZBZ,jdbcType=DECIMAL},
            QIANMINGBZ =  #{QIANMINGBZ,jdbcType=DECIMAL},
            SHENFANGSQM =  #{SHENFANGSQM,jdbcType=VARCHAR},
            SHENFANGSQMSJ =  #{SHENFANGSQMSJ,jdbcType=TIMESTAMP},
            YONGYAOYY =  #{YONGYAOYY,jdbcType=VARCHAR},
            SHUQIANYZSSMC =  #{SHUQIANYZSSMC,jdbcType=VARCHAR},
            KANGJUNYWSHBZ =  #{KANGJUNYWSHBZ,jdbcType=VARCHAR},
            MAZUIBZ =  #{MAZUIBZ,jdbcType=DECIMAL},
            SHUQIANYZSSID =  #{SHUQIANYZSSID,jdbcType=VARCHAR},
            YIZHULX =  #{YIZHULX,jdbcType=DECIMAL},
            YAOPINJJBZ =  #{YAOPINJJBZ,jdbcType=DECIMAL},
            KANGJUNYWQM =  #{KANGJUNYWQM,jdbcType=VARCHAR},
            KANGJUNYWQMSJ =  #{KANGJUNYWQMSJ,jdbcType=TIMESTAMP},
            GUANDAOMC =  #{GUANDAOMC,jdbcType=VARCHAR},
            GUOMINYFL =  #{GUOMINYFL,jdbcType=VARCHAR},
            YUTINGSJ =  #{YUTINGSJ,jdbcType=TIMESTAMP},
            CHUYUANZDID =  #{CHUYUANZDID,jdbcType=VARCHAR},
            CHUYUANZDMC =  #{CHUYUANZDMC,jdbcType=VARCHAR},
            ZHONGYIZD =  #{ZHONGYIZD,jdbcType=VARCHAR},
            ZHUANRUYLZ =  #{ZHUANRUYLZ,jdbcType=VARCHAR},
            ZHONGYIZHID =  #{ZHONGYIZHID,jdbcType=VARCHAR},
            FANGSHIZQ =  #{FANGSHIZQ,jdbcType=VARCHAR},
            SHIYONGFW =  #{SHIYONGFW,jdbcType=VARCHAR},
            FUHESJYH =  #{FUHESJYH,jdbcType=VARCHAR},
            FUHERENYH =  #{FUHERENYH,jdbcType=TIMESTAMP},
            BADIANJCNR =  #{BADIANJCNR,jdbcType=VARCHAR},
            BADIANJCJG =  #{BADIANJCJG,jdbcType=VARCHAR},
            SHOUSHUT =  #{SHOUSHUT,jdbcType=VARCHAR},
            KAPIANLX =  #{KAPIANLX,jdbcType=VARCHAR},
            DAGUIGCD =  #{DAGUIGCD,jdbcType=VARCHAR},
            DAGUIGCDMC =  #{DAGUIGCDMC,jdbcType=VARCHAR},
            DAGUIGBZL =  #{DAGUIGBZL,jdbcType=DECIMAL},
            BAIZHUANGDW =  #{BAIZHUANGDW,jdbcType=VARCHAR},
            TIJIDW =  #{TIJIDW,jdbcType=VARCHAR},
            TIJI =  #{TIJI,jdbcType=VARCHAR},
            DAGUIGJGID =  #{DAGUIGJGID,jdbcType=VARCHAR},
            DAGUIGID =  #{DAGUIGID,jdbcType=VARCHAR},
            YIZHUXMLX =  #{YIZHUXMLX,jdbcType=VARCHAR},
            JIEDUANID =  #{JIEDUANID,jdbcType=VARCHAR},
            BINGRENLJID =  #{BINGRENLJID,jdbcType=VARCHAR},
            QIANGJIUBZ =  #{QIANGJIUBZ,jdbcType=DECIMAL},
            APIANLX =  #{APIANLX,jdbcType=VARCHAR},
            SHENGFANGSQMYY =  #{SHENGFANGSQMYY,jdbcType=VARCHAR},
            SHENGFANGSQMSJ =  #{SHENGFANGSQMSJ,jdbcType=VARCHAR},
            SHENGFANGSQMMC =  #{SHENGFANGSQMMC,jdbcType=VARCHAR},
            SHENGFANGSQM =  #{SHENGFANGSQM,jdbcType=VARCHAR},
            FEIYONGKZBZ =  #{FEIYONGKZBZ,jdbcType=DECIMAL},
            WAIPEIBABH =  #{WAIPEIBABH,jdbcType=VARCHAR},
            GAOFANGJX =  #{GAOFANGJX,jdbcType=VARCHAR},
            GAOFANGFLXX =  #{GAOFANGFLXX,jdbcType=VARCHAR},
            MEIRIJL =  #{MEIRIJL,jdbcType=VARCHAR},
            JIANYAOFF =  #{JIANYAOFF,jdbcType=VARCHAR},
            FUYAOCS =  #{FUYAOCS,jdbcType=VARCHAR},
            FUYAOFF =  #{FUYAOFF,jdbcType=VARCHAR},
            TONGSHUOYZID =  #{TONGSHUOYZID,jdbcType=VARCHAR},
            YIBAODJ =  #{YIBAODJ,jdbcType=VARCHAR},
            WAIPEISFR =  #{WAIPEISFR,jdbcType=VARCHAR},
            WAIPEISFSJ =  #{WAIPEISFSJ,jdbcType=TIMESTAMP},
            SHENGLIZQ =  #{SHENGLIZQ,jdbcType=VARCHAR}
        where   YIZHUID = #{YIZHUID,jdbcType=VARCHAR} 
    </update>
</mapper>
