package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 患者诊疗信息请求对象
 * 对应 ApifoxModel 中的 PatientDiagnosisInfoReq 结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientDiagnosisInfoReq {
    /**
     * 床位号
     */
    private String bedNo;
    
    /**
     * 开立时间
     */
    private String doctorAdviceCreateTime;
    
    /**
     * 医嘱名称
     */
    private String doctorAdviceName;
    
    /**
     * 医嘱状态
     */
    private String doctorAdviceStatus;
    
    /**
     * 医嘱类型
     */
    private String doctorAdviceType;
    
    /**
     * 医生嘱托
     */
    private String doctorInstruction;
    
    /**
     * 病人ID
     */
    private Long idNumber;
    
    /**
     * 显示状态(0:未完成,1:已完成)
     */
    private Long isFinish;
    
    /**
     * 病人名称
     */
    private String patientName;
    
    /**
     * 计划执行时间
     */
    private String planExecTime;

    /**
     * 诊疗地点
     */
    private String diagnosisLocation;

    /**
     * 院区ID
     */
    private Long wardCode;
    
    /**
     * 住院号
     */
    private String zyh;
}
