package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 公用_床位
 * @TableName GY_CHUANGWEI
 */
@TableName(value ="GY_CHUANGWEI")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GyChuangwei implements Serializable {
    /**
     * 床位IDHR3-32668(319040)
     */
    @TableId(value = "CHUANGWEIID")
    private String CHUANGWEIID;

    /**
     * 病区ID
     */
    @TableField(value = "BINGQUID")
    private String BINGQUID;

    /**
     * 院区ID
     */
    @TableField(value = "YUANQUID")
    private String YUANQUID;

    /**
     * 科室ID
     */
    @TableField(value = "KESHIID")
    private String KESHIID;

    /**
     * 房间ID
     */
    @TableField(value = "FANGJIANID")
    private String FANGJIANID;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 床位状态空床	0/分配	1/借床	2/换床	3/转床	4/包床	5/包房	6/预分配	7/预留分配	8/占床	9/冻结	10/急诊优先	11
     */
    @TableField(value = "CHUANGWEIZT")
    private String CHUANGWEIZT;

    /**
     * 收费项目
     */
    @TableField(value = "SHOUFEIXM")
    private String SHOUFEIXM;

    /**
     * 分组代码
     */
    @TableField(value = "FENZUDM")
    private String FENZUDM;

    /**
     * 床位类型
     */
    @TableField(value = "CHUANGWEILX")
    private String CHUANGWEILX;

    /**
     * 编制分类
     */
    @TableField(value = "BIANZHIFL")
    private String BIANZHIFL;

    /**
     * 病人标志
     */
    @TableField(value = "BINGRENBZ")
    private String BINGRENBZ;

    /**
     * 主治医师
     */
    @TableField(value = "ZHUZHIYS")
    private String ZHUZHIYS;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private Date XIUGAISJ;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 床位等级
     */
    @TableField(value = "CHUANGWEIDJ")
    private String CHUANGWEIDJ;

    /**
     * 占床标志
     */
    @TableField(value = "ZHANCHUANGBZ")
    private Integer ZHANCHUANGBZ;

    /**
     * 套餐ID
     */
    @TableField(value = "CHUANGWEITCID")
    private String CHUANGWEITCID;

    /**
     * 预约状态HR3-14491(183963)
     */
    @TableField(value = "YUYUEZT")
    private String YUYUEZT;

    /**
     * 预约病人IDHR3-14491(183963)
     */
    @TableField(value = "YUYUEBRID")
    private String YUYUEBRID;

    /**
     * 顺序号HR3-15205(191889) 
     */
    @TableField(value = "SHUNXUHAO")
    private Integer SHUNXUHAO;

    /**
     * 陪轮椅标志 默认1：有 0：无
     */
    @TableField(value = "PEILUNYBZ")
    private Integer PEILUNYBZ;

    /**
     * 床位分区HR3-39344(359082)
     */
    @TableField(value = "CHUANGWEIFQ")
    private String CHUANGWEIFQ;

    /**
     * 未离标志HR3-50434(421109)
     */
    @TableField(value = "WEILIBZ")
    private Integer WEILIBZ;

    /**
     * 未离设置人HR3-50434(421109)
     */
    @TableField(value = "WEILICZR")
    private String WEILICZR;

    /**
     * 未离设置日期HR3-50434(421109)
     */
    @TableField(value = "WEILICZRQ")
    private Date WEILICZRQ;

    /**
     * 中心设置占床标志HR3-50434(421109)
     */
    @TableField(value = "ZHONGXINSZZCBZ")
    private Integer ZHONGXINSZZCBZ;

    /**
     * 中心设置占床人HR3-50434(421109)
     */
    @TableField(value = "ZHONGXINSZZCR")
    private String ZHONGXINSZZCR;

    /**
     * 中心设置占床日期HR3-50434(421109)
     */
    @TableField(value = "ZHONGXINSZZCRQ")
    private Date ZHONGXINSZZCRQ;

    /**
     * 中心设置假离开HR3-50434(421109)
     */
    @TableField(value = "ZHONGXINSZJLKBZ")
    private Integer ZHONGXINSZJLKBZ;

    /**
     * 中心设置假离开操作员HR3-50434(421109)
     */
    @TableField(value = "ZHONGXINSZJLKCZR")
    private String ZHONGXINSZJLKCZR;

    /**
     * 中心设置假离开操作日期HR3-50434(421109)
     */
    @TableField(value = "ZHONGXINSZJLKCZRQ")
    private Date ZHONGXINSZJLKCZRQ;

    /**
     * 院前准备中心冻结床位时的备注HOCR3841 
     */
    @TableField(value = "BEIZHU")
    private String BEIZHU;

    /**
     * 今日明日出院床位预约病人ID
     */
    @TableField(value = "JINRIMRYUYUEBRID")
    private String JINRIMRYUYUEBRID;

    /**
     * 今日名词 出院床位预约标注0或空表示未预约，1表示预约,2表示在院床位冻结
     */
    @TableField(value = "JINRIMRYYBZ")
    private Integer JINRIMRYYBZ;

    /**
     * 院前准备中心备注注意事项的床位
     */
    @TableField(value = "ZHUYISX")
    private String ZHUYISX;

    /**
     * 院前标志
     */
    @TableField(value = "YUANQIANBZ")
    private String YUANQIANBZ;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}