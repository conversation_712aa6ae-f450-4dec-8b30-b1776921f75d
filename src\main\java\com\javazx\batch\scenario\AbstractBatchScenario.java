package com.javazx.batch.scenario;

import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;

/**
 * 批处理场景抽象基类
 * 提供通用的场景实现
 *
 */
public abstract class AbstractBatchScenario<I, O> implements BatchScenario {
    
    protected String scenarioName;
    protected String scenarioDescription;
    protected int commitInterval = 1000;
    protected int pageSize = 100;
    protected boolean enabled = true;
    
    public AbstractBatchScenario(String scenarioName, String scenarioDescription) {
        this.scenarioName = scenarioName;
        this.scenarioDescription = scenarioDescription;
    }
    
    @Override
    public String getScenarioName() {
        return scenarioName;
    }
    
    @Override
    public String getScenarioDescription() {
        return scenarioDescription;
    }
    
    @Override
    public String getReaderBeanName() {
        return scenarioName + "Reader";
    }
    
    @Override
    public String getProcessorBeanName() {
        return scenarioName + "Processor";
    }
    
    @Override
    public String getWriterBeanName() {
        return scenarioName + "Writer";
    }
    
    @Override
    public String getJobName() {
        return scenarioName + "Job";
    }
    
    @Override
    public String getStepName() {
        return scenarioName + "Step";
    }
    
    @Override
    public int getCommitInterval() {
        return commitInterval;
    }
    
    @Override
    public int getPageSize() {
        return pageSize;
    }
    
    @Override
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 创建读取器
     * @return ItemReader实例
     */
    public abstract ItemReader<I> createReader();
    
    /**
     * 创建处理器
     * @return ItemProcessor实例
     */
    public abstract ItemProcessor<I, O> createProcessor();
    
    /**
     * 创建写入器
     * @return ItemWriter实例
     */
    public abstract ItemWriter<O> createWriter();
    
    /**
     * 设置提交间隔
     */
    public void setCommitInterval(int commitInterval) {
        this.commitInterval = commitInterval;
    }
    
    /**
     * 设置页面大小
     */
    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
    
    /**
     * 设置是否启用
     */
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
}
