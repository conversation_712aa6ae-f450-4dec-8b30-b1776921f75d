<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.GyChuangweiMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.GyChuangwei">
            <id property="CHUANGWEIID" column="CHUANGWEIID" jdbcType="VARCHAR"/>
            <id property="BINGQUID" column="BINGQUID" jdbcType="VARCHAR"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="VARCHAR"/>
            <result property="KESHIID" column="KESHIID" jdbcType="VARCHAR"/>
            <result property="FANGJIANID" column="FANGJIANID" jdbcType="VARCHAR"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="CHUANGWEIZT" column="CHUANGWEIZT" jdbcType="VARCHAR"/>
            <result property="SHOUFEIXM" column="SHOUFEIXM" jdbcType="VARCHAR"/>
            <result property="FENZUDM" column="FENZUDM" jdbcType="VARCHAR"/>
            <result property="CHUANGWEILX" column="CHUANGWEILX" jdbcType="VARCHAR"/>
            <result property="BIANZHIFL" column="BIANZHIFL" jdbcType="VARCHAR"/>
            <result property="BINGRENBZ" column="BINGRENBZ" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYS" column="ZHUZHIYS" jdbcType="VARCHAR"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="CHUANGWEIDJ" column="CHUANGWEIDJ" jdbcType="VARCHAR"/>
            <result property="ZHANCHUANGBZ" column="ZHANCHUANGBZ" jdbcType="DECIMAL"/>
            <result property="CHUANGWEITCID" column="CHUANGWEITCID" jdbcType="VARCHAR"/>
            <result property="YUYUEZT" column="YUYUEZT" jdbcType="VARCHAR"/>
            <result property="YUYUEBRID" column="YUYUEBRID" jdbcType="VARCHAR"/>
            <result property="SHUNXUHAO" column="SHUNXUHAO" jdbcType="DECIMAL"/>
            <result property="PEILUNYBZ" column="PEILUNYBZ" jdbcType="DECIMAL"/>
            <result property="CHUANGWEIFQ" column="CHUANGWEIFQ" jdbcType="VARCHAR"/>
            <result property="WEILIBZ" column="WEILIBZ" jdbcType="DECIMAL"/>
            <result property="WEILICZR" column="WEILICZR" jdbcType="VARCHAR"/>
            <result property="WEILICZRQ" column="WEILICZRQ" jdbcType="TIMESTAMP"/>
            <result property="ZHONGXINSZZCBZ" column="ZHONGXINSZZCBZ" jdbcType="DECIMAL"/>
            <result property="ZHONGXINSZZCR" column="ZHONGXINSZZCR" jdbcType="VARCHAR"/>
            <result property="ZHONGXINSZZCRQ" column="ZHONGXINSZZCRQ" jdbcType="TIMESTAMP"/>
            <result property="ZHONGXINSZJLKBZ" column="ZHONGXINSZJLKBZ" jdbcType="DECIMAL"/>
            <result property="ZHONGXINSZJLKCZR" column="ZHONGXINSZJLKCZR" jdbcType="VARCHAR"/>
            <result property="ZHONGXINSZJLKCZRQ" column="ZHONGXINSZJLKCZRQ" jdbcType="TIMESTAMP"/>
            <result property="BEIZHU" column="BEIZHU" jdbcType="VARCHAR"/>
            <result property="JINRIMRYUYUEBRID" column="JINRIMRYUYUEBRID" jdbcType="VARCHAR"/>
            <result property="JINRIMRYYBZ" column="JINRIMRYYBZ" jdbcType="DECIMAL"/>
            <result property="ZHUYISX" column="ZHUYISX" jdbcType="VARCHAR"/>
            <result property="YUANQIANBZ" column="YUANQIANBZ" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHUANGWEIID,BINGQUID,YUANQUID,
        KESHIID,FANGJIANID,BINGRENZYID,
        CHUANGWEIZT,SHOUFEIXM,FENZUDM,
        CHUANGWEILX,BIANZHIFL,BINGRENBZ,
        ZHUZHIYS,XIUGAIREN,XIUGAISJ,
        ZUOFEIBZ,CHUANGWEIDJ,ZHANCHUANGBZ,
        CHUANGWEITCID,YUYUEZT,YUYUEBRID,
        SHUNXUHAO,PEILUNYBZ,CHUANGWEIFQ,
        WEILIBZ,WEILICZR,WEILICZRQ,
        ZHONGXINSZZCBZ,ZHONGXINSZZCR,ZHONGXINSZZCRQ,
        ZHONGXINSZJLKBZ,ZHONGXINSZJLKCZR,ZHONGXINSZJLKCZRQ,
        BEIZHU,JINRIMRYUYUEBRID,JINRIMRYYBZ,
        ZHUYISX,YUANQIANBZ
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from GY_CHUANGWEI
        where  CHUANGWEIID = #{CHUANGWEIID,jdbcType=VARCHAR} AND BINGQUID = #{BINGQUID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from GY_CHUANGWEI
        where  CHUANGWEIID = #{CHUANGWEIID,jdbcType=VARCHAR} AND BINGQUID = #{BINGQUID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert">
        insert into GY_CHUANGWEI
        ( CHUANGWEIID,BINGQUID,YUANQUID
        ,KESHIID,FANGJIANID,BINGRENZYID
        ,CHUANGWEIZT,SHOUFEIXM,FENZUDM
        ,CHUANGWEILX,BIANZHIFL,BINGRENBZ
        ,ZHUZHIYS,XIUGAIREN,XIUGAISJ
        ,ZUOFEIBZ,CHUANGWEIDJ,ZHANCHUANGBZ
        ,CHUANGWEITCID,YUYUEZT,YUYUEBRID
        ,SHUNXUHAO,PEILUNYBZ,CHUANGWEIFQ
        ,WEILIBZ,WEILICZR,WEILICZRQ
        ,ZHONGXINSZZCBZ,ZHONGXINSZZCR,ZHONGXINSZZCRQ
        ,ZHONGXINSZJLKBZ,ZHONGXINSZJLKCZR,ZHONGXINSZJLKCZRQ
        ,BEIZHU,JINRIMRYUYUEBRID,JINRIMRYYBZ
        ,ZHUYISX,YUANQIANBZ)
        values (#{CHUANGWEIID,jdbcType=VARCHAR},#{BINGQUID,jdbcType=VARCHAR},#{YUANQUID,jdbcType=VARCHAR}
        ,#{KESHIID,jdbcType=VARCHAR},#{FANGJIANID,jdbcType=VARCHAR},#{BINGRENZYID,jdbcType=VARCHAR}
        ,#{CHUANGWEIZT,jdbcType=VARCHAR},#{SHOUFEIXM,jdbcType=VARCHAR},#{FENZUDM,jdbcType=VARCHAR}
        ,#{CHUANGWEILX,jdbcType=VARCHAR},#{BIANZHIFL,jdbcType=VARCHAR},#{BINGRENBZ,jdbcType=VARCHAR}
        ,#{ZHUZHIYS,jdbcType=VARCHAR},#{XIUGAIREN,jdbcType=VARCHAR},#{XIUGAISJ,jdbcType=TIMESTAMP}
        ,#{ZUOFEIBZ,jdbcType=DECIMAL},#{CHUANGWEIDJ,jdbcType=VARCHAR},#{ZHANCHUANGBZ,jdbcType=DECIMAL}
        ,#{CHUANGWEITCID,jdbcType=VARCHAR},#{YUYUEZT,jdbcType=VARCHAR},#{YUYUEBRID,jdbcType=VARCHAR}
        ,#{SHUNXUHAO,jdbcType=DECIMAL},#{PEILUNYBZ,jdbcType=DECIMAL},#{CHUANGWEIFQ,jdbcType=VARCHAR}
        ,#{WEILIBZ,jdbcType=DECIMAL},#{WEILICZR,jdbcType=VARCHAR},#{WEILICZRQ,jdbcType=TIMESTAMP}
        ,#{ZHONGXINSZZCBZ,jdbcType=DECIMAL},#{ZHONGXINSZZCR,jdbcType=VARCHAR},#{ZHONGXINSZZCRQ,jdbcType=TIMESTAMP}
        ,#{ZHONGXINSZJLKBZ,jdbcType=DECIMAL},#{ZHONGXINSZJLKCZR,jdbcType=VARCHAR},#{ZHONGXINSZJLKCZRQ,jdbcType=TIMESTAMP}
        ,#{BEIZHU,jdbcType=VARCHAR},#{JINRIMRYUYUEBRID,jdbcType=VARCHAR},#{JINRIMRYYBZ,jdbcType=DECIMAL}
        ,#{ZHUYISX,jdbcType=VARCHAR},#{YUANQIANBZ,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective">
        insert into GY_CHUANGWEI
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="CHUANGWEIID != null">CHUANGWEIID,</if>
                <if test="BINGQUID != null">BINGQUID,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
                <if test="KESHIID != null">KESHIID,</if>
                <if test="FANGJIANID != null">FANGJIANID,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="CHUANGWEIZT != null">CHUANGWEIZT,</if>
                <if test="SHOUFEIXM != null">SHOUFEIXM,</if>
                <if test="FENZUDM != null">FENZUDM,</if>
                <if test="CHUANGWEILX != null">CHUANGWEILX,</if>
                <if test="BIANZHIFL != null">BIANZHIFL,</if>
                <if test="BINGRENBZ != null">BINGRENBZ,</if>
                <if test="ZHUZHIYS != null">ZHUZHIYS,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="CHUANGWEIDJ != null">CHUANGWEIDJ,</if>
                <if test="ZHANCHUANGBZ != null">ZHANCHUANGBZ,</if>
                <if test="CHUANGWEITCID != null">CHUANGWEITCID,</if>
                <if test="YUYUEZT != null">YUYUEZT,</if>
                <if test="YUYUEBRID != null">YUYUEBRID,</if>
                <if test="SHUNXUHAO != null">SHUNXUHAO,</if>
                <if test="PEILUNYBZ != null">PEILUNYBZ,</if>
                <if test="CHUANGWEIFQ != null">CHUANGWEIFQ,</if>
                <if test="WEILIBZ != null">WEILIBZ,</if>
                <if test="WEILICZR != null">WEILICZR,</if>
                <if test="WEILICZRQ != null">WEILICZRQ,</if>
                <if test="ZHONGXINSZZCBZ != null">ZHONGXINSZZCBZ,</if>
                <if test="ZHONGXINSZZCR != null">ZHONGXINSZZCR,</if>
                <if test="ZHONGXINSZZCRQ != null">ZHONGXINSZZCRQ,</if>
                <if test="ZHONGXINSZJLKBZ != null">ZHONGXINSZJLKBZ,</if>
                <if test="ZHONGXINSZJLKCZR != null">ZHONGXINSZJLKCZR,</if>
                <if test="ZHONGXINSZJLKCZRQ != null">ZHONGXINSZJLKCZRQ,</if>
                <if test="BEIZHU != null">BEIZHU,</if>
                <if test="JINRIMRYUYUEBRID != null">JINRIMRYUYUEBRID,</if>
                <if test="JINRIMRYYBZ != null">JINRIMRYYBZ,</if>
                <if test="ZHUYISX != null">ZHUYISX,</if>
                <if test="YUANQIANBZ != null">YUANQIANBZ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="CHUANGWEIID != null">#{CHUANGWEIID,jdbcType=VARCHAR},</if>
                <if test="BINGQUID != null">#{BINGQUID,jdbcType=VARCHAR},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=VARCHAR},</if>
                <if test="KESHIID != null">#{KESHIID,jdbcType=VARCHAR},</if>
                <if test="FANGJIANID != null">#{FANGJIANID,jdbcType=VARCHAR},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="CHUANGWEIZT != null">#{CHUANGWEIZT,jdbcType=VARCHAR},</if>
                <if test="SHOUFEIXM != null">#{SHOUFEIXM,jdbcType=VARCHAR},</if>
                <if test="FENZUDM != null">#{FENZUDM,jdbcType=VARCHAR},</if>
                <if test="CHUANGWEILX != null">#{CHUANGWEILX,jdbcType=VARCHAR},</if>
                <if test="BIANZHIFL != null">#{BIANZHIFL,jdbcType=VARCHAR},</if>
                <if test="BINGRENBZ != null">#{BINGRENBZ,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYS != null">#{ZHUZHIYS,jdbcType=VARCHAR},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="CHUANGWEIDJ != null">#{CHUANGWEIDJ,jdbcType=VARCHAR},</if>
                <if test="ZHANCHUANGBZ != null">#{ZHANCHUANGBZ,jdbcType=DECIMAL},</if>
                <if test="CHUANGWEITCID != null">#{CHUANGWEITCID,jdbcType=VARCHAR},</if>
                <if test="YUYUEZT != null">#{YUYUEZT,jdbcType=VARCHAR},</if>
                <if test="YUYUEBRID != null">#{YUYUEBRID,jdbcType=VARCHAR},</if>
                <if test="SHUNXUHAO != null">#{SHUNXUHAO,jdbcType=DECIMAL},</if>
                <if test="PEILUNYBZ != null">#{PEILUNYBZ,jdbcType=DECIMAL},</if>
                <if test="CHUANGWEIFQ != null">#{CHUANGWEIFQ,jdbcType=VARCHAR},</if>
                <if test="WEILIBZ != null">#{WEILIBZ,jdbcType=DECIMAL},</if>
                <if test="WEILICZR != null">#{WEILICZR,jdbcType=VARCHAR},</if>
                <if test="WEILICZRQ != null">#{WEILICZRQ,jdbcType=TIMESTAMP},</if>
                <if test="ZHONGXINSZZCBZ != null">#{ZHONGXINSZZCBZ,jdbcType=DECIMAL},</if>
                <if test="ZHONGXINSZZCR != null">#{ZHONGXINSZZCR,jdbcType=VARCHAR},</if>
                <if test="ZHONGXINSZZCRQ != null">#{ZHONGXINSZZCRQ,jdbcType=TIMESTAMP},</if>
                <if test="ZHONGXINSZJLKBZ != null">#{ZHONGXINSZJLKBZ,jdbcType=DECIMAL},</if>
                <if test="ZHONGXINSZJLKCZR != null">#{ZHONGXINSZJLKCZR,jdbcType=VARCHAR},</if>
                <if test="ZHONGXINSZJLKCZRQ != null">#{ZHONGXINSZJLKCZRQ,jdbcType=TIMESTAMP},</if>
                <if test="BEIZHU != null">#{BEIZHU,jdbcType=VARCHAR},</if>
                <if test="JINRIMRYUYUEBRID != null">#{JINRIMRYUYUEBRID,jdbcType=VARCHAR},</if>
                <if test="JINRIMRYYBZ != null">#{JINRIMRYYBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUYISX != null">#{ZHUYISX,jdbcType=VARCHAR},</if>
                <if test="YUANQIANBZ != null">#{YUANQIANBZ,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.GyChuangwei">
        update GY_CHUANGWEI
        <set>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=VARCHAR},
                </if>
                <if test="KESHIID != null">
                    KESHIID = #{KESHIID,jdbcType=VARCHAR},
                </if>
                <if test="FANGJIANID != null">
                    FANGJIANID = #{FANGJIANID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGWEIZT != null">
                    CHUANGWEIZT = #{CHUANGWEIZT,jdbcType=VARCHAR},
                </if>
                <if test="SHOUFEIXM != null">
                    SHOUFEIXM = #{SHOUFEIXM,jdbcType=VARCHAR},
                </if>
                <if test="FENZUDM != null">
                    FENZUDM = #{FENZUDM,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGWEILX != null">
                    CHUANGWEILX = #{CHUANGWEILX,jdbcType=VARCHAR},
                </if>
                <if test="BIANZHIFL != null">
                    BIANZHIFL = #{BIANZHIFL,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENBZ != null">
                    BINGRENBZ = #{BINGRENBZ,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYS != null">
                    ZHUZHIYS = #{ZHUZHIYS,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHUANGWEIDJ != null">
                    CHUANGWEIDJ = #{CHUANGWEIDJ,jdbcType=VARCHAR},
                </if>
                <if test="ZHANCHUANGBZ != null">
                    ZHANCHUANGBZ = #{ZHANCHUANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHUANGWEITCID != null">
                    CHUANGWEITCID = #{CHUANGWEITCID,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEZT != null">
                    YUYUEZT = #{YUYUEZT,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEBRID != null">
                    YUYUEBRID = #{YUYUEBRID,jdbcType=VARCHAR},
                </if>
                <if test="SHUNXUHAO != null">
                    SHUNXUHAO = #{SHUNXUHAO,jdbcType=DECIMAL},
                </if>
                <if test="PEILUNYBZ != null">
                    PEILUNYBZ = #{PEILUNYBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHUANGWEIFQ != null">
                    CHUANGWEIFQ = #{CHUANGWEIFQ,jdbcType=VARCHAR},
                </if>
                <if test="WEILIBZ != null">
                    WEILIBZ = #{WEILIBZ,jdbcType=DECIMAL},
                </if>
                <if test="WEILICZR != null">
                    WEILICZR = #{WEILICZR,jdbcType=VARCHAR},
                </if>
                <if test="WEILICZRQ != null">
                    WEILICZRQ = #{WEILICZRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHONGXINSZZCBZ != null">
                    ZHONGXINSZZCBZ = #{ZHONGXINSZZCBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHONGXINSZZCR != null">
                    ZHONGXINSZZCR = #{ZHONGXINSZZCR,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGXINSZZCRQ != null">
                    ZHONGXINSZZCRQ = #{ZHONGXINSZZCRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHONGXINSZJLKBZ != null">
                    ZHONGXINSZJLKBZ = #{ZHONGXINSZJLKBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHONGXINSZJLKCZR != null">
                    ZHONGXINSZJLKCZR = #{ZHONGXINSZJLKCZR,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGXINSZJLKCZRQ != null">
                    ZHONGXINSZJLKCZRQ = #{ZHONGXINSZJLKCZRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="BEIZHU != null">
                    BEIZHU = #{BEIZHU,jdbcType=VARCHAR},
                </if>
                <if test="JINRIMRYUYUEBRID != null">
                    JINRIMRYUYUEBRID = #{JINRIMRYUYUEBRID,jdbcType=VARCHAR},
                </if>
                <if test="JINRIMRYYBZ != null">
                    JINRIMRYYBZ = #{JINRIMRYYBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUYISX != null">
                    ZHUYISX = #{ZHUYISX,jdbcType=VARCHAR},
                </if>
                <if test="YUANQIANBZ != null">
                    YUANQIANBZ = #{YUANQIANBZ,jdbcType=VARCHAR},
                </if>
        </set>
        where   CHUANGWEIID = #{CHUANGWEIID,jdbcType=VARCHAR} AND BINGQUID = #{BINGQUID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.GyChuangwei">
        update GY_CHUANGWEI
        set 
            YUANQUID =  #{YUANQUID,jdbcType=VARCHAR},
            KESHIID =  #{KESHIID,jdbcType=VARCHAR},
            FANGJIANID =  #{FANGJIANID,jdbcType=VARCHAR},
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            CHUANGWEIZT =  #{CHUANGWEIZT,jdbcType=VARCHAR},
            SHOUFEIXM =  #{SHOUFEIXM,jdbcType=VARCHAR},
            FENZUDM =  #{FENZUDM,jdbcType=VARCHAR},
            CHUANGWEILX =  #{CHUANGWEILX,jdbcType=VARCHAR},
            BIANZHIFL =  #{BIANZHIFL,jdbcType=VARCHAR},
            BINGRENBZ =  #{BINGRENBZ,jdbcType=VARCHAR},
            ZHUZHIYS =  #{ZHUZHIYS,jdbcType=VARCHAR},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            CHUANGWEIDJ =  #{CHUANGWEIDJ,jdbcType=VARCHAR},
            ZHANCHUANGBZ =  #{ZHANCHUANGBZ,jdbcType=DECIMAL},
            CHUANGWEITCID =  #{CHUANGWEITCID,jdbcType=VARCHAR},
            YUYUEZT =  #{YUYUEZT,jdbcType=VARCHAR},
            YUYUEBRID =  #{YUYUEBRID,jdbcType=VARCHAR},
            SHUNXUHAO =  #{SHUNXUHAO,jdbcType=DECIMAL},
            PEILUNYBZ =  #{PEILUNYBZ,jdbcType=DECIMAL},
            CHUANGWEIFQ =  #{CHUANGWEIFQ,jdbcType=VARCHAR},
            WEILIBZ =  #{WEILIBZ,jdbcType=DECIMAL},
            WEILICZR =  #{WEILICZR,jdbcType=VARCHAR},
            WEILICZRQ =  #{WEILICZRQ,jdbcType=TIMESTAMP},
            ZHONGXINSZZCBZ =  #{ZHONGXINSZZCBZ,jdbcType=DECIMAL},
            ZHONGXINSZZCR =  #{ZHONGXINSZZCR,jdbcType=VARCHAR},
            ZHONGXINSZZCRQ =  #{ZHONGXINSZZCRQ,jdbcType=TIMESTAMP},
            ZHONGXINSZJLKBZ =  #{ZHONGXINSZJLKBZ,jdbcType=DECIMAL},
            ZHONGXINSZJLKCZR =  #{ZHONGXINSZJLKCZR,jdbcType=VARCHAR},
            ZHONGXINSZJLKCZRQ =  #{ZHONGXINSZJLKCZRQ,jdbcType=TIMESTAMP},
            BEIZHU =  #{BEIZHU,jdbcType=VARCHAR},
            JINRIMRYUYUEBRID =  #{JINRIMRYUYUEBRID,jdbcType=VARCHAR},
            JINRIMRYYBZ =  #{JINRIMRYYBZ,jdbcType=DECIMAL},
            ZHUYISX =  #{ZHUYISX,jdbcType=VARCHAR},
            YUANQIANBZ =  #{YUANQIANBZ,jdbcType=VARCHAR}
        where   CHUANGWEIID = #{CHUANGWEIID,jdbcType=VARCHAR} AND BINGQUID = #{BINGQUID,jdbcType=VARCHAR} 
    </update>
</mapper>
