package com.javazx.batch.scenario.surgery;

import com.javazx.batch.po.SmShoushuxx;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.SurgeryDataService;
import com.javazx.batch.vo.PatientSurgerySyncRequest;
import com.javazx.batch.vo.PatientWithSurgeryReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 手术数据同步场景
 * 从数据库读取手术信息数据并同步到外部系统
 */
@Component
public class SurgeryScenario extends AbstractBatchScenario<SmShoushuxx, PatientWithSurgeryReq> {

    private static final Logger log = LoggerFactory.getLogger(SurgeryScenario.class);

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private SurgeryDataService surgeryDataService;

    public SurgeryScenario() {
        super("surgery", "手术数据同步场景：同步患者手术信息数据");
        this.setCommitInterval(30);
        this.setPageSize(20);
    }

    @Override
    public ItemReader<SmShoushuxx> createReader() {
        log.info("创建手术数据读取器，页面大小: {}", getPageSize());

        // 创建通用分页读取器
        GenericPagingItemReader<SmShoushuxx> reader = new GenericPagingItemReader<>(
                "手术信息",
                getPageSize(),
                (offset, limit) -> surgeryDataService.selectSurgeryByPage("1006", offset, limit),
                () -> surgeryDataService.countSurgery("1006")
        );

        // 记录总手术数量
        int totalCount = reader.getTotalCount();
        log.info("病区1006手术信息总数: {}", totalCount);

        return reader;
    }

    @Override
    public ItemProcessor<SmShoushuxx, PatientWithSurgeryReq> createProcessor() {
        log.info("创建手术数据转换器");
        return new SurgeryProcessor(surgeryDataService);
    }

    @Override
    public ItemWriter<PatientWithSurgeryReq> createWriter() {
        log.info("创建手术数据同步写入器");
        return items -> {
            PatientSurgerySyncRequest request = new PatientSurgerySyncRequest();
            request.setPatientWithSurgeryReqList((List<PatientWithSurgeryReq>) items);

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理手术数据量: {}", threadName, items.size());

            try {
                List<String> patientNames = getPatientNameFromRequest((List<PatientWithSurgeryReq>) items);
                String patientNamesStr = String.join(", ", patientNames);
                
                smartwardWebClient
                        .post()
                        .uri("/sync/patient_surgery/sync_patient_surgery")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request)
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("手术 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("手术 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送手术数据: {} (线程: {})", patientNamesStr, threadName);
            } catch (Exception e) {
                log.error("手术数据同步时发生异常: {}", e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理手术数据量: {}", threadName, items.size());
        };
    }

    /**
     * 从请求对象中获取患者姓名
     */
    private List<String> getPatientNameFromRequest(List<PatientWithSurgeryReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知患者");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(PatientWithSurgeryReq::getPatientInfo)
                .filter(Objects::nonNull)
                .map(patientInfo -> patientInfo.getPatientName())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
