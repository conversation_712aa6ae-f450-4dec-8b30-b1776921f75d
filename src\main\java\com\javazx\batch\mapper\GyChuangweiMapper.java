package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.GyChuangwei;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GY_CHUANGWEI(公用_床位)】的数据库操作Mapper
* @createDate 2025-06-18 10:54:05
* @Entity generator.domain.GyChuangwei
*/
@Mapper
@DS("hzzyy")
public interface GyChuangweiMapper extends BaseMapper<GyChuangwei> {

    int deleteByPrimaryKey(Long id);

    int insert(GyChuangwei record);

    int insertSelective(GyChuangwei record);

    GyChuangwei selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GyChuangwei record);

    int updateByPrimaryKey(GyChuangwei record);

    /**
     * 分页查询床位信息（按病区）
     * @param bingquid 病区ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 床位列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT CHUANGWEIID, BINGQUID, BINGRENZYID, CHUANGWEILX, CHUANGWEIZT, ZUOFEIBZ,
                       ROW_NUMBER() OVER (ORDER BY CHUANGWEIID) AS ROW_NUM
                FROM his6.GY_CHUANGWEI
                WHERE BINGQUID = #{bingquid}
                  AND CHUANGWEILX IN ('1', '2')
                  AND ZUOFEIBZ = 0
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<GyChuangwei> selectBedsByPage(@Param("bingquid") String bingquid,
                                       @Param("offset") int offset,
                                       @Param("limit") int limit);

    /**
     * 统计床位总数（按病区）
     * @param bingquid 病区ID
     * @return 床位总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.GY_CHUANGWEI
            WHERE BINGQUID = #{bingquid}
              AND CHUANGWEILX IN ('1', '2')
              AND ZUOFEIBZ = 0
            """)
    int countBeds(@Param("bingquid") String bingquid);

    /**
     * 分页查询所有床位信息
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 床位列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT CHUANGWEIID, BINGQUID, BINGRENZYID, CHUANGWEILX, CHUANGWEIZT, ZUOFEIBZ,
                       ROW_NUMBER() OVER (ORDER BY BINGQUID, CHUANGWEIID) AS ROW_NUM
                FROM his6.GY_CHUANGWEI
                WHERE CHUANGWEILX IN ('1', '2')
                  AND ZUOFEIBZ = 0
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<GyChuangwei> selectAllBedsByPage(@Param("offset") int offset,
                                          @Param("limit") int limit);

    /**
     * 统计所有床位总数
     * @return 床位总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.GY_CHUANGWEI
            WHERE CHUANGWEILX IN ('1', '2')
              AND ZUOFEIBZ = 0
            """)
    int countAllBeds();

    /**
     * 根据床位ID查询床位信息
     * @param chuangweiid 床位ID
     * @return 床位信息
     */
    @Select("""
            SELECT *
            FROM his6.GY_CHUANGWEI
            WHERE CHUANGWEIID = #{chuangweiid}
              AND ZUOFEIBZ = 0
            """)
    GyChuangwei selectBedById(@Param("chuangweiid") String chuangweiid);

}
