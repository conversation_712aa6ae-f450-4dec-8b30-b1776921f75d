package com.javazx.batch.scenario.userTest;

import com.javazx.batch.po.UserFrom;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 动态测试数据读取器
 * 每次任务执行时都会生成新的测试数据
 */
public class DynamicTestDataReader implements ItemReader<UserFrom> {
    
    private final AtomicInteger counter = new AtomicInteger(0);
    private final int maxItems;
    private final long executionTimestamp;
    
    public DynamicTestDataReader(int maxItems) {
        this.maxItems = maxItems;
        this.executionTimestamp = System.currentTimeMillis();
    }
    
    @Override
    public UserFrom read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        int currentCount = counter.incrementAndGet();
        
        if (currentCount > maxItems) {
            return null; // 表示没有更多数据
        }
        
        // 创建测试数据
        UserFrom testUser = new UserFrom();
        testUser.setId(executionTimestamp + currentCount);
        testUser.setUserName("测试用户" + currentCount + "_" + executionTimestamp);
        testUser.setSex(currentCount % 2 == 0 ? 1 : 2); // 交替设置性别
        testUser.setAge(20 + currentCount);
        testUser.setAddress("测试地址" + currentCount);
        testUser.setCreateTime(new java.util.Date());
        testUser.setStatus(1); // 1表示正常状态
        testUser.setRemark("测试数据" + currentCount + "_" + executionTimestamp);
        
        return testUser;
    }
}
