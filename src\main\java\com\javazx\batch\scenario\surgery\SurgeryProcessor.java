package com.javazx.batch.scenario.surgery;

import com.javazx.batch.po.SmShoushuxx;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.SurgeryDataService;
import com.javazx.batch.util.PatientInfoUtil;
import com.javazx.batch.util.ShouShuInfoUtil;
import com.javazx.batch.util.YiZhuInfoUtil;
import com.javazx.batch.vo.BringMedicineBO;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientSurgeryInfoReq;
import com.javazx.batch.vo.PatientWithSurgeryReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 手术数据处理器
 * 将SmShoushuxx对象转换为PatientWithSurgeryReq对象用于同步
 */
@Component
public class SurgeryProcessor implements ItemProcessor<ZyBingrenxx, PatientWithSurgeryReq> {

    private static final Logger log = LoggerFactory.getLogger(SurgeryProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final SurgeryDataService surgeryDataService;
    private final PatientInfoUtil patientInfoUtil;

    private final ShouShuInfoUtil shouShuInfoUtil;

    public SurgeryProcessor(SurgeryDataService surgeryDataService, PatientInfoUtil patientInfoUtil, ShouShuInfoUtil shouShuInfoUtil) {
        this.surgeryDataService = surgeryDataService;
        this.patientInfoUtil = patientInfoUtil;
        this.shouShuInfoUtil = shouShuInfoUtil;
    }

    @Override
    public PatientWithSurgeryReq process(ZyBingrenxx zyBingrenxx) {
        if (zyBingrenxx == null) {
            return null;
        }

        try {
            // 获取今日手术信息
            List<SmShoushuxx> smShoushuxxList = surgeryDataService.selectTodaySurgery(zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
            if (smShoushuxxList == null || smShoushuxxList.isEmpty()) {
                log.warn("未找到患者: {}, 跳过手术处理", zyBingrenxx.getXINGMING());
                return null;
            }

            // 转换为API请求对象
            PatientWithSurgeryReq request = convert(zyBingrenxx, smShoushuxxList);

            log.debug("完成手术数据处理: {}", zyBingrenxx.getXINGMING());
            return request;
            
        } catch (Exception e) {
            log.error("处理手术数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换手术数据为同步请求
     */
    private PatientWithSurgeryReq convert(ZyBingrenxx patient, List<SmShoushuxx> surgeryList) {
        PatientWithSurgeryReq request = new PatientWithSurgeryReq();

        // 创建患者信息
        PatientInfoReq patientInfo = patientInfoUtil.createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建手术信息列表
        List<PatientSurgeryInfoReq> surgeryInfoList = new ArrayList<>();
        surgeryList.forEach(surgery -> surgeryInfoList.add(createSurgeryInfo(surgery, patient)));
        
        request.setPatientSurgeryList(surgeryInfoList);

        return request;
    }

    /**
     * 创建手术信息对象
     */
    private PatientSurgeryInfoReq createSurgeryInfo(SmShoushuxx surgery, ZyBingrenxx patient) {
        PatientSurgeryInfoReq surgeryInfo = new PatientSurgeryInfoReq();

        surgeryInfo.setSyncId(surgery.getSHOUSHUDID());

        // 基本信息
        surgeryInfo.setPatientName(patient.getXINGMING());
        surgeryInfo.setZyh(patient.getZHUYUANHAO());
        surgeryInfo.setBedNo(patient.getDANGQIANCW());

        // 手术信息
        surgeryInfo.setSurgeryName(surgery.getSHOUSHUMC());
        
        // 手术时间（优先使用安排时间，其次进行时间）
        if (surgery.getANPAISJ() != null) {
            surgeryInfo.setSurgeryTime(surgery.getANPAISJ().format(dateFormatter));
        }else if (surgery.getJINXINGSJ() != null) {
            surgeryInfo.setSurgeryTime(surgery.getJINXINGSJ().format(dateFormatter));
        }

        // 手术进程
        surgeryInfo.setSurgeryProcess(shouShuInfoUtil.convertSurgeryProcess(surgery.getZHUANGTAIBZ()));
        
        // 术前准备
        //surgeryInfo.setSurgeryPreparation(generateSurgeryPreparation(surgery));

        // 状态信息
        surgeryInfo.setIsDisplay(1L);
        surgeryInfo.setIsFinish(shouShuInfoUtil.convertFinishStatus(surgery.getZHUANGTAIBZ()));

        // 携带药品信息
        List<BringMedicineBO> bringMedicine = createBringMedicine(surgery.getSHOUSHUDID());
        surgeryInfo.setSurgeryBringMedicine(bringMedicine);

        return surgeryInfo;
    }

    /**
     * 创建携带药品信息
     */
    private List<BringMedicineBO> createBringMedicine(String shoushuyzid) {
        try {
            // 查询该患者的术中带药医嘱
            List<YzBingrenyz> medications = surgeryDataService.selectIntraoperativeMedication(shoushuyzid);

            List<BringMedicineBO> bringMedicineList = new ArrayList<>();
            if (!medications.isEmpty()) {
                for (YzBingrenyz medication : medications) {
                    BringMedicineBO bringMedicine = new BringMedicineBO();
                    bringMedicine.setMedicineName(medication.getYIZHUMC());
                    bringMedicine.setMedicineDosage(medication.getJILIANG() != null ? medication.getJILIANG().doubleValue() : Double.valueOf("0"));
                    bringMedicine.setMedicineDosageUnit(medication.getYICIJLDW());
                    bringMedicine.setMedicineCountUnit(medication.getJILIANGDW());
                    bringMedicine.setMedicineCount(medication.getYICIYL() != null ? medication.getYICIYL().doubleValue() : Double.valueOf("0"));
                    bringMedicine.setMedicineDisplay(medication.getYAOPINGG());
                    bringMedicineList.add(bringMedicine);
                }
            }
            return bringMedicineList;
        } catch (Exception e) {
            log.warn("查询术中带药失败: {}", e.getMessage());
        }

        return new ArrayList<>();
    }


    /**
     * 生成术前准备信息
     */
    private String generateSurgeryPreparation(SmShoushuxx surgery) {
        return null;
    }
}
