package com.javazx.batch.scenario.surgery;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.po.SmShoushuxx;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.SurgeryDataService;
import com.javazx.batch.util.PatientInfoUtil;
import com.javazx.batch.vo.BringMedicineBO;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientSurgeryInfoReq;
import com.javazx.batch.vo.PatientWithSurgeryReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 手术数据处理器
 * 将SmShoushuxx对象转换为PatientWithSurgeryReq对象用于同步
 */
@Component
public class SurgeryProcessor implements ItemProcessor<SmShoushuxx, PatientWithSurgeryReq> {

    private static final Logger log = LoggerFactory.getLogger(SurgeryProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final SurgeryDataService surgeryDataService;
    private final PatientInfoUtil patientInfoUtil;

    public SurgeryProcessor(SurgeryDataService surgeryDataService, PatientInfoUtil patientInfoUtil) {
        this.surgeryDataService = surgeryDataService;
        this.patientInfoUtil = patientInfoUtil;
    }

    @Override
    public PatientWithSurgeryReq process(SmShoushuxx smShoushuxx) {
        if (smShoushuxx == null) {
            return null;
        }

        log.debug("处理手术数据: {} - {}", smShoushuxx.getSHOUSHUDID(), smShoushuxx.getSHOUSHUMC());

        try {
            // 获取患者信息
            ZyBingrenxx patient = surgeryDataService.selectPatientById(smShoushuxx.getBINGRENZYID());
            if (patient == null) {
                log.warn("未找到患者信息: {}", smShoushuxx.getBINGRENZYID());
                return null;
            }

            // 转换为API请求对象
            PatientWithSurgeryReq request = convert(patient, smShoushuxx);

            log.debug("完成手术数据处理: {}", patient.getXINGMING());
            return request;
            
        } catch (Exception e) {
            log.error("处理手术数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换手术数据为同步请求
     */
    private PatientWithSurgeryReq convert(ZyBingrenxx patient, SmShoushuxx surgery) {
        PatientWithSurgeryReq request = new PatientWithSurgeryReq();

        // 创建患者信息
        PatientInfoReq patientInfo = createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建手术信息列表
        List<PatientSurgeryInfoReq> surgeryInfoList = new ArrayList<>();
        PatientSurgeryInfoReq surgeryInfo = createSurgeryInfo(surgery, patient);
        surgeryInfoList.add(surgeryInfo);
        
        request.setPatientSurgeryList(surgeryInfoList);

        return request;
    }

    /**
     * 创建患者信息对象
     */
    private PatientInfoReq createPatientInfo(ZyBingrenxx patient) {
        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(patient.getXINGMING());
        patientInfo.setDesensitizedName(desensitizeName(patient.getXINGMING()));
        patientInfo.setGender(convertGenderToLong(patient.getXINGBIE()));

        // 住院信息
        patientInfo.setZyh(patient.getZHUYUANHAO());
        patientInfo.setZyhm(patient.getBINGANHAO());
        patientInfo.setBedNo(patient.getDANGQIANCW());
        patientInfo.setWardCode(patient.getDANGQIANBQ());

        // 时间信息
        if (patient.getRUYUANRQ() != null) {
            patientInfo.setAdmissionTime(patient.getRUYUANRQ().format(dateFormatter));
        }
        if (patient.getCHUYUANRQ() != null) {
            patientInfo.setDischargeTime(patient.getCHUYUANRQ().format(dateFormatter));
        }

        // 诊断信息
        patientInfo.setDiagnosis(patient.getRUYUANZDMC());

        // 医护人员信息
        patientInfo.setDoctorTitleShow(patient.getZHUZHIYSXM());
        patientInfo.setNurseTitleShow(patient.getZERENHSXM());

        // 状态信息
        patientInfo.setPatientStatus(1L); // 1-在院

        return patientInfo;
    }

    /**
     * 创建手术信息对象
     */
    private PatientSurgeryInfoReq createSurgeryInfo(SmShoushuxx surgery, ZyBingrenxx patient) {
        PatientSurgeryInfoReq surgeryInfo = new PatientSurgeryInfoReq();

        // 基本信息
        surgeryInfo.setPatientName(patient.getXINGMING());
        surgeryInfo.setZyh(patient.getZHUYUANHAO());
        surgeryInfo.setBedNo(patient.getDANGQIANCW());

        // 手术信息
        surgeryInfo.setSurgeryName(surgery.getSHOUSHUMC());
        
        // 手术时间（优先使用进行时间，其次安排时间）
        if (surgery.getJINXINGSJ() != null) {
            surgeryInfo.setSurgeryTime(surgery.getJINXINGSJ().format(dateFormatter));
        } else if (surgery.getANPAISJ() != null) {
            surgeryInfo.setSurgeryTime(surgery.getANPAISJ().format(dateFormatter));
        }

        // 手术进程
        surgeryInfo.setSurgeryProcess(convertSurgeryProcess(surgery.getZHUANGTAIBZ()));
        
        // 术前准备（可以根据手术类型或状态设置）
        surgeryInfo.setSurgeryPreparation(generateSurgeryPreparation(surgery));

        // 状态信息
        surgeryInfo.setIsDisplay(1L); // 默认显示
        surgeryInfo.setIsFinish(convertFinishStatus(surgery.getZHUANGTAIBZ()));

        // 身份证号
        if (StringUtils.hasLength(patient.getSHENFENZH())) {
            try {
                surgeryInfo.setIdNumber(Long.parseLong(patient.getSHENFENZH()));
            } catch (NumberFormatException e) {
                log.warn("身份证号格式错误: {}", patient.getSHENFENZH());
                surgeryInfo.setIdNumber(0L);
            }
        } else {
            surgeryInfo.setIdNumber(0L);
        }

        // 携带药品信息（查询术中带药）
        BringMedicineBO bringMedicine = createBringMedicine(patient.getBINGRENZYID());
        surgeryInfo.setSurgeryBringMedicine(bringMedicine);

        return surgeryInfo;
    }

    /**
     * 创建携带药品信息
     */
    private BringMedicineBO createBringMedicine(String bingrenzyid) {
        try {
            // 查询该患者的术中带药医嘱
            List<YzBingrenyz> medications = surgeryDataService.selectIntraoperativeMedicationByPage(0, 100)
                    .stream()
                    .filter(med -> bingrenzyid.equals(med.getBINGRENZYID()))
                    .collect(java.util.stream.Collectors.toList());

            if (!medications.isEmpty()) {
                // 取第一个术中带药作为示例
                YzBingrenyz firstMed = medications.get(0);
                
                BringMedicineBO bringMedicine = new BringMedicineBO();
                bringMedicine.setMedicineName(firstMed.getYIZHUMC());
                bringMedicine.setMedicineDosage(firstMed.getJILIANG() != null ? firstMed.getJILIANG().toString() : "");
                bringMedicine.setMedicineCount(1L); // 默认数量为1
                
                return bringMedicine;
            }
        } catch (Exception e) {
            log.warn("查询术中带药失败: {}", e.getMessage());
        }

        // 返回空的药品信息
        BringMedicineBO bringMedicine = new BringMedicineBO();
        bringMedicine.setMedicineName("");
        bringMedicine.setMedicineDosage("");
        bringMedicine.setMedicineCount(0L);
        return bringMedicine;
    }

    /**
     * 脱敏姓名
     */
    private String desensitizeName(String name) {
        if (StringUtils.hasLength(name)) {
            return DesensitizedUtil.chineseName(name);
        }
        return name;
    }

    /**
     * 转换性别为Long类型
     */
    private Long convertGenderToLong(String gender) {
        if (gender == null) {
            return 0L;
        }

        switch (gender.trim()) {
            case "男":
            case "1":
                return 1L;
            case "女":
            case "2":
                return 2L;
            default:
                return 0L;
        }
    }

    /**
     * 转换手术进程
     */
    private String convertSurgeryProcess(Integer zhuangtaibz) {
        if (zhuangtaibz == null) {
            return "未知";
        }

        switch (zhuangtaibz) {
            case 1:
                return "申请中";
            case 6:
                return "已安排";
            case 7:
                return "进行中";
            case 9:
                return "已完成";
            default:
                return "未知状态";
        }
    }

    /**
     * 转换完成状态
     */
    private Long convertFinishStatus(Integer zhuangtaibz) {
        return (zhuangtaibz != null && zhuangtaibz == 9) ? 1L : 0L;
    }

    /**
     * 生成术前准备信息
     */
    private String generateSurgeryPreparation(SmShoushuxx surgery) {
        StringBuilder preparation = new StringBuilder();
        
        // 根据手术名称生成术前准备建议
        String surgeryName = surgery.getSHOUSHUMC();
        if (surgeryName != null) {
            if (surgeryName.contains("腹腔镜") || surgeryName.contains("内镜")) {
                preparation.append("禁食禁水12小时；");
            }
            if (surgeryName.contains("心脏") || surgeryName.contains("胸腔")) {
                preparation.append("心电图检查；胸片检查；");
            }
            if (surgeryName.contains("骨科") || surgeryName.contains("关节")) {
                preparation.append("X光检查；");
            }
        }
        
        // 通用术前准备
        if (preparation.length() == 0) {
            preparation.append("常规术前检查；");
        }
        preparation.append("签署手术同意书；备血；");
        
        return preparation.toString();
    }
}
