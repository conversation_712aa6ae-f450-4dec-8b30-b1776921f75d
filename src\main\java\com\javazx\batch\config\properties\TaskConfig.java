package com.javazx.batch.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "data")
public class TaskConfig {
    private List<Task> tasks = new ArrayList<>();

    public Task getTask(String name){
        for (Task task : tasks) {
            if (task.getName().equals(name)) {
                return task;
            }
        }
        return new Task();
    }

    @Data
    public static class Task {
        /**
         * 任务名称
         */
        private String name;
        /**
         * 任务执行表达式
         */
        private String cron;
        /**
         * 是否启用
         */
        private boolean enabled;
        /**
         * 依赖的任务名称
         */
        private String dependsOn;
        /**
         * 最大等待分钟数 基于依赖任务的完成情况 如果依赖任务为空可以不用写
         */
        private int maxWaitMinutes;
    }
}