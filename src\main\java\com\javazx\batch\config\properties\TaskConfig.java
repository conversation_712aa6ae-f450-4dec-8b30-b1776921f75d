package com.javazx.batch.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "data")
public class TaskConfig {
    private List<Task> tasks = new ArrayList<>();

    public Task getTask(String name){
        for (Task task : tasks) {
            if (task.getName().equals(name)) {
                return task;
            }
        }
        return new Task();
    }

    @Data
    public static class Task {
        private String name;
        private String cron;
        private boolean enabled;
        private String dependsOn;
        private int maxWaitMinutes;
    }
}