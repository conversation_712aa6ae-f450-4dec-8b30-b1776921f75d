package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 患者标签请求对象
 * chagine.smartward.DTO.request.label.PatientLabelReq
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientLabelReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否显示（1-显示，0-不显示/删除）
     */
    private Long isDisplay;

    /**
     * 白板标签编号
     */
    private String labelCode;

    /**
     * 病人编号
     */
    private Long patientid;
}
