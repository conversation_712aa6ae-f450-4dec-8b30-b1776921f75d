package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.javazx.batch.po.YzBingrenyz;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 护理任务医嘱数据访问接口
 * 专门处理护理任务相关的医嘱查询
 */
@Mapper
@DS("hzzyy")
public interface NursingTaskOrderMapper {

    /**
     * 查询患者的心电监护医嘱
     * @param patientId 患者住院ID
     * @return 心电监护医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT, 
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (YIZHUMC = '心电监护' OR YIZHUMC = '心电监护+血氧饱和度')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectECGMonitoringOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的测血压医嘱
     * @param patientId 患者住院ID
     * @return 测血压医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (YIZHUMC = '测血压QD' OR YIZHUMC = '测血压' OR YIZHUMC = '测血压BID' OR YIZHUMC = '测血压TID')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectBloodPressureOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的测血糖医嘱
     * @param patientId 患者住院ID
     * @return 测血糖医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND YIZHUMC = '葡萄糖测定'
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectBloodGlucoseOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的雾化吸入医嘱
     * @param patientId 患者住院ID
     * @return 雾化吸入医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (GEIYAOFS = '雾化吸入' OR GEIYAOFS = '雾化')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectNebulizationOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的膀胱冲洗医嘱
     * @param patientId 患者住院ID
     * @return 膀胱冲洗医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (GEIYAOFS = '膀胱冲洗' OR YIZHUMC = '膀胱持续冲洗')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectBladderIrrigationOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的基础护理医嘱
     * @param patientId 患者住院ID
     * @return 基础护理医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (YIZHUMC = '口腔护理' OR YIZHUMC = '会阴护理' OR YIZHUMC = '留置导尿')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectBasicCareOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的导管维护医嘱
     * @param patientId 患者住院ID
     * @return 导管维护医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (YIZHUMC = '更换引流袋' OR YIZHUMC = '静脉置管冲洗' OR YIZHUMC = '动静脉置管护理' 
                   OR YIZHUMC = '输液港置管护理' OR YIZHUMC = 'PICC置管护理' OR YIZHUMC = '肾周引流管护理')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectCatheterCareOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的体征测量医嘱
     * @param patientId 患者住院ID
     * @return 体征测量医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND YIZHUMC = '记24小时尿量'
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectVitalSignsOrders(@Param("patientId") String patientId);

    /**
     * 查询患者的吸氧医嘱
     * @param patientId 患者住院ID
     * @return 吸氧医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND (YIZHUMC = '鼻导管吸氧' OR YIZHUMC = '吸氧(面罩)' OR YIZHUMC = '吸氧面罩' 
                   OR YIZHUMC = '吸氧(鼻导管)' OR YIZHUMC = '吸氧')
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectOxygenTherapyOrders(@Param("patientId") String patientId);

    /**
     * 根据患者ID和医嘱名称查询医嘱（通用方法）
     * @param patientId 患者住院ID
     * @param orderName 医嘱名称
     * @return 医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND YIZHUMC = #{orderName}
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectOrdersByName(@Param("patientId") String patientId, 
                                         @Param("orderName") String orderName);

    /**
     * 根据患者ID和给药方式查询医嘱（通用方法）
     * @param patientId 患者住院ID
     * @param administrationMethod 给药方式
     * @return 医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND GEIYAOFS = #{administrationMethod}
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectOrdersByAdministration(@Param("patientId") String patientId, 
                                                   @Param("administrationMethod") String administrationMethod);

    /**
     * 根据患者ID、医嘱名称和频次查询医嘱（通用方法）
     * @param patientId 患者住院ID
     * @param orderName 医嘱名称
     * @param frequency 频次
     * @return 医嘱列表
     */
    @Select("""
            SELECT YIZHUID, BINGRENZYID, YIZHUMC, PINCI, GEIYAOFS, YIZHUZT,
                   KAISHISJ, JIESHUSJ, KAIZHUSJ, KAIZHUYS, KAIZHUYSXM
            FROM his6.YZ_BINGRENYZ
            WHERE BINGRENZYID = #{patientId}
              AND YIZHUMC = #{orderName}
              AND PINCI = #{frequency}
              AND YIZHUZT IN ('2', '3', '4')
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectOrdersByNameAndFrequency(@Param("patientId") String patientId, 
                                                     @Param("orderName") String orderName,
                                                     @Param("frequency") String frequency);
}
