package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 护理任务医嘱数据访问接口
 * 专门处理护理任务相关的医嘱查询
 */
@Mapper
@DS("hzzyy")
public interface NursingTaskOrderMapper {

    /**
     * 查询病区的心电监护医嘱
     * @param wardId 病区ID
     * @return 心电监护医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.YIZHUMC = '心电监护' OR a.YIZHUMC = '心电监护+血氧饱和度')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectECGMonitoringOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的测血压医嘱
     * @param wardId 病区ID
     * @return 测血压医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.YIZHUMC = '测血压QD' OR a.YIZHUMC = '测血压' OR a.YIZHUMC = '测血压BID' OR a.YIZHUMC = '测血压TID')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectBloodPressureOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的测血糖医嘱
     * @param wardId 病区ID
     * @return 测血糖医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND a.YIZHUMC = '葡萄糖测定'
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectBloodGlucoseOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的雾化吸入医嘱
     * @param wardId 病区ID
     * @return 雾化吸入医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.GEIYAOFS = '雾化吸入' OR a.GEIYAOFS = '雾化')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectNebulizationOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的膀胱冲洗医嘱
     * @param wardId 病区ID
     * @return 膀胱冲洗医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.GEIYAOFS = '膀胱冲洗')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectBladderIrrigationOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的膀胱持续冲洗医嘱
     * @param wardId 病区ID
     * @return 膀胱冲洗医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND a.YIZHUMC = '膀胱持续冲洗'
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectContinuousBladderIrrigationOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的基础护理医嘱
     * @param wardId 病区ID
     * @return 基础护理医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.YIZHUMC = '口腔护理' OR a.YIZHUMC = '会阴护理' OR a.YIZHUMC = '留置导尿')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectBasicCareOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的导管维护医嘱
     * @param wardId 病区ID
     * @return 导管维护医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.YIZHUMC = '更换引流袋' OR a.YIZHUMC = '静脉置管冲洗' OR a.YIZHUMC = '动静脉置管护理'
                   OR a.YIZHUMC = '输液港置管护理' OR a.YIZHUMC = 'PICC置管护理' OR a.YIZHUMC = '肾周引流管护理')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectCatheterCareOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的体征测量医嘱
     * @param wardId 病区ID
     * @return 体征测量医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND a.YIZHUMC = '记24小时尿量'
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectVitalSignsOrders(@Param("wardId") String wardId);

    /**
     * 查询病区的吸氧医嘱
     * @param wardId 病区ID
     * @return 吸氧医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND (a.YIZHUMC = '鼻导管吸氧' OR a.YIZHUMC = '吸氧(面罩)' OR a.YIZHUMC = '吸氧面罩'
                   OR a.YIZHUMC = '吸氧(鼻导管)' OR a.YIZHUMC = '吸氧')
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectOxygenTherapyOrders(@Param("wardId") String wardId);

    /**
     * 根据病区ID和医嘱名称查询医嘱（通用方法）
     * @param wardId 病区ID
     * @param orderName 医嘱名称
     * @return 医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND a.YIZHUMC = #{orderName}
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectOrdersByName(@Param("wardId") String wardId,
                                                  @Param("orderName") String orderName);

    /**
     * 根据病区ID和给药方式查询医嘱（通用方法）
     * @param wardId 病区ID
     * @param administrationMethod 给药方式
     * @return 医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND a.GEIYAOFS = #{administrationMethod}
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectOrdersByAdministration(@Param("wardId") String wardId,
                                                            @Param("administrationMethod") String administrationMethod);

    /**
     * 根据病区ID、医嘱名称和频次查询医嘱（通用方法）
     * @param wardId 病区ID
     * @param orderName 医嘱名称
     * @param frequency 频次
     * @return 医嘱列表
     */
    @Select("""
            SELECT a.YIZHUID, a.BINGRENZYID, a.YIZHUMC, a.PINCI, a.GEIYAOFS, a.YIZHUZT,
                   a.KAISHISJ, a.JIESHUSJ, a.KAIZHUSJ, a.KAIZHUYS, a.KAIZHUYSXM, b.DANGQIANCW, a.BINGQUID
            FROM his6.YZ_BINGRENYZ a
            LEFT JOIN HIS6.ZY_BINGRENXX b on a.BINGRENZYID = b.BINGRENZYID
            WHERE a.BINGQUID = #{wardId}
              AND a.YIZHUMC = #{orderName}
              AND a.PINCI = #{frequency}
              AND a.YIZHUZT IN ('2', '3', '4')
              AND a.KAIZHUSJ > TRUNC(SYSDATE)
            ORDER BY b.DANGQIANCW
            """)
    List<NursingTaskOrderResp> selectOrdersByNameAndFrequency(@Param("wardId") String wardId,
                                                              @Param("orderName") String orderName,
                                                              @Param("frequency") String frequency);
}
