package com.javazx.batch.service;

import com.javazx.batch.mapper.NursingTaskOrderMapper;
import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 护理任务数据服务类
 * 专门处理护理任务相关数据访问
 */
@Service
public class NursingTaskDataService {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskDataService.class);

    @Autowired
    private NursingTaskOrderMapper nursingTaskOrderMapper;

    /**
     * 查询病区的心电监护医嘱
     */
    public List<NursingTaskOrderResp> selectECGMonitoringOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectECGMonitoringOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}心电监护医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的测血压医嘱
     */
    public List<NursingTaskOrderResp> selectBloodPressureOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressureOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}测血压医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的测血糖医嘱
     */
    public List<NursingTaskOrderResp> selectBloodGlucoseOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucoseOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}测血糖医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的雾化吸入医嘱
     */
    public List<NursingTaskOrderResp> selectNebulizationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectNebulizationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}雾化吸入医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的膀胱冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectBladderIrrigationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBladderIrrigationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}膀胱冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }



    /**
     * 查询病区的膀胱持续冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectContinuousBladderIrrigationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectContinuousBladderIrrigationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}膀胱持续冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的口腔护理医嘱
     */
    public List<NursingTaskOrderResp> selectOralCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOralCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}口腔护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的会阴护理医嘱
     */
    public List<NursingTaskOrderResp> selectPerinealCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectPerinealCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}会阴护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的留置导尿医嘱
     */
    public List<NursingTaskOrderResp> selectUrinaryCatheterizationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectUrinaryCatheterizationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}留置导尿医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的记24小时尿量医嘱
     */
    public List<NursingTaskOrderResp> selectUrineVolumeRecordOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectUrineVolumeRecordOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}记24小时尿量医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的更换引流袋医嘱
     */
    public List<NursingTaskOrderResp> selectDrainageBagChangeOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectDrainageBagChangeOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}更换引流袋医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的静脉置管冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectVenousCatheterFlushOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectVenousCatheterFlushOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}静脉置管冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的动静脉置管护理医嘱
     */
    public List<NursingTaskOrderResp> selectArteriovenousCatheterCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectArteriovenousCatheterCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}动静脉置管护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的输液港置管护理医嘱
     */
    public List<NursingTaskOrderResp> selectInfusionPortCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectInfusionPortCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}输液港置管护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的PICC置管护理医嘱
     */
    public List<NursingTaskOrderResp> selectPICCCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectPICCCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}PICC置管护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的肾周引流管护理医嘱
     */
    public List<NursingTaskOrderResp> selectPerinephricDrainageCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectPerinephricDrainageCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}肾周引流管护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的鼻导管吸氧医嘱
     */
    public List<NursingTaskOrderResp> selectNasalCannulaOxygenOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectNasalCannulaOxygenOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}鼻导管吸氧医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的吸氧(面罩)医嘱
     */
    public List<NursingTaskOrderResp> selectOxygenMaskOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOxygenMaskOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}吸氧(面罩)医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的吸氧面罩医嘱
     */
    public List<NursingTaskOrderResp> selectOxygenFaceMaskOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOxygenFaceMaskOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}吸氧面罩医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的吸氧(鼻导管)医嘱
     */
    public List<NursingTaskOrderResp> selectNasalCannulaOxygenBracketsOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectNasalCannulaOxygenBracketsOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}吸氧(鼻导管)医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的吸氧医嘱
     */
    public List<NursingTaskOrderResp> selectOxygenOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOxygenOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}吸氧医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }
}
