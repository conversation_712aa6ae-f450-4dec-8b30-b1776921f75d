package com.javazx.batch.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.javazx.batch.mapper.GyChuangweiMapper;
import com.javazx.batch.mapper.YzBingrenyzMapper;
import com.javazx.batch.mapper.ZyBingrenxxMapper;
import com.javazx.batch.po.GyChuangwei;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 护理任务数据服务类
 * 专门处理护理任务相关数据访问
 */
@Service
public class NursingTaskDataService {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskDataService.class);

    @Autowired
    private YzBingrenyzMapper yzBingrenyzMapper;

    /**
     * 查询患者的心电监护医嘱
     */
    public List<YzBingrenyz> selectECGMonitoringOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("YIZHUMC", "心电监护")
                    .or()
                    .eq("YIZHUMC", "心电监护+血氧饱和度")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的测血压医嘱
     */
    public List<YzBingrenyz> selectBloodPressureOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("YIZHUMC", "测血压QD")
                    .or()
                    .eq("YIZHUMC", "测血压")
                    .or()
                    .eq("YIZHUMC", "测血压BID")
                    .or()
                    .eq("YIZHUMC", "测血压TID")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的测血糖医嘱
     */
    public List<YzBingrenyz> selectBloodGlucoseOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .eq("YIZHUMC", "葡萄糖测定")
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的雾化吸入医嘱
     */
    public List<YzBingrenyz> selectNebulizationOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("GEIYAOFS", "雾化吸入")
                    .or()
                    .eq("GEIYAOFS", "雾化")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的膀胱冲洗医嘱
     */
    public List<YzBingrenyz> selectBladderIrrigationOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("GEIYAOFS", "膀胱冲洗")
                    .or()
                    .eq("YIZHUMC", "膀胱持续冲洗")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的基础护理医嘱
     */
    public List<YzBingrenyz> selectBasicCareOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("YIZHUMC", "口腔护理")
                    .or()
                    .eq("YIZHUMC", "会阴护理")
                    .or()
                    .eq("YIZHUMC", "留置导尿")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的导管维护医嘱
     */
    public List<YzBingrenyz> selectCatheterCareOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("YIZHUMC", "更换引流袋")
                    .or()
                    .eq("YIZHUMC", "静脉置管冲洗")
                    .or()
                    .eq("YIZHUMC", "动静脉置管护理")
                    .or()
                    .eq("YIZHUMC", "输液港置管护理")
                    .or()
                    .eq("YIZHUMC", "PICC置管护理")
                    .or()
                    .eq("YIZHUMC", "肾周引流管护理")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的体征测量医嘱
     */
    public List<YzBingrenyz> selectVitalSignsOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .eq("YIZHUMC", "记24小时尿量")
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }

    /**
     * 查询患者的吸氧医嘱
     */
    public List<YzBingrenyz> selectOxygenTherapyOrders(String patientId) {
        return yzBingrenyzMapper.selectList(
            new QueryWrapper<YzBingrenyz>()
                .eq("BINGRENZYID", patientId)
                .and(wrapper -> wrapper
                    .eq("YIZHUMC", "鼻导管吸氧")
                    .or()
                    .eq("YIZHUMC", "吸氧(面罩)")
                    .or()
                    .eq("YIZHUMC", "吸氧面罩")
                    .or()
                    .eq("YIZHUMC", "吸氧(鼻导管)")
                    .or()
                    .eq("YIZHUMC", "吸氧")
                )
                .in("YIZHUZT", Arrays.asList(2, 3, 4))
                .orderByDesc("KAISHISHIJIAN")
        );
    }
}
