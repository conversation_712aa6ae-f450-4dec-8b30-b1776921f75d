package com.javazx.batch.service;

import com.javazx.batch.mapper.NursingTaskOrderMapper;
import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 护理任务数据服务类
 * 专门处理护理任务相关数据访问
 */
@Service
public class NursingTaskDataService {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskDataService.class);

    @Autowired
    private NursingTaskOrderMapper nursingTaskOrderMapper;

    /**
     * 查询病区的心电监护医嘱
     */
    public List<NursingTaskOrderResp> selectECGMonitoringOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectECGMonitoringOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}心电监护医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的06:00测血压医嘱
     */
    public List<NursingTaskOrderResp> selectBloodPressure06Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressure06Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}06:00测血压医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的10:00测血压医嘱
     */
    public List<NursingTaskOrderResp> selectBloodPressure10Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressure10Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}10:00测血压医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的14:00测血压医嘱
     */
    public List<NursingTaskOrderResp> selectBloodPressure14Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressure14Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}14:00测血压医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的18:00测血压医嘱
     */
    public List<NursingTaskOrderResp> selectBloodPressure18Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressure18Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}18:00测血压医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的06:00测血糖医嘱
     */
    public List<NursingTaskOrderResp> selectBloodGlucose06Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucose06Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}06:00测血糖医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的10:00测血糖医嘱
     */
    public List<NursingTaskOrderResp> selectBloodGlucose10Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucose10Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}10:00测血糖医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的16:00测血糖医嘱
     */
    public List<NursingTaskOrderResp> selectBloodGlucose16Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucose16Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}16:00测血糖医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的20:00测血糖医嘱
     */
    public List<NursingTaskOrderResp> selectBloodGlucose20Orders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucose20Orders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}20:00测血糖医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的雾化吸入医嘱
     */
    public List<NursingTaskOrderResp> selectNebulizationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectNebulizationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}雾化吸入医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的膀胱冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectBladderIrrigationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBladderIrrigationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}膀胱冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }



    /**
     * 查询病区的膀胱持续冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectContinuousBladderIrrigationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectContinuousBladderIrrigationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}膀胱持续冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的口腔护理医嘱
     */
    public List<NursingTaskOrderResp> selectOralCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOralCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}口腔护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的会阴护理医嘱
     */
    public List<NursingTaskOrderResp> selectPerinealCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectPerinealCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}会阴护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的留置导尿医嘱
     */
    public List<NursingTaskOrderResp> selectUrinaryCatheterizationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectUrinaryCatheterizationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}留置导尿医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的记24小时尿量医嘱
     */
    public List<NursingTaskOrderResp> selectUrineVolumeRecordOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectUrineVolumeRecordOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}记24小时尿量医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的更换引流袋医嘱
     */
    public List<NursingTaskOrderResp> selectDrainageBagChangeOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectDrainageBagChangeOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}更换引流袋医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的静脉置管冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectVenousCatheterFlushOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectVenousCatheterFlushOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}静脉置管冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的深静脉置管护理医嘱
     */
    public List<NursingTaskOrderResp> selectDeepVenousCatheterCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectDeepVenousCatheterCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}深静脉置管护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的输液港医嘱
     */
    public List<NursingTaskOrderResp> selectInfusionPortOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectInfusionPortOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}输液港医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的PICC置管医嘱
     */
    public List<NursingTaskOrderResp> selectPICCCatheterOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectPICCCatheterOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}PICC置管医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的肾周引流管护理医嘱
     */
    public List<NursingTaskOrderResp> selectPerinephricDrainageCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectPerinephricDrainageCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}肾周引流管护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的吸氧医嘱（包含所有吸氧方式）
     */
    public List<NursingTaskOrderResp> selectOxygenTherapyOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOxygenTherapyOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}吸氧医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }
}
