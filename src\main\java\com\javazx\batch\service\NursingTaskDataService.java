package com.javazx.batch.service;

import com.javazx.batch.mapper.NursingTaskOrderMapper;
import com.javazx.batch.po.YzBingrenyz;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 护理任务数据服务类
 * 专门处理护理任务相关数据访问
 */
@Service
public class NursingTaskDataService {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskDataService.class);

    @Autowired
    private NursingTaskOrderMapper nursingTaskOrderMapper;

    /**
     * 查询患者的心电监护医嘱
     */
    public List<YzBingrenyz> selectECGMonitoringOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectECGMonitoringOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}心电监护医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的测血压医嘱
     */
    public List<YzBingrenyz> selectBloodPressureOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressureOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}测血压医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的测血糖医嘱
     */
    public List<YzBingrenyz> selectBloodGlucoseOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucoseOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}测血糖医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的雾化吸入医嘱
     */
    public List<YzBingrenyz> selectNebulizationOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectNebulizationOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}雾化吸入医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的膀胱冲洗医嘱
     */
    public List<YzBingrenyz> selectBladderIrrigationOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectBladderIrrigationOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}膀胱冲洗医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的基础护理医嘱
     */
    public List<YzBingrenyz> selectBasicCareOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectBasicCareOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}基础护理医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的导管维护医嘱
     */
    public List<YzBingrenyz> selectCatheterCareOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectCatheterCareOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}导管维护医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的体征测量医嘱
     */
    public List<YzBingrenyz> selectVitalSignsOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectVitalSignsOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}体征测量医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询患者的吸氧医嘱
     */
    public List<YzBingrenyz> selectOxygenTherapyOrders(String patientId) {
        try {
            return nursingTaskOrderMapper.selectOxygenTherapyOrders(patientId);
        } catch (Exception e) {
            log.error("查询患者{}吸氧医嘱失败: {}", patientId, e.getMessage());
            return List.of();
        }
    }
}
