package com.javazx.batch.service;

import com.javazx.batch.mapper.NursingTaskOrderMapper;
import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 护理任务数据服务类
 * 专门处理护理任务相关数据访问
 */
@Service
public class NursingTaskDataService {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskDataService.class);

    @Autowired
    private NursingTaskOrderMapper nursingTaskOrderMapper;

    /**
     * 查询病区的心电监护医嘱
     */
    public List<NursingTaskOrderResp> selectECGMonitoringOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectECGMonitoringOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}心电监护医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的测血压医嘱
     */
    public List<NursingTaskOrderResp> selectBloodPressureOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodPressureOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}测血压医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的测血糖医嘱
     */
    public List<NursingTaskOrderResp> selectBloodGlucoseOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBloodGlucoseOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}测血糖医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的雾化吸入医嘱
     */
    public List<NursingTaskOrderResp> selectNebulizationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectNebulizationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}雾化吸入医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的膀胱冲洗医嘱
     */
    public List<NursingTaskOrderResp> selectBladderIrrigationOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBladderIrrigationOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}膀胱冲洗医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的基础护理医嘱
     */
    public List<NursingTaskOrderResp> selectBasicCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectBasicCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}基础护理医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的导管维护医嘱
     */
    public List<NursingTaskOrderResp> selectCatheterCareOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectCatheterCareOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}导管维护医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的体征测量医嘱
     */
    public List<NursingTaskOrderResp> selectVitalSignsOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectVitalSignsOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}体征测量医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }

    /**
     * 查询病区的吸氧医嘱
     */
    public List<NursingTaskOrderResp> selectOxygenTherapyOrders(String wardId) {
        try {
            return nursingTaskOrderMapper.selectOxygenTherapyOrders(wardId);
        } catch (Exception e) {
            log.error("查询病区{}吸氧医嘱失败: {}", wardId, e.getMessage());
            return List.of();
        }
    }
}
