package com.javazx.batch.service;

import com.javazx.batch.mapper.GyChuangweiMapper;
import com.javazx.batch.mapper.YzBingrenyzMapper;
import com.javazx.batch.mapper.ZyBingrenxxMapper;
import com.javazx.batch.po.GyChuangwei;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 护理任务数据服务类
 * 专门处理护理任务相关数据访问
 */
@Service
public class NursingTaskDataService {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskDataService.class);

    @Autowired
    private YzBingrenyzMapper yzBingrenyzMapper;


}
