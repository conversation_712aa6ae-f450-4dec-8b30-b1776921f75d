package com.javazx.batch.service;

import com.javazx.batch.mapper.GyChuangweiMapper;
import com.javazx.batch.po.GyChuangwei;
import com.javazx.batch.vo.BedInfoReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 床位数据服务类
 * 专门处理床位相关数据访问
 * 基于GY_CHUANGWEI床位表查询
 */
@Service
public class BedDataService {

    private static final Logger log = LoggerFactory.getLogger(BedDataService.class);

    @Autowired
    private GyChuangweiMapper gyChuangweiMapper;

    /**
     * 分页查询床位信息数据（按病区）
     * @param bingquid 病区ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 床位信息列表
     */
    public List<BedInfoReq> selectBedsByPage(String bingquid, int offset, int limit) {
        try {
            log.debug("查询床位信息数据 - 病区: {}, 偏移: {}, 限制: {}", bingquid, offset, limit);
            List<GyChuangwei> beds = gyChuangweiMapper.selectBedsByPage(bingquid, offset, limit);
            return convertToBedInfoList(beds);
        } catch (Exception e) {
            log.error("查询床位信息数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计床位信息总数（按病区）
     * @param bingquid 病区ID
     * @return 床位总数
     */
    public int countBeds(String bingquid) {
        try {
            return gyChuangweiMapper.countBeds(bingquid);
        } catch (Exception e) {
            log.error("统计床位信息总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 分页查询所有床位信息数据
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 床位信息列表
     */
    public List<BedInfoReq> selectAllBedsByPage(int offset, int limit) {
        try {
            log.debug("查询所有床位信息数据 - 偏移: {}, 限制: {}", offset, limit);
            List<GyChuangwei> beds = gyChuangweiMapper.selectAllBedsByPage(offset, limit);
            return convertToBedInfoList(beds);
        } catch (Exception e) {
            log.error("查询所有床位信息数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计所有床位信息总数
     * @return 床位总数
     */
    public int countAllBeds() {
        try {
            return gyChuangweiMapper.countAllBeds();
        } catch (Exception e) {
            log.error("统计所有床位信息总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据床位ID查询床位信息
     * @param chuangweiid 床位ID
     * @return 床位信息
     */
    public BedInfoReq selectBedById(String chuangweiid) {
        try {
            GyChuangwei bed = gyChuangweiMapper.selectBedById(chuangweiid);
            return bed != null ? convertToBedInfo(bed) : null;
        } catch (Exception e) {
            log.error("根据床位ID查询床位信息失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 转换GyChuangwei列表为BedInfoReq列表
     */
    private List<BedInfoReq> convertToBedInfoList(List<GyChuangwei> beds) {
        return beds.stream()
                .map(this::convertToBedInfo)
                .collect(Collectors.toList());
    }

    /**
     * 转换GyChuangwei为BedInfoReq对象
     */
    private BedInfoReq convertToBedInfo(GyChuangwei bed) {
        BedInfoReq bedInfo = new BedInfoReq();

        // 使用床位ID作为床位编号
        if (bed.getCHUANGWEIID() != null) {
            bedInfo.setBedNo(bed.getCHUANGWEIID());
        }

        // 病区ID
        if (bed.getBINGQUID() != null) {
            bedInfo.setWardCode(bed.getBINGQUID());
        }

        return bedInfo;
    }
}
