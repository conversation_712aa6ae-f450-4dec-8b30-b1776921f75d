package com.javazx.batch.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;

/**
 * 手术创建工具类
 * 提供通用的患者信息创建方法，供各个处理器使用
 */
@Component
public class ShouShuInfoUtil {

    private static final Logger log = LoggerFactory.getLogger(ShouShuInfoUtil.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    /**
     * 转换手术进程 0已作废，1申请，2已拒绝，6已安排，7 开始，9已结束
     */
    public String convertSurgeryProcess(Integer zhuangtaibz) {
        if (zhuangtaibz == null) {
            return "未知状态";
        }

        switch (zhuangtaibz) {
            case 0:
                return "已作废";
            case 1:
                return "申请中";
            case 2:
                return "已拒绝";
            case 6:
                return "已安排";
            case 7:
                return "开始";
            case 9:
                return "已结束";
            default:
                return "未知状态";
        }
    }

    /**
     * 转换完成状态
     */
    public Long convertFinishStatus(Integer zhuangtaibz) {
        return 9 == zhuangtaibz ? 1L : 0L;
    }
}
