package com.javazx.batch.scenario.doctor;

import com.javazx.batch.po.GyZhigongxx;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.service.DoctorDataService;
import com.javazx.batch.service.JobTitleService;
import com.javazx.batch.vo.UserInfoReq;
import com.javazx.batch.vo.UserSyncRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 指定病区医护人员数据同步场景
 * 同步指定病区的医护人员信息数据
 */
@Component
@Scope("prototype")
public class DoctorWardScenario extends AbstractBatchScenario<GyZhigongxx, UserInfoReq> {

    private static final Logger log = LoggerFactory.getLogger(DoctorWardScenario.class);

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private DoctorDataService doctorDataService;

    @Autowired
    private JobTitleService jobTitleService;

    // 默认病区ID，可以通过配置修改
    private String wardId = "1006";

    public DoctorWardScenario() {
        super("doctor_ward", "指定病区医护人员数据同步场景：同步指定病区的医护人员信息数据");
        this.setCommitInterval(100);
        this.setPageSize(50);
    }

    /**
     * 设置病区ID
     * @param wardId 病区ID
     */
    public void setWardId(String wardId) {
        this.wardId = wardId;
        log.info("设置医护人员同步场景病区ID为: {}", wardId);
    }

    /**
     * 获取当前配置的病区ID
     * @return 病区ID
     */
    public String getWardId() {
        return wardId;
    }

    @Override
    public ItemReader<GyZhigongxx> createReader() {
        log.info("创建病区{}医护人员数据读取器，页面大小: {}", wardId, getPageSize());
        
        // 获取总数
        int totalCount = doctorDataService.countDoctorsByWard(wardId);
        log.info("病区{}医护人员总数: {}", wardId, totalCount);

        return new ItemReader<GyZhigongxx>() {
            private int currentIndex = 0;
            private final int pageSize = getPageSize(); // 50
            private List<GyZhigongxx> currentPage = null;
            private int currentPageIndex = 0;

            @Override
            public GyZhigongxx read() {
                // 如果当前页为空或已读完，加载下一页
                if (currentPage == null || currentPageIndex >= currentPage.size()) {
                    loadNextPage();
                    currentPageIndex = 0;
                }

                // 如果加载后仍为空，说明没有更多数据
                if (currentPage == null || currentPage.isEmpty()) {
                    return null;
                }

                // 返回当前项并移动到下一项
                GyZhigongxx item = currentPage.get(currentPageIndex++);
                currentIndex++;
                
                log.debug("读取病区{}医护人员数据: {} - {} (进度: {}/{})", 
                    wardId, item.getZHIGONGID(), item.getZHIGONGXM(), currentIndex, totalCount);
                
                return item;
            }

            private void loadNextPage() {
                try {
                    int offset = (currentIndex / pageSize) * pageSize;
                    log.debug("加载病区{}医护人员数据页面 - 偏移: {}, 页面大小: {}", wardId, offset, pageSize);
                    
                    currentPage = doctorDataService.selectDoctorsByPageAndWard(offset, pageSize, wardId);
                    
                    if (currentPage != null && !currentPage.isEmpty()) {
                        log.debug("成功加载病区{}医护人员数据 {} 条", wardId, currentPage.size());
                    } else {
                        log.debug("病区{}没有更多医护人员数据", wardId);
                    }
                } catch (Exception e) {
                    log.error("加载病区{}医护人员数据失败: {}", wardId, e.getMessage(), e);
                    currentPage = null;
                }
            }
        };
    }

    @Override
    public ItemProcessor<GyZhigongxx, UserInfoReq> createProcessor() {
        log.info("创建病区{}医护人员数据转换器", wardId);
        return new DoctorProcessor(doctorDataService, jobTitleService);
    }
    
    @Override
    public ItemWriter<UserInfoReq> createWriter() {
        log.info("创建病区{}医护人员信息同步数据写入器", wardId);
        return items -> {
            UserSyncRequest request = new UserSyncRequest();
            
            // 安全的类型转换
            List<UserInfoReq> userList = new ArrayList<>();
            for (UserInfoReq item : items) {
                userList.add(item);
            }
            request.setUserInfoList(userList);

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理病区{}医护人员数据量: {}", threadName, wardId, items.size());

            try {
                List<String> userNames = getUserNameFromRequest(userList);
                String userNamesStr = String.join(", ", userNames);
                
                smartwardWebClient
                        .post()
                        .uri("/sync/user/sync_user_info")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request.getUserInfoList())
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("病区" + wardId + "医生 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("病区" + wardId + "医生 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送病区{}医护人员信息数据: {} (线程: {})", wardId, userNamesStr, threadName);
            } catch (Exception e) {
                log.error("病区{}医护人员信息同步时发生异常: {}", wardId, e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理病区{}医护人员数据量: {}", threadName, wardId, items.size());
        };
    }

    /**
     * 从请求对象中获取医护人员姓名
     */
    private List<String> getUserNameFromRequest(List<UserInfoReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知用户");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(UserInfoReq::getUserName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
