<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.ZyChuangweibdMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.ZyChuangweibd">
            <id property="CHUANGWEIBDID" column="CHUANGWEIBDID" jdbcType="VARCHAR"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="JIESUANXH" column="JIESUANXH" jdbcType="DECIMAL"/>
            <result property="KESHI1" column="KESHI1" jdbcType="VARCHAR"/>
            <result property="BINGQU1" column="BINGQU1" jdbcType="VARCHAR"/>
            <result property="CHUANGWEI1" column="CHUANGWEI1" jdbcType="VARCHAR"/>
            <result property="DANJIA1" column="DANJIA1" jdbcType="DECIMAL"/>
            <result property="KESHI2" column="KESHI2" jdbcType="VARCHAR"/>
            <result property="BINGQU2" column="BINGQU2" jdbcType="VARCHAR"/>
            <result property="CHUANGWEI2" column="CHUANGWEI2" jdbcType="VARCHAR"/>
            <result property="DANJIA2" column="DANJIA2" jdbcType="DECIMAL"/>
            <result property="ZHUZHIYS1" column="ZHUZHIYS1" jdbcType="VARCHAR"/>
            <result property="ZHURENYS1" column="ZHURENYS1" jdbcType="VARCHAR"/>
            <result property="ZHUYUANYS1" column="ZHUYUANYS1" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYS2" column="ZHUZHIYS2" jdbcType="VARCHAR"/>
            <result property="ZHURENYS2" column="ZHURENYS2" jdbcType="VARCHAR"/>
            <result property="ZHUYUANYS2" column="ZHUYUANYS2" jdbcType="VARCHAR"/>
            <result property="BIANDONGYY" column="BIANDONGYY" jdbcType="VARCHAR"/>
            <result property="XITONGSJ" column="XITONGSJ" jdbcType="TIMESTAMP"/>
            <result property="CAOZUOYUAN" column="CAOZUOYUAN" jdbcType="VARCHAR"/>
            <result property="YINGERID" column="YINGERID" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="YEWURQ" column="YEWURQ" jdbcType="TIMESTAMP"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="JIESHUSJ" column="JIESHUSJ" jdbcType="TIMESTAMP"/>
            <result property="ZHUANCHUSJ" column="ZHUANCHUSJ" jdbcType="TIMESTAMP"/>
            <result property="JIFEIBZ" column="JIFEIBZ" jdbcType="DECIMAL"/>
            <result property="JIECHUANGMD" column="JIECHUANGMD" jdbcType="DECIMAL"/>
            <result property="SHIJIZCSJ" column="SHIJIZCSJ" jdbcType="TIMESTAMP"/>
            <result property="SHIJIZCR" column="SHIJIZCR" jdbcType="VARCHAR"/>
            <result property="YUYUEBRID" column="YUYUEBRID" jdbcType="VARCHAR"/>
            <result property="BEIZHU" column="BEIZHU" jdbcType="VARCHAR"/>
            <result property="YILIAOZU1" column="YILIAOZU1" jdbcType="VARCHAR"/>
            <result property="YILIAOZU2" column="YILIAOZU2" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        CHUANGWEIBDID,BINGRENZYID,JIESUANXH,
        KESHI1,BINGQU1,CHUANGWEI1,
        DANJIA1,KESHI2,BINGQU2,
        CHUANGWEI2,DANJIA2,ZHUZHIYS1,
        ZHURENYS1,ZHUYUANYS1,ZHUZHIYS2,
        ZHURENYS2,ZHUYUANYS2,BIANDONGYY,
        XITONGSJ,CAOZUOYUAN,YINGERID,
        BINGRENID,YEWURQ,ZUOFEIBZ,
        JIESHUSJ,ZHUANCHUSJ,JIFEIBZ,
        JIECHUANGMD,SHIJIZCSJ,SHIJIZCR,
        YUYUEBRID,BEIZHU,YILIAOZU1,
        YILIAOZU2
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ZY_CHUANGWEIBD
        where  CHUANGWEIBDID = #{CHUANGWEIBDID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ZY_CHUANGWEIBD
        where  CHUANGWEIBDID = #{CHUANGWEIBDID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="CHUANGWEIBDID" keyProperty="CHUANGWEIBDID" parameterType="com.javazx.batch.po.ZyChuangweibd" useGeneratedKeys="true">
        insert into ZY_CHUANGWEIBD
        ( CHUANGWEIBDID,BINGRENZYID,JIESUANXH
        ,KESHI1,BINGQU1,CHUANGWEI1
        ,DANJIA1,KESHI2,BINGQU2
        ,CHUANGWEI2,DANJIA2,ZHUZHIYS1
        ,ZHURENYS1,ZHUYUANYS1,ZHUZHIYS2
        ,ZHURENYS2,ZHUYUANYS2,BIANDONGYY
        ,XITONGSJ,CAOZUOYUAN,YINGERID
        ,BINGRENID,YEWURQ,ZUOFEIBZ
        ,JIESHUSJ,ZHUANCHUSJ,JIFEIBZ
        ,JIECHUANGMD,SHIJIZCSJ,SHIJIZCR
        ,YUYUEBRID,BEIZHU,YILIAOZU1
        ,YILIAOZU2)
        values (#{CHUANGWEIBDID,jdbcType=VARCHAR},#{BINGRENZYID,jdbcType=VARCHAR},#{JIESUANXH,jdbcType=DECIMAL}
        ,#{KESHI1,jdbcType=VARCHAR},#{BINGQU1,jdbcType=VARCHAR},#{CHUANGWEI1,jdbcType=VARCHAR}
        ,#{DANJIA1,jdbcType=DECIMAL},#{KESHI2,jdbcType=VARCHAR},#{BINGQU2,jdbcType=VARCHAR}
        ,#{CHUANGWEI2,jdbcType=VARCHAR},#{DANJIA2,jdbcType=DECIMAL},#{ZHUZHIYS1,jdbcType=VARCHAR}
        ,#{ZHURENYS1,jdbcType=VARCHAR},#{ZHUYUANYS1,jdbcType=VARCHAR},#{ZHUZHIYS2,jdbcType=VARCHAR}
        ,#{ZHURENYS2,jdbcType=VARCHAR},#{ZHUYUANYS2,jdbcType=VARCHAR},#{BIANDONGYY,jdbcType=VARCHAR}
        ,#{XITONGSJ,jdbcType=TIMESTAMP},#{CAOZUOYUAN,jdbcType=VARCHAR},#{YINGERID,jdbcType=VARCHAR}
        ,#{BINGRENID,jdbcType=VARCHAR},#{YEWURQ,jdbcType=TIMESTAMP},#{ZUOFEIBZ,jdbcType=DECIMAL}
        ,#{JIESHUSJ,jdbcType=TIMESTAMP},#{ZHUANCHUSJ,jdbcType=TIMESTAMP},#{JIFEIBZ,jdbcType=DECIMAL}
        ,#{JIECHUANGMD,jdbcType=DECIMAL},#{SHIJIZCSJ,jdbcType=TIMESTAMP},#{SHIJIZCR,jdbcType=VARCHAR}
        ,#{YUYUEBRID,jdbcType=VARCHAR},#{BEIZHU,jdbcType=VARCHAR},#{YILIAOZU1,jdbcType=VARCHAR}
        ,#{YILIAOZU2,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="CHUANGWEIBDID" keyProperty="CHUANGWEIBDID" parameterType="com.javazx.batch.po.ZyChuangweibd" useGeneratedKeys="true">
        insert into ZY_CHUANGWEIBD
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="CHUANGWEIBDID != null">CHUANGWEIBDID,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="JIESUANXH != null">JIESUANXH,</if>
                <if test="KESHI1 != null">KESHI1,</if>
                <if test="BINGQU1 != null">BINGQU1,</if>
                <if test="CHUANGWEI1 != null">CHUANGWEI1,</if>
                <if test="DANJIA1 != null">DANJIA1,</if>
                <if test="KESHI2 != null">KESHI2,</if>
                <if test="BINGQU2 != null">BINGQU2,</if>
                <if test="CHUANGWEI2 != null">CHUANGWEI2,</if>
                <if test="DANJIA2 != null">DANJIA2,</if>
                <if test="ZHUZHIYS1 != null">ZHUZHIYS1,</if>
                <if test="ZHURENYS1 != null">ZHURENYS1,</if>
                <if test="ZHUYUANYS1 != null">ZHUYUANYS1,</if>
                <if test="ZHUZHIYS2 != null">ZHUZHIYS2,</if>
                <if test="ZHURENYS2 != null">ZHURENYS2,</if>
                <if test="ZHUYUANYS2 != null">ZHUYUANYS2,</if>
                <if test="BIANDONGYY != null">BIANDONGYY,</if>
                <if test="XITONGSJ != null">XITONGSJ,</if>
                <if test="CAOZUOYUAN != null">CAOZUOYUAN,</if>
                <if test="YINGERID != null">YINGERID,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="YEWURQ != null">YEWURQ,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="JIESHUSJ != null">JIESHUSJ,</if>
                <if test="ZHUANCHUSJ != null">ZHUANCHUSJ,</if>
                <if test="JIFEIBZ != null">JIFEIBZ,</if>
                <if test="JIECHUANGMD != null">JIECHUANGMD,</if>
                <if test="SHIJIZCSJ != null">SHIJIZCSJ,</if>
                <if test="SHIJIZCR != null">SHIJIZCR,</if>
                <if test="YUYUEBRID != null">YUYUEBRID,</if>
                <if test="BEIZHU != null">BEIZHU,</if>
                <if test="YILIAOZU1 != null">YILIAOZU1,</if>
                <if test="YILIAOZU2 != null">YILIAOZU2,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="CHUANGWEIBDID != null">#{CHUANGWEIBDID,jdbcType=VARCHAR},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="JIESUANXH != null">#{JIESUANXH,jdbcType=DECIMAL},</if>
                <if test="KESHI1 != null">#{KESHI1,jdbcType=VARCHAR},</if>
                <if test="BINGQU1 != null">#{BINGQU1,jdbcType=VARCHAR},</if>
                <if test="CHUANGWEI1 != null">#{CHUANGWEI1,jdbcType=VARCHAR},</if>
                <if test="DANJIA1 != null">#{DANJIA1,jdbcType=DECIMAL},</if>
                <if test="KESHI2 != null">#{KESHI2,jdbcType=VARCHAR},</if>
                <if test="BINGQU2 != null">#{BINGQU2,jdbcType=VARCHAR},</if>
                <if test="CHUANGWEI2 != null">#{CHUANGWEI2,jdbcType=VARCHAR},</if>
                <if test="DANJIA2 != null">#{DANJIA2,jdbcType=DECIMAL},</if>
                <if test="ZHUZHIYS1 != null">#{ZHUZHIYS1,jdbcType=VARCHAR},</if>
                <if test="ZHURENYS1 != null">#{ZHURENYS1,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANYS1 != null">#{ZHUYUANYS1,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYS2 != null">#{ZHUZHIYS2,jdbcType=VARCHAR},</if>
                <if test="ZHURENYS2 != null">#{ZHURENYS2,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANYS2 != null">#{ZHUYUANYS2,jdbcType=VARCHAR},</if>
                <if test="BIANDONGYY != null">#{BIANDONGYY,jdbcType=VARCHAR},</if>
                <if test="XITONGSJ != null">#{XITONGSJ,jdbcType=TIMESTAMP},</if>
                <if test="CAOZUOYUAN != null">#{CAOZUOYUAN,jdbcType=VARCHAR},</if>
                <if test="YINGERID != null">#{YINGERID,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="YEWURQ != null">#{YEWURQ,jdbcType=TIMESTAMP},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="JIESHUSJ != null">#{JIESHUSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHUANCHUSJ != null">#{ZHUANCHUSJ,jdbcType=TIMESTAMP},</if>
                <if test="JIFEIBZ != null">#{JIFEIBZ,jdbcType=DECIMAL},</if>
                <if test="JIECHUANGMD != null">#{JIECHUANGMD,jdbcType=DECIMAL},</if>
                <if test="SHIJIZCSJ != null">#{SHIJIZCSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHIJIZCR != null">#{SHIJIZCR,jdbcType=VARCHAR},</if>
                <if test="YUYUEBRID != null">#{YUYUEBRID,jdbcType=VARCHAR},</if>
                <if test="BEIZHU != null">#{BEIZHU,jdbcType=VARCHAR},</if>
                <if test="YILIAOZU1 != null">#{YILIAOZU1,jdbcType=VARCHAR},</if>
                <if test="YILIAOZU2 != null">#{YILIAOZU2,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.ZyChuangweibd">
        update ZY_CHUANGWEIBD
        <set>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="JIESUANXH != null">
                    JIESUANXH = #{JIESUANXH,jdbcType=DECIMAL},
                </if>
                <if test="KESHI1 != null">
                    KESHI1 = #{KESHI1,jdbcType=VARCHAR},
                </if>
                <if test="BINGQU1 != null">
                    BINGQU1 = #{BINGQU1,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGWEI1 != null">
                    CHUANGWEI1 = #{CHUANGWEI1,jdbcType=VARCHAR},
                </if>
                <if test="DANJIA1 != null">
                    DANJIA1 = #{DANJIA1,jdbcType=DECIMAL},
                </if>
                <if test="KESHI2 != null">
                    KESHI2 = #{KESHI2,jdbcType=VARCHAR},
                </if>
                <if test="BINGQU2 != null">
                    BINGQU2 = #{BINGQU2,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGWEI2 != null">
                    CHUANGWEI2 = #{CHUANGWEI2,jdbcType=VARCHAR},
                </if>
                <if test="DANJIA2 != null">
                    DANJIA2 = #{DANJIA2,jdbcType=DECIMAL},
                </if>
                <if test="ZHUZHIYS1 != null">
                    ZHUZHIYS1 = #{ZHUZHIYS1,jdbcType=VARCHAR},
                </if>
                <if test="ZHURENYS1 != null">
                    ZHURENYS1 = #{ZHURENYS1,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANYS1 != null">
                    ZHUYUANYS1 = #{ZHUYUANYS1,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYS2 != null">
                    ZHUZHIYS2 = #{ZHUZHIYS2,jdbcType=VARCHAR},
                </if>
                <if test="ZHURENYS2 != null">
                    ZHURENYS2 = #{ZHURENYS2,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANYS2 != null">
                    ZHUYUANYS2 = #{ZHUYUANYS2,jdbcType=VARCHAR},
                </if>
                <if test="BIANDONGYY != null">
                    BIANDONGYY = #{BIANDONGYY,jdbcType=VARCHAR},
                </if>
                <if test="XITONGSJ != null">
                    XITONGSJ = #{XITONGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CAOZUOYUAN != null">
                    CAOZUOYUAN = #{CAOZUOYUAN,jdbcType=VARCHAR},
                </if>
                <if test="YINGERID != null">
                    YINGERID = #{YINGERID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="YEWURQ != null">
                    YEWURQ = #{YEWURQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIESHUSJ != null">
                    JIESHUSJ = #{JIESHUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHUANCHUSJ != null">
                    ZHUANCHUSJ = #{ZHUANCHUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIFEIBZ != null">
                    JIFEIBZ = #{JIFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIECHUANGMD != null">
                    JIECHUANGMD = #{JIECHUANGMD,jdbcType=DECIMAL},
                </if>
                <if test="SHIJIZCSJ != null">
                    SHIJIZCSJ = #{SHIJIZCSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHIJIZCR != null">
                    SHIJIZCR = #{SHIJIZCR,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEBRID != null">
                    YUYUEBRID = #{YUYUEBRID,jdbcType=VARCHAR},
                </if>
                <if test="BEIZHU != null">
                    BEIZHU = #{BEIZHU,jdbcType=VARCHAR},
                </if>
                <if test="YILIAOZU1 != null">
                    YILIAOZU1 = #{YILIAOZU1,jdbcType=VARCHAR},
                </if>
                <if test="YILIAOZU2 != null">
                    YILIAOZU2 = #{YILIAOZU2,jdbcType=VARCHAR},
                </if>
        </set>
        where   CHUANGWEIBDID = #{CHUANGWEIBDID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.ZyChuangweibd">
        update ZY_CHUANGWEIBD
        set 
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            JIESUANXH =  #{JIESUANXH,jdbcType=DECIMAL},
            KESHI1 =  #{KESHI1,jdbcType=VARCHAR},
            BINGQU1 =  #{BINGQU1,jdbcType=VARCHAR},
            CHUANGWEI1 =  #{CHUANGWEI1,jdbcType=VARCHAR},
            DANJIA1 =  #{DANJIA1,jdbcType=DECIMAL},
            KESHI2 =  #{KESHI2,jdbcType=VARCHAR},
            BINGQU2 =  #{BINGQU2,jdbcType=VARCHAR},
            CHUANGWEI2 =  #{CHUANGWEI2,jdbcType=VARCHAR},
            DANJIA2 =  #{DANJIA2,jdbcType=DECIMAL},
            ZHUZHIYS1 =  #{ZHUZHIYS1,jdbcType=VARCHAR},
            ZHURENYS1 =  #{ZHURENYS1,jdbcType=VARCHAR},
            ZHUYUANYS1 =  #{ZHUYUANYS1,jdbcType=VARCHAR},
            ZHUZHIYS2 =  #{ZHUZHIYS2,jdbcType=VARCHAR},
            ZHURENYS2 =  #{ZHURENYS2,jdbcType=VARCHAR},
            ZHUYUANYS2 =  #{ZHUYUANYS2,jdbcType=VARCHAR},
            BIANDONGYY =  #{BIANDONGYY,jdbcType=VARCHAR},
            XITONGSJ =  #{XITONGSJ,jdbcType=TIMESTAMP},
            CAOZUOYUAN =  #{CAOZUOYUAN,jdbcType=VARCHAR},
            YINGERID =  #{YINGERID,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            YEWURQ =  #{YEWURQ,jdbcType=TIMESTAMP},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            JIESHUSJ =  #{JIESHUSJ,jdbcType=TIMESTAMP},
            ZHUANCHUSJ =  #{ZHUANCHUSJ,jdbcType=TIMESTAMP},
            JIFEIBZ =  #{JIFEIBZ,jdbcType=DECIMAL},
            JIECHUANGMD =  #{JIECHUANGMD,jdbcType=DECIMAL},
            SHIJIZCSJ =  #{SHIJIZCSJ,jdbcType=TIMESTAMP},
            SHIJIZCR =  #{SHIJIZCR,jdbcType=VARCHAR},
            YUYUEBRID =  #{YUYUEBRID,jdbcType=VARCHAR},
            BEIZHU =  #{BEIZHU,jdbcType=VARCHAR},
            YILIAOZU1 =  #{YILIAOZU1,jdbcType=VARCHAR},
            YILIAOZU2 =  #{YILIAOZU2,jdbcType=VARCHAR}
        where   CHUANGWEIBDID = #{CHUANGWEIBDID,jdbcType=VARCHAR} 
    </update>
</mapper>
