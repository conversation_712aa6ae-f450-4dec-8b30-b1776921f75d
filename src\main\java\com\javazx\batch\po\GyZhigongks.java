package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 公用_职工科室
 * @TableName GY_ZHIGONGKS
 */
@TableName(value ="GY_ZHIGONGKS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GyZhigongks implements Serializable {
    /**
     * 职工ID
     */
    @TableId(value = "ZHIGONGID")
    private String ZHIGONGID;

    /**
     * 科室病区ID
     */
    @TableField(value = "KESHIBQID")
    private String KESHIBQID;

    /**
     * 科室病区标志
     */
    @TableField(value = "KESHIBQBZ")
    private Integer KESHIBQBZ;

    /**
     * 顺序号
     */
    @TableField(value = "SHUNXUHAO")
    private Integer SHUNXUHAO;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private Date XIUGAISJ;

    /**
     * 科室病区名称
     */
    @TableField(value = "KESHIBQMC")
    private String KESHIBQMC;

    /**
     * 门诊标志HR5-14574(661457)
     */
    @TableField(value = "MENZHENBZ")
    private Integer MENZHENBZ;

    /**
     * 住院标志HR5-14574(661457)
     */
    @TableField(value = "ZHUYUANBZ")
    private Integer ZHUYUANBZ;

    /**
     * 职务ID
     */
    @TableField(value = "ZHIWUID")
    private String ZHIWUID;

    /**
     * 职务名称
     */
    @TableField(value = "ZHIWUMC")
    private String ZHIWUMC;

    /**
     * 留抢使用HR6-2902(566667)
     */
    @TableField(value = "LIUQIANGSY")
    private Integer LIUQIANGSY;

    /**
     * 体检标志
     */
    @TableField(value = "TIJIANBZ")
    private Integer TIJIANBZ;

    /**
     * 
     */
    @TableField(value = "YUANQUID")
    private Integer YUANQUID;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}