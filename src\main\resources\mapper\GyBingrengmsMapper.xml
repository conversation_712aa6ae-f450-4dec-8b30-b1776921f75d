<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.GyBingrengmsMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.GyBingrengms">
            <id property="GUOMINSID" column="GUOMINSID" jdbcType="VARCHAR"/>
            <result property="YINGYONGID" column="YINGYONGID" jdbcType="VARCHAR"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="LAIYUANID" column="LAIYUANID" jdbcType="VARCHAR"/>
            <result property="JILULY" column="JILULY" jdbcType="VARCHAR"/>
            <result property="JIAGEID" column="JIAGEID" jdbcType="VARCHAR"/>
            <result property="YAOPINMC" column="YAOPINMC" jdbcType="VARCHAR"/>
            <result property="CHULIYJ" column="CHULIYJ" jdbcType="VARCHAR"/>
            <result property="PISHIJG" column="PISHIJG" jdbcType="VARCHAR"/>
            <result property="ZHIXINGSJ" column="ZHIXINGSJ" jdbcType="VARCHAR"/>
            <result property="ZHIXINGREN" column="ZHIXINGREN" jdbcType="VARCHAR"/>
            <result property="ZHIXINGRXM" column="ZHIXINGRXM" jdbcType="VARCHAR"/>
            <result property="GUOMINLX" column="GUOMINLX" jdbcType="VARCHAR"/>
            <result property="YAOPINID" column="YAOPINID" jdbcType="VARCHAR"/>
            <result property="SHENFENZH" column="SHENFENZH" jdbcType="VARCHAR"/>
            <result property="YAOWUFY" column="YAOWUFY" jdbcType="VARCHAR"/>
            <result property="BULIANGFYXQ" column="BULIANGFYXQ" jdbcType="VARCHAR"/>
            <result property="YANJINFLAG" column="YANJINFLAG" jdbcType="DECIMAL"/>
            <result property="GUOMINYFL" column="GUOMINYFL" jdbcType="VARCHAR"/>
            <result property="JLSJ" column="JLSJ" jdbcType="TIMESTAMP"/>
            <result property="JLREN" column="JLREN" jdbcType="VARCHAR"/>
            <result property="GUOMINFY" column="GUOMINFY" jdbcType="VARCHAR"/>
            <result property="MPI" column="MPI" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        GUOMINSID,YINGYONGID,YUANQUID,
        BINGRENID,LAIYUANID,JILULY,
        JIAGEID,YAOPINMC,CHULIYJ,
        PISHIJG,ZHIXINGSJ,ZHIXINGREN,
        ZHIXINGRXM,GUOMINLX,YAOPINID,
        SHENFENZH,YAOWUFY,BULIANGFYXQ,
        YANJINFLAG,GUOMINYFL,JLSJ,
        JLREN,GUOMINFY,MPI
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from HIS6.GY_BINGRENGMS
        where  GUOMINSID = #{GUOMINSID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from HIS6.GY_BINGRENGMS
        where  GUOMINSID = #{GUOMINSID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="GUOMINSID" keyProperty="GUOMINSID" parameterType="com.javazx.batch.po.GyBingrengms" useGeneratedKeys="true">
        insert into HIS6.GY_BINGRENGMS
        ( GUOMINSID,YINGYONGID,YUANQUID
        ,BINGRENID,LAIYUANID,JILULY
        ,JIAGEID,YAOPINMC,CHULIYJ
        ,PISHIJG,ZHIXINGSJ,ZHIXINGREN
        ,ZHIXINGRXM,GUOMINLX,YAOPINID
        ,SHENFENZH,YAOWUFY,BULIANGFYXQ
        ,YANJINFLAG,GUOMINYFL,JLSJ
        ,JLREN,GUOMINFY,MPI
        )
        values (#{GUOMINSID,jdbcType=VARCHAR},#{YINGYONGID,jdbcType=VARCHAR},#{YUANQUID,jdbcType=VARCHAR}
        ,#{BINGRENID,jdbcType=VARCHAR},#{LAIYUANID,jdbcType=VARCHAR},#{JILULY,jdbcType=VARCHAR}
        ,#{JIAGEID,jdbcType=VARCHAR},#{YAOPINMC,jdbcType=VARCHAR},#{CHULIYJ,jdbcType=VARCHAR}
        ,#{PISHIJG,jdbcType=VARCHAR},#{ZHIXINGSJ,jdbcType=VARCHAR},#{ZHIXINGREN,jdbcType=VARCHAR}
        ,#{ZHIXINGRXM,jdbcType=VARCHAR},#{GUOMINLX,jdbcType=VARCHAR},#{YAOPINID,jdbcType=VARCHAR}
        ,#{SHENFENZH,jdbcType=VARCHAR},#{YAOWUFY,jdbcType=VARCHAR},#{BULIANGFYXQ,jdbcType=VARCHAR}
        ,#{YANJINFLAG,jdbcType=DECIMAL},#{GUOMINYFL,jdbcType=VARCHAR},#{JLSJ,jdbcType=TIMESTAMP}
        ,#{JLREN,jdbcType=VARCHAR},#{GUOMINFY,jdbcType=VARCHAR},#{MPI,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" keyColumn="GUOMINSID" keyProperty="GUOMINSID" parameterType="com.javazx.batch.po.GyBingrengms" useGeneratedKeys="true">
        insert into HIS6.GY_BINGRENGMS
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="GUOMINSID != null">GUOMINSID,</if>
                <if test="YINGYONGID != null">YINGYONGID,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="LAIYUANID != null">LAIYUANID,</if>
                <if test="JILULY != null">JILULY,</if>
                <if test="JIAGEID != null">JIAGEID,</if>
                <if test="YAOPINMC != null">YAOPINMC,</if>
                <if test="CHULIYJ != null">CHULIYJ,</if>
                <if test="PISHIJG != null">PISHIJG,</if>
                <if test="ZHIXINGSJ != null">ZHIXINGSJ,</if>
                <if test="ZHIXINGREN != null">ZHIXINGREN,</if>
                <if test="ZHIXINGRXM != null">ZHIXINGRXM,</if>
                <if test="GUOMINLX != null">GUOMINLX,</if>
                <if test="YAOPINID != null">YAOPINID,</if>
                <if test="SHENFENZH != null">SHENFENZH,</if>
                <if test="YAOWUFY != null">YAOWUFY,</if>
                <if test="BULIANGFYXQ != null">BULIANGFYXQ,</if>
                <if test="YANJINFLAG != null">YANJINFLAG,</if>
                <if test="GUOMINYFL != null">GUOMINYFL,</if>
                <if test="JLSJ != null">JLSJ,</if>
                <if test="JLREN != null">JLREN,</if>
                <if test="GUOMINFY != null">GUOMINFY,</if>
                <if test="MPI != null">MPI,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="GUOMINSID != null">#{GUOMINSID,jdbcType=VARCHAR},</if>
                <if test="YINGYONGID != null">#{YINGYONGID,jdbcType=VARCHAR},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="LAIYUANID != null">#{LAIYUANID,jdbcType=VARCHAR},</if>
                <if test="JILULY != null">#{JILULY,jdbcType=VARCHAR},</if>
                <if test="JIAGEID != null">#{JIAGEID,jdbcType=VARCHAR},</if>
                <if test="YAOPINMC != null">#{YAOPINMC,jdbcType=VARCHAR},</if>
                <if test="CHULIYJ != null">#{CHULIYJ,jdbcType=VARCHAR},</if>
                <if test="PISHIJG != null">#{PISHIJG,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGSJ != null">#{ZHIXINGSJ,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGREN != null">#{ZHIXINGREN,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGRXM != null">#{ZHIXINGRXM,jdbcType=VARCHAR},</if>
                <if test="GUOMINLX != null">#{GUOMINLX,jdbcType=VARCHAR},</if>
                <if test="YAOPINID != null">#{YAOPINID,jdbcType=VARCHAR},</if>
                <if test="SHENFENZH != null">#{SHENFENZH,jdbcType=VARCHAR},</if>
                <if test="YAOWUFY != null">#{YAOWUFY,jdbcType=VARCHAR},</if>
                <if test="BULIANGFYXQ != null">#{BULIANGFYXQ,jdbcType=VARCHAR},</if>
                <if test="YANJINFLAG != null">#{YANJINFLAG,jdbcType=DECIMAL},</if>
                <if test="GUOMINYFL != null">#{GUOMINYFL,jdbcType=VARCHAR},</if>
                <if test="JLSJ != null">#{JLSJ,jdbcType=TIMESTAMP},</if>
                <if test="JLREN != null">#{JLREN,jdbcType=VARCHAR},</if>
                <if test="GUOMINFY != null">#{GUOMINFY,jdbcType=VARCHAR},</if>
                <if test="MPI != null">#{MPI,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.GyBingrengms">
        update HIS6.GY_BINGRENGMS
        <set>
                <if test="YINGYONGID != null">
                    YINGYONGID = #{YINGYONGID,jdbcType=VARCHAR},
                </if>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="LAIYUANID != null">
                    LAIYUANID = #{LAIYUANID,jdbcType=VARCHAR},
                </if>
                <if test="JILULY != null">
                    JILULY = #{JILULY,jdbcType=VARCHAR},
                </if>
                <if test="JIAGEID != null">
                    JIAGEID = #{JIAGEID,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINMC != null">
                    YAOPINMC = #{YAOPINMC,jdbcType=VARCHAR},
                </if>
                <if test="CHULIYJ != null">
                    CHULIYJ = #{CHULIYJ,jdbcType=VARCHAR},
                </if>
                <if test="PISHIJG != null">
                    PISHIJG = #{PISHIJG,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGSJ != null">
                    ZHIXINGSJ = #{ZHIXINGSJ,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGREN != null">
                    ZHIXINGREN = #{ZHIXINGREN,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGRXM != null">
                    ZHIXINGRXM = #{ZHIXINGRXM,jdbcType=VARCHAR},
                </if>
                <if test="GUOMINLX != null">
                    GUOMINLX = #{GUOMINLX,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINID != null">
                    YAOPINID = #{YAOPINID,jdbcType=VARCHAR},
                </if>
                <if test="SHENFENZH != null">
                    SHENFENZH = #{SHENFENZH,jdbcType=VARCHAR},
                </if>
                <if test="YAOWUFY != null">
                    YAOWUFY = #{YAOWUFY,jdbcType=VARCHAR},
                </if>
                <if test="BULIANGFYXQ != null">
                    BULIANGFYXQ = #{BULIANGFYXQ,jdbcType=VARCHAR},
                </if>
                <if test="YANJINFLAG != null">
                    YANJINFLAG = #{YANJINFLAG,jdbcType=DECIMAL},
                </if>
                <if test="GUOMINYFL != null">
                    GUOMINYFL = #{GUOMINYFL,jdbcType=VARCHAR},
                </if>
                <if test="JLSJ != null">
                    JLSJ = #{JLSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JLREN != null">
                    JLREN = #{JLREN,jdbcType=VARCHAR},
                </if>
                <if test="GUOMINFY != null">
                    GUOMINFY = #{GUOMINFY,jdbcType=VARCHAR},
                </if>
                <if test="MPI != null">
                    MPI = #{MPI,jdbcType=VARCHAR},
                </if>
        </set>
        where   GUOMINSID = #{GUOMINSID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.GyBingrengms">
        update HIS6.GY_BINGRENGMS
        set 
            YINGYONGID =  #{YINGYONGID,jdbcType=VARCHAR},
            YUANQUID =  #{YUANQUID,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            LAIYUANID =  #{LAIYUANID,jdbcType=VARCHAR},
            JILULY =  #{JILULY,jdbcType=VARCHAR},
            JIAGEID =  #{JIAGEID,jdbcType=VARCHAR},
            YAOPINMC =  #{YAOPINMC,jdbcType=VARCHAR},
            CHULIYJ =  #{CHULIYJ,jdbcType=VARCHAR},
            PISHIJG =  #{PISHIJG,jdbcType=VARCHAR},
            ZHIXINGSJ =  #{ZHIXINGSJ,jdbcType=VARCHAR},
            ZHIXINGREN =  #{ZHIXINGREN,jdbcType=VARCHAR},
            ZHIXINGRXM =  #{ZHIXINGRXM,jdbcType=VARCHAR},
            GUOMINLX =  #{GUOMINLX,jdbcType=VARCHAR},
            YAOPINID =  #{YAOPINID,jdbcType=VARCHAR},
            SHENFENZH =  #{SHENFENZH,jdbcType=VARCHAR},
            YAOWUFY =  #{YAOWUFY,jdbcType=VARCHAR},
            BULIANGFYXQ =  #{BULIANGFYXQ,jdbcType=VARCHAR},
            YANJINFLAG =  #{YANJINFLAG,jdbcType=DECIMAL},
            GUOMINYFL =  #{GUOMINYFL,jdbcType=VARCHAR},
            JLSJ =  #{JLSJ,jdbcType=TIMESTAMP},
            JLREN =  #{JLREN,jdbcType=VARCHAR},
            GUOMINFY =  #{GUOMINFY,jdbcType=VARCHAR},
            MPI =  #{MPI,jdbcType=VARCHAR}
        where   GUOMINSID = #{GUOMINSID,jdbcType=VARCHAR} 
    </update>
</mapper>
