<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.GyZhigongxxMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.GyZhigongxx">
            <id property="ZHIGONGID" column="ZHIGONGID" jdbcType="VARCHAR"/>
            <result property="ZHIGONGGH" column="ZHIGONGGH" jdbcType="VARCHAR"/>
            <result property="ZHIGONGXM" column="ZHIGONGXM" jdbcType="VARCHAR"/>
            <result property="XINGBIE" column="XINGBIE" jdbcType="VARCHAR"/>
            <result property="CHUSHENGRQ" column="CHUSHENGRQ" jdbcType="TIMESTAMP"/>
            <result property="SHENFENZH" column="SHENFENZH" jdbcType="VARCHAR"/>
            <result property="JIATINGDZ" column="JIATINGDZ" jdbcType="VARCHAR"/>
            <result property="JIATINGYB" column="JIATINGYB" jdbcType="VARCHAR"/>
            <result property="DIANZIYJ" column="DIANZIYJ" jdbcType="VARCHAR"/>
            <result property="DIANHUA" column="DIANHUA" jdbcType="VARCHAR"/>
            <result property="ZHIWU" column="ZHIWU" jdbcType="VARCHAR"/>
            <result property="ZHICHENG" column="ZHICHENG" jdbcType="VARCHAR"/>
            <result property="CANJIAGZSJ" column="CANJIAGZSJ" jdbcType="TIMESTAMP"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="SHURUMA1" column="SHURUMA1" jdbcType="VARCHAR"/>
            <result property="SHURUMA3" column="SHURUMA3" jdbcType="VARCHAR"/>
            <result property="SHURUMA2" column="SHURUMA2" jdbcType="VARCHAR"/>
            <result property="DANGQIANZT" column="DANGQIANZT" jdbcType="VARCHAR"/>
            <result property="QUANXIAN" column="QUANXIAN" jdbcType="VARCHAR"/>
            <result property="MENZHENLB" column="MENZHENLB" jdbcType="VARCHAR"/>
            <result property="ZHIGONGLB" column="ZHIGONGLB" jdbcType="VARCHAR"/>
            <result property="MIMA" column="MIMA" jdbcType="VARCHAR"/>
            <result property="RENSHIKS" column="RENSHIKS" jdbcType="VARCHAR"/>
            <result property="GUAHAOFXM" column="GUAHAOFXM" jdbcType="VARCHAR"/>
            <result property="ZHENLIAOFXM" column="ZHENLIAOFXM" jdbcType="VARCHAR"/>
            <result property="KESHIID" column="KESHIID" jdbcType="VARCHAR"/>
            <result property="YISHENGDJ" column="YISHENGDJ" jdbcType="VARCHAR"/>
            <result property="YINGWENMING" column="YINGWENMING" jdbcType="VARCHAR"/>
            <result property="QIANMINGBZ" column="QIANMINGBZ" jdbcType="DECIMAL"/>
            <result property="TUZHANGHAO" column="TUZHANGHAO" jdbcType="VARCHAR"/>
            <result property="ZHIGONGZL" column="ZHIGONGZL" jdbcType="VARCHAR"/>
            <result property="HESUANKS" column="HESUANKS" jdbcType="VARCHAR"/>
            <result property="YISHIZGZS" column="YISHIZGZS" jdbcType="VARCHAR"/>
            <result property="ZHIYEKSRQ" column="ZHIYEKSRQ" jdbcType="TIMESTAMP"/>
            <result property="ZHIYEJSRQ" column="ZHIYEJSRQ" jdbcType="TIMESTAMP"/>
            <result property="YISHIZYZS" column="YISHIZYZS" jdbcType="VARCHAR"/>
            <result property="ZAIZHIZT" column="ZAIZHIZT" jdbcType="VARCHAR"/>
            <result property="ZHIYEZG" column="ZHIYEZG" jdbcType="VARCHAR"/>
            <result property="RENYUANBZLB" column="RENYUANBZLB" jdbcType="VARCHAR"/>
            <result property="ZHIYESXH" column="ZHIYESXH" jdbcType="VARCHAR"/>
            <result property="ZHIYEFW" column="ZHIYEFW" jdbcType="VARCHAR"/>
            <result property="ZHIYEKB" column="ZHIYEKB" jdbcType="VARCHAR"/>
            <result property="KANGFUZZ" column="KANGFUZZ" jdbcType="VARCHAR"/>
            <result property="SHIFOUJSJL" column="SHIFOUJSJL" jdbcType="VARCHAR"/>
            <result property="YIBAOYSFWBM" column="YIBAOYSFWBM" jdbcType="VARCHAR"/>
            <result property="YISHIJB" column="YISHIJB" jdbcType="VARCHAR"/>
            <result property="ZHIYEDD" column="ZHIYEDD" jdbcType="VARCHAR"/>
            <result property="ZHIYELB" column="ZHIYELB" jdbcType="VARCHAR"/>
            <result property="DIANHUA1" column="DIANHUA1" jdbcType="VARCHAR"/>
            <result property="DIANHUA2" column="DIANHUA2" jdbcType="VARCHAR"/>
            <result property="RENYUANXH" column="RENYUANXH" jdbcType="VARCHAR"/>
            <result property="GUAHAOWID" column="GUAHAOWID" jdbcType="VARCHAR"/>
            <result property="XUELI" column="XUELI" jdbcType="VARCHAR"/>
            <result property="MENZHENDZBLQYBZ" column="MENZHENDZBLQYBZ" jdbcType="DECIMAL"/>
            <result property="SHEQUGH" column="SHEQUGH" jdbcType="VARCHAR"/>
            <result property="SHEQUMM" column="SHEQUMM" jdbcType="VARCHAR"/>
            <result property="SHEQUJS" column="SHEQUJS" jdbcType="VARCHAR"/>
            <result property="JISHUZC" column="JISHUZC" jdbcType="VARCHAR"/>
            <result property="CABZ" column="CABZ" jdbcType="DECIMAL"/>
            <result property="TUIJIANSX" column="TUIJIANSX" jdbcType="VARCHAR"/>
            <result property="ZHENDUANJMYCGJLQYBZ" column="ZHENDUANJMYCGJLQYBZ" jdbcType="DECIMAL"/>
            <result property="ZYCABZ" column="ZYCABZ" jdbcType="DECIMAL"/>
            <result property="DIANZIFPDM" column="DIANZIFPDM" jdbcType="VARCHAR"/>
            <result property="NINGFANQMBZ" column="NINGFANQMBZ" jdbcType="DECIMAL"/>
            <result property="ZHIGONGZC" column="ZHIGONGZC" jdbcType="VARCHAR"/>
            <result property="ZHAOPIANBZ" column="ZHAOPIANBZ" jdbcType="DECIMAL"/>
            <result property="BINGQUID" column="BINGQUID" jdbcType="VARCHAR"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="YIBAOYSGJDM" column="YIBAOYSGJDM" jdbcType="VARCHAR"/>
            <result property="YIBAOGJDM" column="YIBAOGJDM" jdbcType="VARCHAR"/>
            <result property="ZHONGYISSFXM" column="ZHONGYISSFXM" jdbcType="VARCHAR"/>
            <result property="ZHONGYISBZ" column="ZHONGYISBZ" jdbcType="VARCHAR"/>
            <result property="TESHUHZQX" column="TESHUHZQX" jdbcType="VARCHAR"/>
            <result property="PINRENSJ" column="PINRENSJ" jdbcType="TIMESTAMP"/>
            <result property="ZHONGYIBZFXM" column="ZHONGYIBZFXM" jdbcType="VARCHAR"/>
            <result property="BINGCHENGZKBZ" column="BINGCHENGZKBZ" jdbcType="DECIMAL"/>
            <result property="YUANQUSY" column="YUANQUSY" jdbcType="VARCHAR"/>
            <result property="MENZHENKLJYF" column="MENZHENKLJYF" jdbcType="VARCHAR"/>
            <result property="ZHUYUANKLJYF" column="ZHUYUANKLJYF" jdbcType="VARCHAR"/>
            <result property="CDSSQYBZ" column="CDSSQYBZ" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        ZHIGONGID,ZHIGONGGH,ZHIGONGXM,
        XINGBIE,CHUSHENGRQ,SHENFENZH,
        JIATINGDZ,JIATINGYB,DIANZIYJ,
        DIANHUA,ZHIWU,ZHICHENG,
        CANJIAGZSJ,XIUGAIREN,XIUGAISJ,
        SHURUMA1,SHURUMA3,SHURUMA2,
        DANGQIANZT,QUANXIAN,MENZHENLB,
        ZHIGONGLB,MIMA,RENSHIKS,
        GUAHAOFXM,ZHENLIAOFXM,KESHIID,
        YISHENGDJ,YINGWENMING,QIANMINGBZ,
        TUZHANGHAO,ZHIGONGZL,HESUANKS,
        YISHIZGZS,ZHIYEKSRQ,ZHIYEJSRQ,
        YISHIZYZS,ZAIZHIZT,ZHIYEZG,
        RENYUANBZLB,ZHIYESXH,ZHIYEFW,
        ZHIYEKB,KANGFUZZ,SHIFOUJSJL,
        YIBAOYSFWBM,YISHIJB,ZHIYEDD,
        ZHIYELB,DIANHUA1,DIANHUA2,
        RENYUANXH,GUAHAOWID,XUELI,
        MENZHENDZBLQYBZ,SHEQUGH,SHEQUMM,
        SHEQUJS,JISHUZC,CABZ,
        TUIJIANSX,ZHENDUANJMYCGJLQYBZ,ZYCABZ,
        DIANZIFPDM,NINGFANQMBZ,ZHIGONGZC,
        ZHAOPIANBZ,BINGQUID,ZUOFEIBZ,
        YIBAOYSGJDM,YIBAOGJDM,ZHONGYISSFXM,
        ZHONGYISBZ,TESHUHZQX,PINRENSJ,
        ZHONGYIBZFXM,BINGCHENGZKBZ,YUANQUSY,
        MENZHENKLJYF,ZHUYUANKLJYF,CDSSQYBZ,
        MENUDATA
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from GY_ZHIGONGXX
        where  ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from GY_ZHIGONGXX
        where  ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="ZHIGONGID" keyProperty="ZHIGONGID" parameterType="com.javazx.batch.po.GyZhigongxx" useGeneratedKeys="true">
        insert into GY_ZHIGONGXX
        ( ZHIGONGID,ZHIGONGGH,ZHIGONGXM
        ,XINGBIE,CHUSHENGRQ,SHENFENZH
        ,JIATINGDZ,JIATINGYB,DIANZIYJ
        ,DIANHUA,ZHIWU,ZHICHENG
        ,CANJIAGZSJ,XIUGAIREN,XIUGAISJ
        ,SHURUMA1,SHURUMA3,SHURUMA2
        ,DANGQIANZT,QUANXIAN,MENZHENLB
        ,ZHIGONGLB,MIMA,RENSHIKS
        ,GUAHAOFXM,ZHENLIAOFXM,KESHIID
        ,YISHENGDJ,YINGWENMING,QIANMINGBZ
        ,TUZHANGHAO,ZHIGONGZL,HESUANKS
        ,YISHIZGZS,ZHIYEKSRQ,ZHIYEJSRQ
        ,YISHIZYZS,ZAIZHIZT,ZHIYEZG
        ,RENYUANBZLB,ZHIYESXH,ZHIYEFW
        ,ZHIYEKB,KANGFUZZ,SHIFOUJSJL
        ,YIBAOYSFWBM,YISHIJB,ZHIYEDD
        ,ZHIYELB,DIANHUA1,DIANHUA2
        ,RENYUANXH,GUAHAOWID,XUELI
        ,MENZHENDZBLQYBZ,SHEQUGH,SHEQUMM
        ,SHEQUJS,JISHUZC,CABZ
        ,TUIJIANSX,ZHENDUANJMYCGJLQYBZ,ZYCABZ
        ,DIANZIFPDM,NINGFANQMBZ,ZHIGONGZC
        ,ZHAOPIANBZ,BINGQUID,ZUOFEIBZ
        ,YIBAOYSGJDM,YIBAOGJDM,ZHONGYISSFXM
        ,ZHONGYISBZ,TESHUHZQX,PINRENSJ
        ,ZHONGYIBZFXM,BINGCHENGZKBZ,YUANQUSY
        ,MENZHENKLJYF,ZHUYUANKLJYF,CDSSQYBZ
        ,MENUDATA)
        values (#{ZHIGONGID,jdbcType=VARCHAR},#{ZHIGONGGH,jdbcType=VARCHAR},#{ZHIGONGXM,jdbcType=VARCHAR}
        ,#{XINGBIE,jdbcType=VARCHAR},#{CHUSHENGRQ,jdbcType=TIMESTAMP},#{SHENFENZH,jdbcType=VARCHAR}
        ,#{JIATINGDZ,jdbcType=VARCHAR},#{JIATINGYB,jdbcType=VARCHAR},#{DIANZIYJ,jdbcType=VARCHAR}
        ,#{DIANHUA,jdbcType=VARCHAR},#{ZHIWU,jdbcType=VARCHAR},#{ZHICHENG,jdbcType=VARCHAR}
        ,#{CANJIAGZSJ,jdbcType=TIMESTAMP},#{XIUGAIREN,jdbcType=VARCHAR},#{XIUGAISJ,jdbcType=TIMESTAMP}
        ,#{SHURUMA1,jdbcType=VARCHAR},#{SHURUMA3,jdbcType=VARCHAR},#{SHURUMA2,jdbcType=VARCHAR}
        ,#{DANGQIANZT,jdbcType=VARCHAR},#{QUANXIAN,jdbcType=VARCHAR},#{MENZHENLB,jdbcType=VARCHAR}
        ,#{ZHIGONGLB,jdbcType=VARCHAR},#{MIMA,jdbcType=VARCHAR},#{RENSHIKS,jdbcType=VARCHAR}
        ,#{GUAHAOFXM,jdbcType=VARCHAR},#{ZHENLIAOFXM,jdbcType=VARCHAR},#{KESHIID,jdbcType=VARCHAR}
        ,#{YISHENGDJ,jdbcType=VARCHAR},#{YINGWENMING,jdbcType=VARCHAR},#{QIANMINGBZ,jdbcType=DECIMAL}
        ,#{TUZHANGHAO,jdbcType=VARCHAR},#{ZHIGONGZL,jdbcType=VARCHAR},#{HESUANKS,jdbcType=VARCHAR}
        ,#{YISHIZGZS,jdbcType=VARCHAR},#{ZHIYEKSRQ,jdbcType=TIMESTAMP},#{ZHIYEJSRQ,jdbcType=TIMESTAMP}
        ,#{YISHIZYZS,jdbcType=VARCHAR},#{ZAIZHIZT,jdbcType=VARCHAR},#{ZHIYEZG,jdbcType=VARCHAR}
        ,#{RENYUANBZLB,jdbcType=VARCHAR},#{ZHIYESXH,jdbcType=VARCHAR},#{ZHIYEFW,jdbcType=VARCHAR}
        ,#{ZHIYEKB,jdbcType=VARCHAR},#{KANGFUZZ,jdbcType=VARCHAR},#{SHIFOUJSJL,jdbcType=VARCHAR}
        ,#{YIBAOYSFWBM,jdbcType=VARCHAR},#{YISHIJB,jdbcType=VARCHAR},#{ZHIYEDD,jdbcType=VARCHAR}
        ,#{ZHIYELB,jdbcType=VARCHAR},#{DIANHUA1,jdbcType=VARCHAR},#{DIANHUA2,jdbcType=VARCHAR}
        ,#{RENYUANXH,jdbcType=VARCHAR},#{GUAHAOWID,jdbcType=VARCHAR},#{XUELI,jdbcType=VARCHAR}
        ,#{MENZHENDZBLQYBZ,jdbcType=DECIMAL},#{SHEQUGH,jdbcType=VARCHAR},#{SHEQUMM,jdbcType=VARCHAR}
        ,#{SHEQUJS,jdbcType=VARCHAR},#{JISHUZC,jdbcType=VARCHAR},#{CABZ,jdbcType=DECIMAL}
        ,#{TUIJIANSX,jdbcType=VARCHAR},#{ZHENDUANJMYCGJLQYBZ,jdbcType=DECIMAL},#{ZYCABZ,jdbcType=DECIMAL}
        ,#{DIANZIFPDM,jdbcType=VARCHAR},#{NINGFANQMBZ,jdbcType=DECIMAL},#{ZHIGONGZC,jdbcType=VARCHAR}
        ,#{ZHAOPIANBZ,jdbcType=DECIMAL},#{BINGQUID,jdbcType=VARCHAR},#{ZUOFEIBZ,jdbcType=DECIMAL}
        ,#{YIBAOYSGJDM,jdbcType=VARCHAR},#{YIBAOGJDM,jdbcType=VARCHAR},#{ZHONGYISSFXM,jdbcType=VARCHAR}
        ,#{ZHONGYISBZ,jdbcType=VARCHAR},#{TESHUHZQX,jdbcType=VARCHAR},#{PINRENSJ,jdbcType=TIMESTAMP}
        ,#{ZHONGYIBZFXM,jdbcType=VARCHAR},#{BINGCHENGZKBZ,jdbcType=DECIMAL},#{YUANQUSY,jdbcType=VARCHAR}
        ,#{MENZHENKLJYF,jdbcType=VARCHAR},#{ZHUYUANKLJYF,jdbcType=VARCHAR},#{CDSSQYBZ,jdbcType=DECIMAL}
        ,#{MENUDATA,jdbcType=BLOB})
    </insert>
    <insert id="insertSelective" keyColumn="ZHIGONGID" keyProperty="ZHIGONGID" parameterType="com.javazx.batch.po.GyZhigongxx" useGeneratedKeys="true">
        insert into GY_ZHIGONGXX
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="ZHIGONGID != null">ZHIGONGID,</if>
                <if test="ZHIGONGGH != null">ZHIGONGGH,</if>
                <if test="ZHIGONGXM != null">ZHIGONGXM,</if>
                <if test="XINGBIE != null">XINGBIE,</if>
                <if test="CHUSHENGRQ != null">CHUSHENGRQ,</if>
                <if test="SHENFENZH != null">SHENFENZH,</if>
                <if test="JIATINGDZ != null">JIATINGDZ,</if>
                <if test="JIATINGYB != null">JIATINGYB,</if>
                <if test="DIANZIYJ != null">DIANZIYJ,</if>
                <if test="DIANHUA != null">DIANHUA,</if>
                <if test="ZHIWU != null">ZHIWU,</if>
                <if test="ZHICHENG != null">ZHICHENG,</if>
                <if test="CANJIAGZSJ != null">CANJIAGZSJ,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="SHURUMA1 != null">SHURUMA1,</if>
                <if test="SHURUMA3 != null">SHURUMA3,</if>
                <if test="SHURUMA2 != null">SHURUMA2,</if>
                <if test="DANGQIANZT != null">DANGQIANZT,</if>
                <if test="QUANXIAN != null">QUANXIAN,</if>
                <if test="MENZHENLB != null">MENZHENLB,</if>
                <if test="ZHIGONGLB != null">ZHIGONGLB,</if>
                <if test="MIMA != null">MIMA,</if>
                <if test="RENSHIKS != null">RENSHIKS,</if>
                <if test="GUAHAOFXM != null">GUAHAOFXM,</if>
                <if test="ZHENLIAOFXM != null">ZHENLIAOFXM,</if>
                <if test="KESHIID != null">KESHIID,</if>
                <if test="YISHENGDJ != null">YISHENGDJ,</if>
                <if test="YINGWENMING != null">YINGWENMING,</if>
                <if test="QIANMINGBZ != null">QIANMINGBZ,</if>
                <if test="TUZHANGHAO != null">TUZHANGHAO,</if>
                <if test="ZHIGONGZL != null">ZHIGONGZL,</if>
                <if test="HESUANKS != null">HESUANKS,</if>
                <if test="YISHIZGZS != null">YISHIZGZS,</if>
                <if test="ZHIYEKSRQ != null">ZHIYEKSRQ,</if>
                <if test="ZHIYEJSRQ != null">ZHIYEJSRQ,</if>
                <if test="YISHIZYZS != null">YISHIZYZS,</if>
                <if test="ZAIZHIZT != null">ZAIZHIZT,</if>
                <if test="ZHIYEZG != null">ZHIYEZG,</if>
                <if test="RENYUANBZLB != null">RENYUANBZLB,</if>
                <if test="ZHIYESXH != null">ZHIYESXH,</if>
                <if test="ZHIYEFW != null">ZHIYEFW,</if>
                <if test="ZHIYEKB != null">ZHIYEKB,</if>
                <if test="KANGFUZZ != null">KANGFUZZ,</if>
                <if test="SHIFOUJSJL != null">SHIFOUJSJL,</if>
                <if test="YIBAOYSFWBM != null">YIBAOYSFWBM,</if>
                <if test="YISHIJB != null">YISHIJB,</if>
                <if test="ZHIYEDD != null">ZHIYEDD,</if>
                <if test="ZHIYELB != null">ZHIYELB,</if>
                <if test="DIANHUA1 != null">DIANHUA1,</if>
                <if test="DIANHUA2 != null">DIANHUA2,</if>
                <if test="RENYUANXH != null">RENYUANXH,</if>
                <if test="GUAHAOWID != null">GUAHAOWID,</if>
                <if test="XUELI != null">XUELI,</if>
                <if test="MENZHENDZBLQYBZ != null">MENZHENDZBLQYBZ,</if>
                <if test="SHEQUGH != null">SHEQUGH,</if>
                <if test="SHEQUMM != null">SHEQUMM,</if>
                <if test="SHEQUJS != null">SHEQUJS,</if>
                <if test="JISHUZC != null">JISHUZC,</if>
                <if test="CABZ != null">CABZ,</if>
                <if test="TUIJIANSX != null">TUIJIANSX,</if>
                <if test="ZHENDUANJMYCGJLQYBZ != null">ZHENDUANJMYCGJLQYBZ,</if>
                <if test="ZYCABZ != null">ZYCABZ,</if>
                <if test="DIANZIFPDM != null">DIANZIFPDM,</if>
                <if test="NINGFANQMBZ != null">NINGFANQMBZ,</if>
                <if test="ZHIGONGZC != null">ZHIGONGZC,</if>
                <if test="ZHAOPIANBZ != null">ZHAOPIANBZ,</if>
                <if test="BINGQUID != null">BINGQUID,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="YIBAOYSGJDM != null">YIBAOYSGJDM,</if>
                <if test="YIBAOGJDM != null">YIBAOGJDM,</if>
                <if test="ZHONGYISSFXM != null">ZHONGYISSFXM,</if>
                <if test="ZHONGYISBZ != null">ZHONGYISBZ,</if>
                <if test="TESHUHZQX != null">TESHUHZQX,</if>
                <if test="PINRENSJ != null">PINRENSJ,</if>
                <if test="ZHONGYIBZFXM != null">ZHONGYIBZFXM,</if>
                <if test="BINGCHENGZKBZ != null">BINGCHENGZKBZ,</if>
                <if test="YUANQUSY != null">YUANQUSY,</if>
                <if test="MENZHENKLJYF != null">MENZHENKLJYF,</if>
                <if test="ZHUYUANKLJYF != null">ZHUYUANKLJYF,</if>
                <if test="CDSSQYBZ != null">CDSSQYBZ,</if>
                <if test="MENUDATA != null">MENUDATA,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="ZHIGONGID != null">#{ZHIGONGID,jdbcType=VARCHAR},</if>
                <if test="ZHIGONGGH != null">#{ZHIGONGGH,jdbcType=VARCHAR},</if>
                <if test="ZHIGONGXM != null">#{ZHIGONGXM,jdbcType=VARCHAR},</if>
                <if test="XINGBIE != null">#{XINGBIE,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGRQ != null">#{CHUSHENGRQ,jdbcType=TIMESTAMP},</if>
                <if test="SHENFENZH != null">#{SHENFENZH,jdbcType=VARCHAR},</if>
                <if test="JIATINGDZ != null">#{JIATINGDZ,jdbcType=VARCHAR},</if>
                <if test="JIATINGYB != null">#{JIATINGYB,jdbcType=VARCHAR},</if>
                <if test="DIANZIYJ != null">#{DIANZIYJ,jdbcType=VARCHAR},</if>
                <if test="DIANHUA != null">#{DIANHUA,jdbcType=VARCHAR},</if>
                <if test="ZHIWU != null">#{ZHIWU,jdbcType=VARCHAR},</if>
                <if test="ZHICHENG != null">#{ZHICHENG,jdbcType=VARCHAR},</if>
                <if test="CANJIAGZSJ != null">#{CANJIAGZSJ,jdbcType=TIMESTAMP},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="SHURUMA1 != null">#{SHURUMA1,jdbcType=VARCHAR},</if>
                <if test="SHURUMA3 != null">#{SHURUMA3,jdbcType=VARCHAR},</if>
                <if test="SHURUMA2 != null">#{SHURUMA2,jdbcType=VARCHAR},</if>
                <if test="DANGQIANZT != null">#{DANGQIANZT,jdbcType=VARCHAR},</if>
                <if test="QUANXIAN != null">#{QUANXIAN,jdbcType=VARCHAR},</if>
                <if test="MENZHENLB != null">#{MENZHENLB,jdbcType=VARCHAR},</if>
                <if test="ZHIGONGLB != null">#{ZHIGONGLB,jdbcType=VARCHAR},</if>
                <if test="MIMA != null">#{MIMA,jdbcType=VARCHAR},</if>
                <if test="RENSHIKS != null">#{RENSHIKS,jdbcType=VARCHAR},</if>
                <if test="GUAHAOFXM != null">#{GUAHAOFXM,jdbcType=VARCHAR},</if>
                <if test="ZHENLIAOFXM != null">#{ZHENLIAOFXM,jdbcType=VARCHAR},</if>
                <if test="KESHIID != null">#{KESHIID,jdbcType=VARCHAR},</if>
                <if test="YISHENGDJ != null">#{YISHENGDJ,jdbcType=VARCHAR},</if>
                <if test="YINGWENMING != null">#{YINGWENMING,jdbcType=VARCHAR},</if>
                <if test="QIANMINGBZ != null">#{QIANMINGBZ,jdbcType=DECIMAL},</if>
                <if test="TUZHANGHAO != null">#{TUZHANGHAO,jdbcType=VARCHAR},</if>
                <if test="ZHIGONGZL != null">#{ZHIGONGZL,jdbcType=VARCHAR},</if>
                <if test="HESUANKS != null">#{HESUANKS,jdbcType=VARCHAR},</if>
                <if test="YISHIZGZS != null">#{YISHIZGZS,jdbcType=VARCHAR},</if>
                <if test="ZHIYEKSRQ != null">#{ZHIYEKSRQ,jdbcType=TIMESTAMP},</if>
                <if test="ZHIYEJSRQ != null">#{ZHIYEJSRQ,jdbcType=TIMESTAMP},</if>
                <if test="YISHIZYZS != null">#{YISHIZYZS,jdbcType=VARCHAR},</if>
                <if test="ZAIZHIZT != null">#{ZAIZHIZT,jdbcType=VARCHAR},</if>
                <if test="ZHIYEZG != null">#{ZHIYEZG,jdbcType=VARCHAR},</if>
                <if test="RENYUANBZLB != null">#{RENYUANBZLB,jdbcType=VARCHAR},</if>
                <if test="ZHIYESXH != null">#{ZHIYESXH,jdbcType=VARCHAR},</if>
                <if test="ZHIYEFW != null">#{ZHIYEFW,jdbcType=VARCHAR},</if>
                <if test="ZHIYEKB != null">#{ZHIYEKB,jdbcType=VARCHAR},</if>
                <if test="KANGFUZZ != null">#{KANGFUZZ,jdbcType=VARCHAR},</if>
                <if test="SHIFOUJSJL != null">#{SHIFOUJSJL,jdbcType=VARCHAR},</if>
                <if test="YIBAOYSFWBM != null">#{YIBAOYSFWBM,jdbcType=VARCHAR},</if>
                <if test="YISHIJB != null">#{YISHIJB,jdbcType=VARCHAR},</if>
                <if test="ZHIYEDD != null">#{ZHIYEDD,jdbcType=VARCHAR},</if>
                <if test="ZHIYELB != null">#{ZHIYELB,jdbcType=VARCHAR},</if>
                <if test="DIANHUA1 != null">#{DIANHUA1,jdbcType=VARCHAR},</if>
                <if test="DIANHUA2 != null">#{DIANHUA2,jdbcType=VARCHAR},</if>
                <if test="RENYUANXH != null">#{RENYUANXH,jdbcType=VARCHAR},</if>
                <if test="GUAHAOWID != null">#{GUAHAOWID,jdbcType=VARCHAR},</if>
                <if test="XUELI != null">#{XUELI,jdbcType=VARCHAR},</if>
                <if test="MENZHENDZBLQYBZ != null">#{MENZHENDZBLQYBZ,jdbcType=DECIMAL},</if>
                <if test="SHEQUGH != null">#{SHEQUGH,jdbcType=VARCHAR},</if>
                <if test="SHEQUMM != null">#{SHEQUMM,jdbcType=VARCHAR},</if>
                <if test="SHEQUJS != null">#{SHEQUJS,jdbcType=VARCHAR},</if>
                <if test="JISHUZC != null">#{JISHUZC,jdbcType=VARCHAR},</if>
                <if test="CABZ != null">#{CABZ,jdbcType=DECIMAL},</if>
                <if test="TUIJIANSX != null">#{TUIJIANSX,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANJMYCGJLQYBZ != null">#{ZHENDUANJMYCGJLQYBZ,jdbcType=DECIMAL},</if>
                <if test="ZYCABZ != null">#{ZYCABZ,jdbcType=DECIMAL},</if>
                <if test="DIANZIFPDM != null">#{DIANZIFPDM,jdbcType=VARCHAR},</if>
                <if test="NINGFANQMBZ != null">#{NINGFANQMBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIGONGZC != null">#{ZHIGONGZC,jdbcType=VARCHAR},</if>
                <if test="ZHAOPIANBZ != null">#{ZHAOPIANBZ,jdbcType=DECIMAL},</if>
                <if test="BINGQUID != null">#{BINGQUID,jdbcType=VARCHAR},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="YIBAOYSGJDM != null">#{YIBAOYSGJDM,jdbcType=VARCHAR},</if>
                <if test="YIBAOGJDM != null">#{YIBAOGJDM,jdbcType=VARCHAR},</if>
                <if test="ZHONGYISSFXM != null">#{ZHONGYISSFXM,jdbcType=VARCHAR},</if>
                <if test="ZHONGYISBZ != null">#{ZHONGYISBZ,jdbcType=VARCHAR},</if>
                <if test="TESHUHZQX != null">#{TESHUHZQX,jdbcType=VARCHAR},</if>
                <if test="PINRENSJ != null">#{PINRENSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHONGYIBZFXM != null">#{ZHONGYIBZFXM,jdbcType=VARCHAR},</if>
                <if test="BINGCHENGZKBZ != null">#{BINGCHENGZKBZ,jdbcType=DECIMAL},</if>
                <if test="YUANQUSY != null">#{YUANQUSY,jdbcType=VARCHAR},</if>
                <if test="MENZHENKLJYF != null">#{MENZHENKLJYF,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANKLJYF != null">#{ZHUYUANKLJYF,jdbcType=VARCHAR},</if>
                <if test="CDSSQYBZ != null">#{CDSSQYBZ,jdbcType=DECIMAL},</if>
                <if test="MENUDATA != null">#{MENUDATA,jdbcType=BLOB},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.GyZhigongxx">
        update GY_ZHIGONGXX
        <set>
                <if test="ZHIGONGGH != null">
                    ZHIGONGGH = #{ZHIGONGGH,jdbcType=VARCHAR},
                </if>
                <if test="ZHIGONGXM != null">
                    ZHIGONGXM = #{ZHIGONGXM,jdbcType=VARCHAR},
                </if>
                <if test="XINGBIE != null">
                    XINGBIE = #{XINGBIE,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGRQ != null">
                    CHUSHENGRQ = #{CHUSHENGRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHENFENZH != null">
                    SHENFENZH = #{SHENFENZH,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGDZ != null">
                    JIATINGDZ = #{JIATINGDZ,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGYB != null">
                    JIATINGYB = #{JIATINGYB,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIYJ != null">
                    DIANZIYJ = #{DIANZIYJ,jdbcType=VARCHAR},
                </if>
                <if test="DIANHUA != null">
                    DIANHUA = #{DIANHUA,jdbcType=VARCHAR},
                </if>
                <if test="ZHIWU != null">
                    ZHIWU = #{ZHIWU,jdbcType=VARCHAR},
                </if>
                <if test="ZHICHENG != null">
                    ZHICHENG = #{ZHICHENG,jdbcType=VARCHAR},
                </if>
                <if test="CANJIAGZSJ != null">
                    CANJIAGZSJ = #{CANJIAGZSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHURUMA1 != null">
                    SHURUMA1 = #{SHURUMA1,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA3 != null">
                    SHURUMA3 = #{SHURUMA3,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA2 != null">
                    SHURUMA2 = #{SHURUMA2,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANZT != null">
                    DANGQIANZT = #{DANGQIANZT,jdbcType=VARCHAR},
                </if>
                <if test="QUANXIAN != null">
                    QUANXIAN = #{QUANXIAN,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENLB != null">
                    MENZHENLB = #{MENZHENLB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIGONGLB != null">
                    ZHIGONGLB = #{ZHIGONGLB,jdbcType=VARCHAR},
                </if>
                <if test="MIMA != null">
                    MIMA = #{MIMA,jdbcType=VARCHAR},
                </if>
                <if test="RENSHIKS != null">
                    RENSHIKS = #{RENSHIKS,jdbcType=VARCHAR},
                </if>
                <if test="GUAHAOFXM != null">
                    GUAHAOFXM = #{GUAHAOFXM,jdbcType=VARCHAR},
                </if>
                <if test="ZHENLIAOFXM != null">
                    ZHENLIAOFXM = #{ZHENLIAOFXM,jdbcType=VARCHAR},
                </if>
                <if test="KESHIID != null">
                    KESHIID = #{KESHIID,jdbcType=VARCHAR},
                </if>
                <if test="YISHENGDJ != null">
                    YISHENGDJ = #{YISHENGDJ,jdbcType=VARCHAR},
                </if>
                <if test="YINGWENMING != null">
                    YINGWENMING = #{YINGWENMING,jdbcType=VARCHAR},
                </if>
                <if test="QIANMINGBZ != null">
                    QIANMINGBZ = #{QIANMINGBZ,jdbcType=DECIMAL},
                </if>
                <if test="TUZHANGHAO != null">
                    TUZHANGHAO = #{TUZHANGHAO,jdbcType=VARCHAR},
                </if>
                <if test="ZHIGONGZL != null">
                    ZHIGONGZL = #{ZHIGONGZL,jdbcType=VARCHAR},
                </if>
                <if test="HESUANKS != null">
                    HESUANKS = #{HESUANKS,jdbcType=VARCHAR},
                </if>
                <if test="YISHIZGZS != null">
                    YISHIZGZS = #{YISHIZGZS,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYEKSRQ != null">
                    ZHIYEKSRQ = #{ZHIYEKSRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHIYEJSRQ != null">
                    ZHIYEJSRQ = #{ZHIYEJSRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YISHIZYZS != null">
                    YISHIZYZS = #{YISHIZYZS,jdbcType=VARCHAR},
                </if>
                <if test="ZAIZHIZT != null">
                    ZAIZHIZT = #{ZAIZHIZT,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYEZG != null">
                    ZHIYEZG = #{ZHIYEZG,jdbcType=VARCHAR},
                </if>
                <if test="RENYUANBZLB != null">
                    RENYUANBZLB = #{RENYUANBZLB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYESXH != null">
                    ZHIYESXH = #{ZHIYESXH,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYEFW != null">
                    ZHIYEFW = #{ZHIYEFW,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYEKB != null">
                    ZHIYEKB = #{ZHIYEKB,jdbcType=VARCHAR},
                </if>
                <if test="KANGFUZZ != null">
                    KANGFUZZ = #{KANGFUZZ,jdbcType=VARCHAR},
                </if>
                <if test="SHIFOUJSJL != null">
                    SHIFOUJSJL = #{SHIFOUJSJL,jdbcType=VARCHAR},
                </if>
                <if test="YIBAOYSFWBM != null">
                    YIBAOYSFWBM = #{YIBAOYSFWBM,jdbcType=VARCHAR},
                </if>
                <if test="YISHIJB != null">
                    YISHIJB = #{YISHIJB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYEDD != null">
                    ZHIYEDD = #{ZHIYEDD,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYELB != null">
                    ZHIYELB = #{ZHIYELB,jdbcType=VARCHAR},
                </if>
                <if test="DIANHUA1 != null">
                    DIANHUA1 = #{DIANHUA1,jdbcType=VARCHAR},
                </if>
                <if test="DIANHUA2 != null">
                    DIANHUA2 = #{DIANHUA2,jdbcType=VARCHAR},
                </if>
                <if test="RENYUANXH != null">
                    RENYUANXH = #{RENYUANXH,jdbcType=VARCHAR},
                </if>
                <if test="GUAHAOWID != null">
                    GUAHAOWID = #{GUAHAOWID,jdbcType=VARCHAR},
                </if>
                <if test="XUELI != null">
                    XUELI = #{XUELI,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENDZBLQYBZ != null">
                    MENZHENDZBLQYBZ = #{MENZHENDZBLQYBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHEQUGH != null">
                    SHEQUGH = #{SHEQUGH,jdbcType=VARCHAR},
                </if>
                <if test="SHEQUMM != null">
                    SHEQUMM = #{SHEQUMM,jdbcType=VARCHAR},
                </if>
                <if test="SHEQUJS != null">
                    SHEQUJS = #{SHEQUJS,jdbcType=VARCHAR},
                </if>
                <if test="JISHUZC != null">
                    JISHUZC = #{JISHUZC,jdbcType=VARCHAR},
                </if>
                <if test="CABZ != null">
                    CABZ = #{CABZ,jdbcType=DECIMAL},
                </if>
                <if test="TUIJIANSX != null">
                    TUIJIANSX = #{TUIJIANSX,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANJMYCGJLQYBZ != null">
                    ZHENDUANJMYCGJLQYBZ = #{ZHENDUANJMYCGJLQYBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZYCABZ != null">
                    ZYCABZ = #{ZYCABZ,jdbcType=DECIMAL},
                </if>
                <if test="DIANZIFPDM != null">
                    DIANZIFPDM = #{DIANZIFPDM,jdbcType=VARCHAR},
                </if>
                <if test="NINGFANQMBZ != null">
                    NINGFANQMBZ = #{NINGFANQMBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIGONGZC != null">
                    ZHIGONGZC = #{ZHIGONGZC,jdbcType=VARCHAR},
                </if>
                <if test="ZHAOPIANBZ != null">
                    ZHAOPIANBZ = #{ZHAOPIANBZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGQUID != null">
                    BINGQUID = #{BINGQUID,jdbcType=VARCHAR},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="YIBAOYSGJDM != null">
                    YIBAOYSGJDM = #{YIBAOYSGJDM,jdbcType=VARCHAR},
                </if>
                <if test="YIBAOGJDM != null">
                    YIBAOGJDM = #{YIBAOGJDM,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYISSFXM != null">
                    ZHONGYISSFXM = #{ZHONGYISSFXM,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYISBZ != null">
                    ZHONGYISBZ = #{ZHONGYISBZ,jdbcType=VARCHAR},
                </if>
                <if test="TESHUHZQX != null">
                    TESHUHZQX = #{TESHUHZQX,jdbcType=VARCHAR},
                </if>
                <if test="PINRENSJ != null">
                    PINRENSJ = #{PINRENSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHONGYIBZFXM != null">
                    ZHONGYIBZFXM = #{ZHONGYIBZFXM,jdbcType=VARCHAR},
                </if>
                <if test="BINGCHENGZKBZ != null">
                    BINGCHENGZKBZ = #{BINGCHENGZKBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUANQUSY != null">
                    YUANQUSY = #{YUANQUSY,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENKLJYF != null">
                    MENZHENKLJYF = #{MENZHENKLJYF,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANKLJYF != null">
                    ZHUYUANKLJYF = #{ZHUYUANKLJYF,jdbcType=VARCHAR},
                </if>
                <if test="CDSSQYBZ != null">
                    CDSSQYBZ = #{CDSSQYBZ,jdbcType=DECIMAL},
                </if>
                <if test="MENUDATA != null">
                    MENUDATA = #{MENUDATA,jdbcType=BLOB},
                </if>
        </set>
        where   ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.GyZhigongxx">
        update GY_ZHIGONGXX
        set 
            ZHIGONGGH =  #{ZHIGONGGH,jdbcType=VARCHAR},
            ZHIGONGXM =  #{ZHIGONGXM,jdbcType=VARCHAR},
            XINGBIE =  #{XINGBIE,jdbcType=VARCHAR},
            CHUSHENGRQ =  #{CHUSHENGRQ,jdbcType=TIMESTAMP},
            SHENFENZH =  #{SHENFENZH,jdbcType=VARCHAR},
            JIATINGDZ =  #{JIATINGDZ,jdbcType=VARCHAR},
            JIATINGYB =  #{JIATINGYB,jdbcType=VARCHAR},
            DIANZIYJ =  #{DIANZIYJ,jdbcType=VARCHAR},
            DIANHUA =  #{DIANHUA,jdbcType=VARCHAR},
            ZHIWU =  #{ZHIWU,jdbcType=VARCHAR},
            ZHICHENG =  #{ZHICHENG,jdbcType=VARCHAR},
            CANJIAGZSJ =  #{CANJIAGZSJ,jdbcType=TIMESTAMP},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            SHURUMA1 =  #{SHURUMA1,jdbcType=VARCHAR},
            SHURUMA3 =  #{SHURUMA3,jdbcType=VARCHAR},
            SHURUMA2 =  #{SHURUMA2,jdbcType=VARCHAR},
            DANGQIANZT =  #{DANGQIANZT,jdbcType=VARCHAR},
            QUANXIAN =  #{QUANXIAN,jdbcType=VARCHAR},
            MENZHENLB =  #{MENZHENLB,jdbcType=VARCHAR},
            ZHIGONGLB =  #{ZHIGONGLB,jdbcType=VARCHAR},
            MIMA =  #{MIMA,jdbcType=VARCHAR},
            RENSHIKS =  #{RENSHIKS,jdbcType=VARCHAR},
            GUAHAOFXM =  #{GUAHAOFXM,jdbcType=VARCHAR},
            ZHENLIAOFXM =  #{ZHENLIAOFXM,jdbcType=VARCHAR},
            KESHIID =  #{KESHIID,jdbcType=VARCHAR},
            YISHENGDJ =  #{YISHENGDJ,jdbcType=VARCHAR},
            YINGWENMING =  #{YINGWENMING,jdbcType=VARCHAR},
            QIANMINGBZ =  #{QIANMINGBZ,jdbcType=DECIMAL},
            TUZHANGHAO =  #{TUZHANGHAO,jdbcType=VARCHAR},
            ZHIGONGZL =  #{ZHIGONGZL,jdbcType=VARCHAR},
            HESUANKS =  #{HESUANKS,jdbcType=VARCHAR},
            YISHIZGZS =  #{YISHIZGZS,jdbcType=VARCHAR},
            ZHIYEKSRQ =  #{ZHIYEKSRQ,jdbcType=TIMESTAMP},
            ZHIYEJSRQ =  #{ZHIYEJSRQ,jdbcType=TIMESTAMP},
            YISHIZYZS =  #{YISHIZYZS,jdbcType=VARCHAR},
            ZAIZHIZT =  #{ZAIZHIZT,jdbcType=VARCHAR},
            ZHIYEZG =  #{ZHIYEZG,jdbcType=VARCHAR},
            RENYUANBZLB =  #{RENYUANBZLB,jdbcType=VARCHAR},
            ZHIYESXH =  #{ZHIYESXH,jdbcType=VARCHAR},
            ZHIYEFW =  #{ZHIYEFW,jdbcType=VARCHAR},
            ZHIYEKB =  #{ZHIYEKB,jdbcType=VARCHAR},
            KANGFUZZ =  #{KANGFUZZ,jdbcType=VARCHAR},
            SHIFOUJSJL =  #{SHIFOUJSJL,jdbcType=VARCHAR},
            YIBAOYSFWBM =  #{YIBAOYSFWBM,jdbcType=VARCHAR},
            YISHIJB =  #{YISHIJB,jdbcType=VARCHAR},
            ZHIYEDD =  #{ZHIYEDD,jdbcType=VARCHAR},
            ZHIYELB =  #{ZHIYELB,jdbcType=VARCHAR},
            DIANHUA1 =  #{DIANHUA1,jdbcType=VARCHAR},
            DIANHUA2 =  #{DIANHUA2,jdbcType=VARCHAR},
            RENYUANXH =  #{RENYUANXH,jdbcType=VARCHAR},
            GUAHAOWID =  #{GUAHAOWID,jdbcType=VARCHAR},
            XUELI =  #{XUELI,jdbcType=VARCHAR},
            MENZHENDZBLQYBZ =  #{MENZHENDZBLQYBZ,jdbcType=DECIMAL},
            SHEQUGH =  #{SHEQUGH,jdbcType=VARCHAR},
            SHEQUMM =  #{SHEQUMM,jdbcType=VARCHAR},
            SHEQUJS =  #{SHEQUJS,jdbcType=VARCHAR},
            JISHUZC =  #{JISHUZC,jdbcType=VARCHAR},
            CABZ =  #{CABZ,jdbcType=DECIMAL},
            TUIJIANSX =  #{TUIJIANSX,jdbcType=VARCHAR},
            ZHENDUANJMYCGJLQYBZ =  #{ZHENDUANJMYCGJLQYBZ,jdbcType=DECIMAL},
            ZYCABZ =  #{ZYCABZ,jdbcType=DECIMAL},
            DIANZIFPDM =  #{DIANZIFPDM,jdbcType=VARCHAR},
            NINGFANQMBZ =  #{NINGFANQMBZ,jdbcType=DECIMAL},
            ZHIGONGZC =  #{ZHIGONGZC,jdbcType=VARCHAR},
            ZHAOPIANBZ =  #{ZHAOPIANBZ,jdbcType=DECIMAL},
            BINGQUID =  #{BINGQUID,jdbcType=VARCHAR},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            YIBAOYSGJDM =  #{YIBAOYSGJDM,jdbcType=VARCHAR},
            YIBAOGJDM =  #{YIBAOGJDM,jdbcType=VARCHAR},
            ZHONGYISSFXM =  #{ZHONGYISSFXM,jdbcType=VARCHAR},
            ZHONGYISBZ =  #{ZHONGYISBZ,jdbcType=VARCHAR},
            TESHUHZQX =  #{TESHUHZQX,jdbcType=VARCHAR},
            PINRENSJ =  #{PINRENSJ,jdbcType=TIMESTAMP},
            ZHONGYIBZFXM =  #{ZHONGYIBZFXM,jdbcType=VARCHAR},
            BINGCHENGZKBZ =  #{BINGCHENGZKBZ,jdbcType=DECIMAL},
            YUANQUSY =  #{YUANQUSY,jdbcType=VARCHAR},
            MENZHENKLJYF =  #{MENZHENKLJYF,jdbcType=VARCHAR},
            ZHUYUANKLJYF =  #{ZHUYUANKLJYF,jdbcType=VARCHAR},
            CDSSQYBZ =  #{CDSSQYBZ,jdbcType=DECIMAL},
            MENUDATA =  #{MENUDATA,jdbcType=BLOB}
        where   ZHIGONGID = #{ZHIGONGID,jdbcType=VARCHAR} 
    </update>
</mapper>
