package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 公用_病人过敏史
 * @TableName GY_BINGRENGMS
 */
@TableName(value ="GY_BINGRENGMS")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GyBingrengms implements Serializable {
    /**
     * 过敏史ID
     */
    @TableId(value = "GUOMINSID")
    private String GUOMINSID;

    /**
     * 应用ID
     */
    @TableField(value = "YINGYONGID")
    private String YINGYONGID;

    /**
     * 院区ID
     */
    @TableField(value = "YUANQUID")
    private String YUANQUID;

    /**
     * 病人ID
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 来源ID
     */
    @TableField(value = "LAIYUANID")
    private String LAIYUANID;

    /**
     * 记录来源
     */
    @TableField(value = "JILULY")
    private String JILULY;

    /**
     * 价格ID
     */
    @TableField(value = "JIAGEID")
    private String JIAGEID;

    /**
     * 药品名称
     */
    @TableField(value = "YAOPINMC")
    private String YAOPINMC;

    /**
     * 处理意见
     */
    @TableField(value = "CHULIYJ")
    private String CHULIYJ;

    /**
     * 皮试结果
     */
    @TableField(value = "PISHIJG")
    private String PISHIJG;

    /**
     * 执行时间
     */
    @TableField(value = "ZHIXINGSJ")
    private String ZHIXINGSJ;

    /**
     * 执行人
     */
    @TableField(value = "ZHIXINGREN")
    private String ZHIXINGREN;

    /**
     * 执行人姓名
     */
    @TableField(value = "ZHIXINGRXM")
    private String ZHIXINGRXM;

    /**
     * 过敏类型
     */
    @TableField(value = "GUOMINLX")
    private String GUOMINLX;

    /**
     * 药品ID
     */
    @TableField(value = "YAOPINID")
    private String YAOPINID;

    /**
     * 身份证号
     */
    @TableField(value = "SHENFENZH")
    private String SHENFENZH;

    /**
     * 药物反应
     */
    @TableField(value = "YAOWUFY")
    private String YAOWUFY;

    /**
     * 不良反应详情
     */
    @TableField(value = "BULIANGFYXQ")
    private String BULIANGFYXQ;

    /**
     * 严禁标识  1为真0为假
     */
    @TableField(value = "YANJINFLAG")
    private Integer YANJINFLAG;

    /**
     * 过敏源分类HR6-1512(518757)
     */
    @TableField(value = "GUOMINYFL")
    private String GUOMINYFL;

    /**
     * 记录时间
     */
    @TableField(value = "JLSJ")
    private LocalDateTime JLSJ;

    /**
     * 记录人
     */
    @TableField(value = "JLREN")
    private String JLREN;

    /**
     * 过敏反应
     */
    @TableField(value = "GUOMINFY")
    private String GUOMINFY;

    /**
     * mpi
     */
    @TableField(value = "MPI")
    private String MPI;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}