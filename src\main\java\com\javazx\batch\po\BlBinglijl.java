package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  病历保存记录
 * @TableName BL_BINGLIJL
 */
@TableName(value ="BL_BINGLIJL")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BlBinglijl implements Serializable {
    /**
     * 病历记录序号
     */
    @TableId(value = "BINGLIJLXH")
    private Integer BINGLIJLXH;

    /**
     * 病案号   BAH
     */
    @TableField(value = "BINGANH")
    private String BINGANH;

    /**
     * 住院号
     */
    @TableField(value = "ZHUYUANHAO")
    private String ZHUYUANHAO;

    /**
     * 病人序号 PATIENT_NO
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 门诊住院标志1住院2门诊3急诊7门诊文书8眼科文书9组套
     */
    @TableField(value = "MENZHENZYBZ")
    private Integer MENZHENZYBZ;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 模板类型代码 MBLXDM
     */
    @TableField(value = "MOBANLXDM")
    private String MOBANLXDM;

    /**
     * 模板编号 MBBH
     */
    @TableField(value = "MOBANDM")
    private String MOBANDM;

    /**
     * 模板编号
     */
    @TableField(value = "MOBANBH")
    private Integer MOBANBH;

    /**
     * 功能点序号
     */
    @TableField(value = "GONGNENGDXH")
    private String GONGNENGDXH;

    /**
     * 病历名称
     */
    @TableField(value = "BINGLIMC")
    private String BINGLIMC;

    /**
     * 创建时间
     */
    @TableField(value = "CHUANGJIANSJ")
    private LocalDateTime CHUANGJIANSJ;

    /**
     * 创建人
     */
    @TableField(value = "CHUANGJIANREN")
    private String CHUANGJIANREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private LocalDateTime XIUGAISJ;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 网卡地址
     */
    @TableField(value = "WANGKADZ")
    private String WANGKADZ;

    /**
     * 需审核标志
     */
    @TableField(value = "XUSHENGHBZ")
    private Integer XUSHENGHBZ;

    /**
     * 审核标志
     */
    @TableField(value = "SHENGHEBZ")
    private Integer SHENGHEBZ;

    /**
     * 审核人
     */
    @TableField(value = "SHENGHEREN")
    private String SHENGHEREN;

    /**
     * 审核时间
     */
    @TableField(value = "SHENGHESJ")
    private LocalDateTime SHENGHESJ;

    /**
     * 病历记录内容序号(对应内容记录)
     */
    @TableField(value = "BINGLIJLNRXH")
    private Integer BINGLIJLNRXH;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 当前科室
     */
    @TableField(value = "DANGQIANKS")
    private String DANGQIANKS;

    /**
     * 当前病区
     */
    @TableField(value = "DANGQIANBQ")
    private String DANGQIANBQ;

    /**
     * 当前床位
     */
    @TableField(value = "DANGQIANCW")
    private String DANGQIANCW;

    /**
     * 暂存标志
     */
    @TableField(value = "ZANCUNBZ")
    private Integer ZANCUNBZ;

    /**
     * 病程标志
     */
    @TableField(value = "BINGCHENGBZ")
    private Integer BINGCHENGBZ;

    /**
     * 病历时间(病历实际发生时间)
     */
    @TableField(value = "BINGLISJ")
    private LocalDateTime BINGLISJ;

    /**
     * 换页标志(同EMR_GY_MOBANLX.HUANYEBZ)
     */
    @TableField(value = "HUANYEBZ")
    private String HUANYEBZ;

    /**
     * 返回值HR3-27222(284051)
     */
    @TableField(value = "FANHUIZHI")
    private String FANHUIZHI;

    /**
     * 元素ID,标识一级病历对应的元素HB3-20937(288800)
     */
    @TableField(value = "YUANSUID")
    private String YUANSUID;

    /**
     * 申请单IDHR6-301(455960)
     */
    @TableField(value = "SHENQINGDID")
    private String SHENQINGDID;

    /**
     * 业务类型 1手术 2用血 5检查3转运HR6-301(455960)
     */
    @TableField(value = "YEWULX")
    private Integer YEWULX;

    /**
     * 电子病历使用字段
     */
    @TableField(value = "DIANZIBLSY")
    private String DIANZIBLSY;

    /**
     * 
     */
    @TableField(value = "SHANGJIYSXM")
    private String SHANGJIYSXM;

    /**
     * 
     */
    @TableField(value = "SHANGJIYSGH")
    private String SHANGJIYSGH;

    /**
     * 上级病历记录序号 vte使用
     */
    @TableField(value = "SHANGJIBLJLXH")
    private Integer SHANGJIBLJLXH;

    /**
     * 病历打印标志
     */
    @TableField(value = "DAYINBZ")
    private Integer DAYINBZ;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}