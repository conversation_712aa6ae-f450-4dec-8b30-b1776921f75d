package com.javazx.batch.web;

import com.javazx.batch.service.TaskDependencyManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务依赖管理控制器
 * 用于测试和监控任务依赖关系
 */
@Slf4j
@RestController
@RequestMapping("/api/task-dependency")
public class TaskDependencyController {

    @Autowired
    private TaskDependencyManager taskDependencyManager;

    /**
     * 手动标记任务开始
     * @param taskName 任务名称
     * @return 操作结果
     */
    @PostMapping("/start/{taskName}")
    public Map<String, Object> markTaskStarted(@PathVariable String taskName) {
        Map<String, Object> result = new HashMap<>();
        try {
            taskDependencyManager.markTaskStarted(taskName);
            result.put("success", true);
            result.put("message", "任务 " + taskName + " 已标记为开始执行");
        } catch (Exception e) {
            log.error("标记任务开始失败", e);
            result.put("success", false);
            result.put("message", "标记任务开始失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 手动标记任务完成
     * @param taskName 任务名称
     * @param success 是否成功
     * @return 操作结果
     */
    @PostMapping("/complete/{taskName}")
    public Map<String, Object> markTaskCompleted(@PathVariable String taskName, 
                                                 @RequestParam(defaultValue = "true") boolean success) {
        Map<String, Object> result = new HashMap<>();
        try {
            taskDependencyManager.markTaskCompleted(taskName, success);
            result.put("success", true);
            result.put("message", "任务 " + taskName + " 已标记为完成，状态: " + (success ? "成功" : "失败"));
        } catch (Exception e) {
            log.error("标记任务完成失败", e);
            result.put("success", false);
            result.put("message", "标记任务完成失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 检查依赖任务状态
     * @param taskName 任务名称
     * @return 任务状态
     */
    @GetMapping("/status/{taskName}")
    public Map<String, Object> checkTaskStatus(@PathVariable String taskName) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean completed = taskDependencyManager.isDependencyTaskCompleted(taskName);
            boolean running = taskDependencyManager.isDependencyTaskRunning(taskName);
            
            result.put("success", true);
            result.put("taskName", taskName);
            result.put("completed", completed);
            result.put("running", running);
            
            String status;
            if (completed) {
                status = "已完成";
            } else if (running) {
                status = "运行中";
            } else {
                status = "未运行";
            }
            result.put("status", status);
            
        } catch (Exception e) {
            log.error("检查任务状态失败", e);
            result.put("success", false);
            result.put("message", "检查任务状态失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 测试等待依赖任务
     * @param dependencyTask 依赖的任务名称
     * @param maxWaitMinutes 最大等待时间（分钟）
     * @return 等待结果
     */
    @PostMapping("/wait/{dependencyTask}")
    public Map<String, Object> waitForDependency(@PathVariable String dependencyTask,
                                                 @RequestParam(defaultValue = "2") int maxWaitMinutes) {
        Map<String, Object> result = new HashMap<>();
        try {
            long startTime = System.currentTimeMillis();
            boolean completed = taskDependencyManager.waitForDependencyTask(dependencyTask, maxWaitMinutes);
            long endTime = System.currentTimeMillis();
            
            result.put("success", true);
            result.put("dependencyTask", dependencyTask);
            result.put("completed", completed);
            result.put("waitTimeMs", endTime - startTime);
            result.put("maxWaitMinutes", maxWaitMinutes);
            result.put("message", completed ? "依赖任务已完成" : "等待超时");
            
        } catch (Exception e) {
            log.error("等待依赖任务失败", e);
            result.put("success", false);
            result.put("message", "等待依赖任务失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 清理过期任务状态
     * @return 操作结果
     */
    @PostMapping("/cleanup")
    public Map<String, Object> cleanupExpiredStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            taskDependencyManager.cleanupExpiredTaskStatus();
            result.put("success", true);
            result.put("message", "过期任务状态清理完成");
        } catch (Exception e) {
            log.error("清理过期任务状态失败", e);
            result.put("success", false);
            result.put("message", "清理过期任务状态失败: " + e.getMessage());
        }
        return result;
    }
}
