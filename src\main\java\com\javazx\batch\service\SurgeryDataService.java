package com.javazx.batch.service;

import com.javazx.batch.mapper.SmShoushuxxMapper;
import com.javazx.batch.mapper.YzBingrenyzMapper;
import com.javazx.batch.mapper.ZyBingrenxxMapper;
import com.javazx.batch.po.SmShoushuxx;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 手术数据服务类
 * 专门处理手术相关数据访问
 */
@Service
public class SurgeryDataService {

    private static final Logger log = LoggerFactory.getLogger(SurgeryDataService.class);

    @Autowired
    private SmShoushuxxMapper smShoushuxxMapper;

    @Autowired
    private YzBingrenyzMapper yzBingrenyzMapper;

    @Autowired
    private ZyBingrenxxMapper zyBingrenxxMapper;

    // ==================== 手术信息查询 ====================

    /**
     * 分页查询手术信息数据
     */
    public List<SmShoushuxx> selectSurgeryByPage(String bingquid, int offset, int limit) {
        try {
            log.debug("查询手术信息数据 - 病区: {}, 偏移: {}, 限制: {}", bingquid, offset, limit);
            return smShoushuxxMapper.selectSurgeryByPage(bingquid, offset, limit);
        } catch (Exception e) {
            log.error("查询手术信息数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计手术信息总数
     */
    public int countSurgery(String bingquid) {
        try {
            return smShoushuxxMapper.countSurgery(bingquid);
        } catch (Exception e) {
            log.error("统计手术信息总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 分页查询当日手术信息数据
     */
    public List<SmShoushuxx> selectTodaySurgeryByPage(String bingquid, int offset, int limit) {
        try {
            log.debug("查询当日手术信息数据 - 病区: {}, 偏移: {}, 限制: {}", bingquid, offset, limit);
            return smShoushuxxMapper.selectTodaySurgeryByPage(bingquid, offset, limit);
        } catch (Exception e) {
            log.error("查询当日手术信息数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计当日手术信息总数
     */
    public int countTodaySurgery(String bingquid) {
        try {
            return smShoushuxxMapper.countTodaySurgery(bingquid);
        } catch (Exception e) {
            log.error("统计当日手术信息总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 术中带药查询 ====================

    /**
     * 分页查询术中带药医嘱数据
     */
    public List<YzBingrenyz> selectIntraoperativeMedicationByPage(int offset, int limit) {
        try {
            log.debug("查询术中带药医嘱数据 - 偏移: {}, 限制: {}", offset, limit);
            return yzBingrenyzMapper.selectIntraoperativeMedicationByPage(offset, limit);
        } catch (Exception e) {
            log.error("查询术中带药医嘱数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计术中带药医嘱总数
     */
    public int countIntraoperativeMedication() {
        try {
            return yzBingrenyzMapper.countIntraoperativeMedication();
        } catch (Exception e) {
            log.error("统计术中带药医嘱总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据病区分页查询术中带药医嘱数据
     */
    public List<YzBingrenyz> selectIntraoperativeMedicationByPageAndWard(String bingquid, int offset, int limit) {
        try {
            log.debug("查询术中带药医嘱数据 - 病区: {}, 偏移: {}, 限制: {}", bingquid, offset, limit);
            return yzBingrenyzMapper.selectIntraoperativeMedicationByPageAndWard(bingquid, offset, limit);
        } catch (Exception e) {
            log.error("查询术中带药医嘱数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据病区统计术中带药医嘱总数
     */
    public int countIntraoperativeMedicationByWard(String bingquid) {
        try {
            return yzBingrenyzMapper.countIntraoperativeMedicationByWard(bingquid);
        } catch (Exception e) {
            log.error("统计术中带药医嘱总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 患者信息查询 ====================

    /**
     * 根据病人住院ID查询患者信息
     */
    public ZyBingrenxx selectPatientById(String bingrenzyid) {
        try {
            return zyBingrenxxMapper.selectById(bingrenzyid);
        } catch (Exception e) {
            log.error("根据病人ID查询患者信息失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    // ==================== 辅助查询方法 ====================

    /**
     * 查询今日手术数量（用于患者标签）
     */
    public Integer getTodaySurgeryCount(String bingrenzyid, String bingquid) {
        try {
            return smShoushuxxMapper.getTodaySurgeryCount(bingrenzyid, bingquid);
        } catch (Exception e) {
            log.error("查询今日手术数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
