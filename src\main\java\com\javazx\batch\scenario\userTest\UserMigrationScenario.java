package com.javazx.batch.scenario.userTest;

import com.javazx.batch.po.UserFrom;
import com.javazx.batch.po.UserTo;
import com.javazx.batch.scenario.AbstractBatchScenario;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;

/**
 * 用户数据迁移场景
 * 从user_from表迁移数据到user_to表
 *
 */
@Component
public class UserMigrationScenario extends AbstractBatchScenario<UserFrom, UserTo> {

    private static final Logger log = LoggerFactory.getLogger(UserMigrationScenario.class);

   /* @Autowired
    @Qualifier("sqlSessionFactory")
    private SqlSessionFactory sqlSessionFactory;
*/
    public UserMigrationScenario() {
        super("userMigration", "用户数据迁移场景：从user_from表迁移到user_to表");
        this.setCommitInterval(600);
        this.setPageSize(10);
    }
    
    @Override
    public ItemReader<UserFrom> createReader() {
        return new DynamicTestDataReader(10);
    }
    
    @Override
    public ItemProcessor<UserFrom, UserTo> createProcessor() {
        log.info("创建用户数据转换器");
        return new UserMigrationProcessor();
    }
    
    @Override
    public ItemWriter<UserTo> createWriter() {
       // MyBatisBatchItemWriter<UserTo> writer = new MyBatisBatchItemWriter<>();
       /* writer.setSqlSessionFactory(sqlSessionFactory);
        writer.setStatementId("com.javazx.batch.dao.UserToMapper.batchInsert");*/
        log.info("创建user_to表数据写入器");
        return items -> {
            String threadName = Thread.currentThread().getName();
            System.out.println("- 线程: " + threadName + ", 处理数据量: " + items.size());
            for (UserTo item : items) {
                System.out.println("  处理用户: " + item.getUserName() + " (线程: " + threadName + ")");
            }
        };
    }
}
