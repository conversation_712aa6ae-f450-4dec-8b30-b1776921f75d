<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.YjShenqingdanMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.YjShenqingdan">
            <id property="SHENQINDANID" column="SHENQINDANID" jdbcType="VARCHAR"/>
            <result property="YINGYONGID" column="YINGYONGID" jdbcType="VARCHAR"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="VARCHAR"/>
            <result property="MOBANDM" column="MOBANDM" jdbcType="VARCHAR"/>
            <result property="BAOGAODMBDM" column="BAOGAODMBDM" jdbcType="VARCHAR"/>
            <result property="YIZHUID" column="YIZHUID" jdbcType="VARCHAR"/>
            <result property="YIZHUXMID" column="YIZHUXMID" jdbcType="VARCHAR"/>
            <result property="YIZHUMC" column="YIZHUMC" jdbcType="VARCHAR"/>
            <result property="JIUZHENID" column="JIUZHENID" jdbcType="VARCHAR"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="YINGERID" column="YINGERID" jdbcType="VARCHAR"/>
            <result property="BINGRENXM" column="BINGRENXM" jdbcType="VARCHAR"/>
            <result property="XINGBIE" column="XINGBIE" jdbcType="VARCHAR"/>
            <result property="NIANLING" column="NIANLING" jdbcType="DECIMAL"/>
            <result property="NIANLINGDW" column="NIANLINGDW" jdbcType="VARCHAR"/>
            <result property="CHUSHENGRQ" column="CHUSHENGRQ" jdbcType="TIMESTAMP"/>
            <result property="SHURUREN" column="SHURUREN" jdbcType="VARCHAR"/>
            <result property="SHURUSJ" column="SHURUSJ" jdbcType="TIMESTAMP"/>
            <result property="KAIDANREN" column="KAIDANREN" jdbcType="VARCHAR"/>
            <result property="KAIDANKS" column="KAIDANKS" jdbcType="VARCHAR"/>
            <result property="KAIDANRQ" column="KAIDANRQ" jdbcType="TIMESTAMP"/>
            <result property="JIANCHAKS" column="JIANCHAKS" jdbcType="VARCHAR"/>
            <result property="JIANCHAREN" column="JIANCHAREN" jdbcType="VARCHAR"/>
            <result property="JIANCHARQ" column="JIANCHARQ" jdbcType="TIMESTAMP"/>
            <result property="MENZHENZYBZ" column="MENZHENZYBZ" jdbcType="DECIMAL"/>
            <result property="DANGQIANZT" column="DANGQIANZT" jdbcType="VARCHAR"/>
            <result property="QIWANGJCRQ" column="QIWANGJCRQ" jdbcType="TIMESTAMP"/>
            <result property="YUYUESQR" column="YUYUESQR" jdbcType="VARCHAR"/>
            <result property="YUYUESQRQ" column="YUYUESQRQ" jdbcType="TIMESTAMP"/>
            <result property="YUYUEAPKS" column="YUYUEAPKS" jdbcType="VARCHAR"/>
            <result property="YUYUEQRRQ" column="YUYUEQRRQ" jdbcType="TIMESTAMP"/>
            <result property="YUYUEQRR" column="YUYUEQRR" jdbcType="VARCHAR"/>
            <result property="JIANCHAAPRQ" column="JIANCHAAPRQ" jdbcType="TIMESTAMP"/>
            <result property="JIANCHAAPR" column="JIANCHAAPR" jdbcType="VARCHAR"/>
            <result property="BAOGAODFS" column="BAOGAODFS" jdbcType="DECIMAL"/>
            <result property="DAYINREN" column="DAYINREN" jdbcType="VARCHAR"/>
            <result property="DAYINRQ" column="DAYINRQ" jdbcType="TIMESTAMP"/>
            <result property="YOUXIANJI" column="YOUXIANJI" jdbcType="VARCHAR"/>
            <result property="SHOUFEIBZ" column="SHOUFEIBZ" jdbcType="DECIMAL"/>
            <result property="CHEXIAOREN" column="CHEXIAOREN" jdbcType="VARCHAR"/>
            <result property="CHEXIAOSJ" column="CHEXIAOSJ" jdbcType="TIMESTAMP"/>
            <result property="ZHUSU" column="ZHUSU" jdbcType="VARCHAR"/>
            <result property="JIANYAOBS" column="JIANYAOBS" jdbcType="VARCHAR"/>
            <result property="TIGEJC" column="TIGEJC" jdbcType="VARCHAR"/>
            <result property="LINCHUANGZD" column="LINCHUANGZD" jdbcType="VARCHAR"/>
            <result property="JIANCHABW" column="JIANCHABW" jdbcType="VARCHAR"/>
            <result property="JIANCHAMD" column="JIANCHAMD" jdbcType="VARCHAR"/>
            <result property="XIANGGUANJC" column="XIANGGUANJC" jdbcType="VARCHAR"/>
            <result property="JIANCHALX" column="JIANCHALX" jdbcType="VARCHAR"/>
            <result property="TUIDANREN" column="TUIDANREN" jdbcType="VARCHAR"/>
            <result property="TUIDANRQ" column="TUIDANRQ" jdbcType="TIMESTAMP"/>
            <result property="TUIDANRXM" column="TUIDANRXM" jdbcType="VARCHAR"/>
            <result property="TUIDANYY" column="TUIDANYY" jdbcType="VARCHAR"/>
            <result property="FEIYONGHJ" column="FEIYONGHJ" jdbcType="DECIMAL"/>
            <result property="YISHOUJE" column="YISHOUJE" jdbcType="DECIMAL"/>
            <result property="KEZHIXBZ" column="KEZHIXBZ" jdbcType="DECIMAL"/>
            <result property="YUYUEQRRQ2" column="YUYUEQRRQ2" jdbcType="VARCHAR"/>
            <result property="YUYUEHAO" column="YUYUEHAO" jdbcType="VARCHAR"/>
            <result property="CHUANGBIANBZ" column="CHUANGBIANBZ" jdbcType="DECIMAL"/>
            <result property="TESHUSM" column="TESHUSM" jdbcType="VARCHAR"/>
            <result property="BEIZHU" column="BEIZHU" jdbcType="VARCHAR"/>
            <result property="RISDJHM" column="RISDJHM" jdbcType="VARCHAR"/>
            <result property="ZIFEIBZ" column="ZIFEIBZ" jdbcType="DECIMAL"/>
            <result property="YUYUEDYBZ" column="YUYUEDYBZ" jdbcType="DECIMAL"/>
            <result property="YAOLIJDMDBZ" column="YAOLIJDMDBZ" jdbcType="DECIMAL"/>
            <result property="ZHUYISX" column="ZHUYISX" jdbcType="VARCHAR"/>
            <result property="JIZHENBZ" column="JIZHENBZ" jdbcType="DECIMAL"/>
            <result property="TESHUBZMC" column="TESHUBZMC" jdbcType="VARCHAR"/>
            <result property="JIANCHASJ" column="JIANCHASJ" jdbcType="VARCHAR"/>
            <result property="ZHENDUANJG" column="ZHENDUANJG" jdbcType="VARCHAR"/>
            <result property="WEIJIZBZ" column="WEIJIZBZ" jdbcType="DECIMAL"/>
            <result property="JIAOPIANFBZ" column="JIAOPIANFBZ" jdbcType="DECIMAL"/>
            <result property="ZHENDUANLX" column="ZHENDUANLX" jdbcType="VARCHAR"/>
            <result property="ZHENDUANYJ" column="ZHENDUANYJ" jdbcType="VARCHAR"/>
            <result property="SHOUSUOYA" column="SHOUSUOYA" jdbcType="DECIMAL"/>
            <result property="SHUZHANGYA" column="SHUZHANGYA" jdbcType="DECIMAL"/>
            <result property="ZIFU1" column="ZIFU1" jdbcType="VARCHAR"/>
            <result property="ZIFU2" column="ZIFU2" jdbcType="VARCHAR"/>
            <result property="ZIFU3" column="ZIFU3" jdbcType="VARCHAR"/>
            <result property="ZIFU4" column="ZIFU4" jdbcType="VARCHAR"/>
            <result property="ZIFU5" column="ZIFU5" jdbcType="VARCHAR"/>
            <result property="ZIFU6" column="ZIFU6" jdbcType="VARCHAR"/>
            <result property="ZIFU7" column="ZIFU7" jdbcType="VARCHAR"/>
            <result property="ZIFU8" column="ZIFU8" jdbcType="VARCHAR"/>
            <result property="ZIFU9" column="ZIFU9" jdbcType="VARCHAR"/>
            <result property="ZIFU10" column="ZIFU10" jdbcType="VARCHAR"/>
            <result property="ZIFU11" column="ZIFU11" jdbcType="VARCHAR"/>
            <result property="ZIFU12" column="ZIFU12" jdbcType="VARCHAR"/>
            <result property="ZIFU13" column="ZIFU13" jdbcType="VARCHAR"/>
            <result property="ZIFU14" column="ZIFU14" jdbcType="VARCHAR"/>
            <result property="ZIFU15" column="ZIFU15" jdbcType="VARCHAR"/>
            <result property="ZIFU16" column="ZIFU16" jdbcType="VARCHAR"/>
            <result property="ZIFU17" column="ZIFU17" jdbcType="VARCHAR"/>
            <result property="ZIFU18" column="ZIFU18" jdbcType="VARCHAR"/>
            <result property="ZIFU19" column="ZIFU19" jdbcType="VARCHAR"/>
            <result property="ZIFU20" column="ZIFU20" jdbcType="VARCHAR"/>
            <result property="ZIFU21" column="ZIFU21" jdbcType="VARCHAR"/>
            <result property="ZIFU22" column="ZIFU22" jdbcType="VARCHAR"/>
            <result property="ZIFU23" column="ZIFU23" jdbcType="VARCHAR"/>
            <result property="ZIFU24" column="ZIFU24" jdbcType="VARCHAR"/>
            <result property="ZIFU25" column="ZIFU25" jdbcType="VARCHAR"/>
            <result property="ZIFU26" column="ZIFU26" jdbcType="VARCHAR"/>
            <result property="ZIFU27" column="ZIFU27" jdbcType="VARCHAR"/>
            <result property="ZIFU28" column="ZIFU28" jdbcType="VARCHAR"/>
            <result property="ZIFU29" column="ZIFU29" jdbcType="VARCHAR"/>
            <result property="ZIFU30" column="ZIFU30" jdbcType="VARCHAR"/>
            <result property="ZIFU31" column="ZIFU31" jdbcType="VARCHAR"/>
            <result property="ZIFU32" column="ZIFU32" jdbcType="VARCHAR"/>
            <result property="ZIFU33" column="ZIFU33" jdbcType="VARCHAR"/>
            <result property="ZIFU34" column="ZIFU34" jdbcType="VARCHAR"/>
            <result property="ZIFU35" column="ZIFU35" jdbcType="VARCHAR"/>
            <result property="ZIFU36" column="ZIFU36" jdbcType="VARCHAR"/>
            <result property="ZIFU37" column="ZIFU37" jdbcType="VARCHAR"/>
            <result property="ZIFU38" column="ZIFU38" jdbcType="VARCHAR"/>
            <result property="ZIFU39" column="ZIFU39" jdbcType="VARCHAR"/>
            <result property="ZIFU40" column="ZIFU40" jdbcType="VARCHAR"/>
            <result property="ZIFU41" column="ZIFU41" jdbcType="VARCHAR"/>
            <result property="ZIFU42" column="ZIFU42" jdbcType="VARCHAR"/>
            <result property="ZIFU43" column="ZIFU43" jdbcType="VARCHAR"/>
            <result property="ZIFU44" column="ZIFU44" jdbcType="VARCHAR"/>
            <result property="ZIFU45" column="ZIFU45" jdbcType="VARCHAR"/>
            <result property="ZIFU46" column="ZIFU46" jdbcType="VARCHAR"/>
            <result property="ZIFU47" column="ZIFU47" jdbcType="VARCHAR"/>
            <result property="ZIFU48" column="ZIFU48" jdbcType="VARCHAR"/>
            <result property="ZIFU49" column="ZIFU49" jdbcType="VARCHAR"/>
            <result property="ZIFU50" column="ZIFU50" jdbcType="VARCHAR"/>
            <result property="ZIFU51" column="ZIFU51" jdbcType="VARCHAR"/>
            <result property="ZIFU52" column="ZIFU52" jdbcType="VARCHAR"/>
            <result property="ZIFU53" column="ZIFU53" jdbcType="VARCHAR"/>
            <result property="ZIFU54" column="ZIFU54" jdbcType="VARCHAR"/>
            <result property="ZIFU55" column="ZIFU55" jdbcType="VARCHAR"/>
            <result property="ZIFU56" column="ZIFU56" jdbcType="VARCHAR"/>
            <result property="ZIFU57" column="ZIFU57" jdbcType="VARCHAR"/>
            <result property="ZIFU58" column="ZIFU58" jdbcType="VARCHAR"/>
            <result property="ZIFU59" column="ZIFU59" jdbcType="VARCHAR"/>
            <result property="ZIFU60" column="ZIFU60" jdbcType="VARCHAR"/>
            <result property="SHENHERXM" column="SHENHERXM" jdbcType="VARCHAR"/>
            <result property="BAOGAORXM" column="BAOGAORXM" jdbcType="VARCHAR"/>
            <result property="BAOGAORQ" column="BAOGAORQ" jdbcType="TIMESTAMP"/>
            <result property="JIANCHAHAO" column="JIANCHAHAO" jdbcType="VARCHAR"/>
            <result property="JIANCHAJF" column="JIANCHAJF" jdbcType="VARCHAR"/>
            <result property="YUYUEZYSX" column="YUYUEZYSX" jdbcType="VARCHAR"/>
            <result property="TUWENBGFBZ" column="TUWENBGFBZ" jdbcType="DECIMAL"/>
            <result property="WEISHOUXZXBZ" column="WEISHOUXZXBZ" jdbcType="DECIMAL"/>
            <result property="YICHAKBG" column="YICHAKBG" jdbcType="VARCHAR"/>
            <result property="TAOCANXSBRMXID" column="TAOCANXSBRMXID" jdbcType="VARCHAR"/>
            <result property="TAOCANXSBZ" column="TAOCANXSBZ" jdbcType="DECIMAL"/>
            <result property="DIANZIBLSY1" column="DIANZIBLSY1" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY2" column="DIANZIBLSY2" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY3" column="DIANZIBLSY3" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY4" column="DIANZIBLSY4" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY5" column="DIANZIBLSY5" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY6" column="DIANZIBLSY6" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY7" column="DIANZIBLSY7" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY8" column="DIANZIBLSY8" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY9" column="DIANZIBLSY9" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY10" column="DIANZIBLSY10" jdbcType="VARCHAR"/>
            <result property="FANGWENHAO" column="FANGWENHAO" jdbcType="VARCHAR"/>
            <result property="YINGXIANGHAO" column="YINGXIANGHAO" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYS" column="ZHUZHIYS" jdbcType="VARCHAR"/>
            <result property="BINGRENYLZ" column="BINGRENYLZ" jdbcType="VARCHAR"/>
            <result property="YISHENGZID" column="YISHENGZID" jdbcType="VARCHAR"/>
            <result property="JIFEIBZ" column="JIFEIBZ" jdbcType="DECIMAL"/>
            <result property="JIFEIREN" column="JIFEIREN" jdbcType="VARCHAR"/>
            <result property="JIFEISJ" column="JIFEISJ" jdbcType="TIMESTAMP"/>
            <result property="JIFEIKS" column="JIFEIKS" jdbcType="VARCHAR"/>
            <result property="BAOGAODZ" column="BAOGAODZ" jdbcType="VARCHAR"/>
            <result property="DUOCHONGNYBZ" column="DUOCHONGNYBZ" jdbcType="DECIMAL"/>
            <result property="BAOGAOCKBZ" column="BAOGAOCKBZ" jdbcType="DECIMAL"/>
            <result property="BAOGAOCKSJ" column="BAOGAOCKSJ" jdbcType="TIMESTAMP"/>
            <result property="BAOGAOCKR" column="BAOGAOCKR" jdbcType="VARCHAR"/>
            <result property="HUSONGFS" column="HUSONGFS" jdbcType="VARCHAR"/>
            <result property="ZUTUO" column="ZUTUO" jdbcType="VARCHAR"/>
            <result property="SONGJIANSJ" column="SONGJIANSJ" jdbcType="TIMESTAMP"/>
            <result property="SONGJIANYS" column="SONGJIANYS" jdbcType="VARCHAR"/>
            <result property="SQDH" column="SQDH" jdbcType="VARCHAR"/>
            <result property="BINGRENCKBGBZ" column="BINGRENCKBGBZ" jdbcType="DECIMAL"/>
            <result property="BINGRENCKBGSJ" column="BINGRENCKBGSJ" jdbcType="TIMESTAMP"/>
            <result property="JIFEIMS" column="JIFEIMS" jdbcType="DECIMAL"/>
            <result property="ZHIQINGTYSBLJLXH" column="ZHIQINGTYSBLJLXH" jdbcType="DECIMAL"/>
            <result property="YIZHUXMFLID" column="YIZHUXMFLID" jdbcType="VARCHAR"/>
            <result property="YUANQIANBZ" column="YUANQIANBZ" jdbcType="DECIMAL"/>
            <result property="WEIJIBZ" column="WEIJIBZ" jdbcType="VARCHAR"/>
            <result property="GCPBZ" column="GCPBZ" jdbcType="DECIMAL"/>
            <result property="YUYUEBZ" column="YUYUEBZ" jdbcType="DECIMAL"/>
            <result property="SHENQINGDJKID" column="SHENQINGDJKID" jdbcType="VARCHAR"/>
            <result property="BINGLIZT" column="BINGLIZT" jdbcType="VARCHAR"/>
            <result property="HULIANWSQDID" column="HULIANWSQDID" jdbcType="VARCHAR"/>
            <result property="ZHONGXYYBZ" column="ZHONGXYYBZ" jdbcType="DECIMAL"/>
            <result property="ZUHAO" column="ZUHAO" jdbcType="VARCHAR"/>
            <result property="JIZHENYY" column="JIZHENYY" jdbcType="VARCHAR"/>
            <result property="BINGLIQXJFBZ" column="BINGLIQXJFBZ" jdbcType="DECIMAL"/>
            <result property="TONGLEIYZID" column="TONGLEIYZID" jdbcType="VARCHAR"/>
            <result property="ZENGQIANGSMBZ" column="ZENGQIANGSMBZ" jdbcType="DECIMAL"/>
            <result property="YUNYXBZ" column="YUNYXBZ" jdbcType="DECIMAL"/>
            <result property="ORDERID" column="ORDERID" jdbcType="VARCHAR"/>
            <result property="GUTISJ" column="GUTISJ" jdbcType="TIMESTAMP"/>
            <result property="LITISJ" column="LITISJ" jdbcType="TIMESTAMP"/>
            <result property="BINGRENHAO" column="BINGRENHAO" jdbcType="VARCHAR"/>
            <result property="SHENHERQ" column="SHENHERQ" jdbcType="TIMESTAMP"/>
            <result property="HOSPITAL_NAME" column="HOSPITAL_NAME" jdbcType="VARCHAR"/>
            <result property="BAOGAOBZ" column="BAOGAOBZ" jdbcType="VARCHAR"/>
            <result property="FUJIAXMSQD" column="FUJIAXMSQD" jdbcType="VARCHAR"/>
            <result property="YUYUESJ" column="YUYUESJ" jdbcType="VARCHAR"/>
            <result property="GUANLIANSQDID" column="GUANLIANSQDID" jdbcType="VARCHAR"/>
            <result property="FANGJIANHAO" column="FANGJIANHAO" jdbcType="VARCHAR"/>
            <result property="KAIDANYQID" column="KAIDANYQID" jdbcType="VARCHAR"/>
            <result property="EXINGZLBZ" column="EXINGZLBZ" jdbcType="DECIMAL"/>
            <result property="YOUHUILB" column="YOUHUILB" jdbcType="VARCHAR"/>
            <result property="BUWEISL" column="BUWEISL" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        SHENQINDANID,YINGYONGID,YUANQUID,
        MOBANDM,BAOGAODMBDM,YIZHUID,
        YIZHUXMID,YIZHUMC,JIUZHENID,
        BINGRENZYID,BINGRENID,YINGERID,
        BINGRENXM,XINGBIE,NIANLING,
        NIANLINGDW,CHUSHENGRQ,SHURUREN,
        SHURUSJ,KAIDANREN,KAIDANKS,
        KAIDANRQ,JIANCHAKS,JIANCHAREN,
        JIANCHARQ,MENZHENZYBZ,DANGQIANZT,
        QIWANGJCRQ,YUYUESQR,YUYUESQRQ,
        YUYUEAPKS,YUYUEQRRQ,YUYUEQRR,
        JIANCHAAPRQ,JIANCHAAPR,BAOGAODFS,
        DAYINREN,DAYINRQ,YOUXIANJI,
        SHOUFEIBZ,CHEXIAOREN,CHEXIAOSJ,
        ZHUSU,JIANYAOBS,TIGEJC,
        LINCHUANGZD,JIANCHABW,JIANCHAMD,
        XIANGGUANJC,JIANCHALX,TUIDANREN,
        TUIDANRQ,TUIDANRXM,TUIDANYY,
        FEIYONGHJ,YISHOUJE,KEZHIXBZ,
        YUYUEQRRQ2,YUYUEHAO,CHUANGBIANBZ,
        TESHUSM,BEIZHU,RISDJHM,
        ZIFEIBZ,YUYUEDYBZ,YAOLIJDMDBZ,
        ZHUYISX,JIZHENBZ,TESHUBZMC,
        JIANCHASJ,ZHENDUANJG,WEIJIZBZ,
        JIAOPIANFBZ,ZHENDUANLX,ZHENDUANYJ,
        SHOUSUOYA,SHUZHANGYA,ZIFU1,
        ZIFU2,ZIFU3,ZIFU4,
        ZIFU5,ZIFU6,ZIFU7,
        ZIFU8,ZIFU9,ZIFU10,
        ZIFU11,ZIFU12,ZIFU13,
        ZIFU14,ZIFU15,ZIFU16,
        ZIFU17,ZIFU18,ZIFU19,
        ZIFU20,ZIFU21,ZIFU22,
        ZIFU23,ZIFU24,ZIFU25,
        ZIFU26,ZIFU27,ZIFU28,
        ZIFU29,ZIFU30,ZIFU31,
        ZIFU32,ZIFU33,ZIFU34,
        ZIFU35,ZIFU36,ZIFU37,
        ZIFU38,ZIFU39,ZIFU40,
        ZIFU41,ZIFU42,ZIFU43,
        ZIFU44,ZIFU45,ZIFU46,
        ZIFU47,ZIFU48,ZIFU49,
        ZIFU50,ZIFU51,ZIFU52,
        ZIFU53,ZIFU54,ZIFU55,
        ZIFU56,ZIFU57,ZIFU58,
        ZIFU59,ZIFU60,SHENHERXM,
        BAOGAORXM,BAOGAORQ,JIANCHAHAO,
        JIANCHAJF,YUYUEZYSX,TUWENBGFBZ,
        WEISHOUXZXBZ,YICHAKBG,TAOCANXSBRMXID,
        TAOCANXSBZ,DIANZIBLSY1,DIANZIBLSY2,
        DIANZIBLSY3,DIANZIBLSY4,DIANZIBLSY5,
        DIANZIBLSY6,DIANZIBLSY7,DIANZIBLSY8,
        DIANZIBLSY9,DIANZIBLSY10,FANGWENHAO,
        YINGXIANGHAO,ZHUZHIYS,BINGRENYLZ,
        YISHENGZID,JIFEIBZ,JIFEIREN,
        JIFEISJ,JIFEIKS,BAOGAODZ,
        DUOCHONGNYBZ,BAOGAOCKBZ,BAOGAOCKSJ,
        BAOGAOCKR,HUSONGFS,ZUTUO,
        SONGJIANSJ,SONGJIANYS,SQDH,
        BINGRENCKBGBZ,BINGRENCKBGSJ,JIFEIMS,
        ZHIQINGTYSBLJLXH,YIZHUXMFLID,YUANQIANBZ,
        WEIJIBZ,GCPBZ,YUYUEBZ,
        SHENQINGDJKID,BINGLIZT,HULIANWSQDID,
        ZHONGXYYBZ,ZUHAO,JIZHENYY,
        BINGLIQXJFBZ,TONGLEIYZID,ZENGQIANGSMBZ,
        YUNYXBZ,ORDERID,GUTISJ,
        LITISJ,BINGRENHAO,SHENHERQ,
        HOSPITAL_NAME,BAOGAOBZ,FUJIAXMSQD,
        YUYUESJ,GUANLIANSQDID,FANGJIANHAO,
        KAIDANYQID,EXINGZLBZ,YOUHUILB,
        BUWEISL
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from YJ_SHENQINGDAN
        where  SHENQINDANID = #{SHENQINDANID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from YJ_SHENQINGDAN
        where  SHENQINDANID = #{SHENQINDANID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="SHENQINDANID" keyProperty="SHENQINDANID" parameterType="com.javazx.batch.po.YjShenqingdan" useGeneratedKeys="true">
        insert into YJ_SHENQINGDAN
        ( SHENQINDANID,YINGYONGID,YUANQUID
        ,MOBANDM,BAOGAODMBDM,YIZHUID
        ,YIZHUXMID,YIZHUMC,JIUZHENID
        ,BINGRENZYID,BINGRENID,YINGERID
        ,BINGRENXM,XINGBIE,NIANLING
        ,NIANLINGDW,CHUSHENGRQ,SHURUREN
        ,SHURUSJ,KAIDANREN,KAIDANKS
        ,KAIDANRQ,JIANCHAKS,JIANCHAREN
        ,JIANCHARQ,MENZHENZYBZ,DANGQIANZT
        ,QIWANGJCRQ,YUYUESQR,YUYUESQRQ
        ,YUYUEAPKS,YUYUEQRRQ,YUYUEQRR
        ,JIANCHAAPRQ,JIANCHAAPR,BAOGAODFS
        ,DAYINREN,DAYINRQ,YOUXIANJI
        ,SHOUFEIBZ,CHEXIAOREN,CHEXIAOSJ
        ,ZHUSU,JIANYAOBS,TIGEJC
        ,LINCHUANGZD,JIANCHABW,JIANCHAMD
        ,XIANGGUANJC,JIANCHALX,TUIDANREN
        ,TUIDANRQ,TUIDANRXM,TUIDANYY
        ,FEIYONGHJ,YISHOUJE,KEZHIXBZ
        ,YUYUEQRRQ2,YUYUEHAO,CHUANGBIANBZ
        ,TESHUSM,BEIZHU,RISDJHM
        ,ZIFEIBZ,YUYUEDYBZ,YAOLIJDMDBZ
        ,ZHUYISX,JIZHENBZ,TESHUBZMC
        ,JIANCHASJ,ZHENDUANJG,WEIJIZBZ
        ,JIAOPIANFBZ,ZHENDUANLX,ZHENDUANYJ
        ,SHOUSUOYA,SHUZHANGYA,ZIFU1
        ,ZIFU2,ZIFU3,ZIFU4
        ,ZIFU5,ZIFU6,ZIFU7
        ,ZIFU8,ZIFU9,ZIFU10
        ,ZIFU11,ZIFU12,ZIFU13
        ,ZIFU14,ZIFU15,ZIFU16
        ,ZIFU17,ZIFU18,ZIFU19
        ,ZIFU20,ZIFU21,ZIFU22
        ,ZIFU23,ZIFU24,ZIFU25
        ,ZIFU26,ZIFU27,ZIFU28
        ,ZIFU29,ZIFU30,ZIFU31
        ,ZIFU32,ZIFU33,ZIFU34
        ,ZIFU35,ZIFU36,ZIFU37
        ,ZIFU38,ZIFU39,ZIFU40
        ,ZIFU41,ZIFU42,ZIFU43
        ,ZIFU44,ZIFU45,ZIFU46
        ,ZIFU47,ZIFU48,ZIFU49
        ,ZIFU50,ZIFU51,ZIFU52
        ,ZIFU53,ZIFU54,ZIFU55
        ,ZIFU56,ZIFU57,ZIFU58
        ,ZIFU59,ZIFU60,SHENHERXM
        ,BAOGAORXM,BAOGAORQ,JIANCHAHAO
        ,JIANCHAJF,YUYUEZYSX,TUWENBGFBZ
        ,WEISHOUXZXBZ,YICHAKBG,TAOCANXSBRMXID
        ,TAOCANXSBZ,DIANZIBLSY1,DIANZIBLSY2
        ,DIANZIBLSY3,DIANZIBLSY4,DIANZIBLSY5
        ,DIANZIBLSY6,DIANZIBLSY7,DIANZIBLSY8
        ,DIANZIBLSY9,DIANZIBLSY10,FANGWENHAO
        ,YINGXIANGHAO,ZHUZHIYS,BINGRENYLZ
        ,YISHENGZID,JIFEIBZ,JIFEIREN
        ,JIFEISJ,JIFEIKS,BAOGAODZ
        ,DUOCHONGNYBZ,BAOGAOCKBZ,BAOGAOCKSJ
        ,BAOGAOCKR,HUSONGFS,ZUTUO
        ,SONGJIANSJ,SONGJIANYS,SQDH
        ,BINGRENCKBGBZ,BINGRENCKBGSJ,JIFEIMS
        ,ZHIQINGTYSBLJLXH,YIZHUXMFLID,YUANQIANBZ
        ,WEIJIBZ,GCPBZ,YUYUEBZ
        ,SHENQINGDJKID,BINGLIZT,HULIANWSQDID
        ,ZHONGXYYBZ,ZUHAO,JIZHENYY
        ,BINGLIQXJFBZ,TONGLEIYZID,ZENGQIANGSMBZ
        ,YUNYXBZ,ORDERID,GUTISJ
        ,LITISJ,BINGRENHAO,SHENHERQ
        ,HOSPITAL_NAME,BAOGAOBZ,FUJIAXMSQD
        ,YUYUESJ,GUANLIANSQDID,FANGJIANHAO
        ,KAIDANYQID,EXINGZLBZ,YOUHUILB
        ,BUWEISL)
        values (#{SHENQINDANID,jdbcType=VARCHAR},#{YINGYONGID,jdbcType=VARCHAR},#{YUANQUID,jdbcType=VARCHAR}
        ,#{MOBANDM,jdbcType=VARCHAR},#{BAOGAODMBDM,jdbcType=VARCHAR},#{YIZHUID,jdbcType=VARCHAR}
        ,#{YIZHUXMID,jdbcType=VARCHAR},#{YIZHUMC,jdbcType=VARCHAR},#{JIUZHENID,jdbcType=VARCHAR}
        ,#{BINGRENZYID,jdbcType=VARCHAR},#{BINGRENID,jdbcType=VARCHAR},#{YINGERID,jdbcType=VARCHAR}
        ,#{BINGRENXM,jdbcType=VARCHAR},#{XINGBIE,jdbcType=VARCHAR},#{NIANLING,jdbcType=DECIMAL}
        ,#{NIANLINGDW,jdbcType=VARCHAR},#{CHUSHENGRQ,jdbcType=TIMESTAMP},#{SHURUREN,jdbcType=VARCHAR}
        ,#{SHURUSJ,jdbcType=TIMESTAMP},#{KAIDANREN,jdbcType=VARCHAR},#{KAIDANKS,jdbcType=VARCHAR}
        ,#{KAIDANRQ,jdbcType=TIMESTAMP},#{JIANCHAKS,jdbcType=VARCHAR},#{JIANCHAREN,jdbcType=VARCHAR}
        ,#{JIANCHARQ,jdbcType=TIMESTAMP},#{MENZHENZYBZ,jdbcType=DECIMAL},#{DANGQIANZT,jdbcType=VARCHAR}
        ,#{QIWANGJCRQ,jdbcType=TIMESTAMP},#{YUYUESQR,jdbcType=VARCHAR},#{YUYUESQRQ,jdbcType=TIMESTAMP}
        ,#{YUYUEAPKS,jdbcType=VARCHAR},#{YUYUEQRRQ,jdbcType=TIMESTAMP},#{YUYUEQRR,jdbcType=VARCHAR}
        ,#{JIANCHAAPRQ,jdbcType=TIMESTAMP},#{JIANCHAAPR,jdbcType=VARCHAR},#{BAOGAODFS,jdbcType=DECIMAL}
        ,#{DAYINREN,jdbcType=VARCHAR},#{DAYINRQ,jdbcType=TIMESTAMP},#{YOUXIANJI,jdbcType=VARCHAR}
        ,#{SHOUFEIBZ,jdbcType=DECIMAL},#{CHEXIAOREN,jdbcType=VARCHAR},#{CHEXIAOSJ,jdbcType=TIMESTAMP}
        ,#{ZHUSU,jdbcType=VARCHAR},#{JIANYAOBS,jdbcType=VARCHAR},#{TIGEJC,jdbcType=VARCHAR}
        ,#{LINCHUANGZD,jdbcType=VARCHAR},#{JIANCHABW,jdbcType=VARCHAR},#{JIANCHAMD,jdbcType=VARCHAR}
        ,#{XIANGGUANJC,jdbcType=VARCHAR},#{JIANCHALX,jdbcType=VARCHAR},#{TUIDANREN,jdbcType=VARCHAR}
        ,#{TUIDANRQ,jdbcType=TIMESTAMP},#{TUIDANRXM,jdbcType=VARCHAR},#{TUIDANYY,jdbcType=VARCHAR}
        ,#{FEIYONGHJ,jdbcType=DECIMAL},#{YISHOUJE,jdbcType=DECIMAL},#{KEZHIXBZ,jdbcType=DECIMAL}
        ,#{YUYUEQRRQ2,jdbcType=VARCHAR},#{YUYUEHAO,jdbcType=VARCHAR},#{CHUANGBIANBZ,jdbcType=DECIMAL}
        ,#{TESHUSM,jdbcType=VARCHAR},#{BEIZHU,jdbcType=VARCHAR},#{RISDJHM,jdbcType=VARCHAR}
        ,#{ZIFEIBZ,jdbcType=DECIMAL},#{YUYUEDYBZ,jdbcType=DECIMAL},#{YAOLIJDMDBZ,jdbcType=DECIMAL}
        ,#{ZHUYISX,jdbcType=VARCHAR},#{JIZHENBZ,jdbcType=DECIMAL},#{TESHUBZMC,jdbcType=VARCHAR}
        ,#{JIANCHASJ,jdbcType=VARCHAR},#{ZHENDUANJG,jdbcType=VARCHAR},#{WEIJIZBZ,jdbcType=DECIMAL}
        ,#{JIAOPIANFBZ,jdbcType=DECIMAL},#{ZHENDUANLX,jdbcType=VARCHAR},#{ZHENDUANYJ,jdbcType=VARCHAR}
        ,#{SHOUSUOYA,jdbcType=DECIMAL},#{SHUZHANGYA,jdbcType=DECIMAL},#{ZIFU1,jdbcType=VARCHAR}
        ,#{ZIFU2,jdbcType=VARCHAR},#{ZIFU3,jdbcType=VARCHAR},#{ZIFU4,jdbcType=VARCHAR}
        ,#{ZIFU5,jdbcType=VARCHAR},#{ZIFU6,jdbcType=VARCHAR},#{ZIFU7,jdbcType=VARCHAR}
        ,#{ZIFU8,jdbcType=VARCHAR},#{ZIFU9,jdbcType=VARCHAR},#{ZIFU10,jdbcType=VARCHAR}
        ,#{ZIFU11,jdbcType=VARCHAR},#{ZIFU12,jdbcType=VARCHAR},#{ZIFU13,jdbcType=VARCHAR}
        ,#{ZIFU14,jdbcType=VARCHAR},#{ZIFU15,jdbcType=VARCHAR},#{ZIFU16,jdbcType=VARCHAR}
        ,#{ZIFU17,jdbcType=VARCHAR},#{ZIFU18,jdbcType=VARCHAR},#{ZIFU19,jdbcType=VARCHAR}
        ,#{ZIFU20,jdbcType=VARCHAR},#{ZIFU21,jdbcType=VARCHAR},#{ZIFU22,jdbcType=VARCHAR}
        ,#{ZIFU23,jdbcType=VARCHAR},#{ZIFU24,jdbcType=VARCHAR},#{ZIFU25,jdbcType=VARCHAR}
        ,#{ZIFU26,jdbcType=VARCHAR},#{ZIFU27,jdbcType=VARCHAR},#{ZIFU28,jdbcType=VARCHAR}
        ,#{ZIFU29,jdbcType=VARCHAR},#{ZIFU30,jdbcType=VARCHAR},#{ZIFU31,jdbcType=VARCHAR}
        ,#{ZIFU32,jdbcType=VARCHAR},#{ZIFU33,jdbcType=VARCHAR},#{ZIFU34,jdbcType=VARCHAR}
        ,#{ZIFU35,jdbcType=VARCHAR},#{ZIFU36,jdbcType=VARCHAR},#{ZIFU37,jdbcType=VARCHAR}
        ,#{ZIFU38,jdbcType=VARCHAR},#{ZIFU39,jdbcType=VARCHAR},#{ZIFU40,jdbcType=VARCHAR}
        ,#{ZIFU41,jdbcType=VARCHAR},#{ZIFU42,jdbcType=VARCHAR},#{ZIFU43,jdbcType=VARCHAR}
        ,#{ZIFU44,jdbcType=VARCHAR},#{ZIFU45,jdbcType=VARCHAR},#{ZIFU46,jdbcType=VARCHAR}
        ,#{ZIFU47,jdbcType=VARCHAR},#{ZIFU48,jdbcType=VARCHAR},#{ZIFU49,jdbcType=VARCHAR}
        ,#{ZIFU50,jdbcType=VARCHAR},#{ZIFU51,jdbcType=VARCHAR},#{ZIFU52,jdbcType=VARCHAR}
        ,#{ZIFU53,jdbcType=VARCHAR},#{ZIFU54,jdbcType=VARCHAR},#{ZIFU55,jdbcType=VARCHAR}
        ,#{ZIFU56,jdbcType=VARCHAR},#{ZIFU57,jdbcType=VARCHAR},#{ZIFU58,jdbcType=VARCHAR}
        ,#{ZIFU59,jdbcType=VARCHAR},#{ZIFU60,jdbcType=VARCHAR},#{SHENHERXM,jdbcType=VARCHAR}
        ,#{BAOGAORXM,jdbcType=VARCHAR},#{BAOGAORQ,jdbcType=TIMESTAMP},#{JIANCHAHAO,jdbcType=VARCHAR}
        ,#{JIANCHAJF,jdbcType=VARCHAR},#{YUYUEZYSX,jdbcType=VARCHAR},#{TUWENBGFBZ,jdbcType=DECIMAL}
        ,#{WEISHOUXZXBZ,jdbcType=DECIMAL},#{YICHAKBG,jdbcType=VARCHAR},#{TAOCANXSBRMXID,jdbcType=VARCHAR}
        ,#{TAOCANXSBZ,jdbcType=DECIMAL},#{DIANZIBLSY1,jdbcType=VARCHAR},#{DIANZIBLSY2,jdbcType=VARCHAR}
        ,#{DIANZIBLSY3,jdbcType=VARCHAR},#{DIANZIBLSY4,jdbcType=VARCHAR},#{DIANZIBLSY5,jdbcType=VARCHAR}
        ,#{DIANZIBLSY6,jdbcType=VARCHAR},#{DIANZIBLSY7,jdbcType=VARCHAR},#{DIANZIBLSY8,jdbcType=VARCHAR}
        ,#{DIANZIBLSY9,jdbcType=VARCHAR},#{DIANZIBLSY10,jdbcType=VARCHAR},#{FANGWENHAO,jdbcType=VARCHAR}
        ,#{YINGXIANGHAO,jdbcType=VARCHAR},#{ZHUZHIYS,jdbcType=VARCHAR},#{BINGRENYLZ,jdbcType=VARCHAR}
        ,#{YISHENGZID,jdbcType=VARCHAR},#{JIFEIBZ,jdbcType=DECIMAL},#{JIFEIREN,jdbcType=VARCHAR}
        ,#{JIFEISJ,jdbcType=TIMESTAMP},#{JIFEIKS,jdbcType=VARCHAR},#{BAOGAODZ,jdbcType=VARCHAR}
        ,#{DUOCHONGNYBZ,jdbcType=DECIMAL},#{BAOGAOCKBZ,jdbcType=DECIMAL},#{BAOGAOCKSJ,jdbcType=TIMESTAMP}
        ,#{BAOGAOCKR,jdbcType=VARCHAR},#{HUSONGFS,jdbcType=VARCHAR},#{ZUTUO,jdbcType=VARCHAR}
        ,#{SONGJIANSJ,jdbcType=TIMESTAMP},#{SONGJIANYS,jdbcType=VARCHAR},#{SQDH,jdbcType=VARCHAR}
        ,#{BINGRENCKBGBZ,jdbcType=DECIMAL},#{BINGRENCKBGSJ,jdbcType=TIMESTAMP},#{JIFEIMS,jdbcType=DECIMAL}
        ,#{ZHIQINGTYSBLJLXH,jdbcType=DECIMAL},#{YIZHUXMFLID,jdbcType=VARCHAR},#{YUANQIANBZ,jdbcType=DECIMAL}
        ,#{WEIJIBZ,jdbcType=VARCHAR},#{GCPBZ,jdbcType=DECIMAL},#{YUYUEBZ,jdbcType=DECIMAL}
        ,#{SHENQINGDJKID,jdbcType=VARCHAR},#{BINGLIZT,jdbcType=VARCHAR},#{HULIANWSQDID,jdbcType=VARCHAR}
        ,#{ZHONGXYYBZ,jdbcType=DECIMAL},#{ZUHAO,jdbcType=VARCHAR},#{JIZHENYY,jdbcType=VARCHAR}
        ,#{BINGLIQXJFBZ,jdbcType=DECIMAL},#{TONGLEIYZID,jdbcType=VARCHAR},#{ZENGQIANGSMBZ,jdbcType=DECIMAL}
        ,#{YUNYXBZ,jdbcType=DECIMAL},#{ORDERID,jdbcType=VARCHAR},#{GUTISJ,jdbcType=TIMESTAMP}
        ,#{LITISJ,jdbcType=TIMESTAMP},#{BINGRENHAO,jdbcType=VARCHAR},#{SHENHERQ,jdbcType=TIMESTAMP}
        ,#{HOSPITAL_NAME,jdbcType=VARCHAR},#{BAOGAOBZ,jdbcType=VARCHAR},#{FUJIAXMSQD,jdbcType=VARCHAR}
        ,#{YUYUESJ,jdbcType=VARCHAR},#{GUANLIANSQDID,jdbcType=VARCHAR},#{FANGJIANHAO,jdbcType=VARCHAR}
        ,#{KAIDANYQID,jdbcType=VARCHAR},#{EXINGZLBZ,jdbcType=DECIMAL},#{YOUHUILB,jdbcType=VARCHAR}
        ,#{BUWEISL,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="SHENQINDANID" keyProperty="SHENQINDANID" parameterType="com.javazx.batch.po.YjShenqingdan" useGeneratedKeys="true">
        insert into YJ_SHENQINGDAN
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="SHENQINDANID != null">SHENQINDANID,</if>
                <if test="YINGYONGID != null">YINGYONGID,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
                <if test="MOBANDM != null">MOBANDM,</if>
                <if test="BAOGAODMBDM != null">BAOGAODMBDM,</if>
                <if test="YIZHUID != null">YIZHUID,</if>
                <if test="YIZHUXMID != null">YIZHUXMID,</if>
                <if test="YIZHUMC != null">YIZHUMC,</if>
                <if test="JIUZHENID != null">JIUZHENID,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="YINGERID != null">YINGERID,</if>
                <if test="BINGRENXM != null">BINGRENXM,</if>
                <if test="XINGBIE != null">XINGBIE,</if>
                <if test="NIANLING != null">NIANLING,</if>
                <if test="NIANLINGDW != null">NIANLINGDW,</if>
                <if test="CHUSHENGRQ != null">CHUSHENGRQ,</if>
                <if test="SHURUREN != null">SHURUREN,</if>
                <if test="SHURUSJ != null">SHURUSJ,</if>
                <if test="KAIDANREN != null">KAIDANREN,</if>
                <if test="KAIDANKS != null">KAIDANKS,</if>
                <if test="KAIDANRQ != null">KAIDANRQ,</if>
                <if test="JIANCHAKS != null">JIANCHAKS,</if>
                <if test="JIANCHAREN != null">JIANCHAREN,</if>
                <if test="JIANCHARQ != null">JIANCHARQ,</if>
                <if test="MENZHENZYBZ != null">MENZHENZYBZ,</if>
                <if test="DANGQIANZT != null">DANGQIANZT,</if>
                <if test="QIWANGJCRQ != null">QIWANGJCRQ,</if>
                <if test="YUYUESQR != null">YUYUESQR,</if>
                <if test="YUYUESQRQ != null">YUYUESQRQ,</if>
                <if test="YUYUEAPKS != null">YUYUEAPKS,</if>
                <if test="YUYUEQRRQ != null">YUYUEQRRQ,</if>
                <if test="YUYUEQRR != null">YUYUEQRR,</if>
                <if test="JIANCHAAPRQ != null">JIANCHAAPRQ,</if>
                <if test="JIANCHAAPR != null">JIANCHAAPR,</if>
                <if test="BAOGAODFS != null">BAOGAODFS,</if>
                <if test="DAYINREN != null">DAYINREN,</if>
                <if test="DAYINRQ != null">DAYINRQ,</if>
                <if test="YOUXIANJI != null">YOUXIANJI,</if>
                <if test="SHOUFEIBZ != null">SHOUFEIBZ,</if>
                <if test="CHEXIAOREN != null">CHEXIAOREN,</if>
                <if test="CHEXIAOSJ != null">CHEXIAOSJ,</if>
                <if test="ZHUSU != null">ZHUSU,</if>
                <if test="JIANYAOBS != null">JIANYAOBS,</if>
                <if test="TIGEJC != null">TIGEJC,</if>
                <if test="LINCHUANGZD != null">LINCHUANGZD,</if>
                <if test="JIANCHABW != null">JIANCHABW,</if>
                <if test="JIANCHAMD != null">JIANCHAMD,</if>
                <if test="XIANGGUANJC != null">XIANGGUANJC,</if>
                <if test="JIANCHALX != null">JIANCHALX,</if>
                <if test="TUIDANREN != null">TUIDANREN,</if>
                <if test="TUIDANRQ != null">TUIDANRQ,</if>
                <if test="TUIDANRXM != null">TUIDANRXM,</if>
                <if test="TUIDANYY != null">TUIDANYY,</if>
                <if test="FEIYONGHJ != null">FEIYONGHJ,</if>
                <if test="YISHOUJE != null">YISHOUJE,</if>
                <if test="KEZHIXBZ != null">KEZHIXBZ,</if>
                <if test="YUYUEQRRQ2 != null">YUYUEQRRQ2,</if>
                <if test="YUYUEHAO != null">YUYUEHAO,</if>
                <if test="CHUANGBIANBZ != null">CHUANGBIANBZ,</if>
                <if test="TESHUSM != null">TESHUSM,</if>
                <if test="BEIZHU != null">BEIZHU,</if>
                <if test="RISDJHM != null">RISDJHM,</if>
                <if test="ZIFEIBZ != null">ZIFEIBZ,</if>
                <if test="YUYUEDYBZ != null">YUYUEDYBZ,</if>
                <if test="YAOLIJDMDBZ != null">YAOLIJDMDBZ,</if>
                <if test="ZHUYISX != null">ZHUYISX,</if>
                <if test="JIZHENBZ != null">JIZHENBZ,</if>
                <if test="TESHUBZMC != null">TESHUBZMC,</if>
                <if test="JIANCHASJ != null">JIANCHASJ,</if>
                <if test="ZHENDUANJG != null">ZHENDUANJG,</if>
                <if test="WEIJIZBZ != null">WEIJIZBZ,</if>
                <if test="JIAOPIANFBZ != null">JIAOPIANFBZ,</if>
                <if test="ZHENDUANLX != null">ZHENDUANLX,</if>
                <if test="ZHENDUANYJ != null">ZHENDUANYJ,</if>
                <if test="SHOUSUOYA != null">SHOUSUOYA,</if>
                <if test="SHUZHANGYA != null">SHUZHANGYA,</if>
                <if test="ZIFU1 != null">ZIFU1,</if>
                <if test="ZIFU2 != null">ZIFU2,</if>
                <if test="ZIFU3 != null">ZIFU3,</if>
                <if test="ZIFU4 != null">ZIFU4,</if>
                <if test="ZIFU5 != null">ZIFU5,</if>
                <if test="ZIFU6 != null">ZIFU6,</if>
                <if test="ZIFU7 != null">ZIFU7,</if>
                <if test="ZIFU8 != null">ZIFU8,</if>
                <if test="ZIFU9 != null">ZIFU9,</if>
                <if test="ZIFU10 != null">ZIFU10,</if>
                <if test="ZIFU11 != null">ZIFU11,</if>
                <if test="ZIFU12 != null">ZIFU12,</if>
                <if test="ZIFU13 != null">ZIFU13,</if>
                <if test="ZIFU14 != null">ZIFU14,</if>
                <if test="ZIFU15 != null">ZIFU15,</if>
                <if test="ZIFU16 != null">ZIFU16,</if>
                <if test="ZIFU17 != null">ZIFU17,</if>
                <if test="ZIFU18 != null">ZIFU18,</if>
                <if test="ZIFU19 != null">ZIFU19,</if>
                <if test="ZIFU20 != null">ZIFU20,</if>
                <if test="ZIFU21 != null">ZIFU21,</if>
                <if test="ZIFU22 != null">ZIFU22,</if>
                <if test="ZIFU23 != null">ZIFU23,</if>
                <if test="ZIFU24 != null">ZIFU24,</if>
                <if test="ZIFU25 != null">ZIFU25,</if>
                <if test="ZIFU26 != null">ZIFU26,</if>
                <if test="ZIFU27 != null">ZIFU27,</if>
                <if test="ZIFU28 != null">ZIFU28,</if>
                <if test="ZIFU29 != null">ZIFU29,</if>
                <if test="ZIFU30 != null">ZIFU30,</if>
                <if test="ZIFU31 != null">ZIFU31,</if>
                <if test="ZIFU32 != null">ZIFU32,</if>
                <if test="ZIFU33 != null">ZIFU33,</if>
                <if test="ZIFU34 != null">ZIFU34,</if>
                <if test="ZIFU35 != null">ZIFU35,</if>
                <if test="ZIFU36 != null">ZIFU36,</if>
                <if test="ZIFU37 != null">ZIFU37,</if>
                <if test="ZIFU38 != null">ZIFU38,</if>
                <if test="ZIFU39 != null">ZIFU39,</if>
                <if test="ZIFU40 != null">ZIFU40,</if>
                <if test="ZIFU41 != null">ZIFU41,</if>
                <if test="ZIFU42 != null">ZIFU42,</if>
                <if test="ZIFU43 != null">ZIFU43,</if>
                <if test="ZIFU44 != null">ZIFU44,</if>
                <if test="ZIFU45 != null">ZIFU45,</if>
                <if test="ZIFU46 != null">ZIFU46,</if>
                <if test="ZIFU47 != null">ZIFU47,</if>
                <if test="ZIFU48 != null">ZIFU48,</if>
                <if test="ZIFU49 != null">ZIFU49,</if>
                <if test="ZIFU50 != null">ZIFU50,</if>
                <if test="ZIFU51 != null">ZIFU51,</if>
                <if test="ZIFU52 != null">ZIFU52,</if>
                <if test="ZIFU53 != null">ZIFU53,</if>
                <if test="ZIFU54 != null">ZIFU54,</if>
                <if test="ZIFU55 != null">ZIFU55,</if>
                <if test="ZIFU56 != null">ZIFU56,</if>
                <if test="ZIFU57 != null">ZIFU57,</if>
                <if test="ZIFU58 != null">ZIFU58,</if>
                <if test="ZIFU59 != null">ZIFU59,</if>
                <if test="ZIFU60 != null">ZIFU60,</if>
                <if test="SHENHERXM != null">SHENHERXM,</if>
                <if test="BAOGAORXM != null">BAOGAORXM,</if>
                <if test="BAOGAORQ != null">BAOGAORQ,</if>
                <if test="JIANCHAHAO != null">JIANCHAHAO,</if>
                <if test="JIANCHAJF != null">JIANCHAJF,</if>
                <if test="YUYUEZYSX != null">YUYUEZYSX,</if>
                <if test="TUWENBGFBZ != null">TUWENBGFBZ,</if>
                <if test="WEISHOUXZXBZ != null">WEISHOUXZXBZ,</if>
                <if test="YICHAKBG != null">YICHAKBG,</if>
                <if test="TAOCANXSBRMXID != null">TAOCANXSBRMXID,</if>
                <if test="TAOCANXSBZ != null">TAOCANXSBZ,</if>
                <if test="DIANZIBLSY1 != null">DIANZIBLSY1,</if>
                <if test="DIANZIBLSY2 != null">DIANZIBLSY2,</if>
                <if test="DIANZIBLSY3 != null">DIANZIBLSY3,</if>
                <if test="DIANZIBLSY4 != null">DIANZIBLSY4,</if>
                <if test="DIANZIBLSY5 != null">DIANZIBLSY5,</if>
                <if test="DIANZIBLSY6 != null">DIANZIBLSY6,</if>
                <if test="DIANZIBLSY7 != null">DIANZIBLSY7,</if>
                <if test="DIANZIBLSY8 != null">DIANZIBLSY8,</if>
                <if test="DIANZIBLSY9 != null">DIANZIBLSY9,</if>
                <if test="DIANZIBLSY10 != null">DIANZIBLSY10,</if>
                <if test="FANGWENHAO != null">FANGWENHAO,</if>
                <if test="YINGXIANGHAO != null">YINGXIANGHAO,</if>
                <if test="ZHUZHIYS != null">ZHUZHIYS,</if>
                <if test="BINGRENYLZ != null">BINGRENYLZ,</if>
                <if test="YISHENGZID != null">YISHENGZID,</if>
                <if test="JIFEIBZ != null">JIFEIBZ,</if>
                <if test="JIFEIREN != null">JIFEIREN,</if>
                <if test="JIFEISJ != null">JIFEISJ,</if>
                <if test="JIFEIKS != null">JIFEIKS,</if>
                <if test="BAOGAODZ != null">BAOGAODZ,</if>
                <if test="DUOCHONGNYBZ != null">DUOCHONGNYBZ,</if>
                <if test="BAOGAOCKBZ != null">BAOGAOCKBZ,</if>
                <if test="BAOGAOCKSJ != null">BAOGAOCKSJ,</if>
                <if test="BAOGAOCKR != null">BAOGAOCKR,</if>
                <if test="HUSONGFS != null">HUSONGFS,</if>
                <if test="ZUTUO != null">ZUTUO,</if>
                <if test="SONGJIANSJ != null">SONGJIANSJ,</if>
                <if test="SONGJIANYS != null">SONGJIANYS,</if>
                <if test="SQDH != null">SQDH,</if>
                <if test="BINGRENCKBGBZ != null">BINGRENCKBGBZ,</if>
                <if test="BINGRENCKBGSJ != null">BINGRENCKBGSJ,</if>
                <if test="JIFEIMS != null">JIFEIMS,</if>
                <if test="ZHIQINGTYSBLJLXH != null">ZHIQINGTYSBLJLXH,</if>
                <if test="YIZHUXMFLID != null">YIZHUXMFLID,</if>
                <if test="YUANQIANBZ != null">YUANQIANBZ,</if>
                <if test="WEIJIBZ != null">WEIJIBZ,</if>
                <if test="GCPBZ != null">GCPBZ,</if>
                <if test="YUYUEBZ != null">YUYUEBZ,</if>
                <if test="SHENQINGDJKID != null">SHENQINGDJKID,</if>
                <if test="BINGLIZT != null">BINGLIZT,</if>
                <if test="HULIANWSQDID != null">HULIANWSQDID,</if>
                <if test="ZHONGXYYBZ != null">ZHONGXYYBZ,</if>
                <if test="ZUHAO != null">ZUHAO,</if>
                <if test="JIZHENYY != null">JIZHENYY,</if>
                <if test="BINGLIQXJFBZ != null">BINGLIQXJFBZ,</if>
                <if test="TONGLEIYZID != null">TONGLEIYZID,</if>
                <if test="ZENGQIANGSMBZ != null">ZENGQIANGSMBZ,</if>
                <if test="YUNYXBZ != null">YUNYXBZ,</if>
                <if test="ORDERID != null">ORDERID,</if>
                <if test="GUTISJ != null">GUTISJ,</if>
                <if test="LITISJ != null">LITISJ,</if>
                <if test="BINGRENHAO != null">BINGRENHAO,</if>
                <if test="SHENHERQ != null">SHENHERQ,</if>
                <if test="HOSPITAL_NAME != null">HOSPITAL_NAME,</if>
                <if test="BAOGAOBZ != null">BAOGAOBZ,</if>
                <if test="FUJIAXMSQD != null">FUJIAXMSQD,</if>
                <if test="YUYUESJ != null">YUYUESJ,</if>
                <if test="GUANLIANSQDID != null">GUANLIANSQDID,</if>
                <if test="FANGJIANHAO != null">FANGJIANHAO,</if>
                <if test="KAIDANYQID != null">KAIDANYQID,</if>
                <if test="EXINGZLBZ != null">EXINGZLBZ,</if>
                <if test="YOUHUILB != null">YOUHUILB,</if>
                <if test="BUWEISL != null">BUWEISL,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="SHENQINDANID != null">#{SHENQINDANID,jdbcType=VARCHAR},</if>
                <if test="YINGYONGID != null">#{YINGYONGID,jdbcType=VARCHAR},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=VARCHAR},</if>
                <if test="MOBANDM != null">#{MOBANDM,jdbcType=VARCHAR},</if>
                <if test="BAOGAODMBDM != null">#{BAOGAODMBDM,jdbcType=VARCHAR},</if>
                <if test="YIZHUID != null">#{YIZHUID,jdbcType=VARCHAR},</if>
                <if test="YIZHUXMID != null">#{YIZHUXMID,jdbcType=VARCHAR},</if>
                <if test="YIZHUMC != null">#{YIZHUMC,jdbcType=VARCHAR},</if>
                <if test="JIUZHENID != null">#{JIUZHENID,jdbcType=VARCHAR},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="YINGERID != null">#{YINGERID,jdbcType=VARCHAR},</if>
                <if test="BINGRENXM != null">#{BINGRENXM,jdbcType=VARCHAR},</if>
                <if test="XINGBIE != null">#{XINGBIE,jdbcType=VARCHAR},</if>
                <if test="NIANLING != null">#{NIANLING,jdbcType=DECIMAL},</if>
                <if test="NIANLINGDW != null">#{NIANLINGDW,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGRQ != null">#{CHUSHENGRQ,jdbcType=TIMESTAMP},</if>
                <if test="SHURUREN != null">#{SHURUREN,jdbcType=VARCHAR},</if>
                <if test="SHURUSJ != null">#{SHURUSJ,jdbcType=TIMESTAMP},</if>
                <if test="KAIDANREN != null">#{KAIDANREN,jdbcType=VARCHAR},</if>
                <if test="KAIDANKS != null">#{KAIDANKS,jdbcType=VARCHAR},</if>
                <if test="KAIDANRQ != null">#{KAIDANRQ,jdbcType=TIMESTAMP},</if>
                <if test="JIANCHAKS != null">#{JIANCHAKS,jdbcType=VARCHAR},</if>
                <if test="JIANCHAREN != null">#{JIANCHAREN,jdbcType=VARCHAR},</if>
                <if test="JIANCHARQ != null">#{JIANCHARQ,jdbcType=TIMESTAMP},</if>
                <if test="MENZHENZYBZ != null">#{MENZHENZYBZ,jdbcType=DECIMAL},</if>
                <if test="DANGQIANZT != null">#{DANGQIANZT,jdbcType=VARCHAR},</if>
                <if test="QIWANGJCRQ != null">#{QIWANGJCRQ,jdbcType=TIMESTAMP},</if>
                <if test="YUYUESQR != null">#{YUYUESQR,jdbcType=VARCHAR},</if>
                <if test="YUYUESQRQ != null">#{YUYUESQRQ,jdbcType=TIMESTAMP},</if>
                <if test="YUYUEAPKS != null">#{YUYUEAPKS,jdbcType=VARCHAR},</if>
                <if test="YUYUEQRRQ != null">#{YUYUEQRRQ,jdbcType=TIMESTAMP},</if>
                <if test="YUYUEQRR != null">#{YUYUEQRR,jdbcType=VARCHAR},</if>
                <if test="JIANCHAAPRQ != null">#{JIANCHAAPRQ,jdbcType=TIMESTAMP},</if>
                <if test="JIANCHAAPR != null">#{JIANCHAAPR,jdbcType=VARCHAR},</if>
                <if test="BAOGAODFS != null">#{BAOGAODFS,jdbcType=DECIMAL},</if>
                <if test="DAYINREN != null">#{DAYINREN,jdbcType=VARCHAR},</if>
                <if test="DAYINRQ != null">#{DAYINRQ,jdbcType=TIMESTAMP},</if>
                <if test="YOUXIANJI != null">#{YOUXIANJI,jdbcType=VARCHAR},</if>
                <if test="SHOUFEIBZ != null">#{SHOUFEIBZ,jdbcType=DECIMAL},</if>
                <if test="CHEXIAOREN != null">#{CHEXIAOREN,jdbcType=VARCHAR},</if>
                <if test="CHEXIAOSJ != null">#{CHEXIAOSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHUSU != null">#{ZHUSU,jdbcType=VARCHAR},</if>
                <if test="JIANYAOBS != null">#{JIANYAOBS,jdbcType=VARCHAR},</if>
                <if test="TIGEJC != null">#{TIGEJC,jdbcType=VARCHAR},</if>
                <if test="LINCHUANGZD != null">#{LINCHUANGZD,jdbcType=VARCHAR},</if>
                <if test="JIANCHABW != null">#{JIANCHABW,jdbcType=VARCHAR},</if>
                <if test="JIANCHAMD != null">#{JIANCHAMD,jdbcType=VARCHAR},</if>
                <if test="XIANGGUANJC != null">#{XIANGGUANJC,jdbcType=VARCHAR},</if>
                <if test="JIANCHALX != null">#{JIANCHALX,jdbcType=VARCHAR},</if>
                <if test="TUIDANREN != null">#{TUIDANREN,jdbcType=VARCHAR},</if>
                <if test="TUIDANRQ != null">#{TUIDANRQ,jdbcType=TIMESTAMP},</if>
                <if test="TUIDANRXM != null">#{TUIDANRXM,jdbcType=VARCHAR},</if>
                <if test="TUIDANYY != null">#{TUIDANYY,jdbcType=VARCHAR},</if>
                <if test="FEIYONGHJ != null">#{FEIYONGHJ,jdbcType=DECIMAL},</if>
                <if test="YISHOUJE != null">#{YISHOUJE,jdbcType=DECIMAL},</if>
                <if test="KEZHIXBZ != null">#{KEZHIXBZ,jdbcType=DECIMAL},</if>
                <if test="YUYUEQRRQ2 != null">#{YUYUEQRRQ2,jdbcType=VARCHAR},</if>
                <if test="YUYUEHAO != null">#{YUYUEHAO,jdbcType=VARCHAR},</if>
                <if test="CHUANGBIANBZ != null">#{CHUANGBIANBZ,jdbcType=DECIMAL},</if>
                <if test="TESHUSM != null">#{TESHUSM,jdbcType=VARCHAR},</if>
                <if test="BEIZHU != null">#{BEIZHU,jdbcType=VARCHAR},</if>
                <if test="RISDJHM != null">#{RISDJHM,jdbcType=VARCHAR},</if>
                <if test="ZIFEIBZ != null">#{ZIFEIBZ,jdbcType=DECIMAL},</if>
                <if test="YUYUEDYBZ != null">#{YUYUEDYBZ,jdbcType=DECIMAL},</if>
                <if test="YAOLIJDMDBZ != null">#{YAOLIJDMDBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUYISX != null">#{ZHUYISX,jdbcType=VARCHAR},</if>
                <if test="JIZHENBZ != null">#{JIZHENBZ,jdbcType=DECIMAL},</if>
                <if test="TESHUBZMC != null">#{TESHUBZMC,jdbcType=VARCHAR},</if>
                <if test="JIANCHASJ != null">#{JIANCHASJ,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANJG != null">#{ZHENDUANJG,jdbcType=VARCHAR},</if>
                <if test="WEIJIZBZ != null">#{WEIJIZBZ,jdbcType=DECIMAL},</if>
                <if test="JIAOPIANFBZ != null">#{JIAOPIANFBZ,jdbcType=DECIMAL},</if>
                <if test="ZHENDUANLX != null">#{ZHENDUANLX,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANYJ != null">#{ZHENDUANYJ,jdbcType=VARCHAR},</if>
                <if test="SHOUSUOYA != null">#{SHOUSUOYA,jdbcType=DECIMAL},</if>
                <if test="SHUZHANGYA != null">#{SHUZHANGYA,jdbcType=DECIMAL},</if>
                <if test="ZIFU1 != null">#{ZIFU1,jdbcType=VARCHAR},</if>
                <if test="ZIFU2 != null">#{ZIFU2,jdbcType=VARCHAR},</if>
                <if test="ZIFU3 != null">#{ZIFU3,jdbcType=VARCHAR},</if>
                <if test="ZIFU4 != null">#{ZIFU4,jdbcType=VARCHAR},</if>
                <if test="ZIFU5 != null">#{ZIFU5,jdbcType=VARCHAR},</if>
                <if test="ZIFU6 != null">#{ZIFU6,jdbcType=VARCHAR},</if>
                <if test="ZIFU7 != null">#{ZIFU7,jdbcType=VARCHAR},</if>
                <if test="ZIFU8 != null">#{ZIFU8,jdbcType=VARCHAR},</if>
                <if test="ZIFU9 != null">#{ZIFU9,jdbcType=VARCHAR},</if>
                <if test="ZIFU10 != null">#{ZIFU10,jdbcType=VARCHAR},</if>
                <if test="ZIFU11 != null">#{ZIFU11,jdbcType=VARCHAR},</if>
                <if test="ZIFU12 != null">#{ZIFU12,jdbcType=VARCHAR},</if>
                <if test="ZIFU13 != null">#{ZIFU13,jdbcType=VARCHAR},</if>
                <if test="ZIFU14 != null">#{ZIFU14,jdbcType=VARCHAR},</if>
                <if test="ZIFU15 != null">#{ZIFU15,jdbcType=VARCHAR},</if>
                <if test="ZIFU16 != null">#{ZIFU16,jdbcType=VARCHAR},</if>
                <if test="ZIFU17 != null">#{ZIFU17,jdbcType=VARCHAR},</if>
                <if test="ZIFU18 != null">#{ZIFU18,jdbcType=VARCHAR},</if>
                <if test="ZIFU19 != null">#{ZIFU19,jdbcType=VARCHAR},</if>
                <if test="ZIFU20 != null">#{ZIFU20,jdbcType=VARCHAR},</if>
                <if test="ZIFU21 != null">#{ZIFU21,jdbcType=VARCHAR},</if>
                <if test="ZIFU22 != null">#{ZIFU22,jdbcType=VARCHAR},</if>
                <if test="ZIFU23 != null">#{ZIFU23,jdbcType=VARCHAR},</if>
                <if test="ZIFU24 != null">#{ZIFU24,jdbcType=VARCHAR},</if>
                <if test="ZIFU25 != null">#{ZIFU25,jdbcType=VARCHAR},</if>
                <if test="ZIFU26 != null">#{ZIFU26,jdbcType=VARCHAR},</if>
                <if test="ZIFU27 != null">#{ZIFU27,jdbcType=VARCHAR},</if>
                <if test="ZIFU28 != null">#{ZIFU28,jdbcType=VARCHAR},</if>
                <if test="ZIFU29 != null">#{ZIFU29,jdbcType=VARCHAR},</if>
                <if test="ZIFU30 != null">#{ZIFU30,jdbcType=VARCHAR},</if>
                <if test="ZIFU31 != null">#{ZIFU31,jdbcType=VARCHAR},</if>
                <if test="ZIFU32 != null">#{ZIFU32,jdbcType=VARCHAR},</if>
                <if test="ZIFU33 != null">#{ZIFU33,jdbcType=VARCHAR},</if>
                <if test="ZIFU34 != null">#{ZIFU34,jdbcType=VARCHAR},</if>
                <if test="ZIFU35 != null">#{ZIFU35,jdbcType=VARCHAR},</if>
                <if test="ZIFU36 != null">#{ZIFU36,jdbcType=VARCHAR},</if>
                <if test="ZIFU37 != null">#{ZIFU37,jdbcType=VARCHAR},</if>
                <if test="ZIFU38 != null">#{ZIFU38,jdbcType=VARCHAR},</if>
                <if test="ZIFU39 != null">#{ZIFU39,jdbcType=VARCHAR},</if>
                <if test="ZIFU40 != null">#{ZIFU40,jdbcType=VARCHAR},</if>
                <if test="ZIFU41 != null">#{ZIFU41,jdbcType=VARCHAR},</if>
                <if test="ZIFU42 != null">#{ZIFU42,jdbcType=VARCHAR},</if>
                <if test="ZIFU43 != null">#{ZIFU43,jdbcType=VARCHAR},</if>
                <if test="ZIFU44 != null">#{ZIFU44,jdbcType=VARCHAR},</if>
                <if test="ZIFU45 != null">#{ZIFU45,jdbcType=VARCHAR},</if>
                <if test="ZIFU46 != null">#{ZIFU46,jdbcType=VARCHAR},</if>
                <if test="ZIFU47 != null">#{ZIFU47,jdbcType=VARCHAR},</if>
                <if test="ZIFU48 != null">#{ZIFU48,jdbcType=VARCHAR},</if>
                <if test="ZIFU49 != null">#{ZIFU49,jdbcType=VARCHAR},</if>
                <if test="ZIFU50 != null">#{ZIFU50,jdbcType=VARCHAR},</if>
                <if test="ZIFU51 != null">#{ZIFU51,jdbcType=VARCHAR},</if>
                <if test="ZIFU52 != null">#{ZIFU52,jdbcType=VARCHAR},</if>
                <if test="ZIFU53 != null">#{ZIFU53,jdbcType=VARCHAR},</if>
                <if test="ZIFU54 != null">#{ZIFU54,jdbcType=VARCHAR},</if>
                <if test="ZIFU55 != null">#{ZIFU55,jdbcType=VARCHAR},</if>
                <if test="ZIFU56 != null">#{ZIFU56,jdbcType=VARCHAR},</if>
                <if test="ZIFU57 != null">#{ZIFU57,jdbcType=VARCHAR},</if>
                <if test="ZIFU58 != null">#{ZIFU58,jdbcType=VARCHAR},</if>
                <if test="ZIFU59 != null">#{ZIFU59,jdbcType=VARCHAR},</if>
                <if test="ZIFU60 != null">#{ZIFU60,jdbcType=VARCHAR},</if>
                <if test="SHENHERXM != null">#{SHENHERXM,jdbcType=VARCHAR},</if>
                <if test="BAOGAORXM != null">#{BAOGAORXM,jdbcType=VARCHAR},</if>
                <if test="BAOGAORQ != null">#{BAOGAORQ,jdbcType=TIMESTAMP},</if>
                <if test="JIANCHAHAO != null">#{JIANCHAHAO,jdbcType=VARCHAR},</if>
                <if test="JIANCHAJF != null">#{JIANCHAJF,jdbcType=VARCHAR},</if>
                <if test="YUYUEZYSX != null">#{YUYUEZYSX,jdbcType=VARCHAR},</if>
                <if test="TUWENBGFBZ != null">#{TUWENBGFBZ,jdbcType=DECIMAL},</if>
                <if test="WEISHOUXZXBZ != null">#{WEISHOUXZXBZ,jdbcType=DECIMAL},</if>
                <if test="YICHAKBG != null">#{YICHAKBG,jdbcType=VARCHAR},</if>
                <if test="TAOCANXSBRMXID != null">#{TAOCANXSBRMXID,jdbcType=VARCHAR},</if>
                <if test="TAOCANXSBZ != null">#{TAOCANXSBZ,jdbcType=DECIMAL},</if>
                <if test="DIANZIBLSY1 != null">#{DIANZIBLSY1,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY2 != null">#{DIANZIBLSY2,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY3 != null">#{DIANZIBLSY3,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY4 != null">#{DIANZIBLSY4,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY5 != null">#{DIANZIBLSY5,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY6 != null">#{DIANZIBLSY6,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY7 != null">#{DIANZIBLSY7,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY8 != null">#{DIANZIBLSY8,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY9 != null">#{DIANZIBLSY9,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY10 != null">#{DIANZIBLSY10,jdbcType=VARCHAR},</if>
                <if test="FANGWENHAO != null">#{FANGWENHAO,jdbcType=VARCHAR},</if>
                <if test="YINGXIANGHAO != null">#{YINGXIANGHAO,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYS != null">#{ZHUZHIYS,jdbcType=VARCHAR},</if>
                <if test="BINGRENYLZ != null">#{BINGRENYLZ,jdbcType=VARCHAR},</if>
                <if test="YISHENGZID != null">#{YISHENGZID,jdbcType=VARCHAR},</if>
                <if test="JIFEIBZ != null">#{JIFEIBZ,jdbcType=DECIMAL},</if>
                <if test="JIFEIREN != null">#{JIFEIREN,jdbcType=VARCHAR},</if>
                <if test="JIFEISJ != null">#{JIFEISJ,jdbcType=TIMESTAMP},</if>
                <if test="JIFEIKS != null">#{JIFEIKS,jdbcType=VARCHAR},</if>
                <if test="BAOGAODZ != null">#{BAOGAODZ,jdbcType=VARCHAR},</if>
                <if test="DUOCHONGNYBZ != null">#{DUOCHONGNYBZ,jdbcType=DECIMAL},</if>
                <if test="BAOGAOCKBZ != null">#{BAOGAOCKBZ,jdbcType=DECIMAL},</if>
                <if test="BAOGAOCKSJ != null">#{BAOGAOCKSJ,jdbcType=TIMESTAMP},</if>
                <if test="BAOGAOCKR != null">#{BAOGAOCKR,jdbcType=VARCHAR},</if>
                <if test="HUSONGFS != null">#{HUSONGFS,jdbcType=VARCHAR},</if>
                <if test="ZUTUO != null">#{ZUTUO,jdbcType=VARCHAR},</if>
                <if test="SONGJIANSJ != null">#{SONGJIANSJ,jdbcType=TIMESTAMP},</if>
                <if test="SONGJIANYS != null">#{SONGJIANYS,jdbcType=VARCHAR},</if>
                <if test="SQDH != null">#{SQDH,jdbcType=VARCHAR},</if>
                <if test="BINGRENCKBGBZ != null">#{BINGRENCKBGBZ,jdbcType=DECIMAL},</if>
                <if test="BINGRENCKBGSJ != null">#{BINGRENCKBGSJ,jdbcType=TIMESTAMP},</if>
                <if test="JIFEIMS != null">#{JIFEIMS,jdbcType=DECIMAL},</if>
                <if test="ZHIQINGTYSBLJLXH != null">#{ZHIQINGTYSBLJLXH,jdbcType=DECIMAL},</if>
                <if test="YIZHUXMFLID != null">#{YIZHUXMFLID,jdbcType=VARCHAR},</if>
                <if test="YUANQIANBZ != null">#{YUANQIANBZ,jdbcType=DECIMAL},</if>
                <if test="WEIJIBZ != null">#{WEIJIBZ,jdbcType=VARCHAR},</if>
                <if test="GCPBZ != null">#{GCPBZ,jdbcType=DECIMAL},</if>
                <if test="YUYUEBZ != null">#{YUYUEBZ,jdbcType=DECIMAL},</if>
                <if test="SHENQINGDJKID != null">#{SHENQINGDJKID,jdbcType=VARCHAR},</if>
                <if test="BINGLIZT != null">#{BINGLIZT,jdbcType=VARCHAR},</if>
                <if test="HULIANWSQDID != null">#{HULIANWSQDID,jdbcType=VARCHAR},</if>
                <if test="ZHONGXYYBZ != null">#{ZHONGXYYBZ,jdbcType=DECIMAL},</if>
                <if test="ZUHAO != null">#{ZUHAO,jdbcType=VARCHAR},</if>
                <if test="JIZHENYY != null">#{JIZHENYY,jdbcType=VARCHAR},</if>
                <if test="BINGLIQXJFBZ != null">#{BINGLIQXJFBZ,jdbcType=DECIMAL},</if>
                <if test="TONGLEIYZID != null">#{TONGLEIYZID,jdbcType=VARCHAR},</if>
                <if test="ZENGQIANGSMBZ != null">#{ZENGQIANGSMBZ,jdbcType=DECIMAL},</if>
                <if test="YUNYXBZ != null">#{YUNYXBZ,jdbcType=DECIMAL},</if>
                <if test="ORDERID != null">#{ORDERID,jdbcType=VARCHAR},</if>
                <if test="GUTISJ != null">#{GUTISJ,jdbcType=TIMESTAMP},</if>
                <if test="LITISJ != null">#{LITISJ,jdbcType=TIMESTAMP},</if>
                <if test="BINGRENHAO != null">#{BINGRENHAO,jdbcType=VARCHAR},</if>
                <if test="SHENHERQ != null">#{SHENHERQ,jdbcType=TIMESTAMP},</if>
                <if test="HOSPITAL_NAME != null">#{HOSPITAL_NAME,jdbcType=VARCHAR},</if>
                <if test="BAOGAOBZ != null">#{BAOGAOBZ,jdbcType=VARCHAR},</if>
                <if test="FUJIAXMSQD != null">#{FUJIAXMSQD,jdbcType=VARCHAR},</if>
                <if test="YUYUESJ != null">#{YUYUESJ,jdbcType=VARCHAR},</if>
                <if test="GUANLIANSQDID != null">#{GUANLIANSQDID,jdbcType=VARCHAR},</if>
                <if test="FANGJIANHAO != null">#{FANGJIANHAO,jdbcType=VARCHAR},</if>
                <if test="KAIDANYQID != null">#{KAIDANYQID,jdbcType=VARCHAR},</if>
                <if test="EXINGZLBZ != null">#{EXINGZLBZ,jdbcType=DECIMAL},</if>
                <if test="YOUHUILB != null">#{YOUHUILB,jdbcType=VARCHAR},</if>
                <if test="BUWEISL != null">#{BUWEISL,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.YjShenqingdan">
        update YJ_SHENQINGDAN
        <set>
                <if test="YINGYONGID != null">
                    YINGYONGID = #{YINGYONGID,jdbcType=VARCHAR},
                </if>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=VARCHAR},
                </if>
                <if test="MOBANDM != null">
                    MOBANDM = #{MOBANDM,jdbcType=VARCHAR},
                </if>
                <if test="BAOGAODMBDM != null">
                    BAOGAODMBDM = #{BAOGAODMBDM,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUID != null">
                    YIZHUID = #{YIZHUID,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUXMID != null">
                    YIZHUXMID = #{YIZHUXMID,jdbcType=VARCHAR},
                </if>
                <if test="YIZHUMC != null">
                    YIZHUMC = #{YIZHUMC,jdbcType=VARCHAR},
                </if>
                <if test="JIUZHENID != null">
                    JIUZHENID = #{JIUZHENID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="YINGERID != null">
                    YINGERID = #{YINGERID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENXM != null">
                    BINGRENXM = #{BINGRENXM,jdbcType=VARCHAR},
                </if>
                <if test="XINGBIE != null">
                    XINGBIE = #{XINGBIE,jdbcType=VARCHAR},
                </if>
                <if test="NIANLING != null">
                    NIANLING = #{NIANLING,jdbcType=DECIMAL},
                </if>
                <if test="NIANLINGDW != null">
                    NIANLINGDW = #{NIANLINGDW,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGRQ != null">
                    CHUSHENGRQ = #{CHUSHENGRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHURUREN != null">
                    SHURUREN = #{SHURUREN,jdbcType=VARCHAR},
                </if>
                <if test="SHURUSJ != null">
                    SHURUSJ = #{SHURUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="KAIDANREN != null">
                    KAIDANREN = #{KAIDANREN,jdbcType=VARCHAR},
                </if>
                <if test="KAIDANKS != null">
                    KAIDANKS = #{KAIDANKS,jdbcType=VARCHAR},
                </if>
                <if test="KAIDANRQ != null">
                    KAIDANRQ = #{KAIDANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIANCHAKS != null">
                    JIANCHAKS = #{JIANCHAKS,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAREN != null">
                    JIANCHAREN = #{JIANCHAREN,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHARQ != null">
                    JIANCHARQ = #{JIANCHARQ,jdbcType=TIMESTAMP},
                </if>
                <if test="MENZHENZYBZ != null">
                    MENZHENZYBZ = #{MENZHENZYBZ,jdbcType=DECIMAL},
                </if>
                <if test="DANGQIANZT != null">
                    DANGQIANZT = #{DANGQIANZT,jdbcType=VARCHAR},
                </if>
                <if test="QIWANGJCRQ != null">
                    QIWANGJCRQ = #{QIWANGJCRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUYUESQR != null">
                    YUYUESQR = #{YUYUESQR,jdbcType=VARCHAR},
                </if>
                <if test="YUYUESQRQ != null">
                    YUYUESQRQ = #{YUYUESQRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUYUEAPKS != null">
                    YUYUEAPKS = #{YUYUEAPKS,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEQRRQ != null">
                    YUYUEQRRQ = #{YUYUEQRRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUYUEQRR != null">
                    YUYUEQRR = #{YUYUEQRR,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAAPRQ != null">
                    JIANCHAAPRQ = #{JIANCHAAPRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIANCHAAPR != null">
                    JIANCHAAPR = #{JIANCHAAPR,jdbcType=VARCHAR},
                </if>
                <if test="BAOGAODFS != null">
                    BAOGAODFS = #{BAOGAODFS,jdbcType=DECIMAL},
                </if>
                <if test="DAYINREN != null">
                    DAYINREN = #{DAYINREN,jdbcType=VARCHAR},
                </if>
                <if test="DAYINRQ != null">
                    DAYINRQ = #{DAYINRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YOUXIANJI != null">
                    YOUXIANJI = #{YOUXIANJI,jdbcType=VARCHAR},
                </if>
                <if test="SHOUFEIBZ != null">
                    SHOUFEIBZ = #{SHOUFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHEXIAOREN != null">
                    CHEXIAOREN = #{CHEXIAOREN,jdbcType=VARCHAR},
                </if>
                <if test="CHEXIAOSJ != null">
                    CHEXIAOSJ = #{CHEXIAOSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHUSU != null">
                    ZHUSU = #{ZHUSU,jdbcType=VARCHAR},
                </if>
                <if test="JIANYAOBS != null">
                    JIANYAOBS = #{JIANYAOBS,jdbcType=VARCHAR},
                </if>
                <if test="TIGEJC != null">
                    TIGEJC = #{TIGEJC,jdbcType=VARCHAR},
                </if>
                <if test="LINCHUANGZD != null">
                    LINCHUANGZD = #{LINCHUANGZD,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHABW != null">
                    JIANCHABW = #{JIANCHABW,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAMD != null">
                    JIANCHAMD = #{JIANCHAMD,jdbcType=VARCHAR},
                </if>
                <if test="XIANGGUANJC != null">
                    XIANGGUANJC = #{XIANGGUANJC,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHALX != null">
                    JIANCHALX = #{JIANCHALX,jdbcType=VARCHAR},
                </if>
                <if test="TUIDANREN != null">
                    TUIDANREN = #{TUIDANREN,jdbcType=VARCHAR},
                </if>
                <if test="TUIDANRQ != null">
                    TUIDANRQ = #{TUIDANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="TUIDANRXM != null">
                    TUIDANRXM = #{TUIDANRXM,jdbcType=VARCHAR},
                </if>
                <if test="TUIDANYY != null">
                    TUIDANYY = #{TUIDANYY,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGHJ != null">
                    FEIYONGHJ = #{FEIYONGHJ,jdbcType=DECIMAL},
                </if>
                <if test="YISHOUJE != null">
                    YISHOUJE = #{YISHOUJE,jdbcType=DECIMAL},
                </if>
                <if test="KEZHIXBZ != null">
                    KEZHIXBZ = #{KEZHIXBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUYUEQRRQ2 != null">
                    YUYUEQRRQ2 = #{YUYUEQRRQ2,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEHAO != null">
                    YUYUEHAO = #{YUYUEHAO,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGBIANBZ != null">
                    CHUANGBIANBZ = #{CHUANGBIANBZ,jdbcType=DECIMAL},
                </if>
                <if test="TESHUSM != null">
                    TESHUSM = #{TESHUSM,jdbcType=VARCHAR},
                </if>
                <if test="BEIZHU != null">
                    BEIZHU = #{BEIZHU,jdbcType=VARCHAR},
                </if>
                <if test="RISDJHM != null">
                    RISDJHM = #{RISDJHM,jdbcType=VARCHAR},
                </if>
                <if test="ZIFEIBZ != null">
                    ZIFEIBZ = #{ZIFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUYUEDYBZ != null">
                    YUYUEDYBZ = #{YUYUEDYBZ,jdbcType=DECIMAL},
                </if>
                <if test="YAOLIJDMDBZ != null">
                    YAOLIJDMDBZ = #{YAOLIJDMDBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUYISX != null">
                    ZHUYISX = #{ZHUYISX,jdbcType=VARCHAR},
                </if>
                <if test="JIZHENBZ != null">
                    JIZHENBZ = #{JIZHENBZ,jdbcType=DECIMAL},
                </if>
                <if test="TESHUBZMC != null">
                    TESHUBZMC = #{TESHUBZMC,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHASJ != null">
                    JIANCHASJ = #{JIANCHASJ,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANJG != null">
                    ZHENDUANJG = #{ZHENDUANJG,jdbcType=VARCHAR},
                </if>
                <if test="WEIJIZBZ != null">
                    WEIJIZBZ = #{WEIJIZBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIAOPIANFBZ != null">
                    JIAOPIANFBZ = #{JIAOPIANFBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHENDUANLX != null">
                    ZHENDUANLX = #{ZHENDUANLX,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANYJ != null">
                    ZHENDUANYJ = #{ZHENDUANYJ,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSUOYA != null">
                    SHOUSUOYA = #{SHOUSUOYA,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHANGYA != null">
                    SHUZHANGYA = #{SHUZHANGYA,jdbcType=DECIMAL},
                </if>
                <if test="ZIFU1 != null">
                    ZIFU1 = #{ZIFU1,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU2 != null">
                    ZIFU2 = #{ZIFU2,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU3 != null">
                    ZIFU3 = #{ZIFU3,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU4 != null">
                    ZIFU4 = #{ZIFU4,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU5 != null">
                    ZIFU5 = #{ZIFU5,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU6 != null">
                    ZIFU6 = #{ZIFU6,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU7 != null">
                    ZIFU7 = #{ZIFU7,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU8 != null">
                    ZIFU8 = #{ZIFU8,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU9 != null">
                    ZIFU9 = #{ZIFU9,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU10 != null">
                    ZIFU10 = #{ZIFU10,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU11 != null">
                    ZIFU11 = #{ZIFU11,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU12 != null">
                    ZIFU12 = #{ZIFU12,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU13 != null">
                    ZIFU13 = #{ZIFU13,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU14 != null">
                    ZIFU14 = #{ZIFU14,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU15 != null">
                    ZIFU15 = #{ZIFU15,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU16 != null">
                    ZIFU16 = #{ZIFU16,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU17 != null">
                    ZIFU17 = #{ZIFU17,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU18 != null">
                    ZIFU18 = #{ZIFU18,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU19 != null">
                    ZIFU19 = #{ZIFU19,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU20 != null">
                    ZIFU20 = #{ZIFU20,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU21 != null">
                    ZIFU21 = #{ZIFU21,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU22 != null">
                    ZIFU22 = #{ZIFU22,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU23 != null">
                    ZIFU23 = #{ZIFU23,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU24 != null">
                    ZIFU24 = #{ZIFU24,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU25 != null">
                    ZIFU25 = #{ZIFU25,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU26 != null">
                    ZIFU26 = #{ZIFU26,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU27 != null">
                    ZIFU27 = #{ZIFU27,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU28 != null">
                    ZIFU28 = #{ZIFU28,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU29 != null">
                    ZIFU29 = #{ZIFU29,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU30 != null">
                    ZIFU30 = #{ZIFU30,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU31 != null">
                    ZIFU31 = #{ZIFU31,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU32 != null">
                    ZIFU32 = #{ZIFU32,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU33 != null">
                    ZIFU33 = #{ZIFU33,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU34 != null">
                    ZIFU34 = #{ZIFU34,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU35 != null">
                    ZIFU35 = #{ZIFU35,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU36 != null">
                    ZIFU36 = #{ZIFU36,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU37 != null">
                    ZIFU37 = #{ZIFU37,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU38 != null">
                    ZIFU38 = #{ZIFU38,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU39 != null">
                    ZIFU39 = #{ZIFU39,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU40 != null">
                    ZIFU40 = #{ZIFU40,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU41 != null">
                    ZIFU41 = #{ZIFU41,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU42 != null">
                    ZIFU42 = #{ZIFU42,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU43 != null">
                    ZIFU43 = #{ZIFU43,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU44 != null">
                    ZIFU44 = #{ZIFU44,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU45 != null">
                    ZIFU45 = #{ZIFU45,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU46 != null">
                    ZIFU46 = #{ZIFU46,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU47 != null">
                    ZIFU47 = #{ZIFU47,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU48 != null">
                    ZIFU48 = #{ZIFU48,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU49 != null">
                    ZIFU49 = #{ZIFU49,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU50 != null">
                    ZIFU50 = #{ZIFU50,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU51 != null">
                    ZIFU51 = #{ZIFU51,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU52 != null">
                    ZIFU52 = #{ZIFU52,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU53 != null">
                    ZIFU53 = #{ZIFU53,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU54 != null">
                    ZIFU54 = #{ZIFU54,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU55 != null">
                    ZIFU55 = #{ZIFU55,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU56 != null">
                    ZIFU56 = #{ZIFU56,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU57 != null">
                    ZIFU57 = #{ZIFU57,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU58 != null">
                    ZIFU58 = #{ZIFU58,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU59 != null">
                    ZIFU59 = #{ZIFU59,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU60 != null">
                    ZIFU60 = #{ZIFU60,jdbcType=VARCHAR},
                </if>
                <if test="SHENHERXM != null">
                    SHENHERXM = #{SHENHERXM,jdbcType=VARCHAR},
                </if>
                <if test="BAOGAORXM != null">
                    BAOGAORXM = #{BAOGAORXM,jdbcType=VARCHAR},
                </if>
                <if test="BAOGAORQ != null">
                    BAOGAORQ = #{BAOGAORQ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIANCHAHAO != null">
                    JIANCHAHAO = #{JIANCHAHAO,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAJF != null">
                    JIANCHAJF = #{JIANCHAJF,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEZYSX != null">
                    YUYUEZYSX = #{YUYUEZYSX,jdbcType=VARCHAR},
                </if>
                <if test="TUWENBGFBZ != null">
                    TUWENBGFBZ = #{TUWENBGFBZ,jdbcType=DECIMAL},
                </if>
                <if test="WEISHOUXZXBZ != null">
                    WEISHOUXZXBZ = #{WEISHOUXZXBZ,jdbcType=DECIMAL},
                </if>
                <if test="YICHAKBG != null">
                    YICHAKBG = #{YICHAKBG,jdbcType=VARCHAR},
                </if>
                <if test="TAOCANXSBRMXID != null">
                    TAOCANXSBRMXID = #{TAOCANXSBRMXID,jdbcType=VARCHAR},
                </if>
                <if test="TAOCANXSBZ != null">
                    TAOCANXSBZ = #{TAOCANXSBZ,jdbcType=DECIMAL},
                </if>
                <if test="DIANZIBLSY1 != null">
                    DIANZIBLSY1 = #{DIANZIBLSY1,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY2 != null">
                    DIANZIBLSY2 = #{DIANZIBLSY2,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY3 != null">
                    DIANZIBLSY3 = #{DIANZIBLSY3,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY4 != null">
                    DIANZIBLSY4 = #{DIANZIBLSY4,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY5 != null">
                    DIANZIBLSY5 = #{DIANZIBLSY5,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY6 != null">
                    DIANZIBLSY6 = #{DIANZIBLSY6,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY7 != null">
                    DIANZIBLSY7 = #{DIANZIBLSY7,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY8 != null">
                    DIANZIBLSY8 = #{DIANZIBLSY8,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY9 != null">
                    DIANZIBLSY9 = #{DIANZIBLSY9,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY10 != null">
                    DIANZIBLSY10 = #{DIANZIBLSY10,jdbcType=VARCHAR},
                </if>
                <if test="FANGWENHAO != null">
                    FANGWENHAO = #{FANGWENHAO,jdbcType=VARCHAR},
                </if>
                <if test="YINGXIANGHAO != null">
                    YINGXIANGHAO = #{YINGXIANGHAO,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYS != null">
                    ZHUZHIYS = #{ZHUZHIYS,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENYLZ != null">
                    BINGRENYLZ = #{BINGRENYLZ,jdbcType=VARCHAR},
                </if>
                <if test="YISHENGZID != null">
                    YISHENGZID = #{YISHENGZID,jdbcType=VARCHAR},
                </if>
                <if test="JIFEIBZ != null">
                    JIFEIBZ = #{JIFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIFEIREN != null">
                    JIFEIREN = #{JIFEIREN,jdbcType=VARCHAR},
                </if>
                <if test="JIFEISJ != null">
                    JIFEISJ = #{JIFEISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIFEIKS != null">
                    JIFEIKS = #{JIFEIKS,jdbcType=VARCHAR},
                </if>
                <if test="BAOGAODZ != null">
                    BAOGAODZ = #{BAOGAODZ,jdbcType=VARCHAR},
                </if>
                <if test="DUOCHONGNYBZ != null">
                    DUOCHONGNYBZ = #{DUOCHONGNYBZ,jdbcType=DECIMAL},
                </if>
                <if test="BAOGAOCKBZ != null">
                    BAOGAOCKBZ = #{BAOGAOCKBZ,jdbcType=DECIMAL},
                </if>
                <if test="BAOGAOCKSJ != null">
                    BAOGAOCKSJ = #{BAOGAOCKSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="BAOGAOCKR != null">
                    BAOGAOCKR = #{BAOGAOCKR,jdbcType=VARCHAR},
                </if>
                <if test="HUSONGFS != null">
                    HUSONGFS = #{HUSONGFS,jdbcType=VARCHAR},
                </if>
                <if test="ZUTUO != null">
                    ZUTUO = #{ZUTUO,jdbcType=VARCHAR},
                </if>
                <if test="SONGJIANSJ != null">
                    SONGJIANSJ = #{SONGJIANSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SONGJIANYS != null">
                    SONGJIANYS = #{SONGJIANYS,jdbcType=VARCHAR},
                </if>
                <if test="SQDH != null">
                    SQDH = #{SQDH,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENCKBGBZ != null">
                    BINGRENCKBGBZ = #{BINGRENCKBGBZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGRENCKBGSJ != null">
                    BINGRENCKBGSJ = #{BINGRENCKBGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIFEIMS != null">
                    JIFEIMS = #{JIFEIMS,jdbcType=DECIMAL},
                </if>
                <if test="ZHIQINGTYSBLJLXH != null">
                    ZHIQINGTYSBLJLXH = #{ZHIQINGTYSBLJLXH,jdbcType=DECIMAL},
                </if>
                <if test="YIZHUXMFLID != null">
                    YIZHUXMFLID = #{YIZHUXMFLID,jdbcType=VARCHAR},
                </if>
                <if test="YUANQIANBZ != null">
                    YUANQIANBZ = #{YUANQIANBZ,jdbcType=DECIMAL},
                </if>
                <if test="WEIJIBZ != null">
                    WEIJIBZ = #{WEIJIBZ,jdbcType=VARCHAR},
                </if>
                <if test="GCPBZ != null">
                    GCPBZ = #{GCPBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUYUEBZ != null">
                    YUYUEBZ = #{YUYUEBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENQINGDJKID != null">
                    SHENQINGDJKID = #{SHENQINGDJKID,jdbcType=VARCHAR},
                </if>
                <if test="BINGLIZT != null">
                    BINGLIZT = #{BINGLIZT,jdbcType=VARCHAR},
                </if>
                <if test="HULIANWSQDID != null">
                    HULIANWSQDID = #{HULIANWSQDID,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGXYYBZ != null">
                    ZHONGXYYBZ = #{ZHONGXYYBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZUHAO != null">
                    ZUHAO = #{ZUHAO,jdbcType=VARCHAR},
                </if>
                <if test="JIZHENYY != null">
                    JIZHENYY = #{JIZHENYY,jdbcType=VARCHAR},
                </if>
                <if test="BINGLIQXJFBZ != null">
                    BINGLIQXJFBZ = #{BINGLIQXJFBZ,jdbcType=DECIMAL},
                </if>
                <if test="TONGLEIYZID != null">
                    TONGLEIYZID = #{TONGLEIYZID,jdbcType=VARCHAR},
                </if>
                <if test="ZENGQIANGSMBZ != null">
                    ZENGQIANGSMBZ = #{ZENGQIANGSMBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUNYXBZ != null">
                    YUNYXBZ = #{YUNYXBZ,jdbcType=DECIMAL},
                </if>
                <if test="ORDERID != null">
                    ORDERID = #{ORDERID,jdbcType=VARCHAR},
                </if>
                <if test="GUTISJ != null">
                    GUTISJ = #{GUTISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="LITISJ != null">
                    LITISJ = #{LITISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="BINGRENHAO != null">
                    BINGRENHAO = #{BINGRENHAO,jdbcType=VARCHAR},
                </if>
                <if test="SHENHERQ != null">
                    SHENHERQ = #{SHENHERQ,jdbcType=TIMESTAMP},
                </if>
                <if test="HOSPITAL_NAME != null">
                    HOSPITAL_NAME = #{HOSPITAL_NAME,jdbcType=VARCHAR},
                </if>
                <if test="BAOGAOBZ != null">
                    BAOGAOBZ = #{BAOGAOBZ,jdbcType=VARCHAR},
                </if>
                <if test="FUJIAXMSQD != null">
                    FUJIAXMSQD = #{FUJIAXMSQD,jdbcType=VARCHAR},
                </if>
                <if test="YUYUESJ != null">
                    YUYUESJ = #{YUYUESJ,jdbcType=VARCHAR},
                </if>
                <if test="GUANLIANSQDID != null">
                    GUANLIANSQDID = #{GUANLIANSQDID,jdbcType=VARCHAR},
                </if>
                <if test="FANGJIANHAO != null">
                    FANGJIANHAO = #{FANGJIANHAO,jdbcType=VARCHAR},
                </if>
                <if test="KAIDANYQID != null">
                    KAIDANYQID = #{KAIDANYQID,jdbcType=VARCHAR},
                </if>
                <if test="EXINGZLBZ != null">
                    EXINGZLBZ = #{EXINGZLBZ,jdbcType=DECIMAL},
                </if>
                <if test="YOUHUILB != null">
                    YOUHUILB = #{YOUHUILB,jdbcType=VARCHAR},
                </if>
                <if test="BUWEISL != null">
                    BUWEISL = #{BUWEISL,jdbcType=DECIMAL},
                </if>
        </set>
        where   SHENQINDANID = #{SHENQINDANID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.YjShenqingdan">
        update YJ_SHENQINGDAN
        set 
            YINGYONGID =  #{YINGYONGID,jdbcType=VARCHAR},
            YUANQUID =  #{YUANQUID,jdbcType=VARCHAR},
            MOBANDM =  #{MOBANDM,jdbcType=VARCHAR},
            BAOGAODMBDM =  #{BAOGAODMBDM,jdbcType=VARCHAR},
            YIZHUID =  #{YIZHUID,jdbcType=VARCHAR},
            YIZHUXMID =  #{YIZHUXMID,jdbcType=VARCHAR},
            YIZHUMC =  #{YIZHUMC,jdbcType=VARCHAR},
            JIUZHENID =  #{JIUZHENID,jdbcType=VARCHAR},
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            YINGERID =  #{YINGERID,jdbcType=VARCHAR},
            BINGRENXM =  #{BINGRENXM,jdbcType=VARCHAR},
            XINGBIE =  #{XINGBIE,jdbcType=VARCHAR},
            NIANLING =  #{NIANLING,jdbcType=DECIMAL},
            NIANLINGDW =  #{NIANLINGDW,jdbcType=VARCHAR},
            CHUSHENGRQ =  #{CHUSHENGRQ,jdbcType=TIMESTAMP},
            SHURUREN =  #{SHURUREN,jdbcType=VARCHAR},
            SHURUSJ =  #{SHURUSJ,jdbcType=TIMESTAMP},
            KAIDANREN =  #{KAIDANREN,jdbcType=VARCHAR},
            KAIDANKS =  #{KAIDANKS,jdbcType=VARCHAR},
            KAIDANRQ =  #{KAIDANRQ,jdbcType=TIMESTAMP},
            JIANCHAKS =  #{JIANCHAKS,jdbcType=VARCHAR},
            JIANCHAREN =  #{JIANCHAREN,jdbcType=VARCHAR},
            JIANCHARQ =  #{JIANCHARQ,jdbcType=TIMESTAMP},
            MENZHENZYBZ =  #{MENZHENZYBZ,jdbcType=DECIMAL},
            DANGQIANZT =  #{DANGQIANZT,jdbcType=VARCHAR},
            QIWANGJCRQ =  #{QIWANGJCRQ,jdbcType=TIMESTAMP},
            YUYUESQR =  #{YUYUESQR,jdbcType=VARCHAR},
            YUYUESQRQ =  #{YUYUESQRQ,jdbcType=TIMESTAMP},
            YUYUEAPKS =  #{YUYUEAPKS,jdbcType=VARCHAR},
            YUYUEQRRQ =  #{YUYUEQRRQ,jdbcType=TIMESTAMP},
            YUYUEQRR =  #{YUYUEQRR,jdbcType=VARCHAR},
            JIANCHAAPRQ =  #{JIANCHAAPRQ,jdbcType=TIMESTAMP},
            JIANCHAAPR =  #{JIANCHAAPR,jdbcType=VARCHAR},
            BAOGAODFS =  #{BAOGAODFS,jdbcType=DECIMAL},
            DAYINREN =  #{DAYINREN,jdbcType=VARCHAR},
            DAYINRQ =  #{DAYINRQ,jdbcType=TIMESTAMP},
            YOUXIANJI =  #{YOUXIANJI,jdbcType=VARCHAR},
            SHOUFEIBZ =  #{SHOUFEIBZ,jdbcType=DECIMAL},
            CHEXIAOREN =  #{CHEXIAOREN,jdbcType=VARCHAR},
            CHEXIAOSJ =  #{CHEXIAOSJ,jdbcType=TIMESTAMP},
            ZHUSU =  #{ZHUSU,jdbcType=VARCHAR},
            JIANYAOBS =  #{JIANYAOBS,jdbcType=VARCHAR},
            TIGEJC =  #{TIGEJC,jdbcType=VARCHAR},
            LINCHUANGZD =  #{LINCHUANGZD,jdbcType=VARCHAR},
            JIANCHABW =  #{JIANCHABW,jdbcType=VARCHAR},
            JIANCHAMD =  #{JIANCHAMD,jdbcType=VARCHAR},
            XIANGGUANJC =  #{XIANGGUANJC,jdbcType=VARCHAR},
            JIANCHALX =  #{JIANCHALX,jdbcType=VARCHAR},
            TUIDANREN =  #{TUIDANREN,jdbcType=VARCHAR},
            TUIDANRQ =  #{TUIDANRQ,jdbcType=TIMESTAMP},
            TUIDANRXM =  #{TUIDANRXM,jdbcType=VARCHAR},
            TUIDANYY =  #{TUIDANYY,jdbcType=VARCHAR},
            FEIYONGHJ =  #{FEIYONGHJ,jdbcType=DECIMAL},
            YISHOUJE =  #{YISHOUJE,jdbcType=DECIMAL},
            KEZHIXBZ =  #{KEZHIXBZ,jdbcType=DECIMAL},
            YUYUEQRRQ2 =  #{YUYUEQRRQ2,jdbcType=VARCHAR},
            YUYUEHAO =  #{YUYUEHAO,jdbcType=VARCHAR},
            CHUANGBIANBZ =  #{CHUANGBIANBZ,jdbcType=DECIMAL},
            TESHUSM =  #{TESHUSM,jdbcType=VARCHAR},
            BEIZHU =  #{BEIZHU,jdbcType=VARCHAR},
            RISDJHM =  #{RISDJHM,jdbcType=VARCHAR},
            ZIFEIBZ =  #{ZIFEIBZ,jdbcType=DECIMAL},
            YUYUEDYBZ =  #{YUYUEDYBZ,jdbcType=DECIMAL},
            YAOLIJDMDBZ =  #{YAOLIJDMDBZ,jdbcType=DECIMAL},
            ZHUYISX =  #{ZHUYISX,jdbcType=VARCHAR},
            JIZHENBZ =  #{JIZHENBZ,jdbcType=DECIMAL},
            TESHUBZMC =  #{TESHUBZMC,jdbcType=VARCHAR},
            JIANCHASJ =  #{JIANCHASJ,jdbcType=VARCHAR},
            ZHENDUANJG =  #{ZHENDUANJG,jdbcType=VARCHAR},
            WEIJIZBZ =  #{WEIJIZBZ,jdbcType=DECIMAL},
            JIAOPIANFBZ =  #{JIAOPIANFBZ,jdbcType=DECIMAL},
            ZHENDUANLX =  #{ZHENDUANLX,jdbcType=VARCHAR},
            ZHENDUANYJ =  #{ZHENDUANYJ,jdbcType=VARCHAR},
            SHOUSUOYA =  #{SHOUSUOYA,jdbcType=DECIMAL},
            SHUZHANGYA =  #{SHUZHANGYA,jdbcType=DECIMAL},
            ZIFU1 =  #{ZIFU1,jdbcType=VARCHAR},
            ZIFU2 =  #{ZIFU2,jdbcType=VARCHAR},
            ZIFU3 =  #{ZIFU3,jdbcType=VARCHAR},
            ZIFU4 =  #{ZIFU4,jdbcType=VARCHAR},
            ZIFU5 =  #{ZIFU5,jdbcType=VARCHAR},
            ZIFU6 =  #{ZIFU6,jdbcType=VARCHAR},
            ZIFU7 =  #{ZIFU7,jdbcType=VARCHAR},
            ZIFU8 =  #{ZIFU8,jdbcType=VARCHAR},
            ZIFU9 =  #{ZIFU9,jdbcType=VARCHAR},
            ZIFU10 =  #{ZIFU10,jdbcType=VARCHAR},
            ZIFU11 =  #{ZIFU11,jdbcType=VARCHAR},
            ZIFU12 =  #{ZIFU12,jdbcType=VARCHAR},
            ZIFU13 =  #{ZIFU13,jdbcType=VARCHAR},
            ZIFU14 =  #{ZIFU14,jdbcType=VARCHAR},
            ZIFU15 =  #{ZIFU15,jdbcType=VARCHAR},
            ZIFU16 =  #{ZIFU16,jdbcType=VARCHAR},
            ZIFU17 =  #{ZIFU17,jdbcType=VARCHAR},
            ZIFU18 =  #{ZIFU18,jdbcType=VARCHAR},
            ZIFU19 =  #{ZIFU19,jdbcType=VARCHAR},
            ZIFU20 =  #{ZIFU20,jdbcType=VARCHAR},
            ZIFU21 =  #{ZIFU21,jdbcType=VARCHAR},
            ZIFU22 =  #{ZIFU22,jdbcType=VARCHAR},
            ZIFU23 =  #{ZIFU23,jdbcType=VARCHAR},
            ZIFU24 =  #{ZIFU24,jdbcType=VARCHAR},
            ZIFU25 =  #{ZIFU25,jdbcType=VARCHAR},
            ZIFU26 =  #{ZIFU26,jdbcType=VARCHAR},
            ZIFU27 =  #{ZIFU27,jdbcType=VARCHAR},
            ZIFU28 =  #{ZIFU28,jdbcType=VARCHAR},
            ZIFU29 =  #{ZIFU29,jdbcType=VARCHAR},
            ZIFU30 =  #{ZIFU30,jdbcType=VARCHAR},
            ZIFU31 =  #{ZIFU31,jdbcType=VARCHAR},
            ZIFU32 =  #{ZIFU32,jdbcType=VARCHAR},
            ZIFU33 =  #{ZIFU33,jdbcType=VARCHAR},
            ZIFU34 =  #{ZIFU34,jdbcType=VARCHAR},
            ZIFU35 =  #{ZIFU35,jdbcType=VARCHAR},
            ZIFU36 =  #{ZIFU36,jdbcType=VARCHAR},
            ZIFU37 =  #{ZIFU37,jdbcType=VARCHAR},
            ZIFU38 =  #{ZIFU38,jdbcType=VARCHAR},
            ZIFU39 =  #{ZIFU39,jdbcType=VARCHAR},
            ZIFU40 =  #{ZIFU40,jdbcType=VARCHAR},
            ZIFU41 =  #{ZIFU41,jdbcType=VARCHAR},
            ZIFU42 =  #{ZIFU42,jdbcType=VARCHAR},
            ZIFU43 =  #{ZIFU43,jdbcType=VARCHAR},
            ZIFU44 =  #{ZIFU44,jdbcType=VARCHAR},
            ZIFU45 =  #{ZIFU45,jdbcType=VARCHAR},
            ZIFU46 =  #{ZIFU46,jdbcType=VARCHAR},
            ZIFU47 =  #{ZIFU47,jdbcType=VARCHAR},
            ZIFU48 =  #{ZIFU48,jdbcType=VARCHAR},
            ZIFU49 =  #{ZIFU49,jdbcType=VARCHAR},
            ZIFU50 =  #{ZIFU50,jdbcType=VARCHAR},
            ZIFU51 =  #{ZIFU51,jdbcType=VARCHAR},
            ZIFU52 =  #{ZIFU52,jdbcType=VARCHAR},
            ZIFU53 =  #{ZIFU53,jdbcType=VARCHAR},
            ZIFU54 =  #{ZIFU54,jdbcType=VARCHAR},
            ZIFU55 =  #{ZIFU55,jdbcType=VARCHAR},
            ZIFU56 =  #{ZIFU56,jdbcType=VARCHAR},
            ZIFU57 =  #{ZIFU57,jdbcType=VARCHAR},
            ZIFU58 =  #{ZIFU58,jdbcType=VARCHAR},
            ZIFU59 =  #{ZIFU59,jdbcType=VARCHAR},
            ZIFU60 =  #{ZIFU60,jdbcType=VARCHAR},
            SHENHERXM =  #{SHENHERXM,jdbcType=VARCHAR},
            BAOGAORXM =  #{BAOGAORXM,jdbcType=VARCHAR},
            BAOGAORQ =  #{BAOGAORQ,jdbcType=TIMESTAMP},
            JIANCHAHAO =  #{JIANCHAHAO,jdbcType=VARCHAR},
            JIANCHAJF =  #{JIANCHAJF,jdbcType=VARCHAR},
            YUYUEZYSX =  #{YUYUEZYSX,jdbcType=VARCHAR},
            TUWENBGFBZ =  #{TUWENBGFBZ,jdbcType=DECIMAL},
            WEISHOUXZXBZ =  #{WEISHOUXZXBZ,jdbcType=DECIMAL},
            YICHAKBG =  #{YICHAKBG,jdbcType=VARCHAR},
            TAOCANXSBRMXID =  #{TAOCANXSBRMXID,jdbcType=VARCHAR},
            TAOCANXSBZ =  #{TAOCANXSBZ,jdbcType=DECIMAL},
            DIANZIBLSY1 =  #{DIANZIBLSY1,jdbcType=VARCHAR},
            DIANZIBLSY2 =  #{DIANZIBLSY2,jdbcType=VARCHAR},
            DIANZIBLSY3 =  #{DIANZIBLSY3,jdbcType=VARCHAR},
            DIANZIBLSY4 =  #{DIANZIBLSY4,jdbcType=VARCHAR},
            DIANZIBLSY5 =  #{DIANZIBLSY5,jdbcType=VARCHAR},
            DIANZIBLSY6 =  #{DIANZIBLSY6,jdbcType=VARCHAR},
            DIANZIBLSY7 =  #{DIANZIBLSY7,jdbcType=VARCHAR},
            DIANZIBLSY8 =  #{DIANZIBLSY8,jdbcType=VARCHAR},
            DIANZIBLSY9 =  #{DIANZIBLSY9,jdbcType=VARCHAR},
            DIANZIBLSY10 =  #{DIANZIBLSY10,jdbcType=VARCHAR},
            FANGWENHAO =  #{FANGWENHAO,jdbcType=VARCHAR},
            YINGXIANGHAO =  #{YINGXIANGHAO,jdbcType=VARCHAR},
            ZHUZHIYS =  #{ZHUZHIYS,jdbcType=VARCHAR},
            BINGRENYLZ =  #{BINGRENYLZ,jdbcType=VARCHAR},
            YISHENGZID =  #{YISHENGZID,jdbcType=VARCHAR},
            JIFEIBZ =  #{JIFEIBZ,jdbcType=DECIMAL},
            JIFEIREN =  #{JIFEIREN,jdbcType=VARCHAR},
            JIFEISJ =  #{JIFEISJ,jdbcType=TIMESTAMP},
            JIFEIKS =  #{JIFEIKS,jdbcType=VARCHAR},
            BAOGAODZ =  #{BAOGAODZ,jdbcType=VARCHAR},
            DUOCHONGNYBZ =  #{DUOCHONGNYBZ,jdbcType=DECIMAL},
            BAOGAOCKBZ =  #{BAOGAOCKBZ,jdbcType=DECIMAL},
            BAOGAOCKSJ =  #{BAOGAOCKSJ,jdbcType=TIMESTAMP},
            BAOGAOCKR =  #{BAOGAOCKR,jdbcType=VARCHAR},
            HUSONGFS =  #{HUSONGFS,jdbcType=VARCHAR},
            ZUTUO =  #{ZUTUO,jdbcType=VARCHAR},
            SONGJIANSJ =  #{SONGJIANSJ,jdbcType=TIMESTAMP},
            SONGJIANYS =  #{SONGJIANYS,jdbcType=VARCHAR},
            SQDH =  #{SQDH,jdbcType=VARCHAR},
            BINGRENCKBGBZ =  #{BINGRENCKBGBZ,jdbcType=DECIMAL},
            BINGRENCKBGSJ =  #{BINGRENCKBGSJ,jdbcType=TIMESTAMP},
            JIFEIMS =  #{JIFEIMS,jdbcType=DECIMAL},
            ZHIQINGTYSBLJLXH =  #{ZHIQINGTYSBLJLXH,jdbcType=DECIMAL},
            YIZHUXMFLID =  #{YIZHUXMFLID,jdbcType=VARCHAR},
            YUANQIANBZ =  #{YUANQIANBZ,jdbcType=DECIMAL},
            WEIJIBZ =  #{WEIJIBZ,jdbcType=VARCHAR},
            GCPBZ =  #{GCPBZ,jdbcType=DECIMAL},
            YUYUEBZ =  #{YUYUEBZ,jdbcType=DECIMAL},
            SHENQINGDJKID =  #{SHENQINGDJKID,jdbcType=VARCHAR},
            BINGLIZT =  #{BINGLIZT,jdbcType=VARCHAR},
            HULIANWSQDID =  #{HULIANWSQDID,jdbcType=VARCHAR},
            ZHONGXYYBZ =  #{ZHONGXYYBZ,jdbcType=DECIMAL},
            ZUHAO =  #{ZUHAO,jdbcType=VARCHAR},
            JIZHENYY =  #{JIZHENYY,jdbcType=VARCHAR},
            BINGLIQXJFBZ =  #{BINGLIQXJFBZ,jdbcType=DECIMAL},
            TONGLEIYZID =  #{TONGLEIYZID,jdbcType=VARCHAR},
            ZENGQIANGSMBZ =  #{ZENGQIANGSMBZ,jdbcType=DECIMAL},
            YUNYXBZ =  #{YUNYXBZ,jdbcType=DECIMAL},
            ORDERID =  #{ORDERID,jdbcType=VARCHAR},
            GUTISJ =  #{GUTISJ,jdbcType=TIMESTAMP},
            LITISJ =  #{LITISJ,jdbcType=TIMESTAMP},
            BINGRENHAO =  #{BINGRENHAO,jdbcType=VARCHAR},
            SHENHERQ =  #{SHENHERQ,jdbcType=TIMESTAMP},
            HOSPITAL_NAME =  #{HOSPITAL_NAME,jdbcType=VARCHAR},
            BAOGAOBZ =  #{BAOGAOBZ,jdbcType=VARCHAR},
            FUJIAXMSQD =  #{FUJIAXMSQD,jdbcType=VARCHAR},
            YUYUESJ =  #{YUYUESJ,jdbcType=VARCHAR},
            GUANLIANSQDID =  #{GUANLIANSQDID,jdbcType=VARCHAR},
            FANGJIANHAO =  #{FANGJIANHAO,jdbcType=VARCHAR},
            KAIDANYQID =  #{KAIDANYQID,jdbcType=VARCHAR},
            EXINGZLBZ =  #{EXINGZLBZ,jdbcType=DECIMAL},
            YOUHUILB =  #{YOUHUILB,jdbcType=VARCHAR},
            BUWEISL =  #{BUWEISL,jdbcType=DECIMAL}
        where   SHENQINDANID = #{SHENQINDANID,jdbcType=VARCHAR} 
    </update>
</mapper>
