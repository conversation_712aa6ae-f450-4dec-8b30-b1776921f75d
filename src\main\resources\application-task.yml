data:
  tasks:
    - name: user<PERSON>ig<PERSON><PERSON><PERSON>
      cron: "0 0/1 * * * ?"
      enabled: false
    - name: patient<PERSON><PERSON>
      cron: "0 0/2 * * * ?"
      enabled: true
    - name: check<PERSON><PERSON>
      cron: "0 0/2 * * * ?"
      enabled: true
    - name: practice<PERSON>ob
      cron: "0 0/2 * * * ?"
      enabled: true
    - name: bed<PERSON>ob
      cron: "0 0/2 * * * ?"
      enabled: true
    - name: surgery<PERSON><PERSON>
      cron: "0 0/2 * * * ?"
      enabled: true

