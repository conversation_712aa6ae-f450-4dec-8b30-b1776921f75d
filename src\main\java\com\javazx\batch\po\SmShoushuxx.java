package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 手麻_手术信息
 * @TableName SM_SHOUSHUXX
 */
@TableName(value ="HIS6.SM_SHOUSHUXX")
@Data
@NoArgsConstructor
public class SmShoushuxx implements Serializable {
    /**
     * 手术单ID
     */
    @TableId(value = "SHOUSHUDID")
    private String SHOUSHUDID;

    /**
     * 手术单号
     */
    @TableField(value = "SHOUSHUDH")
    private String SHOUSHUDH;

    /**
     * 申请单ID
     */
    @TableField(value = "SHENQINGDID")
    private String SHENQINGDID;

    /**
     * 应用ID
     */
    @TableField(value = "YINGYONGID")
    private String YINGYONGID;

    /**
     * 院区ID
     */
    @TableField(value = "YUANQUID")
    private String YUANQUID;

    /**
     * 病人ID
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 住院号
     */
    @TableField(value = "ZHUYUANHAO")
    private String ZHUYUANHAO;

    /**
     * 病人姓名
     */
    @TableField(value = "BINGRENXM")
    private String BINGRENXM;

    /**
     * 性别
     */
    @TableField(value = "XINGBIE")
    private String XINGBIE;

    /**
     * 年龄
     */
    @TableField(value = "NIANLING")
    private Integer NIANLING;

    /**
     * 病人科室
     */
    @TableField(value = "BINGRENKS")
    private String BINGRENKS;

    /**
     * 病人病区
     */
    @TableField(value = "BINGRENBQ")
    private String BINGRENBQ;

    /**
     * 病人床位
     */
    @TableField(value = "BINGRENCW")
    private String BINGRENCW;

    /**
     * 费用性质
     */
    @TableField(value = "FEIYONGXZ")
    private String FEIYONGXZ;

    /**
     * 费用类别
     */
    @TableField(value = "FEIYONGLB")
    private String FEIYONGLB;

    /**
     * 病人体重
     */
    @TableField(value = "BINGRENTZ")
    private BigDecimal BINGRENTZ;

    /**
     * 申请科室
     */
    @TableField(value = "SHENQINGKS")
    private String SHENQINGKS;

    /**
     * 执行科室
     */
    @TableField(value = "ZHIXINGKS")
    private String ZHIXINGKS;

    /**
     * 申请时间
     */
    @TableField(value = "SHENQINGSJ")
    private LocalDateTime SHENQINGSJ;

    /**
     * 要求时间
     */
    @TableField(value = "YAOQIUSJ")
    private LocalDateTime YAOQIUSJ;

    /**
     * 安排时间
     */
    @TableField(value = "ANPAISJ")
    private LocalDateTime ANPAISJ;

    /**
     * 进行时间
     */
    @TableField(value = "JINXINGSJ")
    private LocalDateTime JINXINGSJ;

    /**
     * 结束时间
     */
    @TableField(value = "JIESHUSJ")
    private LocalDateTime JIESHUSJ;

    /**
     * 手术类别,1择期，2急诊，3危机
     */
    @TableField(value = "SHOUSHULB")
    private String SHOUSHULB;

    /**
     * 手术名称ID
     */
    @TableField(value = "SHOUSHUMCID")
    private String SHOUSHUMCID;

    /**
     * 手术名称
     */
    @TableField(value = "SHOUSHUMC")
    private String SHOUSHUMC;

    /**
     * 手术名称ID1
     */
    @TableField(value = "SHOUSHUMCID1")
    private String SHOUSHUMCID1;

    /**
     * 手术名称1
     */
    @TableField(value = "SHOUSHUMC1")
    private String SHOUSHUMC1;

    /**
     * 手术名称ID2
     */
    @TableField(value = "SHOUSHUMCID2")
    private String SHOUSHUMCID2;

    /**
     * 手术名称2
     */
    @TableField(value = "SHOUSHUMC2")
    private String SHOUSHUMC2;

    /**
     * 手术名称ID3
     */
    @TableField(value = "SHOUSHUMCID3")
    private String SHOUSHUMCID3;

    /**
     * 手术名称3
     */
    @TableField(value = "SHOUSHUMC3")
    private String SHOUSHUMC3;

    /**
     * 手术医师
     */
    @TableField(value = "SHOUSHUYS")
    private String SHOUSHUYS;

    /**
     * 助理医师1
     */
    @TableField(value = "ZHULIYS1")
    private String ZHULIYS1;

    /**
     * 助理医师2
     */
    @TableField(value = "ZHULIYS2")
    private String ZHULIYS2;

    /**
     * 助理医师3
     */
    @TableField(value = "ZHULIYS3")
    private String ZHULIYS3;

    /**
     * 洗手护士
     */
    @TableField(value = "XISHOUHS")
    private String XISHOUHS;

    /**
     * 司械护士1
     */
    @TableField(value = "SIXIEHS1")
    private String SIXIEHS1;

    /**
     * 司械护士2
     */
    @TableField(value = "SIXIEHS2")
    private String SIXIEHS2;

    /**
     * 巡回护士1
     */
    @TableField(value = "XUNHUIHS1")
    private String XUNHUIHS1;

    /**
     * 巡回护士2
     */
    @TableField(value = "XUNHUIHS2")
    private String XUNHUIHS2;

    /**
     * 巡回护士3
     */
    @TableField(value = "XUNHUIHS3")
    private String XUNHUIHS3;

    /**
     * 麻醉ID
     */
    @TableField(value = "MAZUIID")
    private String MAZUIID;

    /**
     * 麻醉方法
     */
    @TableField(value = "MAZUIFF")
    private String MAZUIFF;

    /**
     * 麻醉医生
     */
    @TableField(value = "MAZUIYS")
    private String MAZUIYS;

    /**
     * 麻醉医生1
     */
    @TableField(value = "MAZUIYS1")
    private String MAZUIYS1;

    /**
     * 麻醉医生2
     */
    @TableField(value = "MAZUIYS2")
    private String MAZUIYS2;

    /**
     * 麻醉医生3
     */
    @TableField(value = "MAZUIYS3")
    private String MAZUIYS3;

    /**
     * 手术台ID
     */
    @TableField(value = "SHOUSHUTID")
    private String SHOUSHUTID;

    /**
     * 接台号
     */
    @TableField(value = "JIETAIHAO")
    private String JIETAIHAO;

    /**
     * 首次手术标志
     */
    @TableField(value = "SHOUCISSBZ")
    private Integer SHOUCISSBZ;

    /**
     * 急诊标志
     */
    @TableField(value = "JIZHENBZ")
    private Integer JIZHENBZ;

    /**
     * 镇痛泵使用标志
     */
    @TableField(value = "ZHENTONGBSYBZ")
    private Integer ZHENTONGBSYBZ;

    /**
     * 手术创口类型
     */
    @TableField(value = "SHOUSHUCKLX")
    private String SHOUSHUCKLX;

    /**
     * 创口愈合情况
     */
    @TableField(value = "CHUANGKOUYHQK")
    private String CHUANGKOUYHQK;

    /**
     * 创口感染情况
     */
    @TableField(value = "CHUANGKOUGRQK")
    private String CHUANGKOUGRQK;

    /**
     * 术前信息
     */
    @TableField(value = "SHUQIANXX")
    private String SHUQIANXX;

    /**
     * 申请人
     */
    @TableField(value = "SHENQINGREN")
    private String SHENQINGREN;

    /**
     * 操作员
     */
    @TableField(value = "CAOZUOYUAN")
    private String CAOZUOYUAN;

    /**
     * 诊断代码1
     */
    @TableField(value = "ZHENDUANDM1")
    private String ZHENDUANDM1;

    /**
     * 诊断名称1
     */
    @TableField(value = "ZHENDUANMC1")
    private String ZHENDUANMC1;

    /**
     * 诊断代码2
     */
    @TableField(value = "ZHENDUANDM2")
    private String ZHENDUANDM2;

    /**
     * 诊断名称2
     */
    @TableField(value = "ZHENDUANMC2")
    private String ZHENDUANMC2;

    /**
     * 诊断代码3
     */
    @TableField(value = "ZHENDUANDM3")
    private String ZHENDUANDM3;

    /**
     * 诊断名称3
     */
    @TableField(value = "ZHENDUANMC3")
    private String ZHENDUANMC3;

    /**
     * 诊断代码4
     */
    @TableField(value = "ZHENDUANDM4")
    private String ZHENDUANDM4;

    /**
     * 诊断名称4
     */
    @TableField(value = "ZHENDUANMC4")
    private String ZHENDUANMC4;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 状态标志非空。 0已作废，1申请，2已拒绝，6已安排，7 开始，9已结束
     */
    @TableField(value = "ZHUANGTAIBZ")
    private Integer ZHUANGTAIBZ;

    /**
     * 对话信息
     */
    @TableField(value = "DUIHUAXX")
    private String DUIHUAXX;

    /**
     * 备注
     */
    @TableField(value = "BEIZHU")
    private String BEIZHU;

    /**
     * 门诊住院标志
     */
    @TableField(value = "MENZHENZYBZ")
    private Integer MENZHENZYBZ;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private LocalDateTime XIUGAISJ;

    /**
     * 年龄单位HR6-342(462214)
     */
    @TableField(value = "NIANLINGDW")
    private String NIANLINGDW;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 婴儿标志
     */
    @TableField(value = "YINGERBZ")
    private Integer YINGERBZ;

    /**
     * 婴儿ID
     */
    @TableField(value = "YINGERID")
    private String YINGERID;

    /**
     * 要求洗手护士
     */
    @TableField(value = "XISHOUHSBZ")
    private Integer XISHOUHSBZ;

    /**
     * 隔离标志
     */
    @TableField(value = "GELIBZ")
    private Integer GELIBZ;

    /**
     * 要求首台
     */
    @TableField(value = "YAOQIUST")
    private Integer YAOQIUST;

    /**
     * 手术级别
     */
    @TableField(value = "SHOUSHUJB")
    private String SHOUSHUJB;

    /**
     * 身高
     */
    @TableField(value = "SHENGAO")
    private BigDecimal SHENGAO;

    /**
     * 造影
     */
    @TableField(value = "ZAOYING")
    private String ZAOYING;

    /**
     * 特殊手术标志
     */
    @TableField(value = "TESHUSSBZ")
    private Integer TESHUSSBZ;

    /**
     * 开单应用id
     */
    @TableField(value = "KAIDANYYID")
    private String KAIDANYYID;

    /**
     * 检查项目id
     */
    @TableField(value = "JIANCHAXMID")
    private String JIANCHAXMID;

    /**
     * 检查接口id1
     */
    @TableField(value = "JIANCHAJKID1")
    private String JIANCHAJKID1;

    /**
     * 体外循环师
     */
    @TableField(value = "TIWAIXHS")
    private String TIWAIXHS;

    /**
     * 麻醉安排状态
     */
    @TableField(value = "MAZUIAPZT")
    private Integer MAZUIAPZT;

    /**
     * 麻醉方法1
     */
    @TableField(value = "MAZUIFF1")
    private String MAZUIFF1;

    /**
     * 复合麻醉方法名称
     */
    @TableField(value = "FUHEMZFFMC")
    private String FUHEMZFFMC;

    /**
     * 手术部位
     */
    @TableField(value = "SHOUSHUBW")
    private String SHOUSHUBW;

    /**
     * 手术名称ID4
     */
    @TableField(value = "SHOUSHUMCID4")
    private String SHOUSHUMCID4;

    /**
     * 手术名称4
     */
    @TableField(value = "SHOUSHUMC4")
    private String SHOUSHUMC4;

    /**
     * 手术名称ID5
     */
    @TableField(value = "SHOUSHUMCID5")
    private String SHOUSHUMCID5;

    /**
     * 手术名称5
     */
    @TableField(value = "SHOUSHUMC5")
    private String SHOUSHUMC5;

    /**
     * 就诊ID
     */
    @TableField(value = "JIUZHENID")
    private String JIUZHENID;

    /**
     * 术后手术名称ID
     */
    @TableField(value = "SHUHOUSSMCID")
    private String SHUHOUSSMCID;

    /**
     * 术后手术名称
     */
    @TableField(value = "SHUHOUSSMC")
    private String SHUHOUSSMC;

    /**
     * 入室时间
     */
    @TableField(value = "RUSHISJ")
    private LocalDateTime RUSHISJ;

    /**
     * 出室时间
     */
    @TableField(value = "CHUSHISJ")
    private LocalDateTime CHUSHISJ;

    /**
     * 手术科室
     */
    @TableField(value = "SHOUSHUKS")
    private String SHOUSHUKS;

    /**
     * 麻醉时间
     */
    @TableField(value = "MAZUISJ")
    private LocalDateTime MAZUISJ;

    /**
     * 非计划重返手术室标志
     */
    @TableField(value = "FEIJIHCFSSSBZ")
    private Integer FEIJIHCFSSSBZ;

    /**
     * 术前讨论
     */
    @TableField(value = "SHUQIANTL")
    private Integer SHUQIANTL;

    /**
     * 医务处备案标志
     */
    @TableField(value = "YIWUCBABZ")
    private Integer YIWUCBABZ;

    /**
     * 乙肝两对半HBsAg1阴性2阳性3未回报
     */
    @TableField(value = "YIGANGLDB")
    private Integer YIGANGLDB;

    /**
     * 梅毒抗体1阴性2阳性3未回报
     */
    @TableField(value = "MEIDUKT")
    private Integer MEIDUKT;

    /**
     * 艾滋病抗体hiv1阴性2阳性3未回报
     */
    @TableField(value = "AIZIBKT")
    private Integer AIZIBKT;

    /**
     *  丙肝抗体hcv1阴性2阳性3未回报
     */
    @TableField(value = "BINGGANKT")
    private Integer BINGGANKT;

    /**
     * 其他特殊感染
     */
    @TableField(value = "QITATSGR")
    private String QITATSGR;

    /**
     * 不传手术室标志
     */
    @TableField(value = "BUCHUANSSS")
    private Integer BUCHUANSSS;

    /**
     * 排斥标志
     */
    @TableField(value = "PAICHIBZ")
    private Integer PAICHIBZ;

    /**
     * 审核标志1已审核0待审核
     */
    @TableField(value = "SHENHEBZ")
    private Integer SHENHEBZ;

    /**
     * 审核时间
     */
    @TableField(value = "SHENHESJ")
    private LocalDateTime SHENHESJ;

    /**
     * 审核人
     */
    @TableField(value = "SHENHEREN")
    private String SHENHEREN;

    /**
     * 冰冻病理标志HR3-11087(150472)
     */
    @TableField(value = "BINGDONGBLBZ")
    private Integer BINGDONGBLBZ;

    /**
     * 手术隔离措施
     */
    @TableField(value = "GELICS")
    private String GELICS;

    /**
     * 预防用药时间HR3-13037(169126)
     */
    @TableField(value = "YUFANGYYSJ")
    private LocalDateTime YUFANGYYSJ;

    /**
     * 手术风险级别HR3-13037(169126)
     */
    @TableField(value = "SHOUSHUFXJB")
    private String SHOUSHUFXJB;

    /**
     * 麻醉分级HR3-13037(169126)
     */
    @TableField(value = "MAZUIFJ")
    private String MAZUIFJ;

    /**
     * 重大手术标志HR3-13324(171788)
     */
    @TableField(value = "ZHONGDASSBZ")
    private Integer ZHONGDASSBZ;

    /**
     * 创口修改理由HR3-13311(171641)
     */
    @TableField(value = "CHUANGKOUXGLY")
    private String CHUANGKOUXGLY;

    /**
     * 手术预计完成时间HR3-14950(188675)
     */
    @TableField(value = "SHOUSHUYJWCSJ")
    private LocalDateTime SHOUSHUYJWCSJ;

    /**
     * 输血前四项报告标志HR3-17358(207289)
     */
    @TableField(value = "SHUXUEQSXBGBZ")
    private Integer SHUXUEQSXBGBZ;

    /**
     * 输血前四项结果HR3-17358(207289)
     */
    @TableField(value = "SHUXUEQSXJG")
    private String SHUXUEQSXJG;

    /**
     * 生育史HR3-18784(216293)
     */
    @TableField(value = "SHENGYUSHI")
    private String SHENGYUSHI;

    /**
     * 婚姻HR3-18784(216293)
     */
    @TableField(value = "HUNYIN")
    private String HUNYIN;

    /**
     * 术前检查是否齐全HR3-18784(216293)
     */
    @TableField(value = "SHUQIANJCSFQQ")
    private Integer SHUQIANJCSFQQ;

    /**
     * 是否送病理化验HR3-18784(216293)
     */
    @TableField(value = "SHIFOUSBLHY")
    private Integer SHIFOUSBLHY;

    /**
     * 标本HR3-18784(216293)
     */
    @TableField(value = "BIAOBEN")
    private String BIAOBEN;

    /**
     * 标本件数HR3-18784(216293)
     */
    @TableField(value = "BIAOBENJS")
    private Integer BIAOBENJS;

    /**
     * 术中用药HR3-18784(216293)
     */
    @TableField(value = "SHUZHONGYY")
    private String SHUZHONGYY;

    /**
     * 流产原因HR3-18784(216293)
     */
    @TableField(value = "LIUCHANYY")
    private String LIUCHANYY;

    /**
     * 绒毛肢体HR3-18784(216293)
     */
    @TableField(value = "RONGMAOZT")
    private Integer RONGMAOZT;

    /**
     * 术前宫腔HR3-18784(216293)
     */
    @TableField(value = "SHUQIANGQ")
    private BigDecimal SHUQIANGQ;

    /**
     * 手术中情况HR3-18784(216293)
     */
    @TableField(value = "SHOUSHUZQK")
    private String SHOUSHUZQK;

    /**
     * 节育环名称HR3-18784(216293)
     */
    @TableField(value = "JIEYUHMC")
    private String JIEYUHMC;

    /**
     * 术后宫腔HR3-18784(216293)
     */
    @TableField(value = "SHUHOUGQ")
    private BigDecimal SHUHOUGQ;

    /**
     * 出血情况HR3-18784(216293)
     */
    @TableField(value = "CHUXUEQK")
    private String CHUXUEQK;

    /**
     * 跨科手术标志HR3-13788(176152)
     */
    @TableField(value = "KUAKESSBZ")
    private Integer KUAKESSBZ;

    /**
     * 关联手术单IDHR3-13788(176152)
     */
    @TableField(value = "GUANLIANSSDID")
    private String GUANLIANSSDID;

    /**
     * 手术级别1HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUJB1")
    private String SHOUSHUJB1;

    /**
     * 手术创口类型1HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUCKLX1")
    private String SHOUSHUCKLX1;

    /**
     * 手术级别2HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUJB2")
    private String SHOUSHUJB2;

    /**
     * 手术创口类型2HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUCKLX2")
    private String SHOUSHUCKLX2;

    /**
     * 手术级别3HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUJB3")
    private String SHOUSHUJB3;

    /**
     * 手术创口类型3HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUCKLX3")
    private String SHOUSHUCKLX3;

    /**
     * 手术级别4HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUJB4")
    private String SHOUSHUJB4;

    /**
     * 手术创口类型4HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUCKLX4")
    private String SHOUSHUCKLX4;

    /**
     * 手术级别5HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUJB5")
    private String SHOUSHUJB5;

    /**
     * 手术创口类型5HR3-13788(176152)
     */
    @TableField(value = "SHOUSHUCKLX5")
    private String SHOUSHUCKLX5;

    /**
     * 内镜手术标志HR3-13788(176152)
     */
    @TableField(value = "NEIJINGSSBZ")
    private Integer NEIJINGSSBZ;

    /**
     * 内镜手术HR3-13788(176152)
     */
    @TableField(value = "NEIJINGSS")
    private String NEIJINGSS;

    /**
     * 术后手术手术级别HR3-13788(176152)
     */
    @TableField(value = "SHUHOUSSJB")
    private String SHUHOUSSJB;

    /**
     * 术后手术创口类型HR3-13788(176152)
     */
    @TableField(value = "SHUHOUCKLX")
    private String SHUHOUCKLX;

    /**
     * 择期预约手术HR3-21147(243694)
     */
    @TableField(value = "SHUZI1")
    private Integer SHUZI1;

    /**
     * 择期手术急诊预约HR3-21147(243694)
     */
    @TableField(value = "SHUZI2")
    private Integer SHUZI2;

    /**
     * 按预约时间送达HR3-21147(243694)
     */
    @TableField(value = "SHUZI3")
    private Integer SHUZI3;

    /**
     * 主刀医生到达时间HR3-21147(243694)
     */
    @TableField(value = "SHUZI4")
    private Integer SHUZI4;

    /**
     * 手术标识HR3-21147(243694)
     */
    @TableField(value = "SHUZI5")
    private Integer SHUZI5;

    /**
     * 专科手术HR3-21147(243694)
     */
    @TableField(value = "SHUZI6")
    private Integer SHUZI6;

    /**
     * 手术结束标志HR3-21405(245106)
     */
    @TableField(value = "SHOUSHUJSBZ")
    private Integer SHOUSHUJSBZ;

    /**
     * 苏醒室HR3-20932(241815)
     */
    @TableField(value = "SUXINGS")
    private String SUXINGS;

    /**
     * 术中用药时间HR3-21641(246639)
     */
    @TableField(value = "SHUZHONGYYSJ")
    private LocalDateTime SHUZHONGYYSJ;

    /**
     * 临床路径导入标志HR3-22714(254818)
     */
    @TableField(value = "LINCHUANGLJDRBZ")
    private Integer LINCHUANGLJDRBZ;

    /**
     * 隔离措施名称HR3-22810(255350)
     */
    @TableField(value = "GELICSMC")
    private String GELICSMC;

    /**
     * 电子病历使用1HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY1")
    private String DIANZIBLSY1;

    /**
     * 电子病历使用2HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY2")
    private String DIANZIBLSY2;

    /**
     * 电子病历使用3HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY3")
    private String DIANZIBLSY3;

    /**
     * 电子病历使用4HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY4")
    private String DIANZIBLSY4;

    /**
     * 电子病历使用5HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY5")
    private String DIANZIBLSY5;

    /**
     * 电子病历使用6HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY6")
    private String DIANZIBLSY6;

    /**
     * 电子病历使用7HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY7")
    private String DIANZIBLSY7;

    /**
     * 电子病历使用8HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY8")
    private String DIANZIBLSY8;

    /**
     * 电子病历使用9HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY9")
    private String DIANZIBLSY9;

    /**
     * 电子病历使用10HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY10")
    private String DIANZIBLSY10;

    /**
     * 电子病历使用11HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY11")
    private String DIANZIBLSY11;

    /**
     * 电子病历使用12HR3-23606(260606)
     */
    @TableField(value = "DIANZIBLSY12")
    private String DIANZIBLSY12;

    /**
     * 失血量HR3-24267(264643)
     */
    @TableField(value = "SHIXUELIANG")
    private String SHIXUELIANG;

    /**
     * 输血量HR3-24267(264643)
     */
    @TableField(value = "SHUXUELIANG")
    private String SHUXUELIANG;

    /**
     * 预申请标志HR3-24525(266203)
     */
    @TableField(value = "YUSHENQBZ")
    private Integer YUSHENQBZ;

    /**
     * 手术医生医疗组HR3-25837(275373) 
     */
    @TableField(value = "SHOUSHUYSYLZ")
    private String SHOUSHUYSYLZ;

    /**
     * 手术预计时长HR3-28831(293698)
     */
    @TableField(value = "SHOUSHUYJSC")
    private String SHOUSHUYJSC;

    /**
     * 临床路径IDHB3-21618(309611)
     */
    @TableField(value = "LINCHUANGLJID")
    private String LINCHUANGLJID;

    /**
     * 物理表面酒精处理HR3-33875(326568)
     */
    @TableField(value = "WULIJJ")
    private String WULIJJ;

    /**
     * 物理表面清水擦拭HR3-33875(326568)
     */
    @TableField(value = "WULIQS")
    private Integer WULIQS;

    /**
     * 地面酒精处理HR3-33875(326568)
     */
    @TableField(value = "DIMIANJJ")
    private String DIMIANJJ;

    /**
     * 地面清水擦拭HR3-33875(326568)
     */
    @TableField(value = "DIMIANQS")
    private Integer DIMIANQS;

    /**
     * 滤网表面酒精处理HR3-33875(326568)
     */
    @TableField(value = "LVWANGJJ")
    private String LVWANGJJ;

    /**
     * 滤网表面清水擦拭HR3-33875(326568)
     */
    @TableField(value = "LVWANGQS")
    private Integer LVWANGQS;

    /**
     * 感染类型1一般感染2特殊感染0无感染HR3-33875(326568)
     */
    @TableField(value = "GANRANLX")
    private String GANRANLX;

    /**
     * 责任人HR3-33875(326568)
     */
    @TableField(value = "ZERENREN")
    private String ZERENREN;

    /**
     * 预防用药术中追加标志HR3-35007(333164)
     */
    @TableField(value = "YUFANGYYSZZJBZ")
    private Integer YUFANGYYSZZJBZ;

    /**
     * 
     */
    @TableField(value = "ZHIRUWU")
    private Integer ZHIRUWU;

    /**
     * 
     */
    @TableField(value = "SHOUSHUQJ")
    private Integer SHOUSHUQJ;

    /**
     * 病历是否完整HR3-38599(354207)
     */
    @TableField(value = "BINGLISFWZ")
    private Integer BINGLISFWZ;

    /**
     * 病历不完整信息HR3-38599(354207)：第一位入院记录；第二位首次病程录；第三位术前小结；第四位手术知情同意书
     */
    @TableField(value = "BINGLIBWZXX")
    private String BINGLIBWZXX;

    /**
     * 是否带人外来器械HR3-41222(370228)
     */
    @TableField(value = "SHIFOUDLWLQX")
    private Integer SHIFOUDLWLQX;

    /**
     * 病人医疗组HR3-44546(387839)
     */
    @TableField(value = "BINGRENYLZ")
    private String BINGRENYLZ;

    /**
     * 主刀医生医疗组HR3-44546(387839)
     */
    @TableField(value = "YISHENGZID")
    private String YISHENGZID;

    /**
     * 指导老师HR3-45778(394532)
     */
    @TableField(value = "ZHIDAOLS")
    private String ZHIDAOLS;

    /**
     * 
     */
    @TableField(value = "SIXIEHS3")
    private String SIXIEHS3;

    /**
     * 
     */
    @TableField(value = "JIESHENG1")
    private String JIESHENG1;

    /**
     * 
     */
    @TableField(value = "JIESHENG2")
    private String JIESHENG2;

    /**
     * 术后手术前缀HR3-55959(453335)
     */
    @TableField(value = "SHUHOUSSQZ")
    private String SHUHOUSSQZ;

    /**
     * 
     */
    @TableField(value = "SHUHOUSSHZ")
    private String SHUHOUSSHZ;

    /**
     * 需审核标志HR6-342(462214)
     */
    @TableField(value = "XUSHENHBZ")
    private Integer XUSHENHBZ;

    /**
     * 术后手术名称ID1HR6-342(462214)
     */
    @TableField(value = "SHUHOUMCID1")
    private String SHUHOUMCID1;

    /**
     * 术后手术名称1HR6-342(462214)
     */
    @TableField(value = "SHUHOUMC1")
    private String SHUHOUMC1;

    /**
     * 术后手术名称ID2HR6-342(462214)
     */
    @TableField(value = "SHUHOUMCID2")
    private String SHUHOUMCID2;

    /**
     * 术后手术名称2HR6-342(462214)
     */
    @TableField(value = "SHUHOUMC2")
    private String SHUHOUMC2;

    /**
     * 术后手术名称ID3HR6-342(462214)
     */
    @TableField(value = "SHUHOUMCID3")
    private String SHUHOUMCID3;

    /**
     * 术后手术名称3HR6-342(462214)
     */
    @TableField(value = "SHUHOUMC3")
    private String SHUHOUMC3;

    /**
     * 术后手术名称ID4HR6-342(462214)
     */
    @TableField(value = "SHUHOUMCID4")
    private String SHUHOUMCID4;

    /**
     * 术后手术名称4HR6-342(462214)
     */
    @TableField(value = "SHUHOUMC4")
    private String SHUHOUMC4;

    /**
     * 术后手术名称ID5HR6-342(462214)
     */
    @TableField(value = "SHUHOUMCID5")
    private String SHUHOUMCID5;

    /**
     * 术后手术名称5HR6-342(462214)
     */
    @TableField(value = "SHUHOUMC5")
    private String SHUHOUMC5;

    /**
     * 术后手术级别1HR6-342(462214)
     */
    @TableField(value = "SHUHOUJB1")
    private String SHUHOUJB1;

    /**
     * 术后手术创口类型1HR6-342(462214)
     */
    @TableField(value = "SHUHOUCKLX1")
    private String SHUHOUCKLX1;

    /**
     * 术后手术级别2HR6-342(462214)
     */
    @TableField(value = "SHUHOUJB2")
    private String SHUHOUJB2;

    /**
     * 术后手术创口类型2HR6-342(462214)
     */
    @TableField(value = "SHUHOUCKLX2")
    private String SHUHOUCKLX2;

    /**
     * 术后手术级别3HR6-342(462214)
     */
    @TableField(value = "SHUHOUJB3")
    private String SHUHOUJB3;

    /**
     * 术后手术创口类型3HR6-342(462214)
     */
    @TableField(value = "SHUHOUCKLX3")
    private String SHUHOUCKLX3;

    /**
     * 术后手术级别4HR6-342(462214)
     */
    @TableField(value = "SHUHOUJB4")
    private String SHUHOUJB4;

    /**
     * 术后手术创口类型4HR6-342(462214)
     */
    @TableField(value = "SHUHOUCKLX4")
    private String SHUHOUCKLX4;

    /**
     * 术后手术级别5HR6-342(462214)
     */
    @TableField(value = "SHUHOUJB5")
    private String SHUHOUJB5;

    /**
     * 术后手术创口类型5HR6-342(462214)
     */
    @TableField(value = "SHUHOUCKLX5")
    private String SHUHOUCKLX5;

    /**
     * 术后手术部位HR6-342(462214)
     */
    @TableField(value = "SHUHOUSSBW")
    private String SHUHOUSSBW;

    /**
     * 去手术时间HR6-355(468160)
     */
    @TableField(value = "QUSHOUSSJ")
    private LocalDateTime QUSHOUSSJ;

    /**
     * 去手术交接护士HR6-355(468160)
     */
    @TableField(value = "QUSHOUSJJHS")
    private String QUSHOUSJJHS;

    /**
     * 去手术交接护士姓名HR6-355(468160)
     */
    @TableField(value = "QUSHOUSJJHSXM")
    private String QUSHOUSJJHSXM;

    /**
     * 回病房时间HR6-355(468160)
     */
    @TableField(value = "HUIBINGFSJ")
    private LocalDateTime HUIBINGFSJ;

    /**
     * 回病房交接护士HR6-355(468160)
     */
    @TableField(value = "HUIBINGFJJHS")
    private String HUIBINGFJJHS;

    /**
     * 回病房交接护士姓名HR6-355(468160)
     */
    @TableField(value = "HUIBINGFJJHSXM")
    private String HUIBINGFJJHSXM;

    /**
     * 麻醉方式1,局麻;2,全麻    ([HR6-6108] 手术申请单格式调整 宁波手术界面调整 麻醉会诊相当于2全麻  也就是2 麻醉会诊 1麻醉方式)
     */
    @TableField(value = "MAZUIFS")
    private Integer MAZUIFS;

    /**
     * 手术安排人
     */
    @TableField(value = "SHOUSHUAPR")
    private String SHOUSHUAPR;

    /**
     * 手术安排时间
     */
    @TableField(value = "SHOUSHUAPSJ")
    private LocalDateTime SHOUSHUAPSJ;

    /**
     * 手术登记人
     */
    @TableField(value = "SHOUSHUDJR")
    private String SHOUSHUDJR;

    /**
     * 手术登记时间
     */
    @TableField(value = "SHOUSHUDJSJ")
    private LocalDateTime SHOUSHUDJSJ;

    /**
     * 麻醉安排人
     */
    @TableField(value = "MAZUIAPR")
    private String MAZUIAPR;

    /**
     * 麻醉安排时间
     */
    @TableField(value = "MAZUIAPSJ")
    private LocalDateTime MAZUIAPSJ;

    /**
     * 麻醉登记人
     */
    @TableField(value = "MAZUIDJR")
    private String MAZUIDJR;

    /**
     * 麻醉登记时间
     */
    @TableField(value = "MAZUIDJSJ")
    private LocalDateTime MAZUIDJSJ;

    /**
     * 预计时长（小时）
     */
    @TableField(value = "YUJISC")
    private BigDecimal YUJISC;

    /**
     * 审核不通过原因476199
     */
    @TableField(value = "SHENHEBTGYY")
    private String SHENHEBTGYY;

    /**
     * 进恢复室时间HR6-355(468160)
     */
    @TableField(value = "JINGHUIFSSJ")
    private LocalDateTime JINGHUIFSSJ;

    /**
     * 进恢复室交接护士HR6-355(468160)
     */
    @TableField(value = "JINGHUIFSJJHS")
    private String JINGHUIFSJJHS;

    /**
     * 进恢复室交接护士姓名HR6-355(468160)
     */
    @TableField(value = "JINGHUIFSJJHSXM")
    private String JINGHUIFSJJHSXM;

    /**
     * 进手术间时间HR6-355(468160)
     */
    @TableField(value = "JINGSHOUSJSJ")
    private LocalDateTime JINGSHOUSJSJ;

    /**
     * 进手术间交接护士HR6-355(468160)
     */
    @TableField(value = "JINGSHOUSJJJHS")
    private String JINGSHOUSJJJHS;

    /**
     * 进手术间交接护士姓名HR6-355(468160)
     */
    @TableField(value = "JINGSHOUSJJJHSXM")
    private String JINGSHOUSJJJHSXM;

    /**
     * 手术流转状态1去手术2进手术间3终止4出手术室5等待进恢复室6进恢复室7出恢复室8出手术室回病房9出恢复室回病房0终止手术回病房
     */
    @TableField(value = "SHOUSHULZZT")
    private String SHOUSHULZZT;

    /**
     * 终止时间HR6-355(468160)
     */
    @TableField(value = "ZHONGZHISJ")
    private LocalDateTime ZHONGZHISJ;

    /**
     * 终止交接护士HR6-355(468160)
     */
    @TableField(value = "ZHONGZHIJJHS")
    private String ZHONGZHIJJHS;

    /**
     * 终止交接护士姓名HR6-355(468160)
     */
    @TableField(value = "ZHONGZHIJJHSXM")
    private String ZHONGZHIJJHSXM;

    /**
     * 出手术室时间HR6-355(468160)
     */
    @TableField(value = "CHUSHOUSSSJ")
    private LocalDateTime CHUSHOUSSSJ;

    /**
     * 出手术室交接护士HR6-355(468160)
     */
    @TableField(value = "CHUSHOUSSJJHS")
    private String CHUSHOUSSJJHS;

    /**
     * 出手术室交接护士姓名HR6-355(468160)
     */
    @TableField(value = "CHUSHOUSSJJHSXM")
    private String CHUSHOUSSJJHSXM;

    /**
     * 出恢复室时间HR6-355(468160)
     */
    @TableField(value = "CHUHUIFSSJ")
    private LocalDateTime CHUHUIFSSJ;

    /**
     * 出恢复室交接护士HR6-355(468160)
     */
    @TableField(value = "CHUHUIFSJJHS")
    private String CHUHUIFSJJHS;

    /**
     * 出恢复室交接护士姓名HR6-355(468160)
     */
    @TableField(value = "CHUHUIFSJJHSXM")
    private String CHUHUIFSJJHSXM;

    /**
     * 分管院长审核标志3待审核1通过2不通过HR6-1106(505240)
     */
    @TableField(value = "FENGUANYZSHBZ")
    private Integer FENGUANYZSHBZ;

    /**
     * 分管院长HR6-1106(505240)
     */
    @TableField(value = "FENGUANYZ")
    private String FENGUANYZ;

    /**
     * 分管院长审核时间HR6-1106(505240)
     */
    @TableField(value = "FENGUANYZSHSJ")
    private LocalDateTime FENGUANYZSHSJ;

    /**
     * 分管院长审核不通过原因HR6-1106(505240)
     */
    @TableField(value = "FENGUANYZSHBTGYY")
    private String FENGUANYZSHBTGYY;

    /**
     * 科主任审核标志3待审核1通过2不通过HR6-1106(505240)
     */
    @TableField(value = "KEZHURSHBZ")
    private Integer KEZHURSHBZ;

    /**
     * 科主任HR6-1106(505240)
     */
    @TableField(value = "KEZHUREN")
    private String KEZHUREN;

    /**
     * 科主任审核时间HR6-1106(505240)
     */
    @TableField(value = "KEZHURSHSJ")
    private LocalDateTime KEZHURSHSJ;

    /**
     * 科主任审核不通过原因HR6-1106(505240)
     */
    @TableField(value = "KEZHURSHBTGYY")
    private String KEZHURSHBTGYY;

    /**
     * 医务科审核标志3待审核1通过2不通过HR6-1106(505240)
     */
    @TableField(value = "YIWUKSHBZ")
    private Integer YIWUKSHBZ;

    /**
     * 医务科HR6-1106(505240)
     */
    @TableField(value = "YIWUKE")
    private String YIWUKE;

    /**
     * 医务科审核时间HR6-1106(505240)
     */
    @TableField(value = "YIWUKSHSJ")
    private LocalDateTime YIWUKSHSJ;

    /**
     * 医务科审核不通过原因HR6-1106(505240)
     */
    @TableField(value = "YIWUKSHBTGYY")
    private String YIWUKSHBTGYY;

    /**
     * 上报事由HR6-1106(505240)；病史及会诊意见摘要HR6-1505(518673)
     */
    @TableField(value = "SHANGBAOSY")
    private String SHANGBAOSY;

    /**
     * 上报医生HR6-1106(505240)
     */
    @TableField(value = "SHANGBAOYS")
    private String SHANGBAOYS;

    /**
     * 需解决问题HR6-1106(505240)；术中可能发生的困难或意外及其预防HR6-1505(518673)
     */
    @TableField(value = "XUJIEJWT")
    private String XUJIEJWT;

    /**
     * 术前讨论结果HR6-1106(505240)；手术论证方式及性质HR6-1505(518673)
     */
    @TableField(value = "SHUQIANTLJG")
    private String SHUQIANTLJG;

    /**
     * 已采取措施HR6-1106(505240)
     */
    @TableField(value = "YICAIQCS")
    private String YICAIQCS;

    /**
     * 主治医生审核标志3待审核1通过2不通过HR6-1106(505240)
     */
    @TableField(value = "ZHUZHIYSSHBZ")
    private Integer ZHUZHIYSSHBZ;

    /**
     * 主治医生审核人HR6-1106(505240)
     */
    @TableField(value = "ZHUZHIYSSHR")
    private String ZHUZHIYSSHR;

    /**
     * 主治医生审核时间HR6-1106(505240)
     */
    @TableField(value = "ZHUZHIYSSHSJ")
    private LocalDateTime ZHUZHIYSSHSJ;

    /**
     * 主治医生审核不通过原因HR6-1106(505240)
     */
    @TableField(value = "ZHUZHIYSSHBTGYY")
    private Integer ZHUZHIYSSHBTGYY;

    /**
     * 术前感染情况HR6-1505(518673)
     */
    @TableField(value = "SHUQIANGRQK")
    private String SHUQIANGRQK;

    /**
     * 并发症HR6-1505(518673)
     */
    @TableField(value = "BINGFAZHENG")
    private String BINGFAZHENG;

    /**
     * 多重耐药标志HR6-1505(518673)
     */
    @TableField(value = "DUOCHONGNYBZ")
    private Integer DUOCHONGNYBZ;

    /**
     * 主刀医生审核标志3待审核1通过2不通过HR6-1505(518673)
     */
    @TableField(value = "ZHUDAOYSSHBZ")
    private Integer ZHUDAOYSSHBZ;

    /**
     * 主刀医生审核人HR6-1505(518673)
     */
    @TableField(value = "ZHUDAOYSSHR")
    private String ZHUDAOYSSHR;

    /**
     * 主刀医生审核时间HR6-1505(518673)
     */
    @TableField(value = "ZHUDAOYSSHSJ")
    private LocalDateTime ZHUDAOYSSHSJ;

    /**
     * 麻醉医生审核标志3待审核1通过2不通过HR6-1505(518673)
     */
    @TableField(value = "MAZUIYSSHBZ")
    private Integer MAZUIYSSHBZ;

    /**
     * 麻醉医生审核人HR6-1505(518673)
     */
    @TableField(value = "MAZUIYSSHR")
    private String MAZUIYSSHR;

    /**
     * 麻醉医生审核时间HR6-1505(518673)
     */
    @TableField(value = "MAZUIYSSHSJ")
    private LocalDateTime MAZUIYSSHSJ;

    /**
     * 麻醉中可能发生的困难或意外及其预防HR6-1505(518673)
     */
    @TableField(value = "MAZUIYF")
    private String MAZUIYF;

    /**
     * 申请医生HR6-1505(518673)
     */
    @TableField(value = "SHENQINGYS")
    private String SHENQINGYS;

    /**
     * 医生申请日期HR6-1505(518673)
     */
    @TableField(value = "YISHENGSQRQ")
    private LocalDateTime YISHENGSQRQ;

    /**
     *  诊断信息 HR6-1106
     */
    @TableField(value = "ZHENDUAN")
    private String ZHENDUAN;

    /**
     * 上级医师审核标志3待审核1通过2不通过HR6-1505(518673)
     */
    @TableField(value = "SHANGJIYSSHBZ")
    private Integer SHANGJIYSSHBZ;

    /**
     * 上级医师审核时间HR6-1505(518673)
     */
    @TableField(value = "SHANGJIYSSHSJ")
    private LocalDateTime SHANGJIYSSHSJ;

    /**
     * 上级医师审核人HR6-1505(518673)
     */
    @TableField(value = "SHANGJIYSSHR")
    private String SHANGJIYSSHR;

    /**
     * 上级医师HR6-1505(518673)
     */
    @TableField(value = "SHANGJIYS")
    private String SHANGJIYS;

    /**
     * 上级医生审核不通过原因HR6-1106(505240)
     */
    @TableField(value = "SHANGJIYSSHBTGYY")
    private String SHANGJIYSSHBTGYY;

    /**
     * 术前手术级别(取主手术和5个次手术中级别最高的那个)
     */
    @TableField(value = "SHUQIANSSJB")
    private String SHUQIANSSJB;

    /**
     * 麻醉会诊单ID  HB6-10426(577214)
     */
    @TableField(value = "MAZUIHZDID")
    private String MAZUIHZDID;

    /**
     * 手术代码
     */
    @TableField(value = "SHOUSHUDM")
    private String SHOUSHUDM;

    /**
     * 进ICU时间HR6-3926(591666)
     */
    @TableField(value = "JINICUHS")
    private String JINICUHS;

    /**
     * 进ICU交接护士HR6-3926(591666)
     */
    @TableField(value = "JINICUSJ")
    private LocalDateTime JINICUSJ;

    /**
     * 进ICU交接护士姓名HR6-3926(591666)
     */
    @TableField(value = "JINICUHSXM")
    private String JINICUHSXM;

    /**
     * 出ICU交接护士HR6-3926(591666)
     */
    @TableField(value = "CHUICUHS")
    private String CHUICUHS;

    /**
     * 出ICU时间HR6-3926(591666)
     */
    @TableField(value = "CHUICUSJ")
    private LocalDateTime CHUICUSJ;

    /**
     * 出ICU交接护士姓名HR6-3926(591666)
     */
    @TableField(value = "CHUICUHSXM")
    private String CHUICUHSXM;

    /**
     * 拒绝类型（1.手术间已满；2.手术其他；3.不符合麻醉要求；4.麻醉其他）
     */
    @TableField(value = "JUJUELX")
    private String JUJUELX;

    /**
     * 拒绝原因（手术、麻醉通用）
     */
    @TableField(value = "JUJUEYY")
    private String JUJUEYY;

    /**
     * 手术台次
     */
    @TableField(value = "TAICI")
    private String TAICI;

    /**
     * 备注1（手术安排）
     */
    @TableField(value = "BEIZHU1")
    private String BEIZHU1;

    /**
     * 备注2（麻醉安排）
     */
    @TableField(value = "BEIZHU2")
    private String BEIZHU2;

    /**
     * 知情同意书状态：1已打印，2已签名，0未处理
     */
    @TableField(value = "ZHIQINGTYSZT")
    private Integer ZHIQINGTYSZT;

    /**
     * 危急值标志 0未勾选 1已勾选[HR6-6108] 手术申请单格式调整。
     */
    @TableField(value = "WEIJIZHIBZ")
    private Integer WEIJIZHIBZ;

    /**
     * 术后镇痛0未勾选 1已勾选[HR6-6108] 手术申请单格式调整。
     */
    @TableField(value = "SHUHOUZHENTONGBZ")
    private Integer SHUHOUZHENTONGBZ;

    /**
     * 是否使用抗菌药物HOCR5752 【宁波市一】手术申请单页面更改 是 1 否 0
     */
    @TableField(value = "ISKANGJUNYW")
    private Integer ISKANGJUNYW;

    /**
     * 
     */
    @TableField(value = "SHOUSHUSLX")
    private String SHOUSHUSLX;

    /**
     * 日间手术标志HOCR6471
     */
    @TableField(value = "RIJIANSSBZ")
    private Integer RIJIANSSBZ;

    /**
     * 诊断代码
     */
    @TableField(value = "ZHENDUANDM")
    private String ZHENDUANDM;

    /**
     * 手术部位2
     */
    @TableField(value = "SHOUSHUBW2")
    private String SHOUSHUBW2;

    /**
     * 手术部位3
     */
    @TableField(value = "SHOUSHUBW3")
    private String SHOUSHUBW3;

    /**
     * 手术部位4
     */
    @TableField(value = "SHOUSHUBW4")
    private String SHOUSHUBW4;

    /**
     * 手术部位5
     */
    @TableField(value = "SHOUSHUBW5")
    private String SHOUSHUBW5;

    /**
     * 手术部位1
     */
    @TableField(value = "SHOUSHUBW1")
    private String SHOUSHUBW1;

    /**
     * 麻醉结束标志
     */
    @TableField(value = "MAZUIJSBZ")
    private Integer MAZUIJSBZ;

    /**
     * 麻醉结束人
     */
    @TableField(value = "MAZUIJSREN")
    private String MAZUIJSREN;

    /**
     * 麻醉结束时间
     */
    @TableField(value = "MAZUIJSSJ")
    private LocalDateTime MAZUIJSSJ;

    /**
     * 手术记账结束人
     */
    @TableField(value = "SHOUSHUJZJSREN")
    private String SHOUSHUJZJSREN;

    /**
     * 麻醉记账结束人
     */
    @TableField(value = "MAZUIJZJSREN")
    private String MAZUIJZJSREN;

    /**
     * 手术记账结束时间
     */
    @TableField(value = "SHOUSHUJZSJ")
    private LocalDateTime SHOUSHUJZSJ;

    /**
     * 取消原因
     */
    @TableField(value = "QUXIAOYY")
    private String QUXIAOYY;

    /**
     * 原因类型
     */
    @TableField(value = "YUANYINLX")
    private String YUANYINLX;

    /**
     * 取消人
     */
    @TableField(value = "QUXIAOR")
    private String QUXIAOR;

    /**
     * 取消时间
     */
    @TableField(value = "QUXIAOSJ")
    private LocalDateTime QUXIAOSJ;

    /**
     * 手术开始进行执行人
     */
    @TableField(value = "SHOUSHUKSREN")
    private String SHOUSHUKSREN;

    /**
     * 手术结束执行人
     */
    @TableField(value = "SHOUSHUJSREN")
    private String SHOUSHUJSREN;

    /**
     * 手术记账结束标记 1:已结束记账
     */
    @TableField(value = "SHOUSHUJZBZ")
    private Integer SHOUSHUJZBZ;

    /**
     * 麻醉记账结束标记 1:已结束记账
     */
    @TableField(value = "MAZUIJZBZ")
    private Integer MAZUIJZBZ;

    /**
     * 麻醉记账结束时间
     */
    @TableField(value = "MAZUIJZSJ")
    private LocalDateTime MAZUIJZSJ;

    /**
     * 外院标志
     */
    @TableField(value = "WAIYUANBZ")
    private Integer WAIYUANBZ;

    /**
     * 转运方式
     */
    @TableField(value = "ZHUANYUNFS")
    private String ZHUANYUNFS;

    /**
     * 手术性质
     */
    @TableField(value = "SHOUSHUXZ")
    private String SHOUSHUXZ;

    /**
     * 手术性质其他
     */
    @TableField(value = "SHOUSHUXZQT")
    private String SHOUSHUXZQT;

    /**
     * 非计划再次手术
     */
    @TableField(value = "FEIJIHUAZCSS")
    private String FEIJIHUAZCSS;

    /**
     * 体位
     */
    @TableField(value = "TIWEI")
    private String TIWEI;

    /**
     * 指导医生
     */
    @TableField(value = "ZHIDAOYS")
    private String ZHIDAOYS;

    /**
     * 需要术中冰冻
     */
    @TableField(value = "XUYAOSZBD")
    private Integer XUYAOSZBD;

    /**
     * 需要标本采集
     */
    @TableField(value = "XUYAOBBCJ")
    private Integer XUYAOBBCJ;

    /**
     * 申请术后综合病房
     */
    @TableField(value = "SHENQINGSHZHBF")
    private Integer SHENQINGSHZHBF;

    /**
     * 申请转ICU
     */
    @TableField(value = "SHENQINGZICU")
    private Integer SHENQINGZICU;

    /**
     * 术后镇痛
     */
    @TableField(value = "SHUHOUZT")
    private Integer SHUHOUZT;

    /**
     * 跟台
     */
    @TableField(value = "GENTAI")
    private Integer GENTAI;

    /**
     * 手术备注 
     */
    @TableField(value = "SHOUSHUBZ")
    private String SHOUSHUBZ;

    /**
     * 送复苏室 HOCR62818
     */
    @TableField(value = "SONGFUSS")
    private Integer SONGFUSS;

    /**
     * 特殊监测 HOCR62818
     */
    @TableField(value = "TESHUJC")
    private String TESHUJC;

    /**
     * 麻醉手术史  HOCR62818
     */
    @TableField(value = "MAZUISSS")
    private String MAZUISSS;

    /**
     * 麻醉评估
     */
    @TableField(value = "MAZUIPG")
    private String MAZUIPG;

    /**
     * 麻醉评估内容
     */
    @TableField(value = "MAZUIPGNR")
    private String MAZUIPGNR;

    /**
     * 微创手术标志
     */
    @TableField(value = "WEICHUANGSSBZ")
    private Long WEICHUANGSSBZ;

    /**
     * 麻醉评估病历记录序号HODT16147
     */
    @TableField(value = "MAZUIPGBLJLXH")
    private Integer MAZUIPGBLJLXH;

    /**
     * 手术部位6 HOCR76403
     */
    @TableField(value = "SHOUSHUBW6")
    private String SHOUSHUBW6;

    /**
     * 手术名称6 HOCR76403
     */
    @TableField(value = "SHOUSHUMC6")
    private String SHOUSHUMC6;

    /**
     * 手术名称ID6 HOCR76403
     */
    @TableField(value = "SHOUSHUID6")
    private String SHOUSHUID6;

    /**
     * 手术部位7 HOCR76403
     */
    @TableField(value = "SHOUSHUBW7")
    private String SHOUSHUBW7;

    /**
     * 手术名称7 HOCR76403
     */
    @TableField(value = "SHOUSHUMC7")
    private String SHOUSHUMC7;

    /**
     * 手术名称ID7 HOCR76403
     */
    @TableField(value = "SHOUSHUID7")
    private String SHOUSHUID7;

    /**
     * 手术部位8 HOCR76403
     */
    @TableField(value = "SHOUSHUBW8")
    private String SHOUSHUBW8;

    /**
     * 手术名称8 HOCR76403
     */
    @TableField(value = "SHOUSHUMC8")
    private String SHOUSHUMC8;

    /**
     * 手术名称ID8 HOCR76403
     */
    @TableField(value = "SHOUSHUID8")
    private String SHOUSHUID8;

    /**
     * 手术部位9 HOCR76403
     */
    @TableField(value = "SHOUSHUBW9")
    private String SHOUSHUBW9;

    /**
     * 手术名称9 HOCR76403
     */
    @TableField(value = "SHOUSHUMC9")
    private String SHOUSHUMC9;

    /**
     * 手术名称ID9 HOCR76403
     */
    @TableField(value = "SHOUSHUID9")
    private String SHOUSHUID9;

    /**
     * 手术部位10 HOCR76403
     */
    @TableField(value = "SHOUSHUBW10")
    private String SHOUSHUBW10;

    /**
     * 手术名称10 HOCR76403
     */
    @TableField(value = "SHOUSHUMC10")
    private String SHOUSHUMC10;

    /**
     * 手术名称ID10 HOCR76403
     */
    @TableField(value = "SHOUSHUID10")
    private String SHOUSHUID10;

    /**
     * 手术部位11 HOCR76403
     */
    @TableField(value = "SHOUSHUBW11")
    private String SHOUSHUBW11;

    /**
     * 手术名称11 HOCR76403
     */
    @TableField(value = "SHOUSHUMC11")
    private String SHOUSHUMC11;

    /**
     * 手术名称ID11 HOCR76403
     */
    @TableField(value = "SHOUSHUID11")
    private String SHOUSHUID11;

    /**
     * 手术部位12 HOCR76403
     */
    @TableField(value = "SHOUSHUBW12")
    private String SHOUSHUBW12;

    /**
     * 手术名称12 HOCR76403
     */
    @TableField(value = "SHOUSHUMC12")
    private String SHOUSHUMC12;

    /**
     * 手术名称ID12 HOCR76403
     */
    @TableField(value = "SHOUSHUID12")
    private String SHOUSHUID12;

    /**
     * 手术部位13 HOCR76403
     */
    @TableField(value = "SHOUSHUBW13")
    private String SHOUSHUBW13;

    /**
     * 手术名称13 HOCR76403
     */
    @TableField(value = "SHOUSHUMC13")
    private String SHOUSHUMC13;

    /**
     * 手术名称ID13 HOCR76403
     */
    @TableField(value = "SHOUSHUID13")
    private String SHOUSHUID13;

    /**
     * 手术部位14 HOCR76403
     */
    @TableField(value = "SHOUSHUBW14")
    private String SHOUSHUBW14;

    /**
     * 手术名称14 HOCR76403
     */
    @TableField(value = "SHOUSHUMC14")
    private String SHOUSHUMC14;

    /**
     * 手术名称ID14 HOCR76403
     */
    @TableField(value = "SHOUSHUID14")
    private String SHOUSHUID14;

    /**
     * 手术部位15 HOCR76403
     */
    @TableField(value = "SHOUSHUBW15")
    private String SHOUSHUBW15;

    /**
     * 手术名称15 HOCR76403
     */
    @TableField(value = "SHOUSHUMC15")
    private String SHOUSHUMC15;

    /**
     * 手术名称ID15 HOCR76403
     */
    @TableField(value = "SHOUSHUID15")
    private String SHOUSHUID15;

    /**
     * 手术部位16 HOCR76403
     */
    @TableField(value = "SHOUSHUBW16")
    private String SHOUSHUBW16;

    /**
     * 手术名称16 HOCR76403
     */
    @TableField(value = "SHOUSHUMC16")
    private String SHOUSHUMC16;

    /**
     * 手术名称ID16 HOCR76403
     */
    @TableField(value = "SHOUSHUID16")
    private String SHOUSHUID16;

    /**
     * 手术部位17 HOCR76403
     */
    @TableField(value = "SHOUSHUBW17")
    private String SHOUSHUBW17;

    /**
     * 手术名称17 HOCR76403
     */
    @TableField(value = "SHOUSHUMC17")
    private String SHOUSHUMC17;

    /**
     * 手术名称ID17 HOCR76403
     */
    @TableField(value = "SHOUSHUID17")
    private String SHOUSHUID17;

    /**
     * 手术部位18 HOCR76403
     */
    @TableField(value = "SHOUSHUBW18")
    private String SHOUSHUBW18;

    /**
     * 手术名称18 HOCR76403
     */
    @TableField(value = "SHOUSHUMC18")
    private String SHOUSHUMC18;

    /**
     * 手术名称ID18 HOCR76403
     */
    @TableField(value = "SHOUSHUID18")
    private String SHOUSHUID18;

    /**
     * 手术部位19 HOCR76403
     */
    @TableField(value = "SHOUSHUBW19")
    private String SHOUSHUBW19;

    /**
     * 手术名称19 HOCR76403
     */
    @TableField(value = "SHOUSHUMC19")
    private String SHOUSHUMC19;

    /**
     * 手术名称ID19 HOCR76403
     */
    @TableField(value = "SHOUSHUID19")
    private String SHOUSHUID19;

    /**
     * 手术部位20 HOCR76403
     */
    @TableField(value = "SHOUSHUBW20")
    private String SHOUSHUBW20;

    /**
     * 手术名称20 HOCR76403
     */
    @TableField(value = "SHOUSHUMC20")
    private String SHOUSHUMC20;

    /**
     * 手术名称ID20 HOCR76403
     */
    @TableField(value = "SHOUSHUID20")
    private String SHOUSHUID20;

    /**
     * 手术级别6 HOCR76403
     */
    @TableField(value = "SHOUSHUJB6")
    private String SHOUSHUJB6;

    /**
     * 手术创口类型6 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX6")
    private String SHOUSHUCKLX6;

    /**
     * 手术级别7 HOCR76403
     */
    @TableField(value = "SHOUSHUJB7")
    private String SHOUSHUJB7;

    /**
     * 手术创口类型7 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX7")
    private String SHOUSHUCKLX7;

    /**
     * 手术级别8 HOCR76403
     */
    @TableField(value = "SHOUSHUJB8")
    private String SHOUSHUJB8;

    /**
     * 手术创口类型8 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX8")
    private String SHOUSHUCKLX8;

    /**
     * 手术级别9 HOCR76403
     */
    @TableField(value = "SHOUSHUJB9")
    private String SHOUSHUJB9;

    /**
     * 手术创口类型9 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX9")
    private String SHOUSHUCKLX9;

    /**
     * 手术级别10 HOCR76403
     */
    @TableField(value = "SHOUSHUJB10")
    private String SHOUSHUJB10;

    /**
     * 手术创口类型10 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX10")
    private String SHOUSHUCKLX10;

    /**
     * 手术级别11 HOCR76403
     */
    @TableField(value = "SHOUSHUJB11")
    private String SHOUSHUJB11;

    /**
     * 手术创口类型11 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX11")
    private String SHOUSHUCKLX11;

    /**
     * 手术级别12 HOCR76403
     */
    @TableField(value = "SHOUSHUJB12")
    private String SHOUSHUJB12;

    /**
     * 手术创口类型12 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX12")
    private String SHOUSHUCKLX12;

    /**
     * 手术级别13 HOCR76403
     */
    @TableField(value = "SHOUSHUJB13")
    private String SHOUSHUJB13;

    /**
     * 手术创口类型13 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX13")
    private String SHOUSHUCKLX13;

    /**
     * 手术级别14 HOCR76403
     */
    @TableField(value = "SHOUSHUJB14")
    private String SHOUSHUJB14;

    /**
     * 手术创口类型14 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX14")
    private String SHOUSHUCKLX14;

    /**
     * 手术级别15 HOCR76403
     */
    @TableField(value = "SHOUSHUJB15")
    private String SHOUSHUJB15;

    /**
     * 手术创口类型15 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX15")
    private String SHOUSHUCKLX15;

    /**
     * 手术级别16 HOCR76403
     */
    @TableField(value = "SHOUSHUJB16")
    private String SHOUSHUJB16;

    /**
     * 手术创口类型16 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX16")
    private String SHOUSHUCKLX16;

    /**
     * 手术级别17 HOCR76403
     */
    @TableField(value = "SHOUSHUJB17")
    private String SHOUSHUJB17;

    /**
     * 手术创口类型17 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX17")
    private String SHOUSHUCKLX17;

    /**
     * 手术级别18 HOCR76403
     */
    @TableField(value = "SHOUSHUJB18")
    private String SHOUSHUJB18;

    /**
     * 手术创口类型18 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX18")
    private String SHOUSHUCKLX18;

    /**
     * 手术级别19 HOCR76403
     */
    @TableField(value = "SHOUSHUJB19")
    private String SHOUSHUJB19;

    /**
     * 手术创口类型19 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX19")
    private String SHOUSHUCKLX19;

    /**
     * 手术级别20 HOCR76403
     */
    @TableField(value = "SHOUSHUJB20")
    private String SHOUSHUJB20;

    /**
     * 手术创口类型20 HOCR76403
     */
    @TableField(value = "SHOUSHUCKLX20")
    private String SHOUSHUCKLX20;

    /**
     * 
     */
    @TableField(value = "SHOUHU")
    private String SHOUHU;

    /**
     * 
     */
    @TableField(value = "SHOUHU1")
    private String SHOUHU1;

    /**
     * 
     */
    @TableField(value = "SHOUHU2")
    private String SHOUHU2;

    /**
     * 
     */
    @TableField(value = "SHOUHU3")
    private String SHOUHU3;

    /**
     * 
     */
    @TableField(value = "SHOUHU4")
    private String SHOUHU4;

    /**
     * 
     */
    @TableField(value = "SHOUHU5")
    private String SHOUHU5;

    /**
     * 
     */
    @TableField(value = "SHOUHU6")
    private String SHOUHU6;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}