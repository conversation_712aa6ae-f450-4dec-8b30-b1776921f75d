package com.javazx.batch.config;

import com.javazx.batch.config.properties.TaskConfig;
import com.javazx.batch.job.UserBatchJob;
import com.javazx.batch.quartz.scheduler.DependentUserSchedulerJob;
import com.javazx.batch.quartz.scheduler.UserSchedulerJob;
import com.javazx.batch.quartz.trigger.UserCronTriggerFactoryBean;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.springframework.batch.core.configuration.JobLocator;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@Order(2)
public class QuartzJobConfig {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private JobLocator jobLocator;

    @Autowired
    private TaskConfig taskConfig;

    @Autowired
    private ApplicationContext applicationContext;

    // 存储所有JobDetail
    private final Map<String, JobDetail> jobDetails = new HashMap<>();

    // 存储所有Trigger
    private final List<Trigger> triggers = new ArrayList<>();

    /**
     * 初始化所有JobDetail和Trigger
     */
    @PostConstruct
    public void initJobs() {
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();

        taskConfig.getTasks().forEach(task -> {
            if (task.isEnabled()) {
                // 1. 创建UserBatchJob实例并注册为Spring Bean
                UserBatchJob userBatchJob = createUserBatchJob(task);
                beanFactory.registerSingleton(task.getName(), userBatchJob);

                // 2. 为每个任务创建JobDetail
                JobDetail jobDetail = createJobDetail(task);
                jobDetails.put(task.getName(), jobDetail);

                // 3. 为每个任务创建Trigger
                try {
                    UserCronTriggerFactoryBean triggerFactory =
                            new UserCronTriggerFactoryBean(task.getName(), task.getCron());
                    triggers.add(triggerFactory.getObject());
                } catch (Exception e) {
                    throw new IllegalStateException("Failed to create trigger for task: " + task.getName(), e);
                }
            }
        });
    }

    /**
     * 创建Scheduler并注册所有JobDetail和Trigger
     */
    @Bean
    public SchedulerFactoryBean schedulerFactoryBean() {
        SchedulerFactoryBean schedulerFactory = new SchedulerFactoryBean();
        schedulerFactory.setOverwriteExistingJobs(true);
        schedulerFactory.setJobDetails(jobDetails.values().toArray(new JobDetail[0]));

        // Trigger 设置
        schedulerFactory.setTriggers(triggers.toArray(new Trigger[0]));

        return schedulerFactory;
    }

    /**
     * 创建JobDetail
     */
    private JobDetail createJobDetail(TaskConfig.Task task) {
        // 准备JobDataMap，存储任务名称和依赖信息
        Map<String, Object> jobData = new HashMap<>();
        jobData.put("batchJob", task.getName());

        JobDetailFactoryBean factoryBean = new JobDetailFactoryBean();

        // 根据任务配置决定使用哪种调度器
        if (task.getDependsOn() != null && !task.getDependsOn().trim().isEmpty()) {
            factoryBean.setJobClass(DependentUserSchedulerJob.class);
            jobData.put("dependencyTask", task.getDependsOn());
            jobData.put("maxWaitMinutes", task.getMaxWaitMinutes());
        } else {
            factoryBean.setJobClass(UserSchedulerJob.class);
        }

        factoryBean.setDurability(true);
        factoryBean.setName(task.getName());
        factoryBean.setGroup("DEFAULT");
        factoryBean.setJobDataAsMap(jobData);

        try {
            factoryBean.afterPropertiesSet();
            return factoryBean.getObject();
        } catch (Exception e) {
            throw new IllegalStateException("Failed to create JobDetail for task: " + task.getName(), e);
        }
    }

    /**
     * 创建任务对应的Job实例
     */
    private UserBatchJob createUserBatchJob(TaskConfig.Task task) {
        UserBatchJob job = new UserBatchJob();
        job.setJobName(task.getName());
        job.setJobLocator(jobLocator);
        job.setJobLauncher(jobLauncher);
        return job;
    }
}