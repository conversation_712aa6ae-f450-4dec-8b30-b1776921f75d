package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * 患者信息请求对象
 * PatientInfoReq
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientInfoReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入院时间
     */
    private String admissionTime;

    /**
     * 床位号
     */
    private String bedNo;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 脱敏后的姓名
     */
    private String desensitizedName;

    /**
     * 诊断
     */
    private String diagnosis;

    /**
     * 出院时间
     */
    private String dischargeTime;

    /**
     * 责任医生ID
     */
    private Long doctorJobNumber;

    /**
     * 医生的职称
     */
    private String doctorTitleShow;

    /**
     * 性别(0-全部, 1-男, 2-女)
     */
    private Long gender;

    /**
     * 身份证号
     */
    private Long idNumber;

    /**
     * 是否写入医嘱
     */
    private Long isWriteToAdvice;

    /**
     * 医保性质
     */
    private String medicalNature;

    /**
     * 护理等级
     */
    private String nurseGrade;

    /**
     * 责任护士ID
     */
    private Long nurseJobNumber;

    /**
     * 护士的职称
     */
    private String nurseTitleShow;

    /**
     * 姓名
     */
    private String patientName;

    /**
     * 病人住院状态(0-出院，1-在院)
     */
    private Long patientStatus;

    /**
     * 转院时间
     */
    private String transferTime;

    /**
     * 病区ID
     */
    private String wardCode;

    /**
     * 住院号
     */
    private String zyh;

    /**
     * 住院号码
     */
    private String zyhm;
}
