package com.javazx.batch.scenario.practice;

import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.PracticeDataService;
import com.javazx.batch.vo.PatientDiagnosisSyncRequest;
import com.javazx.batch.vo.PatientWithDiagnosisReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 诊疗数据同步场景
 * 从数据库读取医嘱数据并同步到外部系统
 */
@Component
public class PracticeScenario extends AbstractBatchScenario<YzBingrenyz, PatientWithDiagnosisReq> {

    private static final Logger log = LoggerFactory.getLogger(PracticeScenario.class);

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private PracticeDataService practiceDataService;

    public PracticeScenario() {
        super("practice", "诊疗数据同步场景：同步患者医嘱诊疗信息");
        this.setCommitInterval(50);
        this.setPageSize(30);
    }

    @Override
    public ItemReader<YzBingrenyz> createReader() {
        log.info("创建诊疗数据读取器，页面大小: {}", getPageSize());

        // 创建通用分页读取器
        GenericPagingItemReader<YzBingrenyz> reader = new GenericPagingItemReader<>(
                "医嘱诊疗信息",
                getPageSize(),
                (offset, limit) -> practiceDataService.selectDiagnosisByPage("1006", offset, limit),
                () -> practiceDataService.countDiagnosis("1006")
        );

        // 记录总医嘱数量
        int totalCount = reader.getTotalCount();
        log.info("病区1006医嘱诊疗信息总数: {}", totalCount);

        return reader;
    }

    @Override
    public ItemProcessor<YzBingrenyz, PatientWithDiagnosisReq> createProcessor() {
        log.info("创建诊疗数据转换器");
        return new PracticeProcessor(practiceDataService);
    }

    @Override
    public ItemWriter<PatientWithDiagnosisReq> createWriter() {
        log.info("创建诊疗数据同步写入器");
        return items -> {
            PatientDiagnosisSyncRequest request = new PatientDiagnosisSyncRequest();
            request.setPatientWithDiagnosisReqList((List<PatientWithDiagnosisReq>) items);

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理诊疗数据量: {}", threadName, items.size());

            try {
                List<String> patientNames = getPatientNameFromRequest((List<PatientWithDiagnosisReq>) items);
                String patientNamesStr = String.join(", ", patientNames);
                
                smartwardWebClient
                        .post()
                        .uri("/sync/patient_diagnosis/sync_patient_diagnosis")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request)
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("诊疗 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("诊疗 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送诊疗数据: {} (线程: {})", patientNamesStr, threadName);
            } catch (Exception e) {
                log.error("诊疗数据同步时发生异常: {}", e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理诊疗数据量: {}", threadName, items.size());
        };
    }

    /**
     * 从请求对象中获取患者姓名
     */
    private List<String> getPatientNameFromRequest(List<PatientWithDiagnosisReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知患者");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(PatientWithDiagnosisReq::getPatientInfo)
                .filter(Objects::nonNull)
                .map(patientInfo -> patientInfo.getPatientName())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
