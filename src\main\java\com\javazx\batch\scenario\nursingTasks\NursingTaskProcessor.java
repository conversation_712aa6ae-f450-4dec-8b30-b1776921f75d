package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.po.GyChuangwei;
import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.vo.NurseTaskReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 护理任务数据处理器
 * 将床位信息转换为护理任务请求对象用于同步
 */
@Component
public class NursingTaskProcessor implements ItemProcessor<YzBingrenyz, List<NurseTaskReq>> {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final NursingTaskDataService nursingTaskDataService;

    public NursingTaskProcessor(NursingTaskDataService nursingTaskDataService) {
        this.nursingTaskDataService = nursingTaskDataService;
    }

    @Override
    public List<NurseTaskReq> process(YzBingrenyz yzBingrenyz) {
        if (yzBingrenyz == null) {
            return null;
        }

        try {
            // 生成该床位的护理任务列表
            List<NurseTaskReq> nursingTasks = new ArrayList<>();
            return nursingTasks;
            
        } catch (Exception e) {
            log.error("处理床位护理任务失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成护理任务列表
     */
    private List<NurseTaskReq> generateNursingTasks(GyChuangwei bed) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        
        // 获取床位基本信息
        String bedNo = bed.getCHUANGWEIID();
        String wardCode = bed.getBINGQUID();
        String bedRemark = getBedRemark(bed);
        
        // 生成基础护理任务
        tasks.addAll(generateBasicNursingTasks(bedNo, wardCode, bedRemark));
        
        // 如果床位有患者，生成患者特定护理任务
/*        if (StringUtils.hasLength(bed.getBINGRENZYID())) {
            ZyBingrenxx patient = nursingTaskDataService.selectPatientById(bed.getBINGRENZYID());
            if (patient != null) {
                tasks.addAll(generatePatientSpecificTasks(bedNo, wardCode, bedRemark, patient));
            }
        }*/
        
        return tasks;
    }

    /**
     * 生成基础护理任务（每个床位都有的常规任务）
     */
    private List<NurseTaskReq> generateBasicNursingTasks(String bedNo, String wardCode, String bedRemark) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 1. 晨间护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "MORNING_CARE", 1L,
            now.withHour(7).withMinute(0).withSecond(0),
            now.withHour(9).withMinute(0).withSecond(0),
            0L
        ));
        
        // 2. 午间护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "NOON_CARE", 2L,
            now.withHour(12).withMinute(0).withSecond(0),
            now.withHour(14).withMinute(0).withSecond(0),
            0L
        ));
        
        // 3. 晚间护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "EVENING_CARE", 3L,
            now.withHour(18).withMinute(0).withSecond(0),
            now.withHour(20).withMinute(0).withSecond(0),
            0L
        ));
        
        // 4. 夜间巡视任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "NIGHT_PATROL", 4L,
            now.withHour(22).withMinute(0).withSecond(0),
            now.plusDays(1).withHour(6).withMinute(0).withSecond(0),
            0L
        ));
        
        return tasks;
    }

    /**
     * 生成患者特定护理任务（基于医嘱数据）
     */
    private List<NurseTaskReq> generatePatientSpecificTasks(String bedNo, String wardCode, String bedRemark, ZyBingrenxx patient) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        String patientId = patient.getBINGRENZYID();
        String patientName = patient.getXINGMING();
        String remarkWithPatient = bedRemark + " - " + patientName;

        // 1. 心电监护任务
        tasks.addAll(generateECGMonitoringTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 2. 测血压任务
        tasks.addAll(generateBloodPressureTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 3. 测血糖任务
        tasks.addAll(generateBloodGlucoseTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 4. 雾化吸入任务
        tasks.addAll(generateNebulizationTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 5. 膀胱冲洗任务
        tasks.addAll(generateBladderIrrigationTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 6. 基础护理任务
        tasks.addAll(generateBasicCareTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 7. 导管维护任务
        tasks.addAll(generateCatheterCareTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 8. 体征测量任务
        tasks.addAll(generateVitalSignsTasks(bedNo, wardCode, remarkWithPatient, patientId));

        // 9. 吸氧任务
        tasks.addAll(generateOxygenTherapyTasks(bedNo, wardCode, remarkWithPatient, patientId));

        return tasks;
    }

    /**
     * 创建护理任务对象
     */
    private NurseTaskReq createNurseTask(String bedNo, String wardCode, String bedRemark,
                                        String taskCode, Long orderNum,
                                        LocalDateTime startTime, LocalDateTime endTime,
                                        Long isExecuted) {
        NurseTaskReq task = new NurseTaskReq();
        task.setBedNo(bedNo);
        task.setWardCode(wardCode);
        task.setBedRemark(bedRemark);
        task.setNurseTaskCode(taskCode);
        task.setOrderNum(orderNum);
        task.setStartRemindTime(startTime.format(dateFormatter));
        task.setEndRemindTime(endTime.format(dateFormatter));
        task.setIsExecuted(isExecuted);
        return task;
    }

    /**
     * 获取床位备注信息
     */
    private String getBedRemark(GyChuangwei bed) {
        StringBuilder remark = new StringBuilder();

        // 床位类型
        if ("1".equals(bed.getCHUANGWEILX())) {
            remark.append("普通床位");
        } else if ("2".equals(bed.getCHUANGWEILX())) {
            remark.append("特殊床位");
        } else {
            remark.append("床位");
        }

        // 床位状态
        if (StringUtils.hasLength(bed.getBINGRENZYID())) {
            remark.append(" - 已占用");
        } else {
            remark.append(" - 空床");
        }

        return remark.toString();
    }

    /**
     * 生成心电监护任务
     */
    private List<NurseTaskReq> generateECGMonitoringTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var ecgOrders = nursingTaskDataService.selectECGMonitoringOrders(patientId);
            if (!ecgOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                tasks.add(createNurseTask(
                    bedNo, wardCode, bedRemark + " - 心电监护",
                    "ECG_MONITORING", 5L,
                    now.withHour(0).withMinute(0).withSecond(0),
                    now.withHour(23).withMinute(59).withSecond(59),
                    0L
                ));
            }
        } catch (Exception e) {
            log.warn("查询患者{}心电监护医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成测血压任务
     */
    private List<NurseTaskReq> generateBloodPressureTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var bpOrders = nursingTaskDataService.selectBloodPressureOrders(patientId);
            if (!bpOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                Long orderNum = 6L;

                for (var order : bpOrders) {
                    String frequency = order.getPINCI();

                    if ("QD".equals(frequency)) {
                        // 每日一次 - 06:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压QD",
                            "BLOOD_PRESSURE_QD", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                    } else if ("BID".equals(frequency)) {
                        // 每日两次 - 06:00, 16:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压BID(06:00)",
                            "BLOOD_PRESSURE_BID_06", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压BID(16:00)",
                            "BLOOD_PRESSURE_BID_16", orderNum++,
                            now.withHour(16).withMinute(0).withSecond(0),
                            now.withHour(16).withMinute(30).withSecond(0),
                            0L
                        ));
                    } else if ("TID".equals(frequency)) {
                        // 每日三次 - 06:00, 14:00, 18:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压TID(06:00)",
                            "BLOOD_PRESSURE_TID_06", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压TID(14:00)",
                            "BLOOD_PRESSURE_TID_14", orderNum++,
                            now.withHour(14).withMinute(0).withSecond(0),
                            now.withHour(14).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压TID(18:00)",
                            "BLOOD_PRESSURE_TID_18", orderNum++,
                            now.withHour(18).withMinute(0).withSecond(0),
                            now.withHour(18).withMinute(30).withSecond(0),
                            0L
                        ));
                    } else if ("QID".equals(frequency)) {
                        // 每日四次 - 06:00, 10:00, 14:00, 18:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压QID(06:00)",
                            "BLOOD_PRESSURE_QID_06", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压QID(10:00)",
                            "BLOOD_PRESSURE_QID_10", orderNum++,
                            now.withHour(10).withMinute(0).withSecond(0),
                            now.withHour(10).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压QID(14:00)",
                            "BLOOD_PRESSURE_QID_14", orderNum++,
                            now.withHour(14).withMinute(0).withSecond(0),
                            now.withHour(14).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血压QID(18:00)",
                            "BLOOD_PRESSURE_QID_18", orderNum++,
                            now.withHour(18).withMinute(0).withSecond(0),
                            now.withHour(18).withMinute(30).withSecond(0),
                            0L
                        ));
                    }
                    break; // 只处理第一个有效医嘱
                }
            }
        } catch (Exception e) {
            log.warn("查询患者{}测血压医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成测血糖任务
     */
    private List<NurseTaskReq> generateBloodGlucoseTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var bgOrders = nursingTaskDataService.selectBloodGlucoseOrders(patientId);
            if (!bgOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                Long orderNum = 20L;

                for (var order : bgOrders) {
                    String frequency = order.getPINCI();

                    if ("QD".equals(frequency)) {
                        // 每日一次 - 06:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖QD",
                            "BLOOD_GLUCOSE_QD", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                    } else if ("BID".equals(frequency)) {
                        // 每日两次 - 06:00, 16:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖BID(06:00)",
                            "BLOOD_GLUCOSE_BID_06", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖BID(16:00)",
                            "BLOOD_GLUCOSE_BID_16", orderNum++,
                            now.withHour(16).withMinute(0).withSecond(0),
                            now.withHour(16).withMinute(30).withSecond(0),
                            0L
                        ));
                    } else if ("TID".equals(frequency)) {
                        // 每日三次 - 06:00, 10:00, 16:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖TID(06:00)",
                            "BLOOD_GLUCOSE_TID_06", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖TID(10:00)",
                            "BLOOD_GLUCOSE_TID_10", orderNum++,
                            now.withHour(10).withMinute(0).withSecond(0),
                            now.withHour(10).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖TID(16:00)",
                            "BLOOD_GLUCOSE_TID_16", orderNum++,
                            now.withHour(16).withMinute(0).withSecond(0),
                            now.withHour(16).withMinute(30).withSecond(0),
                            0L
                        ));
                    } else if ("QID".equals(frequency)) {
                        // 每日四次 - 06:00, 10:00, 16:00, 20:00
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖QID(06:00)",
                            "BLOOD_GLUCOSE_QID_06", orderNum++,
                            now.withHour(6).withMinute(0).withSecond(0),
                            now.withHour(6).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖QID(10:00)",
                            "BLOOD_GLUCOSE_QID_10", orderNum++,
                            now.withHour(10).withMinute(0).withSecond(0),
                            now.withHour(10).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖QID(16:00)",
                            "BLOOD_GLUCOSE_QID_16", orderNum++,
                            now.withHour(16).withMinute(0).withSecond(0),
                            now.withHour(16).withMinute(30).withSecond(0),
                            0L
                        ));
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 测血糖QID(20:00)",
                            "BLOOD_GLUCOSE_QID_20", orderNum++,
                            now.withHour(20).withMinute(0).withSecond(0),
                            now.withHour(20).withMinute(30).withSecond(0),
                            0L
                        ));
                    }
                    break; // 只处理第一个有效医嘱
                }
            }
        } catch (Exception e) {
            log.warn("查询患者{}测血糖医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成雾化吸入任务
     */
    private List<NurseTaskReq> generateNebulizationTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var nebOrders = nursingTaskDataService.selectNebulizationOrders(patientId);
            if (!nebOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                tasks.add(createNurseTask(
                    bedNo, wardCode, bedRemark + " - 雾化吸入",
                    "NEBULIZATION", 30L,
                    now.withHour(8).withMinute(0).withSecond(0),
                    now.withHour(8).withMinute(30).withSecond(0),
                    0L
                ));
            }
        } catch (Exception e) {
            log.warn("查询患者{}雾化吸入医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成膀胱冲洗任务
     */
    private List<NurseTaskReq> generateBladderIrrigationTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var bladderOrders = nursingTaskDataService.selectBladderIrrigationOrders(patientId);
            if (!bladderOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                for (var order : bladderOrders) {
                    if ("膀胱持续冲洗".equals(order.getYIZHUMC())) {
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 膀胱持续冲洗",
                            "BLADDER_CONTINUOUS_IRRIGATION", 31L,
                            now.withHour(0).withMinute(0).withSecond(0),
                            now.withHour(23).withMinute(59).withSecond(59),
                            0L
                        ));
                    } else {
                        tasks.add(createNurseTask(
                            bedNo, wardCode, bedRemark + " - 膀胱冲洗",
                            "BLADDER_IRRIGATION", 32L,
                            now.withHour(8).withMinute(0).withSecond(0),
                            now.withHour(8).withMinute(30).withSecond(0),
                            0L
                        ));
                    }
                    break; // 只处理第一个有效医嘱
                }
            }
        } catch (Exception e) {
            log.warn("查询患者{}膀胱冲洗医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成基础护理任务
     */
    private List<NurseTaskReq> generateBasicCareTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var basicOrders = nursingTaskDataService.selectBasicCareOrders(patientId);
            LocalDateTime now = LocalDateTime.now();
            Long orderNum = 40L;

            for (var order : basicOrders) {
                String orderName = order.getYIZHUMC();
                if ("口腔护理".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 口腔护理",
                        "ORAL_CARE", orderNum++,
                        now.withHour(7).withMinute(0).withSecond(0),
                        now.withHour(7).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("会阴护理".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 会阴护理",
                        "PERINEAL_CARE", orderNum++,
                        now.withHour(8).withMinute(0).withSecond(0),
                        now.withHour(8).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("留置导尿".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 留置导尿",
                        "INDWELLING_CATHETER", orderNum++,
                        now.withHour(9).withMinute(0).withSecond(0),
                        now.withHour(9).withMinute(30).withSecond(0),
                        0L
                    ));
                }
            }
        } catch (Exception e) {
            log.warn("查询患者{}基础护理医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成导管维护任务
     */
    private List<NurseTaskReq> generateCatheterCareTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var catheterOrders = nursingTaskDataService.selectCatheterCareOrders(patientId);
            LocalDateTime now = LocalDateTime.now();
            Long orderNum = 50L;

            for (var order : catheterOrders) {
                String orderName = order.getYIZHUMC();
                if ("更换引流袋".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 更换引流袋",
                        "DRAINAGE_BAG_CHANGE", orderNum++,
                        now.withHour(8).withMinute(0).withSecond(0),
                        now.withHour(8).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("静脉置管冲洗".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 静脉置管冲洗",
                        "IV_CATHETER_FLUSH", orderNum++,
                        now.withHour(9).withMinute(0).withSecond(0),
                        now.withHour(9).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("动静脉置管护理".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 深静脉置管护理",
                        "CENTRAL_VENOUS_CATHETER_CARE", orderNum++,
                        now.withHour(10).withMinute(0).withSecond(0),
                        now.withHour(10).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("输液港置管护理".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 输液港护理",
                        "PORT_CATHETER_CARE", orderNum++,
                        now.withHour(11).withMinute(0).withSecond(0),
                        now.withHour(11).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("PICC置管护理".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - PICC置管护理",
                        "PICC_CATHETER_CARE", orderNum++,
                        now.withHour(12).withMinute(0).withSecond(0),
                        now.withHour(12).withMinute(30).withSecond(0),
                        0L
                    ));
                } else if ("肾周引流管护理".equals(orderName)) {
                    tasks.add(createNurseTask(
                        bedNo, wardCode, bedRemark + " - 肾周引流管护理",
                        "RENAL_DRAINAGE_CARE", orderNum++,
                        now.withHour(13).withMinute(0).withSecond(0),
                        now.withHour(13).withMinute(30).withSecond(0),
                        0L
                    ));
                }
            }
        } catch (Exception e) {
            log.warn("查询患者{}导管维护医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成体征测量任务
     */
    private List<NurseTaskReq> generateVitalSignsTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var vitalOrders = nursingTaskDataService.selectVitalSignsOrders(patientId);
            if (!vitalOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                tasks.add(createNurseTask(
                    bedNo, wardCode, bedRemark + " - 记24小时尿量",
                    "URINE_OUTPUT_24H", 60L,
                    now.withHour(6).withMinute(0).withSecond(0),
                    now.withHour(6).withMinute(30).withSecond(0),
                    0L
                ));
            }
        } catch (Exception e) {
            log.warn("查询患者{}体征测量医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }

    /**
     * 生成吸氧任务
     */
    private List<NurseTaskReq> generateOxygenTherapyTasks(String bedNo, String wardCode, String bedRemark, String patientId) {
        List<NurseTaskReq> tasks = new ArrayList<>();

        try {
            var oxygenOrders = nursingTaskDataService.selectOxygenTherapyOrders(patientId);
            if (!oxygenOrders.isEmpty()) {
                LocalDateTime now = LocalDateTime.now();
                tasks.add(createNurseTask(
                    bedNo, wardCode, bedRemark + " - 吸氧",
                    "OXYGEN_THERAPY", 70L,
                    now.withHour(0).withMinute(0).withSecond(0),
                    now.withHour(23).withMinute(59).withSecond(59),
                    0L
                ));
            }
        } catch (Exception e) {
            log.warn("查询患者{}吸氧医嘱失败: {}", patientId, e.getMessage());
        }

        return tasks;
    }
}
