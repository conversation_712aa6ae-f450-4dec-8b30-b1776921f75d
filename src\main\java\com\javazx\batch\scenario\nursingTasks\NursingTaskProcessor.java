package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import com.javazx.batch.vo.NurseTaskReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 护理任务处理器
 * 基于医嘱数据生成护理任务
 */
@Slf4j
@Component
public class NursingTaskProcessor implements ItemProcessor<List, NurseTaskReq> {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public NurseTaskReq process(List wardIds) {

    }

    /**
     * 生成任务代码 - 根据护理任务编码枚举表
     */
    private String generateTaskCode(String orderName, String frequency) {
        // 心电监护
        if (orderName.contains("心电监护") || orderName.contains("心电监测")) {
            return "RW01";
        }

        // 测血压 - 根据频次和时间点
        if (orderName.contains("测血压")) {
            if ("QD".equals(frequency)) return "RW02"; // 06:00 测血压
            if ("BID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            if ("TID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            if ("QID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            return "RW02"; // 默认06:00
        }

        // 测血糖 - 根据频次和时间点
        if ("葡萄糖测定".equals(orderName) || orderName.contains("测血糖")) {
            return "RW06"; // 默认06:00 测血糖
        }

        // 雾化吸入
        if (orderName.contains("雾化")) {
            return "RW10";
        }

        // 膀胱相关
        if ("膀胱持续冲洗".equals(orderName)) {
            return "RW12";
        }
        if (orderName.contains("膀胱冲洗") || orderName.contains("膀胱灌洗")) {
            return "RW11";
        }
        if (orderName.contains("膀胱造瘘")) {
            return "RW19";
        }

        // 基础护理类
        if ("口腔护理".equals(orderName)) return "RW13";
        if ("会阴护理".equals(orderName)) return "RW14";

        // 体征测量类
        if ("记24小时尿量".equals(orderName)) return "RW15";

        // 导管维护类
        if ("更换引流袋".equals(orderName)) return "RW16";
        if ("静脉置管冲洗".equals(orderName)) return "RW17";
        if ("动静脉置管护理".equals(orderName) || "深静脉置管护理".equals(orderName)) return "RW18";
        if ("输液港置管护理".equals(orderName) || orderName.contains("输液港")) return "RW20";
        if ("PICC置管护理".equals(orderName) || orderName.contains("PICC")) return "RW21";
        if ("肾周引流管护理".equals(orderName)) return "RW22";

        // 默认任务代码
        return "RW01";
    }
}
