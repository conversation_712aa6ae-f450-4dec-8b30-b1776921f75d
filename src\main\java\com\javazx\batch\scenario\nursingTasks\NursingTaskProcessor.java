package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import com.javazx.batch.vo.NurseTaskReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 护理任务处理器
 * 基于医嘱数据生成护理任务
 */
@Slf4j
@Component
public class NursingTaskProcessor implements ItemProcessor<NursingTaskOrderResp, NurseTaskReq> {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public NurseTaskReq process(NursingTaskOrderResp nursingTask) {
        if (nursingTask == null) {
            return null;
        }

        log.debug("处理护理任务: 病区={}, 床位={}, 医嘱={}",
                nursingTask.getBingquid(), nursingTask.getDangqiancw(), nursingTask.getYizhumc());

        // 根据医嘱信息生成护理任务
        return generateNurseTaskFromOrder(nursingTask);
    }

    /**
     * 根据医嘱信息生成护理任务
     */
    private NurseTaskReq generateNurseTaskFromOrder(NursingTaskOrderResp nursingTask) {
        NurseTaskReq taskReq = new NurseTaskReq();

        // 设置基本信息
        taskReq.setWardCode(nursingTask.getBingquid());
        taskReq.setBedNo(nursingTask.getDangqiancw());
        taskReq.setBedRemark(null);

        // 根据医嘱名称和频次确定护理任务代码
        String taskCode = generateTaskCode(nursingTask.getYizhumc(), nursingTask.getPinci());
        taskReq.setNurseTaskCode(taskCode);

        // 设置序号（可以使用医嘱ID的哈希值或其他逻辑）
        taskReq.setOrderNum(Long.valueOf(Math.abs(nursingTask.getYizhuid().hashCode()) % 10000));

        // 设置执行状态
        if (nursingTask.getYizhuzt() != null) {
            taskReq.setIsExecuted("3".equals(nursingTask.getYizhuzt()) ? 1L : 0L);
        } else {
            taskReq.setIsExecuted(0L);
        }

        // 设置执行时间
        LocalDateTime now = LocalDateTime.now();
        String[] executionTimes = determineExecutionTime(nursingTask.getYizhumc(), nursingTask.getPinci(), now);
        taskReq.setStartRemindTime(executionTimes[0]);
        taskReq.setEndRemindTime(executionTimes[1]);

        return taskReq;
    }

    /**
     * 生成任务代码 - 根据护理任务编码枚举表
     */
    private String generateTaskCode(String orderName, String frequency) {
        if (orderName == null) return "RW01";

        // 心电监护
        if (orderName.contains("心电监护") || orderName.contains("心电监测")) {
            return "RW01";
        }

        // 测血压 - 根据频次和时间点
        if (orderName.contains("测血压")) {
            if ("QD".equals(frequency)) return "RW02"; // 06:00 测血压
            if ("BID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            if ("TID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            if ("QID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            return "RW02"; // 默认06:00
        }

        // 测血糖 - 根据频次和时间点
        if ("葡萄糖测定".equals(orderName) || orderName.contains("测血糖")) {
            return "RW06"; // 默认06:00 测血糖
        }

        // 雾化吸入
        if (orderName.contains("雾化")) {
            return "RW10";
        }

        // 膀胱相关
        if ("膀胱持续冲洗".equals(orderName)) {
            return "RW12";
        }
        if (orderName.contains("膀胱冲洗") || orderName.contains("膀胱灌洗")) {
            return "RW11";
        }
        if (orderName.contains("膀胱造瘘")) {
            return "RW19";
        }

        // 基础护理类
        if ("口腔护理".equals(orderName)) return "RW13";
        if ("会阴护理".equals(orderName)) return "RW14";

        // 体征测量类
        if ("记24小时尿量".equals(orderName)) return "RW15";

        // 导管维护类
        if ("更换引流袋".equals(orderName)) return "RW16";
        if ("静脉置管冲洗".equals(orderName)) return "RW17";
        if ("动静脉置管护理".equals(orderName) || "深静脉置管护理".equals(orderName)) return "RW18";
        if ("输液港置管护理".equals(orderName) || orderName.contains("输液港")) return "RW20";
        if ("PICC置管护理".equals(orderName) || orderName.contains("PICC")) return "RW21";
        if ("肾周引流管护理".equals(orderName)) return "RW22";

        // 其他护理任务
        if ("留置导尿".equals(orderName) || orderName.contains("导尿")) return "RW46";
        if (orderName.contains("吸氧")) return "RW47";

        // 默认任务代码
        return "RW01";
    }

    /**
     * 确定执行时间
     */
    private String[] determineExecutionTime(String orderName, String frequency, LocalDateTime now) {
        String startTime = now.withHour(8).withMinute(0).withSecond(0).format(dateFormatter);
        String endTime = now.withHour(8).withMinute(30).withSecond(0).format(dateFormatter);

        // 心电监护 - 全天监护
        if (orderName != null && (orderName.contains("心电监护") || orderName.contains("心电监测"))) {
            startTime = now.withHour(0).withMinute(0).withSecond(0).format(dateFormatter);
            endTime = now.withHour(23).withMinute(59).withSecond(59).format(dateFormatter);
        }
        // 测血压时间安排
        else if (orderName != null && orderName.contains("测血压")) {
            if ("QD".equals(frequency)) {
                startTime = now.withHour(6).withMinute(0).withSecond(0).format(dateFormatter);
                endTime = now.withHour(6).withMinute(30).withSecond(0).format(dateFormatter);
            }
        }
        // 其他护理任务使用默认时间

        return new String[]{startTime, endTime};
    }
}
