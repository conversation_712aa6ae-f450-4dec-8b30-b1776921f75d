package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.po.GyChuangwei;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.vo.NurseTaskReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 护理任务数据处理器
 * 将床位信息转换为护理任务请求对象用于同步
 */
@Component
public class NursingTaskProcessor implements ItemProcessor<GyChuangwei, List<NurseTaskReq>> {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final NursingTaskDataService nursingTaskDataService;

    public NursingTaskProcessor(NursingTaskDataService nursingTaskDataService) {
        this.nursingTaskDataService = nursingTaskDataService;
    }

    @Override
    public List<NurseTaskReq> process(GyChuangwei gyChuangwei) {
        if (gyChuangwei == null) {
            return null;
        }

        try {
            // 生成该床位的护理任务列表
            List<NurseTaskReq> nursingTasks = generateNursingTasks(gyChuangwei);

            if (nursingTasks.isEmpty()) {
                log.debug("床位 {} 没有生成护理任务，跳过处理", gyChuangwei.getCHUANGWEIID());
                return null;
            }

            log.debug("完成床位 {} 护理任务处理，生成 {} 个任务", 
                     gyChuangwei.getCHUANGWEIID(), nursingTasks.size());
            return nursingTasks;
            
        } catch (Exception e) {
            log.error("处理床位护理任务失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成护理任务列表
     */
    private List<NurseTaskReq> generateNursingTasks(GyChuangwei bed) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        
        // 获取床位基本信息
        String bedNo = bed.getCHUANGWEIID();
        String wardCode = bed.getBINGQUID();
        String bedRemark = getBedRemark(bed);
        
        // 生成基础护理任务
        tasks.addAll(generateBasicNursingTasks(bedNo, wardCode, bedRemark));
        
        // 如果床位有患者，生成患者特定护理任务
/*        if (StringUtils.hasLength(bed.getBINGRENZYID())) {
            ZyBingrenxx patient = nursingTaskDataService.selectPatientById(bed.getBINGRENZYID());
            if (patient != null) {
                tasks.addAll(generatePatientSpecificTasks(bedNo, wardCode, bedRemark, patient));
            }
        }*/
        
        return tasks;
    }

    /**
     * 生成基础护理任务（每个床位都有的常规任务）
     */
    private List<NurseTaskReq> generateBasicNursingTasks(String bedNo, String wardCode, String bedRemark) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 1. 晨间护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "MORNING_CARE", 1L,
            now.withHour(7).withMinute(0).withSecond(0),
            now.withHour(9).withMinute(0).withSecond(0),
            0L
        ));
        
        // 2. 午间护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "NOON_CARE", 2L,
            now.withHour(12).withMinute(0).withSecond(0),
            now.withHour(14).withMinute(0).withSecond(0),
            0L
        ));
        
        // 3. 晚间护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "EVENING_CARE", 3L,
            now.withHour(18).withMinute(0).withSecond(0),
            now.withHour(20).withMinute(0).withSecond(0),
            0L
        ));
        
        // 4. 夜间巡视任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark,
            "NIGHT_PATROL", 4L,
            now.withHour(22).withMinute(0).withSecond(0),
            now.plusDays(1).withHour(6).withMinute(0).withSecond(0),
            0L
        ));
        
        return tasks;
    }

    /**
     * 生成患者特定护理任务
     */
    private List<NurseTaskReq> generatePatientSpecificTasks(String bedNo, String wardCode, String bedRemark, ZyBingrenxx patient) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        // 根据患者状态生成特定任务
        
        // 5. 生命体征监测任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark + " - " + patient.getXINGMING(),
            "VITAL_SIGNS", 5L,
            now.withHour(8).withMinute(0).withSecond(0),
            now.withHour(8).withMinute(30).withSecond(0),
            0L
        ));
        
        // 6. 用药护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark + " - " + patient.getXINGMING(),
            "MEDICATION_CARE", 6L,
            now.withHour(9).withMinute(0).withSecond(0),
            now.withHour(9).withMinute(30).withSecond(0),
            0L
        ));
        
        // 7. 饮食护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark + " - " + patient.getXINGMING(),
            "DIET_CARE", 7L,
            now.withHour(11).withMinute(30).withSecond(0),
            now.withHour(12).withMinute(30).withSecond(0),
            0L
        ));
        
        // 8. 康复护理任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark + " - " + patient.getXINGMING(),
            "REHABILITATION", 8L,
            now.withHour(15).withMinute(0).withSecond(0),
            now.withHour(16).withMinute(0).withSecond(0),
            0L
        ));
        
        return tasks;
    }

    /**
     * 创建护理任务对象
     */
    private NurseTaskReq createNurseTask(String bedNo, String wardCode, String bedRemark,
                                        String taskCode, Long orderNum,
                                        LocalDateTime startTime, LocalDateTime endTime,
                                        Long isExecuted) {
        NurseTaskReq task = new NurseTaskReq();
        task.setBedNo(bedNo);
        task.setWardCode(wardCode);
        task.setBedRemark(bedRemark);
        task.setNurseTaskCode(taskCode);
        task.setOrderNum(orderNum);
        task.setStartRemindTime(startTime.format(dateFormatter));
        task.setEndRemindTime(endTime.format(dateFormatter));
        task.setIsExecuted(isExecuted);
        return task;
    }

    /**
     * 获取床位备注信息
     */
    private String getBedRemark(GyChuangwei bed) {
        StringBuilder remark = new StringBuilder();
        
        // 床位类型
        if ("1".equals(bed.getCHUANGWEILX())) {
            remark.append("普通床位");
        } else if ("2".equals(bed.getCHUANGWEILX())) {
            remark.append("特殊床位");
        } else {
            remark.append("床位");
        }
        
        // 床位状态
        if (StringUtils.hasLength(bed.getBINGRENZYID())) {
            remark.append(" - 已占用");
        } else {
            remark.append(" - 空床");
        }
        
        return remark.toString();
    }
}
