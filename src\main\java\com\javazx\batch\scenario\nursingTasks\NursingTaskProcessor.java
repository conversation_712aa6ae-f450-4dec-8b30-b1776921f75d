package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.vo.NurseTaskReq;
import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 护理任务处理器
 * 基于医嘱数据生成护理任务
 */
@Slf4j
@Component
public class NursingTaskProcessor implements ItemProcessor<NursingTaskOrderResp, NurseTaskReq> {

    @Autowired
    private NursingTaskDataService nursingTaskDataService;

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public NurseTaskReq process(NursingTaskOrderResp orderVO) {
        if (orderVO == null || !StringUtils.hasText(orderVO.getDangqiancw())) {
            return null;
        }

        List<NurseTaskReq> tasks = new ArrayList<>();
        String bedNo = orderVO.getDangqiancw();
        String wardCode = orderVO.getBingquid();
        String bedRemark = "床位:" + bedNo;

        // 根据医嘱类型生成对应的护理任务
        String orderName = orderVO.getYizhumc();
        String frequency = orderVO.getPinci();
        String adminMethod = orderVO.getGeiyaofs();

        if (StringUtils.hasText(orderName)) {
            // 1. 心电监护任务
            if (isECGMonitoringOrder(orderName)) {
                tasks.addAll(generateECGMonitoringTasks(bedNo, wardCode, bedRemark, orderName));
            }
            // 2. 测血压任务
            else if (isBloodPressureOrder(orderName)) {
                tasks.addAll(generateBloodPressureTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 3. 测血糖任务
            else if (isBloodGlucoseOrder(orderName)) {
                tasks.addAll(generateBloodGlucoseTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 4. 雾化吸入任务
            else if (isNebulizationOrder(orderName, adminMethod)) {
                tasks.addAll(generateNebulizationTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 5. 膀胱冲洗任务
            else if (isBladderIrrigationOrder(orderName)) {
                tasks.addAll(generateBladderIrrigationTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 6. 基础护理任务
            else if (isBasicCareOrder(orderName)) {
                tasks.addAll(generateBasicCareTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 7. 导管维护任务
            else if (isCatheterCareOrder(orderName)) {
                tasks.addAll(generateCatheterCareTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 8. 体征测量任务
            else if (isVitalSignsOrder(orderName)) {
                tasks.addAll(generateVitalSignsTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
            // 9. 吸氧任务
            else if (isOxygenTherapyOrder(orderName)) {
                tasks.addAll(generateOxygenTherapyTasks(bedNo, wardCode, bedRemark, orderName, frequency));
            }
        }

        return tasks.isEmpty() ? null : tasks;
    }

    /**
     * 判断是否为心电监护医嘱
     */
    private boolean isECGMonitoringOrder(String orderName) {
        return orderName.contains("心电监护") || orderName.contains("心电监测");
    }

    /**
     * 判断是否为测血压医嘱
     */
    private boolean isBloodPressureOrder(String orderName) {
        return orderName.contains("测血压") || orderName.contains("血压测量");
    }

    /**
     * 判断是否为测血糖医嘱
     */
    private boolean isBloodGlucoseOrder(String orderName) {
        return orderName.contains("测血糖") || orderName.contains("血糖监测");
    }

    /**
     * 判断是否为雾化吸入医嘱
     */
    private boolean isNebulizationOrder(String orderName, String adminMethod) {
        return orderName.contains("雾化") || "雾化吸入".equals(adminMethod);
    }

    /**
     * 判断是否为膀胱冲洗医嘱
     */
    private boolean isBladderIrrigationOrder(String orderName) {
        return orderName.contains("膀胱冲洗") || orderName.contains("膀胱灌洗");
    }

    /**
     * 判断是否为基础护理医嘱
     */
    private boolean isBasicCareOrder(String orderName) {
        return orderName.contains("翻身") ||
               orderName.contains("拍背") ||
               orderName.contains("口腔护理") ||
               orderName.contains("皮肤护理") ||
               orderName.contains("会阴护理");
    }

    /**
     * 判断是否为导管维护医嘱
     */
    private boolean isCatheterCareOrder(String orderName) {
        return orderName.contains("导尿管护理") ||
               orderName.contains("胃管护理") ||
               orderName.contains("引流管护理") ||
               orderName.contains("PICC护理") ||
               orderName.contains("中心静脉导管护理");
    }

    /**
     * 判断是否为体征测量医嘱
     */
    private boolean isVitalSignsOrder(String orderName) {
        return orderName.contains("测体温") ||
               orderName.contains("测脉搏") ||
               orderName.contains("测呼吸") ||
               orderName.contains("生命体征");
    }

    /**
     * 判断是否为吸氧医嘱
     */
    private boolean isOxygenTherapyOrder(String orderName) {
        return orderName.contains("吸氧") ||
               orderName.contains("氧疗") ||
               "鼻导管吸氧".equals(orderName) ||
               "面罩吸氧".equals(orderName);
    }

    /**
     * 生成心电监护任务
     */
    private List<NurseTaskReq> generateECGMonitoringTasks(String bedNo, String wardCode, String bedRemark, String orderName) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark + " - " + orderName,
            "ECG_MONITORING", 1L,
            now.withHour(0).withMinute(0).withSecond(0),
            now.withHour(23).withMinute(59).withSecond(59),
            0L
        ));
        
        return tasks;
    }

    /**
     * 生成测血压任务
     */
    private List<NurseTaskReq> generateBloodPressureTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 2L;

        if ("QD".equals(frequency)) {
            // 每日一次 - 06:00
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QD)",
                "BLOOD_PRESSURE_QD", orderNum,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("BID".equals(frequency)) {
            // 每日两次 - 06:00, 16:00
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-06:00)",
                "BLOOD_PRESSURE_BID_06", orderNum++,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-16:00)",
                "BLOOD_PRESSURE_BID_16", orderNum,
                now.withHour(16).withMinute(0).withSecond(0),
                now.withHour(16).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("TID".equals(frequency)) {
            // 每日三次 - 06:00, 14:00, 18:00
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-06:00)",
                "BLOOD_PRESSURE_TID_06", orderNum++,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-14:00)",
                "BLOOD_PRESSURE_TID_14", orderNum++,
                now.withHour(14).withMinute(0).withSecond(0),
                now.withHour(14).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-18:00)",
                "BLOOD_PRESSURE_TID_18", orderNum,
                now.withHour(18).withMinute(0).withSecond(0),
                now.withHour(18).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("QID".equals(frequency)) {
            // 每日四次 - 06:00, 10:00, 14:00, 18:00
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QID-06:00)",
                "BLOOD_PRESSURE_QID_06", orderNum++,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QID-10:00)",
                "BLOOD_PRESSURE_QID_10", orderNum++,
                now.withHour(10).withMinute(0).withSecond(0),
                now.withHour(10).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QID-14:00)",
                "BLOOD_PRESSURE_QID_14", orderNum++,
                now.withHour(14).withMinute(0).withSecond(0),
                now.withHour(14).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QID-18:00)",
                "BLOOD_PRESSURE_QID_18", orderNum,
                now.withHour(18).withMinute(0).withSecond(0),
                now.withHour(18).withMinute(30).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成测血糖任务
     */
    private List<NurseTaskReq> generateBloodGlucoseTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 10L;

        if ("QD".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QD)",
                "BLOOD_GLUCOSE_QD", orderNum,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("BID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-06:00)",
                "BLOOD_GLUCOSE_BID_06", orderNum++,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-18:00)",
                "BLOOD_GLUCOSE_BID_18", orderNum,
                now.withHour(18).withMinute(0).withSecond(0),
                now.withHour(18).withMinute(30).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成雾化吸入任务
     */
    private List<NurseTaskReq> generateNebulizationTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 15L;

        if ("BID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-08:00)",
                "NEBULIZATION_BID_08", orderNum++,
                now.withHour(8).withMinute(0).withSecond(0),
                now.withHour(8).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-20:00)",
                "NEBULIZATION_BID_20", orderNum,
                now.withHour(20).withMinute(0).withSecond(0),
                now.withHour(20).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("TID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-08:00)",
                "NEBULIZATION_TID_08", orderNum++,
                now.withHour(8).withMinute(0).withSecond(0),
                now.withHour(8).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-14:00)",
                "NEBULIZATION_TID_14", orderNum++,
                now.withHour(14).withMinute(0).withSecond(0),
                now.withHour(14).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-20:00)",
                "NEBULIZATION_TID_20", orderNum,
                now.withHour(20).withMinute(0).withSecond(0),
                now.withHour(20).withMinute(30).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成膀胱冲洗任务
     */
    private List<NurseTaskReq> generateBladderIrrigationTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 20L;

        if ("BID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-08:00)",
                "BLADDER_IRRIGATION_BID_08", orderNum++,
                now.withHour(8).withMinute(0).withSecond(0),
                now.withHour(9).withMinute(0).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-20:00)",
                "BLADDER_IRRIGATION_BID_20", orderNum,
                now.withHour(20).withMinute(0).withSecond(0),
                now.withHour(21).withMinute(0).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成基础护理任务
     */
    private List<NurseTaskReq> generateBasicCareTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 25L;

        if ("BID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-09:00)",
                "BASIC_CARE_BID_09", orderNum++,
                now.withHour(9).withMinute(0).withSecond(0),
                now.withHour(9).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-21:00)",
                "BASIC_CARE_BID_21", orderNum,
                now.withHour(21).withMinute(0).withSecond(0),
                now.withHour(21).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("TID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-09:00)",
                "BASIC_CARE_TID_09", orderNum++,
                now.withHour(9).withMinute(0).withSecond(0),
                now.withHour(9).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-15:00)",
                "BASIC_CARE_TID_15", orderNum++,
                now.withHour(15).withMinute(0).withSecond(0),
                now.withHour(15).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-21:00)",
                "BASIC_CARE_TID_21", orderNum,
                now.withHour(21).withMinute(0).withSecond(0),
                now.withHour(21).withMinute(30).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成导管维护任务
     */
    private List<NurseTaskReq> generateCatheterCareTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 30L;

        if ("QD".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QD)",
                "CATHETER_CARE_QD", orderNum,
                now.withHour(10).withMinute(0).withSecond(0),
                now.withHour(10).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("BID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-10:00)",
                "CATHETER_CARE_BID_10", orderNum++,
                now.withHour(10).withMinute(0).withSecond(0),
                now.withHour(10).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-22:00)",
                "CATHETER_CARE_BID_22", orderNum,
                now.withHour(22).withMinute(0).withSecond(0),
                now.withHour(22).withMinute(30).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成体征测量任务
     */
    private List<NurseTaskReq> generateVitalSignsTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 35L;

        if ("QD".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(QD)",
                "VITAL_SIGNS_QD", orderNum,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("BID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-06:00)",
                "VITAL_SIGNS_BID_06", orderNum++,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(BID-18:00)",
                "VITAL_SIGNS_BID_18", orderNum,
                now.withHour(18).withMinute(0).withSecond(0),
                now.withHour(18).withMinute(30).withSecond(0),
                0L
            ));
        } else if ("TID".equals(frequency)) {
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-06:00)",
                "VITAL_SIGNS_TID_06", orderNum++,
                now.withHour(6).withMinute(0).withSecond(0),
                now.withHour(6).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-14:00)",
                "VITAL_SIGNS_TID_14", orderNum++,
                now.withHour(14).withMinute(0).withSecond(0),
                now.withHour(14).withMinute(30).withSecond(0),
                0L
            ));
            tasks.add(createNurseTask(
                bedNo, wardCode, bedRemark + " - " + orderName + "(TID-22:00)",
                "VITAL_SIGNS_TID_22", orderNum,
                now.withHour(22).withMinute(0).withSecond(0),
                now.withHour(22).withMinute(30).withSecond(0),
                0L
            ));
        }

        return tasks;
    }

    /**
     * 生成吸氧任务
     */
    private List<NurseTaskReq> generateOxygenTherapyTasks(String bedNo, String wardCode, String bedRemark, String orderName, String frequency) {
        List<NurseTaskReq> tasks = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        Long orderNum = 40L;

        // 吸氧通常是持续性的，生成全天任务
        tasks.add(createNurseTask(
            bedNo, wardCode, bedRemark + " - " + orderName,
            "OXYGEN_THERAPY", orderNum,
            now.withHour(0).withMinute(0).withSecond(0),
            now.withHour(23).withMinute(59).withSecond(59),
            0L
        ));

        return tasks;
    }

    /**
     * 创建护理任务对象
     */
    private NurseTaskReq createNurseTask(String bedNo, String wardCode, String bedRemark,
                                        String taskCode, Long orderNum,
                                        LocalDateTime startTime, LocalDateTime endTime,
                                        Long isExecuted) {
        NurseTaskReq task = new NurseTaskReq();
        task.setBedNo(bedNo);
        task.setWardCode(wardCode);
        task.setBedRemark(bedRemark);
        task.setNurseTaskCode(taskCode);
        task.setOrderNum(orderNum);
        task.setStartRemindTime(startTime.format(dateFormatter));
        task.setEndRemindTime(endTime.format(dateFormatter));
        task.setIsExecuted(isExecuted);
        return task;
    }
}
