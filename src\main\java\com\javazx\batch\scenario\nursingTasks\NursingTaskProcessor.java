package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.util.NursingInfoUtil;
import com.javazx.batch.vo.NurseTaskReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 护理任务处理器
 * 基于医嘱数据生成护理任务
 */
@Slf4j
@Component
public class NursingTaskProcessor implements ItemProcessor<String, List<NurseTaskReq>> {

    private final NursingTaskDataService nursingTaskDataService;

    private final NursingInfoUtil nursingInfoUtil;

    public NursingTaskProcessor(NursingTaskDataService nursingTaskDataService, NursingInfoUtil nursingInfoUtil) {
        this.nursingTaskDataService = nursingTaskDataService;
        this.nursingInfoUtil = nursingInfoUtil;
    }

    @Override
    public List<NurseTaskReq> process(String wardId) {
        if (wardId == null || wardId.isEmpty()) {
            return null;
        }

        log.info("开始处理病区 {} 的护理任务", wardId);

        // 统计病区内所有护理任务
        HashMap<String, List<NursingTaskOrderResp>> wardOrders = nursingInfoUtil.collectAllNursingTaskOrders(wardId);

        if (wardOrders.isEmpty()) {
            log.info("病区内没有护理任务数据");
            return null;
        }

        return generateMultiWardNursingTaskSummary(wardId, wardOrders);
    }

    /**
     * 收集病区内所有护理任务医嘱数据
     */
    private HashMap<String, List<NursingTaskOrderResp>> collectAllNursingTaskOrders(String wardId) {
        HashMap<String, List<NursingTaskOrderResp>> allOrders = new HashMap<>();
        try {
            // RW01 心电监护
            allOrders.put("RW01",nursingTaskDataService.selectECGMonitoringOrders(wardId));

            // RW02-RW05 测血压医嘱（4个时间点）
            allOrders.put("RW02", nursingTaskDataService.selectBloodPressure06Orders(wardId));
            allOrders.put("RW03", nursingTaskDataService.selectBloodPressure10Orders(wardId));
            allOrders.put("RW04", nursingTaskDataService.selectBloodPressure14Orders(wardId));
            allOrders.put("RW05", nursingTaskDataService.selectBloodPressure18Orders(wardId));

            // RW06-RW09 测血糖医嘱（4个时间点）
            allOrders.put("RW06", nursingTaskDataService.selectBloodGlucose06Orders(wardId));
            allOrders.put("RW07", nursingTaskDataService.selectBloodGlucose10Orders(wardId));
            allOrders.put("RW08", nursingTaskDataService.selectBloodGlucose16Orders(wardId));
            allOrders.put("RW09", nursingTaskDataService.selectBloodGlucose20Orders(wardId));

            // RW10 雾化吸入
            allOrders.put("RW10", nursingTaskDataService.selectNebulizationOrders(wardId));

            // RW11 膀胱冲洗
            allOrders.put("RW11", nursingTaskDataService.selectBladderIrrigationOrders(wardId));

            // RW12 膀胱持续冲洗
            allOrders.put("RW12", nursingTaskDataService.selectContinuousBladderIrrigationOrders(wardId));

            // RW13 口腔护理
            allOrders.put("RW13", nursingTaskDataService.selectOralCareOrders(wardId));

            // RW14 会阴护理
            allOrders.put("RW14", nursingTaskDataService.selectPerinealCareOrders(wardId));

            // RW15 记24小时尿量
            allOrders.put("RW15", nursingTaskDataService.selectUrineVolumeRecordOrders(wardId));

            // RW16 更换引流袋
            allOrders.put("RW16", nursingTaskDataService.selectDrainageBagChangeOrders(wardId));

            // RW17 静脉置管冲洗
            allOrders.put("RW17", nursingTaskDataService.selectVenousCatheterFlushOrders(wardId));

            // RW18 深静脉置管护理
            allOrders.put("RW18", nursingTaskDataService.selectDeepVenousCatheterCareOrders(wardId));

            // RW19 膀胱造瘘 (暂无对应的service方法，跳过)
            // TODO: 需要添加膀胱造瘘医嘱查询方法

            // RW20 输液港
            allOrders.put("RW20", nursingTaskDataService.selectInfusionPortOrders(wardId));

            // RW21 PICC置管
            allOrders.put("RW21", nursingTaskDataService.selectPICCCatheterOrders(wardId));

            // RW22 肾周引流管护理
            allOrders.put("RW22", nursingTaskDataService.selectPerinephricDrainageCareOrders(wardId));

            // RW46 留置导尿
            allOrders.put("RW46", nursingTaskDataService.selectUrinaryCatheterizationOrders(wardId));

            // RW47 吸氧
            allOrders.put("RW47", nursingTaskDataService.selectOxygenTherapyOrders(wardId));

        } catch (Exception e) {
            log.error("收集病区 {} 护理任务数据时发生错误: {}", wardId, e.getMessage(), e);
        }

        log.info("病区 {} 共收集到 {} 条护理任务医嘱数据", wardId, allOrders.size());
        return allOrders;
    }

    /**
     * 生成多病区护理任务汇总
     */
    @SuppressWarnings("rawtypes")
    private List<NurseTaskReq> generateMultiWardNursingTaskSummary(String wardId, HashMap<String, List<NursingTaskOrderResp>> nursingTaskList) {
        List<NurseTaskReq> taskSummaryList = new ArrayList<>();
        nursingTaskList.entrySet().forEach(entry -> {
            String taskCode = entry.getKey();
            List<NursingTaskOrderResp> nursingTasks = entry.getValue();
            AtomicInteger count = new AtomicInteger(0);
            for (NursingTaskOrderResp nursingTask : nursingTasks) {
                NurseTaskReq taskSummary = new NurseTaskReq();
                taskSummary.setWardCode(wardId);

                // 生成病区列表描述
                taskSummary.setBedNo(nursingTask.getDangqiancw());
                taskSummary.setBedRemark(null);

                // 根据医嘱数据确定主要护理任务类型
                taskSummary.setNurseTaskCode(taskCode);
                taskSummary.setOrderNum((long) count.incrementAndGet());

                // 设置执行时间
                taskSummary.setStartRemindTime(null);
                taskSummary.setEndRemindTime(null);
                if (nursingTask.getYizhuzt() != null) {
                    taskSummary.setIsExecuted("3".equals(nursingTask.getYizhuzt()) ? 1L : 0L);
                }
                taskSummaryList.add(taskSummary);
            }
        });
        return taskSummaryList;
    }
}
