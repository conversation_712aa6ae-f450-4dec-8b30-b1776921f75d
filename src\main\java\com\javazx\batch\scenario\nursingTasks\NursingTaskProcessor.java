package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.vo.NurseTaskReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 护理任务处理器
 * 基于医嘱数据生成护理任务
 */
@Slf4j
@Component
public class NursingTaskProcessor implements ItemProcessor<List, NurseTaskReq> {

    @Autowired
    private NursingTaskDataService nursingTaskDataService;

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public NurseTaskReq process(List wardIds) {
        if (wardIds == null || wardIds.isEmpty()) {
            return null;
        }

        // 获取第一个病区ID进行处理
        String wardId = (String) wardIds.get(0);
        log.info("开始处理病区 {} 的护理任务数据", wardId);

        // 统计病区内所有护理任务
        List<NursingTaskOrderResp> allOrders = collectAllNursingTaskOrders(wardId);

        if (allOrders.isEmpty()) {
            log.info("病区 {} 没有护理任务数据", wardId);
            return null;
        }

        // 根据护理任务编码进行统计和转化
        return generateNursingTaskSummary(wardId, allOrders);
    }

    /**
     * 收集病区内所有护理任务医嘱数据
     */
    private List<NursingTaskOrderResp> collectAllNursingTaskOrders(String wardId) {
        List<NursingTaskOrderResp> allOrders = new ArrayList<>();

        try {
            // RW01 心电监护
            allOrders.addAll(nursingTaskDataService.selectECGMonitoringOrders(wardId));

            // RW02-RW05 测血压医嘱（4个时间点）
            allOrders.addAll(nursingTaskDataService.selectBloodPressure06Orders(wardId));
            allOrders.addAll(nursingTaskDataService.selectBloodPressure10Orders(wardId));
            allOrders.addAll(nursingTaskDataService.selectBloodPressure14Orders(wardId));
            allOrders.addAll(nursingTaskDataService.selectBloodPressure18Orders(wardId));

            // RW06-RW09 测血糖医嘱（4个时间点）
            allOrders.addAll(nursingTaskDataService.selectBloodGlucose06Orders(wardId));
            allOrders.addAll(nursingTaskDataService.selectBloodGlucose10Orders(wardId));
            allOrders.addAll(nursingTaskDataService.selectBloodGlucose16Orders(wardId));
            allOrders.addAll(nursingTaskDataService.selectBloodGlucose20Orders(wardId));

            // RW10 雾化吸入
            allOrders.addAll(nursingTaskDataService.selectNebulizationOrders(wardId));

            // RW11 膀胱冲洗
            allOrders.addAll(nursingTaskDataService.selectBladderIrrigationOrders(wardId));

            // RW12 膀胱持续冲洗
            allOrders.addAll(nursingTaskDataService.selectContinuousBladderIrrigationOrders(wardId));

            // RW13 口腔护理
            allOrders.addAll(nursingTaskDataService.selectOralCareOrders(wardId));

            // RW14 会阴护理
            allOrders.addAll(nursingTaskDataService.selectPerinealCareOrders(wardId));

            // RW15 记24小时尿量
            allOrders.addAll(nursingTaskDataService.selectUrineVolumeRecordOrders(wardId));

            // RW16 更换引流袋
            allOrders.addAll(nursingTaskDataService.selectDrainageBagChangeOrders(wardId));

            // RW17 静脉置管冲洗
            allOrders.addAll(nursingTaskDataService.selectVenousCatheterFlushOrders(wardId));

            // RW18 深静脉置管护理
            allOrders.addAll(nursingTaskDataService.selectDeepVenousCatheterCareOrders(wardId));

            // RW19 膀胱造瘘 (暂无对应的service方法，跳过)
            // TODO: 需要添加膀胱造瘘医嘱查询方法

            // RW20 输液港
            allOrders.addAll(nursingTaskDataService.selectInfusionPortOrders(wardId));

            // RW21 PICC置管
            allOrders.addAll(nursingTaskDataService.selectPICCCatheterOrders(wardId));

            // RW22 肾周引流管护理
            allOrders.addAll(nursingTaskDataService.selectPerinephricDrainageCareOrders(wardId));

        } catch (Exception e) {
            log.error("收集病区 {} 护理任务数据时发生错误: {}", wardId, e.getMessage(), e);
        }

        log.info("病区 {} 共收集到 {} 条护理任务医嘱数据", wardId, allOrders.size());
        return allOrders;
    }

    /**
     * 根据护理任务编码生成护理任务汇总
     */
    private NurseTaskReq generateNursingTaskSummary(String wardId, List<NursingTaskOrderResp> allOrders) {
        // 统计各类护理任务数量
        int totalTasks = allOrders.size();

        // 创建护理任务汇总对象
        NurseTaskReq taskSummary = new NurseTaskReq();
        taskSummary.setWardCode(wardId);
        taskSummary.setBedNo("病区汇总");
        taskSummary.setBedRemark("病区 " + wardId + " 护理任务汇总，共 " + totalTasks + " 项任务");

        // 根据医嘱数据确定主要护理任务类型
        String primaryTaskCode = determinePrimaryTaskCode(allOrders);
        taskSummary.setNurseTaskCode(primaryTaskCode);
        taskSummary.setOrderNum(Long.valueOf(totalTasks));

        // 设置执行时间
        LocalDateTime now = LocalDateTime.now();
        taskSummary.setStartRemindTime(now.withHour(8).withMinute(0).withSecond(0).format(dateFormatter));
        taskSummary.setEndRemindTime(now.withHour(18).withMinute(0).withSecond(0).format(dateFormatter));
        taskSummary.setIsExecuted(0L);

        log.info("病区 {} 护理任务汇总完成: 主要任务类型={}, 总任务数={}", wardId, primaryTaskCode, totalTasks);
        return taskSummary;
    }

    /**
     * 确定主要护理任务代码
     */
    private String determinePrimaryTaskCode(List<NursingTaskOrderResp> orders) {
        if (orders.isEmpty()) {
            return "RW01"; // 默认心电监护
        }

        // 统计各类护理任务数量，返回数量最多的任务类型
        java.util.Map<String, Integer> taskCounts = new java.util.HashMap<>();

        for (NursingTaskOrderResp order : orders) {
            String orderName = order.getYizhumc();
            String frequency = order.getPinci();
            String taskCode = generateTaskCode(orderName, frequency);
            taskCounts.put(taskCode, taskCounts.getOrDefault(taskCode, 0) + 1);
        }

        // 找出数量最多的任务类型
        return taskCounts.entrySet().stream()
                .max(java.util.Map.Entry.comparingByValue())
                .map(java.util.Map.Entry::getKey)
                .orElse("RW01");
    }

    /**
     * 生成任务代码 - 根据护理任务编码枚举表
     */
    private String generateTaskCode(String orderName, String frequency) {
        // 心电监护
        if (orderName.contains("心电监护") || orderName.contains("心电监测")) {
            return "RW01";
        }

        // 测血压 - 根据频次和时间点
        if (orderName.contains("测血压")) {
            if ("QD".equals(frequency)) return "RW02"; // 06:00 测血压
            if ("BID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            if ("TID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            if ("QID".equals(frequency)) return "RW02"; // 06:00 测血压 (第一次)
            return "RW02"; // 默认06:00
        }

        // 测血糖 - 根据频次和时间点
        if ("葡萄糖测定".equals(orderName) || orderName.contains("测血糖")) {
            return "RW06"; // 默认06:00 测血糖
        }

        // 雾化吸入
        if (orderName.contains("雾化")) {
            return "RW10";
        }

        // 膀胱相关
        if ("膀胱持续冲洗".equals(orderName)) {
            return "RW12";
        }
        if (orderName.contains("膀胱冲洗") || orderName.contains("膀胱灌洗")) {
            return "RW11";
        }
        if (orderName.contains("膀胱造瘘")) {
            return "RW19";
        }

        // 基础护理类
        if ("口腔护理".equals(orderName)) return "RW13";
        if ("会阴护理".equals(orderName)) return "RW14";

        // 体征测量类
        if ("记24小时尿量".equals(orderName)) return "RW15";

        // 导管维护类
        if ("更换引流袋".equals(orderName)) return "RW16";
        if ("静脉置管冲洗".equals(orderName)) return "RW17";
        if ("动静脉置管护理".equals(orderName) || "深静脉置管护理".equals(orderName)) return "RW18";
        if ("输液港置管护理".equals(orderName) || orderName.contains("输液港")) return "RW20";
        if ("PICC置管护理".equals(orderName) || orderName.contains("PICC")) return "RW21";
        if ("肾周引流管护理".equals(orderName)) return "RW22";

        // 默认任务代码
        return "RW01";
    }
}
