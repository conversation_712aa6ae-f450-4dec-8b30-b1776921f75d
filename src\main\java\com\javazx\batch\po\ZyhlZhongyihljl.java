package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 * @TableName ZYHL_ZHONGYIHLJL
 */
@TableName(value ="ZYHL_ZHONGYIHLJL")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class <PERSON><PERSON>hlZhongyihljl implements Serializable {
    /**
     * 
     */
    @TableField(value = "ZHONGYIHLJLID")
    private String ZHONGYIHLJLID;

    /**
     * 病种id
     */
    @TableField(value = "BINGZHONGID")
    private String BINGZHONGID;

    /**
     * 中医证型id，逗号分隔
     */
    @TableField(value = "ZHONGYIZXID")
    private String ZHONGYIZXID;

    /**
     * 中医病症id，逗号分隔
     */
    @TableField(value = "ZHONGYIBZID")
    private String ZHONGYIBZID;

    /**
     * 效果评价
     */
    @TableField(value = "XIAOGUOPJ")
    private String XIAOGUOPJ;

    /**
     * 0 护理中，1 结束
     */
    @TableField(value = "ZHUANGTAI")
    private String ZHUANGTAI;

    /**
     * 记录时间
     */
    @TableField(value = "JILUSJ")
    private LocalDateTime JILUSJ;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 是否纳入中医临床路径 1 是
     */
    @TableField(value = "NARULJBZ")
    private String NARULJBZ;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 责任护士签名
     */
    @TableField(value = "ZERENHSQM")
    private String ZERENHSQM;

    /**
     * 上级护士或护士长签名
     */
    @TableField(value = "SHANGJIHSQM")
    private String SHANGJIHSQM;

    /**
     * 方案完成时间
     */
    @TableField(value = "FANGANWCSJ")
    private LocalDateTime FANGANWCSJ;

    /**
     * 对本病中医护理方案的评价
     */
    @TableField(value = "HULIFAPJ")
    private String HULIFAPJ;

    /**
     * 评价人(责任护士)签名
     */
    @TableField(value = "PINGJIARENQM")
    private String PINGJIARENQM;

    /**
     * 技术职称
     */
    @TableField(value = "JISHUZC")
    private String JISHUZC;

    /**
     * 上级护士或护士长签名
     */
    @TableField(value = "SHANGJIHSQM2")
    private String SHANGJIHSQM2;

    /**
     * 健康指导
     */
    @TableField(value = "JIANKANGZD")
    private String JIANKANGZD;

    /**
     * 健康指导总分
     */
    @TableField(value = "JIANKANGZDFS")
    private String JIANKANGZDFS;

    /**
     * 疾病知识掌握
     */
    @TableField(value = "JIBINGZSZW")
    private String JIBINGZSZW;

    /**
     * 饮食调护
     */
    @TableField(value = "YINSHITH")
    private String YINSHITH;

    /**
     * 药物知识
     */
    @TableField(value = "YAOWUZS")
    private String YAOWUZS;

    /**
     * 情致调护
     */
    @TableField(value = "QINGZHITH")
    private String QINGZHITH;

    /**
     * 新增的中医证型(不属于原方案 此字段只参与显示)
     */
    @TableField(value = "XINZENGZHONGYIZXID")
    private String XINZENGZHONGYIZXID;

    /**
     * 新增的中医病症(不属于原方案 此字段只参与显示)
     */
    @TableField(value = "XINZENGZHONGYIBZID")
    private String XINZENGZHONGYIBZID;

    /**
     * 剔除原因 1. 转科 2. 诊断不符 3. 时间不足72小时
     */
    @TableField(value = "TICHUYY")
    private String TICHUYY;

    /**
     * 记录病区
     */
    @TableField(value = "JILUBQ")
    private String JILUBQ;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}