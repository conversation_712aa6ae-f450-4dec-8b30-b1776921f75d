package com.javazx.batch.scenario.exam;

import com.javazx.batch.mapper.resp.YjShenqingdanResp;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.CheckDataService;
import com.javazx.batch.vo.PatientCheckSyncRequest;
import com.javazx.batch.vo.PatientWithCheckReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检查数据同步场景
 * 从数据库读取检查申请单数据并同步到外部系统
 */
@Component
public class CheckScenario extends AbstractBatchScenario<YjShenqingdanResp, PatientWithCheckReq> {

    private static final Logger log = LoggerFactory.getLogger(CheckScenario.class);

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private CheckDataService checkDataService;

    public CheckScenario() {
        super("check", "检查数据同步场景：同步患者检查申请单数据");
        this.setCommitInterval(100);
        this.setPageSize(200);
    }

    @Override
    public ItemReader<YjShenqingdanResp> createReader() {
        log.info("创建检查数据读取器，页面大小: {}", getPageSize());

        // 创建通用分页读取器
        GenericPagingItemReader<YjShenqingdanResp> reader = new GenericPagingItemReader<>(
                "检查申请单",
                getPageSize(),
                (offset, limit) -> checkDataService.selectChecksByPage("1006", offset, limit),
                () -> checkDataService.countChecks("1006")
        );

        // 记录总检查申请单数量
        int totalCount = reader.getTotalCount();
        log.info("病区1006检查申请单总数: {}", totalCount);

        return reader;
    }

    @Override
    public ItemProcessor<YjShenqingdanResp, PatientWithCheckReq> createProcessor() {
        log.info("创建检查数据转换器");
        return new CheckProcessor(checkDataService);
    }

    @Override
    public ItemWriter<PatientWithCheckReq> createWriter() {
        log.info("创建检查数据同步写入器");
        return items -> {
            PatientCheckSyncRequest request = new PatientCheckSyncRequest();
            request.setPatientWithCheckReqList((List<PatientWithCheckReq>) items);

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理检查数据量: {}", threadName, items.size());

            try {
                List<String> patientNames = getPatientNameFromRequest((List<PatientWithCheckReq>) items);
                String patientNamesStr = String.join(", ", patientNames);
                
                smartwardWebClient
                        .post()
                        .uri("/sync/patient_check/sync_patient_check")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request)
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("检查 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("检查 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送检查数据: {} (线程: {})", patientNamesStr, threadName);
            } catch (Exception e) {
                log.error("检查数据同步时发生异常: {}", e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理检查数据量: {}", threadName, items.size());
        };
    }

    /**
     * 从请求对象中获取患者姓名
     */
    private List<String> getPatientNameFromRequest(List<PatientWithCheckReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知患者");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(PatientWithCheckReq::getPatientInfo)
                .filter(Objects::nonNull)
                .map(patientInfo -> patientInfo.getPatientName())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
