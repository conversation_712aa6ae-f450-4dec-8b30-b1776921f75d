package com.javazx.batch.service;

import com.javazx.batch.mapper.GyDaimaMapper;
import com.javazx.batch.po.GyDaima;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 职称服务类
 * 处理职称代码相关业务逻辑
 */
@Service
public class JobTitleService {

    private static final Logger log = LoggerFactory.getLogger(JobTitleService.class);

    @Autowired
    private GyDaimaMapper gyDaimaMapper;

    // 缓存职称代码映射 (DAIMALB = '0072')
    private Map<String, GyDaima> jobTitleCache = new HashMap<>();
    private boolean jobTitleCacheInitialized = false;

    // 缓存工作类别代码映射 (DAIMALB = '0074')
    private Map<String, GyDaima> jobTypeCache = new HashMap<>();
    private boolean jobTypeCacheInitialized = false;

    /**
     * 初始化职称代码缓存
     */
    private void initJobTitleCache() {
        if (!jobTitleCacheInitialized) {
            try {
                List<GyDaima> jobTitles = gyDaimaMapper.selectJobTitleCodes();
                jobTitleCache.clear();

                for (GyDaima jobTitle : jobTitles) {
                    // 按代码ID缓存
                    jobTitleCache.put(jobTitle.getDAIMAID(), jobTitle);
                    // 按名称缓存
                    jobTitleCache.put(jobTitle.getDAIMAMC(), jobTitle);
                }

                jobTitleCacheInitialized = true;
                log.info("职称代码缓存初始化完成，共加载{}条记录", jobTitles.size());
            } catch (Exception e) {
                log.error("初始化职称代码缓存失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 初始化工作类别代码缓存
     */
    private void initJobTypeCache() {
        if (!jobTypeCacheInitialized) {
            try {
                List<GyDaima> jobTypes = gyDaimaMapper.selectJobTypeCodes();
                jobTypeCache.clear();

                for (GyDaima jobType : jobTypes) {
                    // 按代码ID缓存
                    jobTypeCache.put(jobType.getDAIMAID(), jobType);
                    // 按名称缓存
                    jobTypeCache.put(jobType.getDAIMAMC(), jobType);
                }

                jobTypeCacheInitialized = true;
                log.info("工作类别代码缓存初始化完成，共加载{}条记录", jobTypes.size());
            } catch (Exception e) {
                log.error("初始化工作类别代码缓存失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 根据职称名称或代码获取职称信息
     * @param jobTitleKey 职称名称或代码
     * @return 职称信息
     */
    public GyDaima getJobTitle(String jobTitleKey) {
        if (jobTitleKey == null || jobTitleKey.trim().isEmpty()) {
            return null;
        }

        initJobTitleCache();
        return jobTitleCache.get(jobTitleKey.trim());
    }

    /**
     * 根据职称代码ID获取职称名称
     * @param jobTitleCode 职称代码ID (来自数据库的ZHICHENG字段)
     * @return 职称名称，如果未找到返回原代码
     */
    public String getJobTitleNameByCode(String jobTitleCode) {
        if (jobTitleCode == null || jobTitleCode.trim().isEmpty()) {
            return jobTitleCode;
        }

        try {
            initJobTitleCache();
            GyDaima jobTitle = jobTitleCache.get(jobTitleCode.trim());
            if (jobTitle != null) {
                return jobTitle.getDAIMAMC(); // 返回职称名称
            }
        } catch (Exception e) {
            log.warn("获取职称名称失败，返回原代码: {}", jobTitleCode, e);
        }

        return jobTitleCode; // 如果未找到，返回原代码
    }

    /**
     * 根据工作类别代码获取工作类别信息
     * @param jobTypeKey 工作类别代码或名称
     * @return 工作类别信息
     */
    public GyDaima getJobType(String jobTypeKey) {
        if (jobTypeKey == null || jobTypeKey.trim().isEmpty()) {
            return null;
        }

        initJobTypeCache();
        return jobTypeCache.get(jobTypeKey.trim());
    }

    /**
     * 根据职称代码转换工作类别（基于0074代码表）
     * @param jobTitleCode 职称代码ID
     * @return 工作类别(0-全部, 1-管理员, 2-医生, 3-护士, 4-技师, 5-药师)
     */
    public Long convertJobTypeByTitleCode(String jobTitleCode) {
        if (jobTitleCode == null || jobTitleCode.trim().isEmpty()) {
            return 0L;
        }

        try {
            initJobTypeCache();
            GyDaima jobTitle = jobTypeCache.get(jobTitleCode.trim());
            if (jobTitle != null) {
                return convertJobType(jobTitle.getDAIMAMC()); // 返回职称名称
            }
        } catch (Exception e) {
            log.warn("职称代码转换工作类别失败: {}", jobTitleCode, e);
            return 0L;
        }
        return 0L;
    }

    /**
     * 根据职称名称转换工作类别
     * @param jobTitle 职称名称
     * @return 工作类别(0-全部, 1-管理员, 2-医生, 3-护士)
     */
    public Long convertJobType(String jobTitle) {
        if (jobTitle == null || jobTitle.trim().isEmpty()) {
            return 0L;
        }

        String title = jobTitle.trim();

        // 医生类职称 (包括中医、中西医结合)
        if ((title).equals("医生")) {
            return 2L; // 医生
        }

        // 护士类职称
        if ((title).equals("护士")) {
            return 3L; // 护士
        }

        // 管理员类职称
        if ((title).equals("管理人员")) {
            return 1L; // 管理员
        }

        return 0L; // 全部/其他
    }

    /**
     * 根据职称名称转换工作类别（兼容旧方法）
     * @param jobTitle 职称名称
     * @return 工作类别(0-全部, 1-管理员, 2-医生, 3-护士)
     */
    public Long convertJobTypeByTitle(String jobTitle) {
        Long jobType = convertJobType(jobTitle);
        if (jobType == 4L || jobType == 5L) {
            return 0L;
        }
        return jobType;
    }
    /**
     * 获取所有职称代码
     * @return 职称代码列表
     */
    public List<GyDaima> getAllJobTitles() {
        try {
            return gyDaimaMapper.selectJobTitleCodes();
        } catch (Exception e) {
            log.error("查询所有职称代码失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 清除缓存（用于刷新数据）
     */
    public void clearCache() {
        jobTitleCache.clear();
        jobTitleCacheInitialized = false;
        jobTypeCache.clear();
        jobTypeCacheInitialized = false;
        log.info("职称代码和工作类别缓存已清除");
    }
}
