package com.javazx.batch.util;

import cn.hutool.core.util.DesensitizedUtil;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.PatientDataService;
import com.javazx.batch.vo.PatientInfoReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * 患者信息创建工具类
 * 提供通用的患者信息创建方法，供各个处理器使用
 */
@Component
public class PatientInfoUtil {

    private static final Logger log = LoggerFactory.getLogger(PatientInfoUtil.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final PatientDataService patientDataService;

    public PatientInfoUtil(PatientDataService patientDataService) {
        this.patientDataService = patientDataService;
    }

    /**
     * 创建患者信息对象
     * 基于PatientProcessor中的createPatientInfo方法
     * 
     * @param zyBingrenxx 患者基础信息
     * @return 患者信息请求对象
     */
    public PatientInfoReq createPatientInfo(ZyBingrenxx zyBingrenxx) {
        if (zyBingrenxx == null) {
            return null;
        }

        PatientInfoReq patientInfo = new PatientInfoReq();

        // 基本信息
        patientInfo.setPatientName(zyBingrenxx.getXINGMING());
        patientInfo.setDesensitizedName(desensitizeName(zyBingrenxx.getXINGMING()));
        patientInfo.setGender(convertGenderToLong(zyBingrenxx.getXINGBIE()));

        // 住院信息
        patientInfo.setZyh(zyBingrenxx.getBINGRENZYID());
        patientInfo.setZyhm(zyBingrenxx.getBINGANHAO());
        patientInfo.setBedNo(zyBingrenxx.getDANGQIANCW());
        patientInfo.setWardCode(zyBingrenxx.getDANGQIANBQ());

        // 时间信息
        if (zyBingrenxx.getRUYUANRQ() != null) {
            patientInfo.setAdmissionTime(zyBingrenxx.getRUYUANRQ().format(dateFormatter));
        }

        patientInfo.setDischargeTime("");

        // 诊断信息
        patientInfo.setDiagnosis(zyBingrenxx.getRUYUANZDMC());

        // 医护人员信息
        patientInfo.setDoctorJobNumber(zyBingrenxx.getZHUYUANYS() != null ? Long.valueOf(zyBingrenxx.getZHUYUANYS()) : null);
        patientInfo.setDoctorTitleShow("住院医师");
        patientInfo.setNurseJobNumber(zyBingrenxx.getZERENHS() != null ? Long.valueOf(zyBingrenxx.getZERENHS()) : null);
        patientInfo.setNurseTitleShow("责任护士");

        // 状态信息
        if ("0".equals(zyBingrenxx.getZAIYUANZT())) {
            patientInfo.setPatientStatus(1L);
        }else {
            patientInfo.setPatientStatus(0L);
        }

        // 身份证号码
        patientInfo.setIdNumber(zyBingrenxx.getSHENFENZH());
        
        // 生日信息
        if (zyBingrenxx.getCHUSHENGRQ() != null) {
            patientInfo.setBirthday(zyBingrenxx.getCHUSHENGRQ().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        } else {
            //从身份证号码获取出生日期 格式 1981-03-22
            patientInfo.setBirthday(
                    Optional.ofNullable(zyBingrenxx.getSHENFENZH())
                            .filter(id -> id.length() >= 14)
                            .map(id -> String.format("%s-%s-%s", id.substring(6, 10), id.substring(10, 12), id.substring(12, 14)))
                            .orElse(null)
            );
        }
        
        // 医疗性质
        patientInfo.setMedicalNature(zyBingrenxx.getFEIYONGXZ());
        
        // 护理等级
        String nursingLevel = getNursingLevelFromOrder(zyBingrenxx.getBINGRENZYID(), zyBingrenxx.getDANGQIANBQ());
        if (!nursingLevel.trim().isEmpty()) {
            patientInfo.setNurseGrade(nursingLevel);
        }
        
        // 转科时间
        if (zyBingrenxx.getZHUANBINGQBZ() != null && zyBingrenxx.getZHUANBINGQBZ() > 0
            && zyBingrenxx.getRUKERQ() != null) {
            patientInfo.setTransferTime(zyBingrenxx.getRUKERQ().format(dateFormatter));
        }
        
        // 出院时间
        if (zyBingrenxx.getCHUYUANRQ() != null) {
            patientInfo.setDischargeTime(zyBingrenxx.getCHUYUANRQ().format(dateFormatter));
        } else if (zyBingrenxx.getYUCHUYRQ() != null) {
            // 如果没有实际出院时间，使用预出院时间
            patientInfo.setDischargeTime(zyBingrenxx.getYUCHUYRQ().format(dateFormatter));
        } else {
            patientInfo.setDischargeTime(null);
        }

        patientInfo.setIsWriteToAdvice(null);

        return patientInfo;
    }

    /**
     * 脱敏姓名
     */
    private String desensitizeName(String name) {
        if (StringUtils.hasLength(name)) {
            var frontIndex = name.length() <= 2 ? 0 : 1;
            var desensitizedInpatientName = DesensitizedUtil.idCardNum(name, frontIndex, 1);
            return desensitizedInpatientName;
        }
        return name;
    }

    /**
     * 转换性别为Long类型
     */
    private Long convertGenderToLong(String gender) {
        if (gender == null) {
            return 0L;
        }

        switch (gender.trim()) {
            case "男":
            case "1":
                return 1L;
            case "女":
            case "2":
                return 2L;
            default:
                return 0L;
        }
    }

    /**
     * 根据医嘱获取护理等级
     * @param bingrenzyid 病人住院ID
     * @param bingquid 病区ID
     * @return 护理等级
     */
    private String getNursingLevelFromOrder(String bingrenzyid, String bingquid) {
        if (bingrenzyid == null || bingquid == null) {
            return "";
        }

        try {
            String orderItemId = patientDataService.getNursingLevelOrderId(bingrenzyid, bingquid);
            if (orderItemId != null && !orderItemId.trim().isEmpty()) {
                // 根据医嘱项目ID转换为护理等级名称
                return convertOrderIdToNursingLevel(orderItemId);
            }
        } catch (Exception e) {
            log.warn("查询护理等级失败，病人ID: {}, 病区: {}, 错误: {}", bingrenzyid, bingquid, e.getMessage());
        }

        return "";
    }

    /**
     * 将医嘱项目ID转换为护理等级名称
     * @param orderItemId 医嘱项目ID
     * @return 护理等级名称
     */
    private String convertOrderIdToNursingLevel(String orderItemId) {
        if (orderItemId == null || orderItemId.trim().isEmpty()) {
            return "";
        }

        switch (orderItemId.trim()) {
            case "15150":
            case "**********":
            case "**********":
                return "特级护理";
            case "**********":
                return "一级护理";
            case "1000005785":
                return "二级护理";
            case "1000006017":
            case "1000005997":
                return "三级护理";
            default:
                log.debug("未知的护理等级医嘱项目ID: {}", orderItemId);
                return "";
        }
    }
}
