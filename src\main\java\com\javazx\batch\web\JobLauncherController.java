package com.javazx.batch.web;

import java.util.Enumeration;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.javazx.batch.config.DynamicBatchConfig;
import com.javazx.batch.scenario.BatchScenario;
import com.javazx.batch.scenario.BatchScenarioManager;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.configuration.JobRegistry;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;


@Controller
public class JobLauncherController {

    private static final String JOB_NAME = "jobName";

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private JobRegistry jobRegistry;

    @Autowired
    private BatchScenarioManager scenarioManager;

    @Autowired
    private DynamicBatchConfig dynamicBatchConfig;

    @RequestMapping(value = "/executeJob", method = RequestMethod.GET)
    public String launch(@RequestParam String jobName,
                         HttpServletRequest request) throws Exception {
        JobParameters jobParameters = bulidParameters(request);

        // 优先从动态配置中获取Job
        Job job = dynamicBatchConfig.getJob(jobName);
        if (job == null) {
            // 如果动态配置中没有，则从JobRegistry中获取
            job = jobRegistry.getJob(jobName);
        }

        JobExecution result = jobLauncher.run(job, jobParameters);
        return "ok";
    }

    /**
     * 列出所有场景
     * @param model
     * @return
     */
    @RequestMapping(value = "/scenarios", method = RequestMethod.GET)
    public String listScenarios(Model model) {
        List<BatchScenario> scenarios = scenarioManager.getAllEnabledScenarios();
        model.addAttribute("scenarios", scenarios);
        return "scenarios";
    }

    /**
     * 执行场景
     * @param scenarioName
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/executeScenario", method = RequestMethod.GET)
    public String executeScenario(@RequestParam String scenarioName,
                                  HttpServletRequest request) throws Exception {
        BatchScenario scenario = scenarioManager.getScenario(scenarioName);
        if (scenario == null) {
            throw new IllegalArgumentException("场景不存在: " + scenarioName);
        }

        return launch(scenario.getJobName(), request);
    }

    private JobParameters bulidParameters(HttpServletRequest request) {
        JobParametersBuilder builder = new JobParametersBuilder();

        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            if (!JOB_NAME.equals(paramName)) {
                builder.addString(paramName, request.getParameter(paramName));
            }
        }
        builder.addLong("time", System.currentTimeMillis());
        return builder.toJobParameters();
    }
}
