package com.javazx.batch.mapper.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医技申请单响应对象
 * 用于检查数据查询结果映射
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YjShenqingdanResp implements Serializable {

    /**
     * 医嘱名称
     */
    @TableField(value = "YIZHUMC")
    private String yizhumc;

    /**
     * 医嘱分类
     */
    @TableField(value = "YIZHUFL")
    private String yizhufl;

    /**
     * 预约申请日期
     */
    @TableField(value = "YUYUESQRQ")
    private LocalDateTime yuyuesqrq;

    /**
     * 检查状态 (decode(b.dangqianzt, '6', 1, '7', 1, '8', 1, 0) as jianchazt)
     * 1-已完成, 0-未完成
     */
    @TableField(value = "JIANCHAZT")
    private Integer jianchazt;

    /**
     * 检查地址
     */
    @TableField(value = "JIANCHADZ")
    private String jianchadz;

    /**
     * 当前病区
     */
    @TableField(value = "DANGQIANBQ")
    private String dangqianbq;

    /**
     * 当前床位
     */
    @TableField(value = "DANGQIANCW")
    private String dangqiancw;

    /**
     * 患者姓名
     */
    @TableField(value = "XINGMING")
    private String xingming;

    /**
     * 住院号
     */
    @TableField(value = "ZHUYUANHAO")
    private String zhuyuanhao;

    /**
     * 病案号
     */
    @TableField(value = "BINGANGHAO")
    private String binganghao;

}