package com.javazx.batch.mapper.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医技申请单响应对象
 * 用于检查数据查询结果映射
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YjShenqingdanResp implements Serializable {

    /**
     * 医嘱id
     */
    private String YIZHUID;

    /**
     * 病人住院ID
     */
    private String BINGRENZYID;


    /**
     * 医嘱名称
     */
    private String YIZHUMC;

    /**
     * 医嘱分类
     */
    private String YIZHUFL;

    /**
     * 预约申请日期
     */
    private LocalDateTime YUYUESQRQ;

    /**
     * 当前状态1新开单2待划价3待登记4已预约5已安排6已完成7已报告8已打印9已撤销10已退单11已发送未接收
     *
     */
    private String DANGQIANZT;

    /**
     * 检查地址
     */
    private String JIANCHADZ;

    /**
     * 当前病区
     */
    private String DANGQIANBQ;

    /**
     * 当前床位
     */
    private String DANGQIANCW;

    /**
     * 患者姓名
     */
    private String XINGMING;

    /**
     * 住院号
     */
    private String ZHUYUANHAO;

    /**
     * 病案号
     */
    private String BINGANGHAO;

}