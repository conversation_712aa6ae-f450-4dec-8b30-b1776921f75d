package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 医技_申请单
 * @TableName YJ_SHENQINGDAN
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YjShenqingdanResp implements Serializable {

    /**
     * 预约申请日期
     */
    @TableField(value = "YUYUESQRQ")
    private LocalDateTime YUYUESQRQ;



   }