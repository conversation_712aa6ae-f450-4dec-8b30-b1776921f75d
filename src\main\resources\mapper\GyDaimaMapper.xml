<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.GyDaimaMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.GyDaima">
            <result property="DAIMAID" column="DAIMAID" jdbcType="VARCHAR"/>
            <result property="DAIMALB" column="DAIMALB" jdbcType="VARCHAR"/>
            <result property="DAIMAMC" column="DAIMAMC" jdbcType="VARCHAR"/>
            <result property="JICI" column="JICI" jdbcType="DECIMAL"/>
            <result property="FULEIDM" column="FULEIDM" jdbcType="VARCHAR"/>
            <result property="MOJIBZ" column="MOJIBZ" jdbcType="DECIMAL"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="SHUNXUHAO" column="SHUNXUHAO" jdbcType="DECIMAL"/>
            <result property="MENZHENSY" column="MENZHENSY" jdbcType="DECIMAL"/>
            <result property="ZHUYUANSY" column="ZHUYUANSY" jdbcType="DECIMAL"/>
            <result property="SHURUMA1" column="SHURUMA1" jdbcType="VARCHAR"/>
            <result property="SHURUMA2" column="SHURUMA2" jdbcType="VARCHAR"/>
            <result property="SHURUMA3" column="SHURUMA3" jdbcType="VARCHAR"/>
            <result property="XITONGBZ" column="XITONGBZ" jdbcType="DECIMAL"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="ZIFU1" column="ZIFU1" jdbcType="VARCHAR"/>
            <result property="ZIFU2" column="ZIFU2" jdbcType="VARCHAR"/>
            <result property="ZIFU3" column="ZIFU3" jdbcType="VARCHAR"/>
            <result property="ZIFU4" column="ZIFU4" jdbcType="VARCHAR"/>
            <result property="ZIFU5" column="ZIFU5" jdbcType="VARCHAR"/>
            <result property="SHUZI1" column="SHUZI1" jdbcType="DECIMAL"/>
            <result property="SHUZI2" column="SHUZI2" jdbcType="DECIMAL"/>
            <result property="SHUZI3" column="SHUZI3" jdbcType="DECIMAL"/>
            <result property="RIQI1" column="RIQI1" jdbcType="TIMESTAMP"/>
            <result property="RIQI2" column="RIQI2" jdbcType="TIMESTAMP"/>
            <result property="RIQI3" column="RIQI3" jdbcType="TIMESTAMP"/>
            <result property="BIAOZHIWEI1" column="BIAOZHIWEI1" jdbcType="DECIMAL"/>
            <result property="BIAOZHIWEI2" column="BIAOZHIWEI2" jdbcType="DECIMAL"/>
            <result property="BIAOZHIWEI3" column="BIAOZHIWEI3" jdbcType="DECIMAL"/>
            <result property="DAIMAJC" column="DAIMAJC" jdbcType="VARCHAR"/>
            <result property="ZIFU6" column="ZIFU6" jdbcType="VARCHAR"/>
            <result property="ZIFU7" column="ZIFU7" jdbcType="VARCHAR"/>
            <result property="ZIFU8" column="ZIFU8" jdbcType="VARCHAR"/>
            <result property="ZIFU9" column="ZIFU9" jdbcType="VARCHAR"/>
            <result property="ZIFU10" column="ZIFU10" jdbcType="VARCHAR"/>
            <result property="SHUZI4" column="SHUZI4" jdbcType="DECIMAL"/>
            <result property="SHUZI5" column="SHUZI5" jdbcType="DECIMAL"/>
            <result property="SHIYONGFW" column="SHIYONGFW" jdbcType="VARCHAR"/>
            <result property="YISHENGKS" column="YISHENGKS" jdbcType="VARCHAR"/>
            <result property="YINGWENMING" column="YINGWENMING" jdbcType="VARCHAR"/>
            <result property="ZIFU11" column="ZIFU11" jdbcType="VARCHAR"/>
            <result property="ZIFU12" column="ZIFU12" jdbcType="VARCHAR"/>
            <result property="ZIFU13" column="ZIFU13" jdbcType="VARCHAR"/>
            <result property="ZIFU14" column="ZIFU14" jdbcType="VARCHAR"/>
            <result property="ZIFU15" column="ZIFU15" jdbcType="VARCHAR"/>
            <result property="ZIFU16" column="ZIFU16" jdbcType="VARCHAR"/>
            <result property="ZIFU17" column="ZIFU17" jdbcType="VARCHAR"/>
            <result property="ZIFU18" column="ZIFU18" jdbcType="VARCHAR"/>
            <result property="ZIFU19" column="ZIFU19" jdbcType="VARCHAR"/>
            <result property="ZIFU20" column="ZIFU20" jdbcType="VARCHAR"/>
            <result property="MORENPC" column="MORENPC" jdbcType="DECIMAL"/>
            <result property="ZIFU21" column="ZIFU21" jdbcType="VARCHAR"/>
            <result property="LIUQIANGSY" column="LIUQIANGSY" jdbcType="DECIMAL"/>
            <result property="YOUXIANJI" column="YOUXIANJI" jdbcType="DECIMAL"/>
            <result property="JIZHENSY" column="JIZHENSY" jdbcType="DECIMAL"/>
            <result property="TIJIANSY" column="TIJIANSY" jdbcType="DECIMAL"/>
            <result property="YUANQUSY" column="YUANQUSY" jdbcType="VARCHAR"/>
            <result property="SHENGHRBM" column="SHENGHRBM" jdbcType="VARCHAR"/>
            <result property="SHENGHRMC" column="SHENGHRMC" jdbcType="VARCHAR"/>
            <result property="JIANYANBZBM" column="JIANYANBZBM" jdbcType="VARCHAR"/>
            <result property="JIANYANBZMC" column="JIANYANBZMC" jdbcType="VARCHAR"/>
            <result property="GCPXMBZ" column="GCPXMBZ" jdbcType="VARCHAR"/>
            <result property="KAIFANGQY" column="KAIFANGQY" jdbcType="VARCHAR"/>
            <result property="SHENGHRMC2" column="SHENGHRMC2" jdbcType="VARCHAR"/>
            <result property="SHENGHRBM2" column="SHENGHRBM2" jdbcType="VARCHAR"/>
            <result property="YUANQIANSY" column="YUANQIANSY" jdbcType="DECIMAL"/>
            <result property="JIANCHABW" column="JIANCHABW" jdbcType="VARCHAR"/>
            <result property="JIANCHASB" column="JIANCHASB" jdbcType="VARCHAR"/>
            <result property="JIANCHAFX" column="JIANCHAFX" jdbcType="VARCHAR"/>
            <result property="DUOBUWEIBZ" column="DUOBUWEIBZ" jdbcType="DECIMAL"/>
            <result property="ZHIQINGTYS" column="ZHIQINGTYS" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        DAIMAID,DAIMALB,DAIMAMC,
        JICI,FULEIDM,MOJIBZ,
        ZUOFEIBZ,SHUNXUHAO,MENZHENSY,
        ZHUYUANSY,SHURUMA1,SHURUMA2,
        SHURUMA3,XITONGBZ,XIUGAIREN,
        XIUGAISJ,ZIFU1,ZIFU2,
        ZIFU3,ZIFU4,ZIFU5,
        SHUZI1,SHUZI2,SHUZI3,
        RIQI1,RIQI2,RIQI3,
        BIAOZHIWEI1,BIAOZHIWEI2,BIAOZHIWEI3,
        DAIMAJC,ZIFU6,ZIFU7,
        ZIFU8,ZIFU9,ZIFU10,
        SHUZI4,SHUZI5,SHIYONGFW,
        YISHENGKS,YINGWENMING,ZIFU11,
        ZIFU12,ZIFU13,ZIFU14,
        ZIFU15,ZIFU16,ZIFU17,
        ZIFU18,ZIFU19,ZIFU20,
        MORENPC,ZIFU21,LIUQIANGSY,
        YOUXIANJI,JIZHENSY,TIJIANSY,
        YUANQUSY,SHENGHRBM,SHENGHRMC,
        JIANYANBZBM,JIANYANBZMC,GCPXMBZ,
        KAIFANGQY,SHENGHRMC2,SHENGHRBM2,
        YUANQIANSY,JIANCHABW,JIANCHASB,
        JIANCHAFX,DUOBUWEIBZ,ZHIQINGTYS
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from GY_DAIMA
        where 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from GY_DAIMA
        where 
    </delete>
    <insert id="insert">
        insert into GY_DAIMA
        ( DAIMAID,DAIMALB,DAIMAMC
        ,JICI,FULEIDM,MOJIBZ
        ,ZUOFEIBZ,SHUNXUHAO,MENZHENSY
        ,ZHUYUANSY,SHURUMA1,SHURUMA2
        ,SHURUMA3,XITONGBZ,XIUGAIREN
        ,XIUGAISJ,ZIFU1,ZIFU2
        ,ZIFU3,ZIFU4,ZIFU5
        ,SHUZI1,SHUZI2,SHUZI3
        ,RIQI1,RIQI2,RIQI3
        ,BIAOZHIWEI1,BIAOZHIWEI2,BIAOZHIWEI3
        ,DAIMAJC,ZIFU6,ZIFU7
        ,ZIFU8,ZIFU9,ZIFU10
        ,SHUZI4,SHUZI5,SHIYONGFW
        ,YISHENGKS,YINGWENMING,ZIFU11
        ,ZIFU12,ZIFU13,ZIFU14
        ,ZIFU15,ZIFU16,ZIFU17
        ,ZIFU18,ZIFU19,ZIFU20
        ,MORENPC,ZIFU21,LIUQIANGSY
        ,YOUXIANJI,JIZHENSY,TIJIANSY
        ,YUANQUSY,SHENGHRBM,SHENGHRMC
        ,JIANYANBZBM,JIANYANBZMC,GCPXMBZ
        ,KAIFANGQY,SHENGHRMC2,SHENGHRBM2
        ,YUANQIANSY,JIANCHABW,JIANCHASB
        ,JIANCHAFX,DUOBUWEIBZ,ZHIQINGTYS
        )
        values (#{DAIMAID,jdbcType=VARCHAR},#{DAIMALB,jdbcType=VARCHAR},#{DAIMAMC,jdbcType=VARCHAR}
        ,#{JICI,jdbcType=DECIMAL},#{FULEIDM,jdbcType=VARCHAR},#{MOJIBZ,jdbcType=DECIMAL}
        ,#{ZUOFEIBZ,jdbcType=DECIMAL},#{SHUNXUHAO,jdbcType=DECIMAL},#{MENZHENSY,jdbcType=DECIMAL}
        ,#{ZHUYUANSY,jdbcType=DECIMAL},#{SHURUMA1,jdbcType=VARCHAR},#{SHURUMA2,jdbcType=VARCHAR}
        ,#{SHURUMA3,jdbcType=VARCHAR},#{XITONGBZ,jdbcType=DECIMAL},#{XIUGAIREN,jdbcType=VARCHAR}
        ,#{XIUGAISJ,jdbcType=TIMESTAMP},#{ZIFU1,jdbcType=VARCHAR},#{ZIFU2,jdbcType=VARCHAR}
        ,#{ZIFU3,jdbcType=VARCHAR},#{ZIFU4,jdbcType=VARCHAR},#{ZIFU5,jdbcType=VARCHAR}
        ,#{SHUZI1,jdbcType=DECIMAL},#{SHUZI2,jdbcType=DECIMAL},#{SHUZI3,jdbcType=DECIMAL}
        ,#{RIQI1,jdbcType=TIMESTAMP},#{RIQI2,jdbcType=TIMESTAMP},#{RIQI3,jdbcType=TIMESTAMP}
        ,#{BIAOZHIWEI1,jdbcType=DECIMAL},#{BIAOZHIWEI2,jdbcType=DECIMAL},#{BIAOZHIWEI3,jdbcType=DECIMAL}
        ,#{DAIMAJC,jdbcType=VARCHAR},#{ZIFU6,jdbcType=VARCHAR},#{ZIFU7,jdbcType=VARCHAR}
        ,#{ZIFU8,jdbcType=VARCHAR},#{ZIFU9,jdbcType=VARCHAR},#{ZIFU10,jdbcType=VARCHAR}
        ,#{SHUZI4,jdbcType=DECIMAL},#{SHUZI5,jdbcType=DECIMAL},#{SHIYONGFW,jdbcType=VARCHAR}
        ,#{YISHENGKS,jdbcType=VARCHAR},#{YINGWENMING,jdbcType=VARCHAR},#{ZIFU11,jdbcType=VARCHAR}
        ,#{ZIFU12,jdbcType=VARCHAR},#{ZIFU13,jdbcType=VARCHAR},#{ZIFU14,jdbcType=VARCHAR}
        ,#{ZIFU15,jdbcType=VARCHAR},#{ZIFU16,jdbcType=VARCHAR},#{ZIFU17,jdbcType=VARCHAR}
        ,#{ZIFU18,jdbcType=VARCHAR},#{ZIFU19,jdbcType=VARCHAR},#{ZIFU20,jdbcType=VARCHAR}
        ,#{MORENPC,jdbcType=DECIMAL},#{ZIFU21,jdbcType=VARCHAR},#{LIUQIANGSY,jdbcType=DECIMAL}
        ,#{YOUXIANJI,jdbcType=DECIMAL},#{JIZHENSY,jdbcType=DECIMAL},#{TIJIANSY,jdbcType=DECIMAL}
        ,#{YUANQUSY,jdbcType=VARCHAR},#{SHENGHRBM,jdbcType=VARCHAR},#{SHENGHRMC,jdbcType=VARCHAR}
        ,#{JIANYANBZBM,jdbcType=VARCHAR},#{JIANYANBZMC,jdbcType=VARCHAR},#{GCPXMBZ,jdbcType=VARCHAR}
        ,#{KAIFANGQY,jdbcType=VARCHAR},#{SHENGHRMC2,jdbcType=VARCHAR},#{SHENGHRBM2,jdbcType=VARCHAR}
        ,#{YUANQIANSY,jdbcType=DECIMAL},#{JIANCHABW,jdbcType=VARCHAR},#{JIANCHASB,jdbcType=VARCHAR}
        ,#{JIANCHAFX,jdbcType=VARCHAR},#{DUOBUWEIBZ,jdbcType=DECIMAL},#{ZHIQINGTYS,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective">
        insert into GY_DAIMA
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="DAIMAID != null">DAIMAID,</if>
                <if test="DAIMALB != null">DAIMALB,</if>
                <if test="DAIMAMC != null">DAIMAMC,</if>
                <if test="JICI != null">JICI,</if>
                <if test="FULEIDM != null">FULEIDM,</if>
                <if test="MOJIBZ != null">MOJIBZ,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="SHUNXUHAO != null">SHUNXUHAO,</if>
                <if test="MENZHENSY != null">MENZHENSY,</if>
                <if test="ZHUYUANSY != null">ZHUYUANSY,</if>
                <if test="SHURUMA1 != null">SHURUMA1,</if>
                <if test="SHURUMA2 != null">SHURUMA2,</if>
                <if test="SHURUMA3 != null">SHURUMA3,</if>
                <if test="XITONGBZ != null">XITONGBZ,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="ZIFU1 != null">ZIFU1,</if>
                <if test="ZIFU2 != null">ZIFU2,</if>
                <if test="ZIFU3 != null">ZIFU3,</if>
                <if test="ZIFU4 != null">ZIFU4,</if>
                <if test="ZIFU5 != null">ZIFU5,</if>
                <if test="SHUZI1 != null">SHUZI1,</if>
                <if test="SHUZI2 != null">SHUZI2,</if>
                <if test="SHUZI3 != null">SHUZI3,</if>
                <if test="RIQI1 != null">RIQI1,</if>
                <if test="RIQI2 != null">RIQI2,</if>
                <if test="RIQI3 != null">RIQI3,</if>
                <if test="BIAOZHIWEI1 != null">BIAOZHIWEI1,</if>
                <if test="BIAOZHIWEI2 != null">BIAOZHIWEI2,</if>
                <if test="BIAOZHIWEI3 != null">BIAOZHIWEI3,</if>
                <if test="DAIMAJC != null">DAIMAJC,</if>
                <if test="ZIFU6 != null">ZIFU6,</if>
                <if test="ZIFU7 != null">ZIFU7,</if>
                <if test="ZIFU8 != null">ZIFU8,</if>
                <if test="ZIFU9 != null">ZIFU9,</if>
                <if test="ZIFU10 != null">ZIFU10,</if>
                <if test="SHUZI4 != null">SHUZI4,</if>
                <if test="SHUZI5 != null">SHUZI5,</if>
                <if test="SHIYONGFW != null">SHIYONGFW,</if>
                <if test="YISHENGKS != null">YISHENGKS,</if>
                <if test="YINGWENMING != null">YINGWENMING,</if>
                <if test="ZIFU11 != null">ZIFU11,</if>
                <if test="ZIFU12 != null">ZIFU12,</if>
                <if test="ZIFU13 != null">ZIFU13,</if>
                <if test="ZIFU14 != null">ZIFU14,</if>
                <if test="ZIFU15 != null">ZIFU15,</if>
                <if test="ZIFU16 != null">ZIFU16,</if>
                <if test="ZIFU17 != null">ZIFU17,</if>
                <if test="ZIFU18 != null">ZIFU18,</if>
                <if test="ZIFU19 != null">ZIFU19,</if>
                <if test="ZIFU20 != null">ZIFU20,</if>
                <if test="MORENPC != null">MORENPC,</if>
                <if test="ZIFU21 != null">ZIFU21,</if>
                <if test="LIUQIANGSY != null">LIUQIANGSY,</if>
                <if test="YOUXIANJI != null">YOUXIANJI,</if>
                <if test="JIZHENSY != null">JIZHENSY,</if>
                <if test="TIJIANSY != null">TIJIANSY,</if>
                <if test="YUANQUSY != null">YUANQUSY,</if>
                <if test="SHENGHRBM != null">SHENGHRBM,</if>
                <if test="SHENGHRMC != null">SHENGHRMC,</if>
                <if test="JIANYANBZBM != null">JIANYANBZBM,</if>
                <if test="JIANYANBZMC != null">JIANYANBZMC,</if>
                <if test="GCPXMBZ != null">GCPXMBZ,</if>
                <if test="KAIFANGQY != null">KAIFANGQY,</if>
                <if test="SHENGHRMC2 != null">SHENGHRMC2,</if>
                <if test="SHENGHRBM2 != null">SHENGHRBM2,</if>
                <if test="YUANQIANSY != null">YUANQIANSY,</if>
                <if test="JIANCHABW != null">JIANCHABW,</if>
                <if test="JIANCHASB != null">JIANCHASB,</if>
                <if test="JIANCHAFX != null">JIANCHAFX,</if>
                <if test="DUOBUWEIBZ != null">DUOBUWEIBZ,</if>
                <if test="ZHIQINGTYS != null">ZHIQINGTYS,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="DAIMAID != null">#{DAIMAID,jdbcType=VARCHAR},</if>
                <if test="DAIMALB != null">#{DAIMALB,jdbcType=VARCHAR},</if>
                <if test="DAIMAMC != null">#{DAIMAMC,jdbcType=VARCHAR},</if>
                <if test="JICI != null">#{JICI,jdbcType=DECIMAL},</if>
                <if test="FULEIDM != null">#{FULEIDM,jdbcType=VARCHAR},</if>
                <if test="MOJIBZ != null">#{MOJIBZ,jdbcType=DECIMAL},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="SHUNXUHAO != null">#{SHUNXUHAO,jdbcType=DECIMAL},</if>
                <if test="MENZHENSY != null">#{MENZHENSY,jdbcType=DECIMAL},</if>
                <if test="ZHUYUANSY != null">#{ZHUYUANSY,jdbcType=DECIMAL},</if>
                <if test="SHURUMA1 != null">#{SHURUMA1,jdbcType=VARCHAR},</if>
                <if test="SHURUMA2 != null">#{SHURUMA2,jdbcType=VARCHAR},</if>
                <if test="SHURUMA3 != null">#{SHURUMA3,jdbcType=VARCHAR},</if>
                <if test="XITONGBZ != null">#{XITONGBZ,jdbcType=DECIMAL},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="ZIFU1 != null">#{ZIFU1,jdbcType=VARCHAR},</if>
                <if test="ZIFU2 != null">#{ZIFU2,jdbcType=VARCHAR},</if>
                <if test="ZIFU3 != null">#{ZIFU3,jdbcType=VARCHAR},</if>
                <if test="ZIFU4 != null">#{ZIFU4,jdbcType=VARCHAR},</if>
                <if test="ZIFU5 != null">#{ZIFU5,jdbcType=VARCHAR},</if>
                <if test="SHUZI1 != null">#{SHUZI1,jdbcType=DECIMAL},</if>
                <if test="SHUZI2 != null">#{SHUZI2,jdbcType=DECIMAL},</if>
                <if test="SHUZI3 != null">#{SHUZI3,jdbcType=DECIMAL},</if>
                <if test="RIQI1 != null">#{RIQI1,jdbcType=TIMESTAMP},</if>
                <if test="RIQI2 != null">#{RIQI2,jdbcType=TIMESTAMP},</if>
                <if test="RIQI3 != null">#{RIQI3,jdbcType=TIMESTAMP},</if>
                <if test="BIAOZHIWEI1 != null">#{BIAOZHIWEI1,jdbcType=DECIMAL},</if>
                <if test="BIAOZHIWEI2 != null">#{BIAOZHIWEI2,jdbcType=DECIMAL},</if>
                <if test="BIAOZHIWEI3 != null">#{BIAOZHIWEI3,jdbcType=DECIMAL},</if>
                <if test="DAIMAJC != null">#{DAIMAJC,jdbcType=VARCHAR},</if>
                <if test="ZIFU6 != null">#{ZIFU6,jdbcType=VARCHAR},</if>
                <if test="ZIFU7 != null">#{ZIFU7,jdbcType=VARCHAR},</if>
                <if test="ZIFU8 != null">#{ZIFU8,jdbcType=VARCHAR},</if>
                <if test="ZIFU9 != null">#{ZIFU9,jdbcType=VARCHAR},</if>
                <if test="ZIFU10 != null">#{ZIFU10,jdbcType=VARCHAR},</if>
                <if test="SHUZI4 != null">#{SHUZI4,jdbcType=DECIMAL},</if>
                <if test="SHUZI5 != null">#{SHUZI5,jdbcType=DECIMAL},</if>
                <if test="SHIYONGFW != null">#{SHIYONGFW,jdbcType=VARCHAR},</if>
                <if test="YISHENGKS != null">#{YISHENGKS,jdbcType=VARCHAR},</if>
                <if test="YINGWENMING != null">#{YINGWENMING,jdbcType=VARCHAR},</if>
                <if test="ZIFU11 != null">#{ZIFU11,jdbcType=VARCHAR},</if>
                <if test="ZIFU12 != null">#{ZIFU12,jdbcType=VARCHAR},</if>
                <if test="ZIFU13 != null">#{ZIFU13,jdbcType=VARCHAR},</if>
                <if test="ZIFU14 != null">#{ZIFU14,jdbcType=VARCHAR},</if>
                <if test="ZIFU15 != null">#{ZIFU15,jdbcType=VARCHAR},</if>
                <if test="ZIFU16 != null">#{ZIFU16,jdbcType=VARCHAR},</if>
                <if test="ZIFU17 != null">#{ZIFU17,jdbcType=VARCHAR},</if>
                <if test="ZIFU18 != null">#{ZIFU18,jdbcType=VARCHAR},</if>
                <if test="ZIFU19 != null">#{ZIFU19,jdbcType=VARCHAR},</if>
                <if test="ZIFU20 != null">#{ZIFU20,jdbcType=VARCHAR},</if>
                <if test="MORENPC != null">#{MORENPC,jdbcType=DECIMAL},</if>
                <if test="ZIFU21 != null">#{ZIFU21,jdbcType=VARCHAR},</if>
                <if test="LIUQIANGSY != null">#{LIUQIANGSY,jdbcType=DECIMAL},</if>
                <if test="YOUXIANJI != null">#{YOUXIANJI,jdbcType=DECIMAL},</if>
                <if test="JIZHENSY != null">#{JIZHENSY,jdbcType=DECIMAL},</if>
                <if test="TIJIANSY != null">#{TIJIANSY,jdbcType=DECIMAL},</if>
                <if test="YUANQUSY != null">#{YUANQUSY,jdbcType=VARCHAR},</if>
                <if test="SHENGHRBM != null">#{SHENGHRBM,jdbcType=VARCHAR},</if>
                <if test="SHENGHRMC != null">#{SHENGHRMC,jdbcType=VARCHAR},</if>
                <if test="JIANYANBZBM != null">#{JIANYANBZBM,jdbcType=VARCHAR},</if>
                <if test="JIANYANBZMC != null">#{JIANYANBZMC,jdbcType=VARCHAR},</if>
                <if test="GCPXMBZ != null">#{GCPXMBZ,jdbcType=VARCHAR},</if>
                <if test="KAIFANGQY != null">#{KAIFANGQY,jdbcType=VARCHAR},</if>
                <if test="SHENGHRMC2 != null">#{SHENGHRMC2,jdbcType=VARCHAR},</if>
                <if test="SHENGHRBM2 != null">#{SHENGHRBM2,jdbcType=VARCHAR},</if>
                <if test="YUANQIANSY != null">#{YUANQIANSY,jdbcType=DECIMAL},</if>
                <if test="JIANCHABW != null">#{JIANCHABW,jdbcType=VARCHAR},</if>
                <if test="JIANCHASB != null">#{JIANCHASB,jdbcType=VARCHAR},</if>
                <if test="JIANCHAFX != null">#{JIANCHAFX,jdbcType=VARCHAR},</if>
                <if test="DUOBUWEIBZ != null">#{DUOBUWEIBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIQINGTYS != null">#{ZHIQINGTYS,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.GyDaima">
        update GY_DAIMA
        <set>
                <if test="DAIMAID != null">
                    DAIMAID = #{DAIMAID,jdbcType=VARCHAR},
                </if>
                <if test="DAIMALB != null">
                    DAIMALB = #{DAIMALB,jdbcType=VARCHAR},
                </if>
                <if test="DAIMAMC != null">
                    DAIMAMC = #{DAIMAMC,jdbcType=VARCHAR},
                </if>
                <if test="JICI != null">
                    JICI = #{JICI,jdbcType=DECIMAL},
                </if>
                <if test="FULEIDM != null">
                    FULEIDM = #{FULEIDM,jdbcType=VARCHAR},
                </if>
                <if test="MOJIBZ != null">
                    MOJIBZ = #{MOJIBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUNXUHAO != null">
                    SHUNXUHAO = #{SHUNXUHAO,jdbcType=DECIMAL},
                </if>
                <if test="MENZHENSY != null">
                    MENZHENSY = #{MENZHENSY,jdbcType=DECIMAL},
                </if>
                <if test="ZHUYUANSY != null">
                    ZHUYUANSY = #{ZHUYUANSY,jdbcType=DECIMAL},
                </if>
                <if test="SHURUMA1 != null">
                    SHURUMA1 = #{SHURUMA1,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA2 != null">
                    SHURUMA2 = #{SHURUMA2,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA3 != null">
                    SHURUMA3 = #{SHURUMA3,jdbcType=VARCHAR},
                </if>
                <if test="XITONGBZ != null">
                    XITONGBZ = #{XITONGBZ,jdbcType=DECIMAL},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZIFU1 != null">
                    ZIFU1 = #{ZIFU1,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU2 != null">
                    ZIFU2 = #{ZIFU2,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU3 != null">
                    ZIFU3 = #{ZIFU3,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU4 != null">
                    ZIFU4 = #{ZIFU4,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU5 != null">
                    ZIFU5 = #{ZIFU5,jdbcType=VARCHAR},
                </if>
                <if test="SHUZI1 != null">
                    SHUZI1 = #{SHUZI1,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI2 != null">
                    SHUZI2 = #{SHUZI2,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI3 != null">
                    SHUZI3 = #{SHUZI3,jdbcType=DECIMAL},
                </if>
                <if test="RIQI1 != null">
                    RIQI1 = #{RIQI1,jdbcType=TIMESTAMP},
                </if>
                <if test="RIQI2 != null">
                    RIQI2 = #{RIQI2,jdbcType=TIMESTAMP},
                </if>
                <if test="RIQI3 != null">
                    RIQI3 = #{RIQI3,jdbcType=TIMESTAMP},
                </if>
                <if test="BIAOZHIWEI1 != null">
                    BIAOZHIWEI1 = #{BIAOZHIWEI1,jdbcType=DECIMAL},
                </if>
                <if test="BIAOZHIWEI2 != null">
                    BIAOZHIWEI2 = #{BIAOZHIWEI2,jdbcType=DECIMAL},
                </if>
                <if test="BIAOZHIWEI3 != null">
                    BIAOZHIWEI3 = #{BIAOZHIWEI3,jdbcType=DECIMAL},
                </if>
                <if test="DAIMAJC != null">
                    DAIMAJC = #{DAIMAJC,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU6 != null">
                    ZIFU6 = #{ZIFU6,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU7 != null">
                    ZIFU7 = #{ZIFU7,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU8 != null">
                    ZIFU8 = #{ZIFU8,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU9 != null">
                    ZIFU9 = #{ZIFU9,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU10 != null">
                    ZIFU10 = #{ZIFU10,jdbcType=VARCHAR},
                </if>
                <if test="SHUZI4 != null">
                    SHUZI4 = #{SHUZI4,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI5 != null">
                    SHUZI5 = #{SHUZI5,jdbcType=DECIMAL},
                </if>
                <if test="SHIYONGFW != null">
                    SHIYONGFW = #{SHIYONGFW,jdbcType=VARCHAR},
                </if>
                <if test="YISHENGKS != null">
                    YISHENGKS = #{YISHENGKS,jdbcType=VARCHAR},
                </if>
                <if test="YINGWENMING != null">
                    YINGWENMING = #{YINGWENMING,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU11 != null">
                    ZIFU11 = #{ZIFU11,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU12 != null">
                    ZIFU12 = #{ZIFU12,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU13 != null">
                    ZIFU13 = #{ZIFU13,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU14 != null">
                    ZIFU14 = #{ZIFU14,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU15 != null">
                    ZIFU15 = #{ZIFU15,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU16 != null">
                    ZIFU16 = #{ZIFU16,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU17 != null">
                    ZIFU17 = #{ZIFU17,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU18 != null">
                    ZIFU18 = #{ZIFU18,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU19 != null">
                    ZIFU19 = #{ZIFU19,jdbcType=VARCHAR},
                </if>
                <if test="ZIFU20 != null">
                    ZIFU20 = #{ZIFU20,jdbcType=VARCHAR},
                </if>
                <if test="MORENPC != null">
                    MORENPC = #{MORENPC,jdbcType=DECIMAL},
                </if>
                <if test="ZIFU21 != null">
                    ZIFU21 = #{ZIFU21,jdbcType=VARCHAR},
                </if>
                <if test="LIUQIANGSY != null">
                    LIUQIANGSY = #{LIUQIANGSY,jdbcType=DECIMAL},
                </if>
                <if test="YOUXIANJI != null">
                    YOUXIANJI = #{YOUXIANJI,jdbcType=DECIMAL},
                </if>
                <if test="JIZHENSY != null">
                    JIZHENSY = #{JIZHENSY,jdbcType=DECIMAL},
                </if>
                <if test="TIJIANSY != null">
                    TIJIANSY = #{TIJIANSY,jdbcType=DECIMAL},
                </if>
                <if test="YUANQUSY != null">
                    YUANQUSY = #{YUANQUSY,jdbcType=VARCHAR},
                </if>
                <if test="SHENGHRBM != null">
                    SHENGHRBM = #{SHENGHRBM,jdbcType=VARCHAR},
                </if>
                <if test="SHENGHRMC != null">
                    SHENGHRMC = #{SHENGHRMC,jdbcType=VARCHAR},
                </if>
                <if test="JIANYANBZBM != null">
                    JIANYANBZBM = #{JIANYANBZBM,jdbcType=VARCHAR},
                </if>
                <if test="JIANYANBZMC != null">
                    JIANYANBZMC = #{JIANYANBZMC,jdbcType=VARCHAR},
                </if>
                <if test="GCPXMBZ != null">
                    GCPXMBZ = #{GCPXMBZ,jdbcType=VARCHAR},
                </if>
                <if test="KAIFANGQY != null">
                    KAIFANGQY = #{KAIFANGQY,jdbcType=VARCHAR},
                </if>
                <if test="SHENGHRMC2 != null">
                    SHENGHRMC2 = #{SHENGHRMC2,jdbcType=VARCHAR},
                </if>
                <if test="SHENGHRBM2 != null">
                    SHENGHRBM2 = #{SHENGHRBM2,jdbcType=VARCHAR},
                </if>
                <if test="YUANQIANSY != null">
                    YUANQIANSY = #{YUANQIANSY,jdbcType=DECIMAL},
                </if>
                <if test="JIANCHABW != null">
                    JIANCHABW = #{JIANCHABW,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHASB != null">
                    JIANCHASB = #{JIANCHASB,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAFX != null">
                    JIANCHAFX = #{JIANCHAFX,jdbcType=VARCHAR},
                </if>
                <if test="DUOBUWEIBZ != null">
                    DUOBUWEIBZ = #{DUOBUWEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIQINGTYS != null">
                    ZHIQINGTYS = #{ZHIQINGTYS,jdbcType=VARCHAR},
                </if>
        </set>
        where  
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.GyDaima">
        update GY_DAIMA
        set 
            DAIMAID =  #{DAIMAID,jdbcType=VARCHAR},
            DAIMALB =  #{DAIMALB,jdbcType=VARCHAR},
            DAIMAMC =  #{DAIMAMC,jdbcType=VARCHAR},
            JICI =  #{JICI,jdbcType=DECIMAL},
            FULEIDM =  #{FULEIDM,jdbcType=VARCHAR},
            MOJIBZ =  #{MOJIBZ,jdbcType=DECIMAL},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            SHUNXUHAO =  #{SHUNXUHAO,jdbcType=DECIMAL},
            MENZHENSY =  #{MENZHENSY,jdbcType=DECIMAL},
            ZHUYUANSY =  #{ZHUYUANSY,jdbcType=DECIMAL},
            SHURUMA1 =  #{SHURUMA1,jdbcType=VARCHAR},
            SHURUMA2 =  #{SHURUMA2,jdbcType=VARCHAR},
            SHURUMA3 =  #{SHURUMA3,jdbcType=VARCHAR},
            XITONGBZ =  #{XITONGBZ,jdbcType=DECIMAL},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            ZIFU1 =  #{ZIFU1,jdbcType=VARCHAR},
            ZIFU2 =  #{ZIFU2,jdbcType=VARCHAR},
            ZIFU3 =  #{ZIFU3,jdbcType=VARCHAR},
            ZIFU4 =  #{ZIFU4,jdbcType=VARCHAR},
            ZIFU5 =  #{ZIFU5,jdbcType=VARCHAR},
            SHUZI1 =  #{SHUZI1,jdbcType=DECIMAL},
            SHUZI2 =  #{SHUZI2,jdbcType=DECIMAL},
            SHUZI3 =  #{SHUZI3,jdbcType=DECIMAL},
            RIQI1 =  #{RIQI1,jdbcType=TIMESTAMP},
            RIQI2 =  #{RIQI2,jdbcType=TIMESTAMP},
            RIQI3 =  #{RIQI3,jdbcType=TIMESTAMP},
            BIAOZHIWEI1 =  #{BIAOZHIWEI1,jdbcType=DECIMAL},
            BIAOZHIWEI2 =  #{BIAOZHIWEI2,jdbcType=DECIMAL},
            BIAOZHIWEI3 =  #{BIAOZHIWEI3,jdbcType=DECIMAL},
            DAIMAJC =  #{DAIMAJC,jdbcType=VARCHAR},
            ZIFU6 =  #{ZIFU6,jdbcType=VARCHAR},
            ZIFU7 =  #{ZIFU7,jdbcType=VARCHAR},
            ZIFU8 =  #{ZIFU8,jdbcType=VARCHAR},
            ZIFU9 =  #{ZIFU9,jdbcType=VARCHAR},
            ZIFU10 =  #{ZIFU10,jdbcType=VARCHAR},
            SHUZI4 =  #{SHUZI4,jdbcType=DECIMAL},
            SHUZI5 =  #{SHUZI5,jdbcType=DECIMAL},
            SHIYONGFW =  #{SHIYONGFW,jdbcType=VARCHAR},
            YISHENGKS =  #{YISHENGKS,jdbcType=VARCHAR},
            YINGWENMING =  #{YINGWENMING,jdbcType=VARCHAR},
            ZIFU11 =  #{ZIFU11,jdbcType=VARCHAR},
            ZIFU12 =  #{ZIFU12,jdbcType=VARCHAR},
            ZIFU13 =  #{ZIFU13,jdbcType=VARCHAR},
            ZIFU14 =  #{ZIFU14,jdbcType=VARCHAR},
            ZIFU15 =  #{ZIFU15,jdbcType=VARCHAR},
            ZIFU16 =  #{ZIFU16,jdbcType=VARCHAR},
            ZIFU17 =  #{ZIFU17,jdbcType=VARCHAR},
            ZIFU18 =  #{ZIFU18,jdbcType=VARCHAR},
            ZIFU19 =  #{ZIFU19,jdbcType=VARCHAR},
            ZIFU20 =  #{ZIFU20,jdbcType=VARCHAR},
            MORENPC =  #{MORENPC,jdbcType=DECIMAL},
            ZIFU21 =  #{ZIFU21,jdbcType=VARCHAR},
            LIUQIANGSY =  #{LIUQIANGSY,jdbcType=DECIMAL},
            YOUXIANJI =  #{YOUXIANJI,jdbcType=DECIMAL},
            JIZHENSY =  #{JIZHENSY,jdbcType=DECIMAL},
            TIJIANSY =  #{TIJIANSY,jdbcType=DECIMAL},
            YUANQUSY =  #{YUANQUSY,jdbcType=VARCHAR},
            SHENGHRBM =  #{SHENGHRBM,jdbcType=VARCHAR},
            SHENGHRMC =  #{SHENGHRMC,jdbcType=VARCHAR},
            JIANYANBZBM =  #{JIANYANBZBM,jdbcType=VARCHAR},
            JIANYANBZMC =  #{JIANYANBZMC,jdbcType=VARCHAR},
            GCPXMBZ =  #{GCPXMBZ,jdbcType=VARCHAR},
            KAIFANGQY =  #{KAIFANGQY,jdbcType=VARCHAR},
            SHENGHRMC2 =  #{SHENGHRMC2,jdbcType=VARCHAR},
            SHENGHRBM2 =  #{SHENGHRBM2,jdbcType=VARCHAR},
            YUANQIANSY =  #{YUANQIANSY,jdbcType=DECIMAL},
            JIANCHABW =  #{JIANCHABW,jdbcType=VARCHAR},
            JIANCHASB =  #{JIANCHASB,jdbcType=VARCHAR},
            JIANCHAFX =  #{JIANCHAFX,jdbcType=VARCHAR},
            DUOBUWEIBZ =  #{DUOBUWEIBZ,jdbcType=DECIMAL},
            ZHIQINGTYS =  #{ZHIQINGTYS,jdbcType=VARCHAR}
        where  
    </update>
</mapper>
