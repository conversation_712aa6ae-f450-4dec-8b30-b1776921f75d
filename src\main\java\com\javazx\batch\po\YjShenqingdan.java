package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 医技_申请单
 * @TableName YJ_SHENQINGDAN
 */
@TableName(value ="YJ_SHENQINGDAN")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YjShenqingdan implements Serializable {
    /**
     * 申请单ID
     */
    @TableId(value = "SHENQINDANID")
    private String SHENQINDANID;

    /**
     * 应用ID
     */
    @TableField(value = "YINGYONGID")
    private String YINGYONGID;

    /**
     * 院区ID
     */
    @TableField(value = "YUANQUID")
    private String YUANQUID;

    /**
     * 模版代码
     */
    @TableField(value = "MOBANDM")
    private String MOBANDM;

    /**
     * 报告单模版代码
     */
    @TableField(value = "BAOGAODMBDM")
    private String BAOGAODMBDM;

    /**
     * 医嘱ID
     */
    @TableField(value = "YIZHUID")
    private String YIZHUID;

    /**
     * 医嘱项目ID
     */
    @TableField(value = "YIZHUXMID")
    private String YIZHUXMID;

    /**
     * 医嘱名称
     */
    @TableField(value = "YIZHUMC")
    private String YIZHUMC;

    /**
     * 就诊ID
     */
    @TableField(value = "JIUZHENID")
    private String JIUZHENID;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 病人ID
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 婴儿ID
     */
    @TableField(value = "YINGERID")
    private String YINGERID;

    /**
     * 病人姓名
     */
    @TableField(value = "BINGRENXM")
    private String BINGRENXM;

    /**
     * 性别
     */
    @TableField(value = "XINGBIE")
    private String XINGBIE;

    /**
     * 年龄
     */
    @TableField(value = "NIANLING")
    private Integer NIANLING;

    /**
     * 年龄单位HR6-342(462214)
     */
    @TableField(value = "NIANLINGDW")
    private String NIANLINGDW;

    /**
     * 出生日期
     */
    @TableField(value = "CHUSHENGRQ")
    private LocalDateTime CHUSHENGRQ;

    /**
     * 输入人
     */
    @TableField(value = "SHURUREN")
    private String SHURUREN;

    /**
     * 输入时间
     */
    @TableField(value = "SHURUSJ")
    private LocalDateTime SHURUSJ;

    /**
     * 开单人
     */
    @TableField(value = "KAIDANREN")
    private String KAIDANREN;

    /**
     * 开单科室
     */
    @TableField(value = "KAIDANKS")
    private String KAIDANKS;

    /**
     * 开单日期
     */
    @TableField(value = "KAIDANRQ")
    private LocalDateTime KAIDANRQ;

    /**
     * 检查科室
     */
    @TableField(value = "JIANCHAKS")
    private String JIANCHAKS;

    /**
     * 检查人
     */
    @TableField(value = "JIANCHAREN")
    private String JIANCHAREN;

    /**
     * 检查日期
     */
    @TableField(value = "JIANCHARQ")
    private LocalDateTime JIANCHARQ;

    /**
     * 门诊住院标志0门诊1住院2体检
     */
    @TableField(value = "MENZHENZYBZ")
    private Integer MENZHENZYBZ;

    /**
     * 当前状态1新开单2待划价3待登记4已预约5已安排6已完成7已报告8已打印9已撤销10已退单11已发送未接收
     */
    @TableField(value = "DANGQIANZT")
    private String DANGQIANZT;

    /**
     * 期望检查日期
     */
    @TableField(value = "QIWANGJCRQ")
    private LocalDateTime QIWANGJCRQ;

    /**
     * 预约申请人
     */
    @TableField(value = "YUYUESQR")
    private String YUYUESQR;

    /**
     * 预约申请日期
     */
    @TableField(value = "YUYUESQRQ")
    private LocalDateTime YUYUESQRQ;

    /**
     * 预约安排科室
     */
    @TableField(value = "YUYUEAPKS")
    private String YUYUEAPKS;

    /**
     * 预约确认日期
     */
    @TableField(value = "YUYUEQRRQ")
    private LocalDateTime YUYUEQRRQ;

    /**
     * 预约确认人
     */
    @TableField(value = "YUYUEQRR")
    private String YUYUEQRR;

    /**
     * 检查安排日期
     */
    @TableField(value = "JIANCHAAPRQ")
    private LocalDateTime JIANCHAAPRQ;

    /**
     * 检查安排人
     */
    @TableField(value = "JIANCHAAPR")
    private String JIANCHAAPR;

    /**
     * 报告单方式
     */
    @TableField(value = "BAOGAODFS")
    private Integer BAOGAODFS;

    /**
     * 打印人
     */
    @TableField(value = "DAYINREN")
    private String DAYINREN;

    /**
     * 打印日期
     */
    @TableField(value = "DAYINRQ")
    private LocalDateTime DAYINRQ;

    /**
     * 优先级0普通，1加急，2危急
     */
    @TableField(value = "YOUXIANJI")
    private String YOUXIANJI;

    /**
     * 收费标志
     */
    @TableField(value = "SHOUFEIBZ")
    private Integer SHOUFEIBZ;

    /**
     * 撤销人
     */
    @TableField(value = "CHEXIAOREN")
    private String CHEXIAOREN;

    /**
     * 撤销时间
     */
    @TableField(value = "CHEXIAOSJ")
    private LocalDateTime CHEXIAOSJ;

    /**
     * 主诉HR3-11445(153529)
     */
    @TableField(value = "ZHUSU")
    private String ZHUSU;

    /**
     * 简要病史
     */
    @TableField(value = "JIANYAOBS")
    private String JIANYAOBS;

    /**
     * 体格检查
     */
    @TableField(value = "TIGEJC")
    private String TIGEJC;

    /**
     * 临床诊断
     */
    @TableField(value = "LINCHUANGZD")
    private String LINCHUANGZD;

    /**
     * 检查部位
     */
    @TableField(value = "JIANCHABW")
    private String JIANCHABW;

    /**
     * 检查目的
     */
    @TableField(value = "JIANCHAMD")
    private String JIANCHAMD;

    /**
     * 相关检查HR3-18613(215110)
     */
    @TableField(value = "XIANGGUANJC")
    private String XIANGGUANJC;

    /**
     * 检查类型
     */
    @TableField(value = "JIANCHALX")
    private String JIANCHALX;

    /**
     * 退单人
     */
    @TableField(value = "TUIDANREN")
    private String TUIDANREN;

    /**
     * 退单日期
     */
    @TableField(value = "TUIDANRQ")
    private LocalDateTime TUIDANRQ;

    /**
     * 退单人姓名
     */
    @TableField(value = "TUIDANRXM")
    private String TUIDANRXM;

    /**
     * 退单原因
     */
    @TableField(value = "TUIDANYY")
    private String TUIDANYY;

    /**
     * 费用合计
     */
    @TableField(value = "FEIYONGHJ")
    private BigDecimal FEIYONGHJ;

    /**
     * 已收金额
     */
    @TableField(value = "YISHOUJE")
    private BigDecimal YISHOUJE;

    /**
     * 可执行标志
     */
    @TableField(value = "KEZHIXBZ")
    private Integer KEZHIXBZ;

    /**
     * 预约确认日期2
     */
    @TableField(value = "YUYUEQRRQ2")
    private String YUYUEQRRQ2;

    /**
     * 预约号
     */
    @TableField(value = "YUYUEHAO")
    private String YUYUEHAO;

    /**
     * 床边标志
     */
    @TableField(value = "CHUANGBIANBZ")
    private Integer CHUANGBIANBZ;

    /**
     * 特殊说明
     */
    @TableField(value = "TESHUSM")
    private String TESHUSM;

    /**
     * 备注
     */
    @TableField(value = "BEIZHU")
    private String BEIZHU;

    /**
     * RIS登记号码
     */
    @TableField(value = "RISDJHM")
    private String RISDJHM;

    /**
     * 自费标志
     */
    @TableField(value = "ZIFEIBZ")
    private Integer ZIFEIBZ;

    /**
     * 预约打印标志
     */
    @TableField(value = "YUYUEDYBZ")
    private Integer YUYUEDYBZ;

    /**
     * 药理基地免单标志
     */
    @TableField(value = "YAOLIJDMDBZ")
    private Integer YAOLIJDMDBZ;

    /**
     * 注意事项
     */
    @TableField(value = "ZHUYISX")
    private String ZHUYISX;

    /**
     * 急诊标志
     */
    @TableField(value = "JIZHENBZ")
    private Integer JIZHENBZ;

    /**
     * 特殊病种名称显示HR3-10919(149050)
     */
    @TableField(value = "TESHUBZMC")
    private String TESHUBZMC;

    /**
     * 检查所见
     */
    @TableField(value = "JIANCHASJ")
    private String JIANCHASJ;

    /**
     * 诊断结果HR3-10935(149340)
     */
    @TableField(value = "ZHENDUANJG")
    private String ZHENDUANJG;

    /**
     * 危机值标志HR3-12592(163923)
     */
    @TableField(value = "WEIJIZBZ")
    private Integer WEIJIZBZ;

    /**
     * 胶片费标志HR3-14507(184101)
     */
    @TableField(value = "JIAOPIANFBZ")
    private Integer JIAOPIANFBZ;

    /**
     * 诊断类型HR3-14892(187986)
     */
    @TableField(value = "ZHENDUANLX")
    private String ZHENDUANLX;

    /**
     * 诊断意见HR3-14892(187986)
     */
    @TableField(value = "ZHENDUANYJ")
    private String ZHENDUANYJ;

    /**
     * HR3-14987(189112) 收缩压
     */
    @TableField(value = "SHOUSUOYA")
    private BigDecimal SHOUSUOYA;

    /**
     * HR3-14987(189112) 舒张压
     */
    @TableField(value = "SHUZHANGYA")
    private BigDecimal SHUZHANGYA;

    /**
     * ZIFU1自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU1")
    private String ZIFU1;

    /**
     * ZIFU2自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU2")
    private String ZIFU2;

    /**
     * ZIFU3自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU3")
    private String ZIFU3;

    /**
     * ZIFU4自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU4")
    private String ZIFU4;

    /**
     * ZIFU5自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU5")
    private String ZIFU5;

    /**
     * ZIFU6自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU6")
    private String ZIFU6;

    /**
     * ZIFU7自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU7")
    private String ZIFU7;

    /**
     * ZIFU8自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU8")
    private String ZIFU8;

    /**
     * ZIFU9自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU9")
    private String ZIFU9;

    /**
     * ZIFU10自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU10")
    private String ZIFU10;

    /**
     * ZIFU11自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU11")
    private String ZIFU11;

    /**
     * ZIFU12自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU12")
    private String ZIFU12;

    /**
     * ZIFU13自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU13")
    private String ZIFU13;

    /**
     * ZIFU14自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU14")
    private String ZIFU14;

    /**
     * ZIFU15自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU15")
    private String ZIFU15;

    /**
     * ZIFU16自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU16")
    private String ZIFU16;

    /**
     * ZIFU17自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU17")
    private String ZIFU17;

    /**
     * ZIFU18自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU18")
    private String ZIFU18;

    /**
     * ZIFU19自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU19")
    private String ZIFU19;

    /**
     * ZIFU20自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU20")
    private String ZIFU20;

    /**
     * ZIFU21自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU21")
    private String ZIFU21;

    /**
     * ZIFU22自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU22")
    private String ZIFU22;

    /**
     * ZIFU23自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU23")
    private String ZIFU23;

    /**
     * ZIFU24自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU24")
    private String ZIFU24;

    /**
     * ZIFU25自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU25")
    private String ZIFU25;

    /**
     * ZIFU26自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU26")
    private String ZIFU26;

    /**
     * ZIFU27自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU27")
    private String ZIFU27;

    /**
     * ZIFU28自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU28")
    private String ZIFU28;

    /**
     * ZIFU29自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU29")
    private String ZIFU29;

    /**
     * ZIFU30自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU30")
    private String ZIFU30;

    /**
     * ZIFU31自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU31")
    private String ZIFU31;

    /**
     * ZIFU32自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU32")
    private String ZIFU32;

    /**
     * ZIFU33自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU33")
    private String ZIFU33;

    /**
     * ZIFU34自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU34")
    private String ZIFU34;

    /**
     * ZIFU35自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU35")
    private String ZIFU35;

    /**
     * ZIFU36自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU36")
    private String ZIFU36;

    /**
     * ZIFU37自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU37")
    private String ZIFU37;

    /**
     * ZIFU38自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU38")
    private String ZIFU38;

    /**
     * ZIFU39自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU39")
    private String ZIFU39;

    /**
     * ZIFU40自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU40")
    private String ZIFU40;

    /**
     * ZIFU41自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU41")
    private String ZIFU41;

    /**
     * ZIFU42自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU42")
    private String ZIFU42;

    /**
     * ZIFU43自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU43")
    private String ZIFU43;

    /**
     * ZIFU44自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU44")
    private String ZIFU44;

    /**
     * ZIFU45自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU45")
    private String ZIFU45;

    /**
     * ZIFU46自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU46")
    private String ZIFU46;

    /**
     * ZIFU47自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU47")
    private String ZIFU47;

    /**
     * ZIFU48自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU48")
    private String ZIFU48;

    /**
     * ZIFU49自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU49")
    private String ZIFU49;

    /**
     * ZIFU50自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU50")
    private String ZIFU50;

    /**
     * ZIFU51自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU51")
    private String ZIFU51;

    /**
     * ZIFU52自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU52")
    private String ZIFU52;

    /**
     * ZIFU53自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU53")
    private String ZIFU53;

    /**
     * ZIFU54自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU54")
    private String ZIFU54;

    /**
     * ZIFU55自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU55")
    private String ZIFU55;

    /**
     * ZIFU56自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU56")
    private String ZIFU56;

    /**
     * ZIFU57自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU57")
    private String ZIFU57;

    /**
     * ZIFU58自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU58")
    private String ZIFU58;

    /**
     * ZIFU59自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU59")
    private String ZIFU59;

    /**
     * ZIFU60自定义HR3-15374(193160)
     */
    @TableField(value = "ZIFU60")
    private String ZIFU60;

    /**
     * 审核人姓名HR3-16015(198148)
     */
    @TableField(value = "SHENHERXM")
    private String SHENHERXM;

    /**
     * 报告人姓名HR3-16015(198148)
     */
    @TableField(value = "BAOGAORXM")
    private String BAOGAORXM;

    /**
     * 报告日期HR3-16015(198148)
     */
    @TableField(value = "BAOGAORQ")
    private LocalDateTime BAOGAORQ;

    /**
     * 检查号HR3-16796(203449)
     */
    @TableField(value = "JIANCHAHAO")
    private String JIANCHAHAO;

    /**
     * 检查机房HR3-16796(203449)
     */
    @TableField(value = "JIANCHAJF")
    private String JIANCHAJF;

    /**
     * 预约注意事项HR3-16796(203449)
     */
    @TableField(value = "YUYUEZYSX")
    private String YUYUEZYSX;

    /**
     * 图文报告费标志HR3-17216(206361)
     */
    @TableField(value = "TUWENBGFBZ")
    private Integer TUWENBGFBZ;

    /**
     * 未收先执行标志HR3-20143(234798)
     */
    @TableField(value = "WEISHOUXZXBZ")
    private Integer WEISHOUXZXBZ;

    /**
     * 已查看报告(267268)
     */
    @TableField(value = "YICHAKBG")
    private String YICHAKBG;

    /**
     * 病人套餐明细ID
     */
    @TableField(value = "TAOCANXSBRMXID")
    private String TAOCANXSBRMXID;

    /**
     * 病人套餐标志
     */
    @TableField(value = "TAOCANXSBZ")
    private Integer TAOCANXSBZ;

    /**
     * 电子病历使用1(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY1")
    private String DIANZIBLSY1;

    /**
     * 电子病历使用2(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY2")
    private String DIANZIBLSY2;

    /**
     * 电子病历使用3(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY3")
    private String DIANZIBLSY3;

    /**
     * 电子病历使用4(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY4")
    private String DIANZIBLSY4;

    /**
     * 电子病历使用5(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY5")
    private String DIANZIBLSY5;

    /**
     * 电子病历使用6(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY6")
    private String DIANZIBLSY6;

    /**
     * 电子病历使用7(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY7")
    private String DIANZIBLSY7;

    /**
     * 电子病历使用8(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY8")
    private String DIANZIBLSY8;

    /**
     * 电子病历使用9(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY9")
    private String DIANZIBLSY9;

    /**
     * 电子病历使用10(HR3-30975(308698))
     */
    @TableField(value = "DIANZIBLSY10")
    private String DIANZIBLSY10;

    /**
     * 
     */
    @TableField(value = "FANGWENHAO")
    private String FANGWENHAO;

    /**
     * 
     */
    @TableField(value = "YINGXIANGHAO")
    private String YINGXIANGHAO;

    /**
     * 主治医生HR3-44546(387839)
     */
    @TableField(value = "ZHUZHIYS")
    private String ZHUZHIYS;

    /**
     * 病人医疗组HR3-44546(387839)
     */
    @TableField(value = "BINGRENYLZ")
    private String BINGRENYLZ;

    /**
     * 开单人医疗组HR3-44546(387839)
     */
    @TableField(value = "YISHENGZID")
    private String YISHENGZID;

    /**
     * 计费标志
HR3-52786(435482)
     */
    @TableField(value = "JIFEIBZ")
    private Integer JIFEIBZ;

    /**
     * 计费人
HR3-52786(435482)
     */
    @TableField(value = "JIFEIREN")
    private String JIFEIREN;

    /**
     * 计费时间
HR3-52786(435482)
     */
    @TableField(value = "JIFEISJ")
    private LocalDateTime JIFEISJ;

    /**
     * 计费科室
HR3-52786(435482)
     */
    @TableField(value = "JIFEIKS")
    private String JIFEIKS;

    /**
     * 报告地址
     */
    @TableField(value = "BAOGAODZ")
    private String BAOGAODZ;

    /**
     * 多重耐药标志HR6-1379(515282)
     */
    @TableField(value = "DUOCHONGNYBZ")
    private Integer DUOCHONGNYBZ;

    /**
     * 报告查看标志HR6-1359(514715)
     */
    @TableField(value = "BAOGAOCKBZ")
    private Integer BAOGAOCKBZ;

    /**
     * 报告查看时间HR6-1359(514715)
     */
    @TableField(value = "BAOGAOCKSJ")
    private LocalDateTime BAOGAOCKSJ;

    /**
     * 报告查看人HR6-1359(514715)
     */
    @TableField(value = "BAOGAOCKR")
    private String BAOGAOCKR;

    /**
     * 护送方式HR6-1750(526369)
     */
    @TableField(value = "HUSONGFS")
    private String HUSONGFS;

    /**
     * 
     */
    @TableField(value = "ZUTUO")
    private String ZUTUO;

    /**
     * 送检时间
     */
    @TableField(value = "SONGJIANSJ")
    private LocalDateTime SONGJIANSJ;

    /**
     * 送检医生
     */
    @TableField(value = "SONGJIANYS")
    private String SONGJIANYS;

    /**
     * 对应HIS1(gy_yxsqd.sqdh)申请单号
     */
    @TableField(value = "SQDH")
    private String SQDH;

    /**
     * 病人查看报告标志HR6-2453(551208)床旁交互用
     */
    @TableField(value = "BINGRENCKBGBZ")
    private Integer BINGRENCKBGBZ;

    /**
     * 病人查看报告时间HR6-2453(551208)床旁交互用
     */
    @TableField(value = "BINGRENCKBGSJ")
    private LocalDateTime BINGRENCKBGSJ;

    /**
     * 计费模式：1：登记后计费，2：执行后计费，3：报告完成后计费，4： 护士站生成执行计划计费
     */
    @TableField(value = "JIFEIMS")
    private Integer JIFEIMS;

    /**
     * 检查知情同意书病历记录序号IDHR6-2962(568210)
     */
    @TableField(value = "ZHIQINGTYSBLJLXH")
    private Long ZHIQINGTYSBLJLXH;

    /**
     * 
     */
    @TableField(value = "YIZHUXMFLID")
    private String YIZHUXMFLID;

    /**
     * 院前标志
     */
    @TableField(value = "YUANQIANBZ")
    private Integer YUANQIANBZ;

    /**
     * 危急标志(HR6-5104)
     */
    @TableField(value = "WEIJIBZ")
    private String WEIJIBZ;

    /**
     * GCP标志HR6-5650(629378)
     */
    @TableField(value = "GCPBZ")
    private Integer GCPBZ;

    /**
     * 预约标志HR6-5716(630984)
     */
    @TableField(value = "YUYUEBZ")
    private Integer YUYUEBZ;

    /**
     * 申请单接口ID
     */
    @TableField(value = "SHENQINGDJKID")
    private String SHENQINGDJKID;

    /**
     * 病理状态：1.申请2.确认3.发送4.撤销5.取消6.中心接收7.中心取消接收
     */
    @TableField(value = "BINGLIZT")
    private String BINGLIZT;

    /**
     * 互联网申请单IDHR6-6175(641485)
     */
    @TableField(value = "HULIANWSQDID")
    private String HULIANWSQDID;

    /**
     * 病理中心预约标志 0 未预约  1 已预约 HR5-12044(641168)
     */
    @TableField(value = "ZHONGXYYBZ")
    private Integer ZHONGXYYBZ;

    /**
     * 组号
     */
    @TableField(value = "ZUHAO")
    private String ZUHAO;

    /**
     * 急诊原因
     */
    @TableField(value = "JIZHENYY")
    private String JIZHENYY;

    /**
     * 病理取消计费标志
     */
    @TableField(value = "BINGLIQXJFBZ")
    private Integer BINGLIQXJFBZ;

    /**
     * 同类型医嘱id,用于在医嘱删除时的级联删除判断 603018
     */
    @TableField(value = "TONGLEIYZID")
    private String TONGLEIYZID;

    /**
     * 
     */
    @TableField(value = "ZENGQIANGSMBZ")
    private Integer ZENGQIANGSMBZ;

    /**
     * 云影像标志
     */
    @TableField(value = "YUNYXBZ")
    private Integer YUNYXBZ;

    /**
     * ORDERID
     */
    @TableField(value = "ORDERID")
    private String ORDERID;

    /**
     * 固体时间
     */
    @TableField(value = "GUTISJ")
    private LocalDateTime GUTISJ;

    /**
     * 离体时间
     */
    @TableField(value = "LITISJ")
    private LocalDateTime LITISJ;

    /**
     * 病人号
     */
    @TableField(value = "BINGRENHAO")
    private String BINGRENHAO;

    /**
     * 申请报告日期
     */
    @TableField(value = "SHENHERQ")
    private LocalDateTime SHENHERQ;

    /**
     * 互认医院名称
     */
    @TableField(value = "HOSPITAL_NAME")
    private String HOSPITAL_NAME;

    /**
     * 报告备注
     */
    @TableField(value = "BAOGAOBZ")
    private String BAOGAOBZ;

    /**
     * 项目组合申请单号（组合联动项目专用：将主项目的申请单号存在此字段）
     */
    @TableField(value = "FUJIAXMSQD")
    private String FUJIAXMSQD;

    /**
     * 预约时间
     */
    @TableField(value = "YUYUESJ")
    private String YUYUESJ;

    /**
     * 普通检查申请单关联的病理申请单,用于Web病理开单使用
     */
    @TableField(value = "GUANLIANSQDID")
    private String GUANLIANSQDID;

    /**
     * 存储pacs返回的房间号
     */
    @TableField(value = "FANGJIANHAO")
    private String FANGJIANHAO;

    /**
     * 开单院区ID
     */
    @TableField(value = "KAIDANYQID")
    private String KAIDANYQID;

    /**
     * 恶性肿瘤标志-湖州中
     */
    @TableField(value = "EXINGZLBZ")
    private Integer EXINGZLBZ;

    /**
     * 优惠类别
     */
    @TableField(value = "YOUHUILB")
    private String YOUHUILB;

    /**
     * 部位数量
     */
    @TableField(value = "BUWEISL")
    private Integer BUWEISL;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}