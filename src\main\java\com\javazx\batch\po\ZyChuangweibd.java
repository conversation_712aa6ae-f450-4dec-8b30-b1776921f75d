package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 住院_床位变动
 * @TableName ZY_CHUANGWEIBD
 */
@TableName(value ="ZY_CHUANGWEIBD")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZyChuangweibd implements Serializable {
    /**
     * 床位变动ID
     */
    @TableId(value = "CHUANGWEIBDID")
    private String CHUANGWEIBDID;

    /**
     * 病人住院ID
     */
    @TableField(value = "BINGRENZYID")
    private String BINGRENZYID;

    /**
     * 结算序号
     */
    @TableField(value = "JIESUANXH")
    private Integer JIESUANXH;

    /**
     * 科室1
     */
    @TableField(value = "KESHI1")
    private String KESHI1;

    /**
     * 病区1
     */
    @TableField(value = "BINGQU1")
    private String BINGQU1;

    /**
     * 床位1HR3-32668(319040)
     */
    @TableField(value = "CHUANGWEI1")
    private String CHUANGWEI1;

    /**
     * 单价1
     */
    @TableField(value = "DANJIA1")
    private BigDecimal DANJIA1;

    /**
     * 科室2
     */
    @TableField(value = "KESHI2")
    private String KESHI2;

    /**
     * 病区2
     */
    @TableField(value = "BINGQU2")
    private String BINGQU2;

    /**
     * 床位2HR3-32668(319040)
     */
    @TableField(value = "CHUANGWEI2")
    private String CHUANGWEI2;

    /**
     * 单价2
     */
    @TableField(value = "DANJIA2")
    private BigDecimal DANJIA2;

    /**
     * 主治医师1
     */
    @TableField(value = "ZHUZHIYS1")
    private String ZHUZHIYS1;

    /**
     * 主任医师1
     */
    @TableField(value = "ZHURENYS1")
    private String ZHURENYS1;

    /**
     * 住院医师1
     */
    @TableField(value = "ZHUYUANYS1")
    private String ZHUYUANYS1;

    /**
     * 主治医师2
     */
    @TableField(value = "ZHUZHIYS2")
    private String ZHUZHIYS2;

    /**
     * 主任医师2
     */
    @TableField(value = "ZHURENYS2")
    private String ZHURENYS2;

    /**
     * 住院医师2
     */
    @TableField(value = "ZHUYUANYS2")
    private String ZHUYUANYS2;

    /**
     * 变动原因  0空床，1分配，2借床，  3换床，4转科，5包床，6包房，7床位归还，8借床归还，9包床归还，10包房归还，11入院调整，12出院调整，13入科，14预约，15取消预约，16冻结，17解冻,18修改
     */
    @TableField(value = "BIANDONGYY")
    private String BIANDONGYY;

    /**
     * 系统时间
     */
    @TableField(value = "XITONGSJ")
    private LocalDateTime XITONGSJ;

    /**
     * 操作员
     */
    @TableField(value = "CAOZUOYUAN")
    private String CAOZUOYUAN;

    /**
     * 婴儿ID
     */
    @TableField(value = "YINGERID")
    private String YINGERID;

    /**
     * 病人ID
     */
    @TableField(value = "BINGRENID")
    private String BINGRENID;

    /**
     * 业务日期
     */
    @TableField(value = "YEWURQ")
    private LocalDateTime YEWURQ;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 结束时间HR3-12383(161800)借床归还的时间
     */
    @TableField(value = "JIESHUSJ")
    private LocalDateTime JIESHUSJ;

    /**
     * 转出时间HR3-15360(193097) 下次转科的系统时间插入到上次转科的转出时间
     */
    @TableField(value = "ZHUANCHUSJ")
    private LocalDateTime ZHUANCHUSJ;

    /**
     * 计费标志HR3-16052(198384)
     */
    @TableField(value = "JIFEIBZ")
    private Integer JIFEIBZ;

    /**
     * 借床目的HR3-28212(289896)1入科2借床 参数决定借床入科是否算入院病人
     */
    @TableField(value = "JIECHUANGMD")
    private Integer JIECHUANGMD;

    /**
     * 实际转出时间  HR6-890(491223)
     */
    @TableField(value = "SHIJIZCSJ")
    private LocalDateTime SHIJIZCSJ;

    /**
     * 实际转出人  HR6-890(491223)
     */
    @TableField(value = "SHIJIZCR")
    private String SHIJIZCR;

    /**
     * 预约病人ID
     */
    @TableField(value = "YUYUEBRID")
    private String YUYUEBRID;

    /**
     * 
     */
    @TableField(value = "BEIZHU")
    private String BEIZHU;

    /**
     * 
     */
    @TableField(value = "YILIAOZU1")
    private String YILIAOZU1;

    /**
     * 
     */
    @TableField(value = "YILIAOZU2")
    private String YILIAOZU2;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}