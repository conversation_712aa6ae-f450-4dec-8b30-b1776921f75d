package com.javazx.batch.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公用_代码
 * @TableName GY_DAIMA
 */
@TableName(value ="GY_DAIMA")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GyDaima implements Serializable {
    /**
     * 代码ID
     */
    @TableField(value = "DAIMAID")
    private String DAIMAID;

    /**
     * 代码类别
     */
    @TableField(value = "DAIMALB")
    private String DAIMALB;

    /**
     * 代码名称
     */
    @TableField(value = "DAIMAMC")
    private String DAIMAMC;

    /**
     * 级次
     */
    @TableField(value = "JICI")
    private Integer JICI;

    /**
     * 父类代码
     */
    @TableField(value = "FULEIDM")
    private String FULEIDM;

    /**
     * 末级标志
     */
    @TableField(value = "MOJIBZ")
    private Integer MOJIBZ;

    /**
     * 作废标志
     */
    @TableField(value = "ZUOFEIBZ")
    private Integer ZUOFEIBZ;

    /**
     * 顺序号
     */
    @TableField(value = "SHUNXUHAO")
    private Integer SHUNXUHAO;

    /**
     * 门诊使用
     */
    @TableField(value = "MENZHENSY")
    private Integer MENZHENSY;

    /**
     * 住院使用
     */
    @TableField(value = "ZHUYUANSY")
    private Integer ZHUYUANSY;

    /**
     * 输入码1
     */
    @TableField(value = "SHURUMA1")
    private String SHURUMA1;

    /**
     * 输入码2
     */
    @TableField(value = "SHURUMA2")
    private String SHURUMA2;

    /**
     * 输入码3
     */
    @TableField(value = "SHURUMA3")
    private String SHURUMA3;

    /**
     * 系统标志
     */
    @TableField(value = "XITONGBZ")
    private Integer XITONGBZ;

    /**
     * 修改人
     */
    @TableField(value = "XIUGAIREN")
    private String XIUGAIREN;

    /**
     * 修改时间
     */
    @TableField(value = "XIUGAISJ")
    private Date XIUGAISJ;

    /**
     * 字符1
     */
    @TableField(value = "ZIFU1")
    private String ZIFU1;

    /**
     * 字符2
     */
    @TableField(value = "ZIFU2")
    private String ZIFU2;

    /**
     * 字符3
     */
    @TableField(value = "ZIFU3")
    private String ZIFU3;

    /**
     * 字符4
     */
    @TableField(value = "ZIFU4")
    private String ZIFU4;

    /**
     * 字符5
     */
    @TableField(value = "ZIFU5")
    private String ZIFU5;

    /**
     * 数字1
     */
    @TableField(value = "SHUZI1")
    private BigDecimal SHUZI1;

    /**
     * 数字2
     */
    @TableField(value = "SHUZI2")
    private BigDecimal SHUZI2;

    /**
     * 数字3
     */
    @TableField(value = "SHUZI3")
    private BigDecimal SHUZI3;

    /**
     * 日期1
     */
    @TableField(value = "RIQI1")
    private Date RIQI1;

    /**
     * 日期2
     */
    @TableField(value = "RIQI2")
    private Date RIQI2;

    /**
     * 日期3
     */
    @TableField(value = "RIQI3")
    private Date RIQI3;

    /**
     * 标志位1
     */
    @TableField(value = "BIAOZHIWEI1")
    private Integer BIAOZHIWEI1;

    /**
     * 标志位2
     */
    @TableField(value = "BIAOZHIWEI2")
    private Integer BIAOZHIWEI2;

    /**
     * 标志位3
     */
    @TableField(value = "BIAOZHIWEI3")
    private Integer BIAOZHIWEI3;

    /**
     * 代码简称
     */
    @TableField(value = "DAIMAJC")
    private String DAIMAJC;

    /**
     * 字符6(等同于shiyongfw列)
     */
    @TableField(value = "ZIFU6")
    private String ZIFU6;

    /**
     * 字符7
     */
    @TableField(value = "ZIFU7")
    private String ZIFU7;

    /**
     * 字符8
     */
    @TableField(value = "ZIFU8")
    private String ZIFU8;

    /**
     * 字符9
     */
    @TableField(value = "ZIFU9")
    private String ZIFU9;

    /**
     * 字符10
     */
    @TableField(value = "ZIFU10")
    private String ZIFU10;

    /**
     * 数值4
     */
    @TableField(value = "SHUZI4")
    private BigDecimal SHUZI4;

    /**
     * 数值5
     */
    @TableField(value = "SHUZI5")
    private BigDecimal SHUZI5;

    /**
     * 适用范围
     */
    @TableField(value = "SHIYONGFW")
    private String SHIYONGFW;

    /**
     * 医生科室
     */
    @TableField(value = "YISHENGKS")
    private String YISHENGKS;

    /**
     * 英文名
     */
    @TableField(value = "YINGWENMING")
    private String YINGWENMING;

    /**
     * 字符11
     */
    @TableField(value = "ZIFU11")
    private String ZIFU11;

    /**
     * 字符12
     */
    @TableField(value = "ZIFU12")
    private String ZIFU12;

    /**
     * 字符13
     */
    @TableField(value = "ZIFU13")
    private String ZIFU13;

    /**
     * 字符14
     */
    @TableField(value = "ZIFU14")
    private String ZIFU14;

    /**
     * 字符15
     */
    @TableField(value = "ZIFU15")
    private String ZIFU15;

    /**
     * 字符16
     */
    @TableField(value = "ZIFU16")
    private String ZIFU16;

    /**
     * 字符17
     */
    @TableField(value = "ZIFU17")
    private String ZIFU17;

    /**
     * 字符18
     */
    @TableField(value = "ZIFU18")
    private String ZIFU18;

    /**
     * 字符19
     */
    @TableField(value = "ZIFU19")
    private String ZIFU19;

    /**
     * 字符20
     */
    @TableField(value = "ZIFU20")
    private String ZIFU20;

    /**
     * 默认频次 HR3-32959(320817)
     */
    @TableField(value = "MORENPC")
    private Integer MORENPC;

    /**
     * 字符21 类别为0073的职工权限使用，字段为职工权限类别 类别为0070的科室性质属性，字段为属性类别
     */
    @TableField(value = "ZIFU21")
    private String ZIFU21;

    /**
     * 留抢使用HR6-2902(566667)
     */
    @TableField(value = "LIUQIANGSY")
    private Integer LIUQIANGSY;

    /**
     * 优先级
     */
    @TableField(value = "YOUXIANJI")
    private Integer YOUXIANJI;

    /**
     * 急诊使用
     */
    @TableField(value = "JIZHENSY")
    private Integer JIZHENSY;

    /**
     * 体检使用
     */
    @TableField(value = "TIJIANSY")
    private Integer TIJIANSY;

    /**
     * 院区使用
     */
    @TableField(value = "YUANQUSY")
    private String YUANQUSY;

    /**
     * 省互认编码
     */
    @TableField(value = "SHENGHRBM")
    private String SHENGHRBM;

    /**
     * 省互认名称
     */
    @TableField(value = "SHENGHRMC")
    private String SHENGHRMC;

    /**
     * 检验中心标本代码
     */
    @TableField(value = "JIANYANBZBM")
    private String JIANYANBZBM;

    /**
     * 检验中心标本名称
     */
    @TableField(value = "JIANYANBZMC")
    private String JIANYANBZMC;

    /**
     * GCP项目标志
     */
    @TableField(value = "GCPXMBZ")
    private String GCPXMBZ;

    /**
     * 开放院区  多院区使用  对应院区勾选 对应院区也能使用
     */
    @TableField(value = "KAIFANGQY")
    private String KAIFANGQY;

    /**
     * 审核人名称--嘉兴中
     */
    @TableField(value = "SHENGHRMC2")
    private String SHENGHRMC2;

    /**
     * 审核人编码--嘉兴中
     */
    @TableField(value = "SHENGHRBM2")
    private String SHENGHRBM2;

    /**
     * 院前使用--嘉兴中
     */
    @TableField(value = "YUANQIANSY")
    private Integer YUANQIANSY;

    /**
     * 检查部位--嘉兴中
     */
    @TableField(value = "JIANCHABW")
    private String JIANCHABW;

    /**
     * --嘉兴中
     */
    @TableField(value = "JIANCHASB")
    private String JIANCHASB;

    /**
     * 检查方向--嘉兴中
     */
    @TableField(value = "JIANCHAFX")
    private String JIANCHAFX;

    /**
     * 多部位标志--嘉兴中
     */
    @TableField(value = "DUOBUWEIBZ")
    private Integer DUOBUWEIBZ;

    /**
     * 知情同意书--嘉兴中
     */
    @TableField(value = "ZHIQINGTYS")
    private String ZHIQINGTYS;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}