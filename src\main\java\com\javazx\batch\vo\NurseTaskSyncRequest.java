package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 护理任务同步请求对象
 * 用于批量同步护理任务数据到外部系统
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class NurseTaskSyncRequest {
    /**
     * 护理任务列表
     */
    private List<NurseTaskReq> nurseTaskList;
}
