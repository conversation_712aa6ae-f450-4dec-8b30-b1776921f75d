package com.javazx.batch.scenario.practice;

import com.javazx.batch.po.YzBingrenyz;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.PracticeDataService;
import com.javazx.batch.util.PatientInfoUtil;
import com.javazx.batch.vo.PatientDiagnosisInfoReq;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientWithDiagnosisReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 诊疗数据处理器
 * 将YzBingrenyz对象转换为PatientWithDiagnosisReq对象用于同步
 */
@Component
public class PracticeProcessor implements ItemProcessor<ZyBingrenxx, PatientWithDiagnosisReq> {

    private static final Logger log = LoggerFactory.getLogger(PracticeProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private final PracticeDataService practiceDataService;
    private final PatientInfoUtil patientInfoUtil;

    public PracticeProcessor(PracticeDataService practiceDataService, PatientInfoUtil patientInfoUtil) {
        this.practiceDataService = practiceDataService;
        this.patientInfoUtil = patientInfoUtil;
    }

    @Override
    public PatientWithDiagnosisReq process(ZyBingrenxx zyBingrenxx) {
        if (zyBingrenxx == null) {
            return null;
        }

        try {
            // 获取该患者的诊疗医嘱 （肾囊肿穿刺、静脉抽血）
            List<YzBingrenyz> patientAdvices = practiceDataService.selectDiagnosisByPatientId(zyBingrenxx.getBINGRENZYID());

            // 转换为API请求对象
            PatientWithDiagnosisReq request = convert(zyBingrenxx, patientAdvices);

            log.debug("完成诊疗数据处理: {}", zyBingrenxx.getXINGMING());
            return request;
            
        } catch (Exception e) {
            log.error("处理诊疗数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换诊疗数据为同步请求
     */
    private PatientWithDiagnosisReq convert(ZyBingrenxx patient, List<YzBingrenyz> advices) {
        PatientWithDiagnosisReq request = new PatientWithDiagnosisReq();

        // 创建患者信息
        PatientInfoReq patientInfo = patientInfoUtil.createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建诊疗信息列表
        List<PatientDiagnosisInfoReq> diagnosisInfoList = new ArrayList<>();
        for (YzBingrenyz advice : advices) {
            PatientDiagnosisInfoReq diagnosisInfo = createDiagnosisInfo(advice, patient);
            diagnosisInfoList.add(diagnosisInfo);
        }
        
        request.setPatientDiagnosisList(diagnosisInfoList);

        return request;
    }

    /**
     * 创建诊疗信息对象
     */
    private PatientDiagnosisInfoReq createDiagnosisInfo(YzBingrenyz advice, ZyBingrenxx patient) {
        PatientDiagnosisInfoReq diagnosisInfo = new PatientDiagnosisInfoReq();

        // 基本信息
        diagnosisInfo.setPatientName(patient.getXINGMING());
        diagnosisInfo.setZyh(patient.getZHUYUANHAO());
        diagnosisInfo.setBedNo(patient.getDANGQIANCW());
        diagnosisInfo.setWardCode(convertWardCodeToLong(patient.getDANGQIANBQ()));

        // 医嘱信息
        diagnosisInfo.setDoctorAdviceName(advice.getYIZHUMC());
        diagnosisInfo.setDoctorAdviceType(convertAdviceType(advice.getYIZHUFL()));
        diagnosisInfo.setDoctorAdviceStatus(convertAdviceStatus(advice.getYIZHUZT()));

        // 时间信息
        if (advice.getKAISHISJ() != null) {
            diagnosisInfo.setDoctorAdviceCreateTime(advice.getKAISHISJ().format(dateFormatter));
        }
        if (advice.getZHIXINGSJ() != null) {
            diagnosisInfo.setPlanExecTime(advice.getZHIXINGSJ().format(dateFormatter));
        }

        // 医生嘱托（可以根据医嘱类型和内容生成）
        diagnosisInfo.setDoctorInstruction(generateDoctorInstruction(advice));

        // 状态信息
        diagnosisInfo.setIsFinish(convertFinishStatus(advice.getYIZHUZT()));

        // 身份证号
        if (StringUtils.hasLength(patient.getSHENFENZH())) {
            try {
                diagnosisInfo.setIdNumber(Long.parseLong(patient.getSHENFENZH()));
            } catch (NumberFormatException e) {
                log.warn("身份证号格式错误: {}", patient.getSHENFENZH());
                diagnosisInfo.setIdNumber(0L);
            }
        } else {
            diagnosisInfo.setIdNumber(0L);
        }

        return diagnosisInfo;
    }



    /**
     * 转换病区代码为Long类型
     */
    private Long convertWardCodeToLong(String wardCode) {
        if (StringUtils.hasLength(wardCode)) {
            try {
                return Long.parseLong(wardCode);
            } catch (NumberFormatException e) {
                log.warn("病区代码格式错误: {}", wardCode);
                return 0L;
            }
        }
        return 0L;
    }

    /**
     * 转换医嘱类型
     */
    private String convertAdviceType(String yizhufl) {
        if (yizhufl == null) {
            return "其他";
        }

        switch (yizhufl.trim()) {
            case "1":
                return "诊疗";
            case "2":
                return "西药";
            case "3":
                return "成药";
            case "4":
                return "草药";
            case "7":
                return "大输液";
            case "8":
                return "文本";
            case "9":
                return "检查";
            case "10":
                return "检验";
            case "11":
                return "血液";
            case "12":
                return "手术";
            case "13":
                return "会诊";
            case "14":
                return "高压氧";
            default:
                return "其他";
        }
    }

    /**
     * 转换医嘱状态
     */
    private String convertAdviceStatus(String yizhuzt) {
        if (yizhuzt == null) {
            return "未知";
        }

        switch (yizhuzt.trim()) {
            case "1":
                return "未确认";
            case "2":
                return "已确认";
            case "3":
                return "已执行";
            case "4":
                return "已停止";
            case "5":
                return "已撤销";
            default:
                return "未知";
        }
    }

    /**
     * 转换完成状态
     */
    private Long convertFinishStatus(String yizhuzt) {
        if (yizhuzt == null) {
            return 0L;
        }
        // 状态为3（已执行）或4（已停止）时认为已完成
        return ("3".equals(yizhuzt.trim()) || "4".equals(yizhuzt.trim())) ? 1L : 0L;
    }

    /**
     * 生成医生嘱托
     */
    private String generateDoctorInstruction(YzBingrenyz advice) {
        StringBuilder instruction = new StringBuilder();
        
        // 根据医嘱名称和类型生成嘱托
        String adviceName = advice.getYIZHUMC();
        if (adviceName != null) {
            if (adviceName.contains("输液") || adviceName.contains("静滴")) {
                instruction.append("静脉滴注，注意滴速；");
            }
            if (adviceName.contains("口服") || adviceName.contains("po")) {
                instruction.append("口服给药，餐后服用；");
            }
            if (adviceName.contains("肌注") || adviceName.contains("im")) {
                instruction.append("肌肉注射，注意无菌操作；");
            }
            if (adviceName.contains("检查") || adviceName.contains("化验")) {
                instruction.append("按时完成检查，空腹采血；");
            }
            if (adviceName.contains("护理") || adviceName.contains("观察")) {
                instruction.append("密切观察病情变化；");
            }
        }
        
        // 根据频次添加嘱托
        String pinci = advice.getPINCI();
        if (pinci != null) {
            if ("qd".equalsIgnoreCase(pinci)) {
                instruction.append("每日一次；");
            } else if ("bid".equalsIgnoreCase(pinci)) {
                instruction.append("每日两次；");
            } else if ("tid".equalsIgnoreCase(pinci)) {
                instruction.append("每日三次；");
            } else if ("qid".equalsIgnoreCase(pinci)) {
                instruction.append("每日四次；");
            } else if ("prn".equalsIgnoreCase(pinci)) {
                instruction.append("必要时使用；");
            }
        }
        
        // 默认嘱托
        if (instruction.length() == 0) {
            instruction.append("按医嘱执行；");
        }
        
        return instruction.toString();
    }
}
