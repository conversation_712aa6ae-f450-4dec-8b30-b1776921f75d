package com.javazx.batch.scenario.practice;

import com.javazx.batch.mapper.resp.YzChuanCIJingMaiResp;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.service.PracticeDataService;
import com.javazx.batch.util.PatientInfoUtil;
import com.javazx.batch.util.YiZhuInfoUtil;
import com.javazx.batch.vo.PatientDiagnosisInfoReq;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientWithDiagnosisReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 诊疗数据处理器
 * 将YzBingrenyz对象转换为PatientWithDiagnosisReq对象用于同步
 */
@Component
public class PracticeProcessor implements ItemProcessor<ZyBingrenxx, PatientWithDiagnosisReq> {

    private static final Logger log = LoggerFactory.getLogger(PracticeProcessor.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final PracticeDataService practiceDataService;

    private final YiZhuInfoUtil yiZhuInfoUtil;

    private final PatientInfoUtil patientInfoUtil;

    public PracticeProcessor(PracticeDataService practiceDataService, PatientInfoUtil patientInfoUtil, YiZhuInfoUtil yiZhuInfoUtil) {
        this.practiceDataService = practiceDataService;
        this.patientInfoUtil = patientInfoUtil;
        this.yiZhuInfoUtil = yiZhuInfoUtil;
    }

    @Override
    public PatientWithDiagnosisReq process(ZyBingrenxx zyBingrenxx) {
        if (zyBingrenxx == null) {
            return null;
        }

        try {
            // 获取该患者的肾囊肿穿刺和静脉抽血医嘱
            List<YzChuanCIJingMaiResp> chuanCIList = practiceDataService.selectRenalCystPunctureByPatientId(
                    zyBingrenxx.getBINGRENZYID(),
                    zyBingrenxx.getDANGQIANBQ()
            );
            List<YzChuanCIJingMaiResp> jingMaiRespList = practiceDataService.selectVenipunctureByPatientId(
                    zyBingrenxx.getBINGRENZYID(),
                    zyBingrenxx.getDANGQIANBQ()
            );

            if ((chuanCIList == null || chuanCIList.isEmpty()) &&
                    (jingMaiRespList == null || jingMaiRespList.isEmpty())) {
                log.debug("患者 {} 没有肾囊肿穿刺或静脉抽血医嘱，跳过处理", zyBingrenxx.getXINGMING());
                return null;
            }

            List<YzChuanCIJingMaiResp> patientAdvices = new ArrayList<>();
            patientAdvices.addAll(chuanCIList);
            patientAdvices.addAll(jingMaiRespList);

            // 转换为API请求对象
            PatientWithDiagnosisReq request = convert(zyBingrenxx, patientAdvices);

            log.debug("完成诊疗数据处理: {} - 找到{}条相关医嘱", zyBingrenxx.getXINGMING(), patientAdvices.size());
            return request;

        } catch (Exception e) {
            log.error("处理诊疗数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换诊疗数据为同步请求
     */
    private PatientWithDiagnosisReq convert(ZyBingrenxx patient, List<YzChuanCIJingMaiResp> advices) {
        PatientWithDiagnosisReq request = new PatientWithDiagnosisReq();

        // 创建患者信息
        PatientInfoReq patientInfo = patientInfoUtil.createPatientInfo(patient);
        request.setPatientInfo(patientInfo);

        // 创建诊疗信息列表
        List<PatientDiagnosisInfoReq> diagnosisInfoList = new ArrayList<>();
        for (YzChuanCIJingMaiResp advice : advices) {
            PatientDiagnosisInfoReq diagnosisInfo = createDiagnosisInfo(advice, patient);
            diagnosisInfoList.add(diagnosisInfo);
        }
        
        request.setPatientDiagnosisList(diagnosisInfoList);

        return request;
    }

    /**
     * 创建诊疗信息对象
     */
    private PatientDiagnosisInfoReq createDiagnosisInfo(YzChuanCIJingMaiResp advice, ZyBingrenxx patient) {
        PatientDiagnosisInfoReq diagnosisInfo = new PatientDiagnosisInfoReq();

        diagnosisInfo.setSyncId(advice.getYIZHUID());

        // 基本信息
        diagnosisInfo.setPatientName(patient.getXINGMING());
        diagnosisInfo.setZyh(patient.getZHUYUANHAO());
        diagnosisInfo.setBedNo(patient.getDANGQIANCW());

        // 医嘱信息
        diagnosisInfo.setDoctorAdviceName(advice.getYIZHUMC());
        diagnosisInfo.setDoctorAdviceType(yiZhuInfoUtil.convertAdviceType(advice.getYIZHUFL()));
        diagnosisInfo.setDoctorAdviceStatus(yiZhuInfoUtil.convertAdviceStatus(advice.getYIZHUFL(), advice.getDANGQIANZT()));

        // 时间信息
        if (advice.getKAIZHUSJ() != null) {
            diagnosisInfo.setDoctorAdviceCreateTime(advice.getKAIZHUSJ().format(dateFormatter));
        }
        diagnosisInfo.setPlanExecTime(null);

        // 医生嘱托
        diagnosisInfo.setDoctorInstruction(advice.getYISHENGZT());

        // 状态信息
        //diagnosisInfo.setIsFinish(yiZhuInfoUtil.convertFinishStatus(advice.getDANGQIANZT()));
        diagnosisInfo.setIsFinish(0L);

        diagnosisInfo.setDiagnosisLocation(null);

        return diagnosisInfo;
    }

}
