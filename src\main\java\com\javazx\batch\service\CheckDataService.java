package com.javazx.batch.service;

import com.javazx.batch.mapper.YjShenqingdanMapper;
import com.javazx.batch.mapper.ZyBingrenxxMapper;
import com.javazx.batch.po.YjShenqingdan;
import com.javazx.batch.po.ZyBingrenxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 检查数据服务类
 * 专门处理检查相关数据访问
 */
@Service
public class CheckDataService {

    private static final Logger log = LoggerFactory.getLogger(CheckDataService.class);

    @Autowired
    private YjShenqingdanMapper yjShenqingdanMapper;

    @Autowired
    private ZyBingrenxxMapper zyBingrenxxMapper;

    /**
     * 分页查询检查申请单数据
     */
    public List<YjShenqingdan> selectChecksByPage(String dangqianbq, int offset, int limit) {
        try {
            log.debug("查询检查申请单数据 - 病区: {}, 偏移: {}, 限制: {}", dangqianbq, offset, limit);
            return yjShenqingdanMapper.selectChecksByPage(dangqianbq, offset, limit);
        } catch (Exception e) {
            log.error("查询检查申请单数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计检查申请单总数
     */
    public int countChecks(String dangqianbq) {
        try {
            return yjShenqingdanMapper.countChecks(dangqianbq);
        } catch (Exception e) {
            log.error("统计检查申请单总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据病人住院ID查询检查申请单列表
     */
    public List<YjShenqingdan> selectChecksByPatientId(String bingrenzyid) {
        try {
            return yjShenqingdanMapper.selectChecksByPatientId(bingrenzyid);
        } catch (Exception e) {
            log.error("根据病人ID查询检查申请单失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 根据病人住院ID查询患者信息
     */
    public ZyBingrenxx selectPatientById(String bingrenzyid) {
        try {
            return zyBingrenxxMapper.selectById(bingrenzyid);
        } catch (Exception e) {
            log.error("根据病人ID查询患者信息失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 分页查询胃肠镜检查申请单数据
     */
    public List<YjShenqingdan> selectGastroscopeChecksByPage(String dangqianbq, int offset, int limit) {
        try {
            log.debug("查询胃肠镜检查申请单数据 - 病区: {}, 偏移: {}, 限制: {}", dangqianbq, offset, limit);
            return yjShenqingdanMapper.selectGastroscopeChecksByPage(dangqianbq, offset, limit);
        } catch (Exception e) {
            log.error("查询胃肠镜检查申请单数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计胃肠镜检查申请单总数
     */
    public int countGastroscopeChecks(String dangqianbq) {
        try {
            return yjShenqingdanMapper.countGastroscopeChecks(dangqianbq);
        } catch (Exception e) {
            log.error("统计胃肠镜检查申请单总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }
}
