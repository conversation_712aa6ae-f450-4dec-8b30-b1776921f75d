package com.javazx.batch;

import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
 
@SpringBootApplication
@EnableCaching
@EnableBatchProcessing
public class MybatiesPlusBatchApplication {
    public static void main(String[] args) {
        SpringApplication.run(MybatiesPlusBatchApplication.class, args);
    }
}