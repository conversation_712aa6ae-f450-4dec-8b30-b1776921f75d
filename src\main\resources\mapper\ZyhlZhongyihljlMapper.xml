<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.ZyhlZhongyihljlMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.ZyhlZhongyihljl">
            <result property="ZHONGYIHLJLID" column="ZHONGYIHLJLID" jdbcType="VARCHAR"/>
            <result property="BINGZHONGID" column="BINGZHONGID" jdbcType="VARCHAR"/>
            <result property="ZHONGYIZXID" column="ZHONGYIZXID" jdbcType="VARCHAR"/>
            <result property="ZHONGYIBZID" column="ZHONGYIBZID" jdbcType="VARCHAR"/>
            <result property="XIAOGUOPJ" column="XIAOGUOPJ" jdbcType="VARCHAR"/>
            <result property="ZHUANGTAI" column="ZHUANGTAI" jdbcType="VARCHAR"/>
            <result property="JILUSJ" column="JILUSJ" jdbcType="TIMESTAMP"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="NARULJBZ" column="NARULJBZ" jdbcType="VARCHAR"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="ZERENHSQM" column="ZERENHSQM" jdbcType="VARCHAR"/>
            <result property="SHANGJIHSQM" column="SHANGJIHSQM" jdbcType="VARCHAR"/>
            <result property="FANGANWCSJ" column="FANGANWCSJ" jdbcType="TIMESTAMP"/>
            <result property="HULIFAPJ" column="HULIFAPJ" jdbcType="VARCHAR"/>
            <result property="PINGJIARENQM" column="PINGJIARENQM" jdbcType="VARCHAR"/>
            <result property="JISHUZC" column="JISHUZC" jdbcType="VARCHAR"/>
            <result property="SHANGJIHSQM2" column="SHANGJIHSQM2" jdbcType="VARCHAR"/>
            <result property="JIANKANGZD" column="JIANKANGZD" jdbcType="VARCHAR"/>
            <result property="JIANKANGZDFS" column="JIANKANGZDFS" jdbcType="VARCHAR"/>
            <result property="JIBINGZSZW" column="JIBINGZSZW" jdbcType="VARCHAR"/>
            <result property="YINSHITH" column="YINSHITH" jdbcType="VARCHAR"/>
            <result property="YAOWUZS" column="YAOWUZS" jdbcType="VARCHAR"/>
            <result property="QINGZHITH" column="QINGZHITH" jdbcType="VARCHAR"/>
            <result property="XINZENGZHONGYIZXID" column="XINZENGZHONGYIZXID" jdbcType="VARCHAR"/>
            <result property="XINZENGZHONGYIBZID" column="XINZENGZHONGYIBZID" jdbcType="VARCHAR"/>
            <result property="TICHUYY" column="TICHUYY" jdbcType="VARCHAR"/>
            <result property="JILUBQ" column="JILUBQ" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ZHONGYIHLJLID,BINGZHONGID,ZHONGYIZXID,
        ZHONGYIBZID,XIAOGUOPJ,ZHUANGTAI,
        JILUSJ,BINGRENZYID,NARULJBZ,
        ZUOFEIBZ,ZERENHSQM,SHANGJIHSQM,
        FANGANWCSJ,HULIFAPJ,PINGJIARENQM,
        JISHUZC,SHANGJIHSQM2,JIANKANGZD,
        JIANKANGZDFS,JIBINGZSZW,YINSHITH,
        YAOWUZS,QINGZHITH,XINZENGZHONGYIZXID,
        XINZENGZHONGYIBZID,TICHUYY,JILUBQ
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ZYHL_ZHONGYIHLJL
        where 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ZYHL_ZHONGYIHLJL
        where 
    </delete>
    <insert id="insert">
        insert into ZYHL_ZHONGYIHLJL
        ( ZHONGYIHLJLID,BINGZHONGID,ZHONGYIZXID
        ,ZHONGYIBZID,XIAOGUOPJ,ZHUANGTAI
        ,JILUSJ,BINGRENZYID,NARULJBZ
        ,ZUOFEIBZ,ZERENHSQM,SHANGJIHSQM
        ,FANGANWCSJ,HULIFAPJ,PINGJIARENQM
        ,JISHUZC,SHANGJIHSQM2,JIANKANGZD
        ,JIANKANGZDFS,JIBINGZSZW,YINSHITH
        ,YAOWUZS,QINGZHITH,XINZENGZHONGYIZXID
        ,XINZENGZHONGYIBZID,TICHUYY,JILUBQ
        )
        values (#{ZHONGYIHLJLID,jdbcType=VARCHAR},#{BINGZHONGID,jdbcType=VARCHAR},#{ZHONGYIZXID,jdbcType=VARCHAR}
        ,#{ZHONGYIBZID,jdbcType=VARCHAR},#{XIAOGUOPJ,jdbcType=VARCHAR},#{ZHUANGTAI,jdbcType=VARCHAR}
        ,#{JILUSJ,jdbcType=TIMESTAMP},#{BINGRENZYID,jdbcType=VARCHAR},#{NARULJBZ,jdbcType=VARCHAR}
        ,#{ZUOFEIBZ,jdbcType=DECIMAL},#{ZERENHSQM,jdbcType=VARCHAR},#{SHANGJIHSQM,jdbcType=VARCHAR}
        ,#{FANGANWCSJ,jdbcType=TIMESTAMP},#{HULIFAPJ,jdbcType=VARCHAR},#{PINGJIARENQM,jdbcType=VARCHAR}
        ,#{JISHUZC,jdbcType=VARCHAR},#{SHANGJIHSQM2,jdbcType=VARCHAR},#{JIANKANGZD,jdbcType=VARCHAR}
        ,#{JIANKANGZDFS,jdbcType=VARCHAR},#{JIBINGZSZW,jdbcType=VARCHAR},#{YINSHITH,jdbcType=VARCHAR}
        ,#{YAOWUZS,jdbcType=VARCHAR},#{QINGZHITH,jdbcType=VARCHAR},#{XINZENGZHONGYIZXID,jdbcType=VARCHAR}
        ,#{XINZENGZHONGYIBZID,jdbcType=VARCHAR},#{TICHUYY,jdbcType=VARCHAR},#{JILUBQ,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective">
        insert into ZYHL_ZHONGYIHLJL
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="ZHONGYIHLJLID != null">ZHONGYIHLJLID,</if>
                <if test="BINGZHONGID != null">BINGZHONGID,</if>
                <if test="ZHONGYIZXID != null">ZHONGYIZXID,</if>
                <if test="ZHONGYIBZID != null">ZHONGYIBZID,</if>
                <if test="XIAOGUOPJ != null">XIAOGUOPJ,</if>
                <if test="ZHUANGTAI != null">ZHUANGTAI,</if>
                <if test="JILUSJ != null">JILUSJ,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="NARULJBZ != null">NARULJBZ,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="ZERENHSQM != null">ZERENHSQM,</if>
                <if test="SHANGJIHSQM != null">SHANGJIHSQM,</if>
                <if test="FANGANWCSJ != null">FANGANWCSJ,</if>
                <if test="HULIFAPJ != null">HULIFAPJ,</if>
                <if test="PINGJIARENQM != null">PINGJIARENQM,</if>
                <if test="JISHUZC != null">JISHUZC,</if>
                <if test="SHANGJIHSQM2 != null">SHANGJIHSQM2,</if>
                <if test="JIANKANGZD != null">JIANKANGZD,</if>
                <if test="JIANKANGZDFS != null">JIANKANGZDFS,</if>
                <if test="JIBINGZSZW != null">JIBINGZSZW,</if>
                <if test="YINSHITH != null">YINSHITH,</if>
                <if test="YAOWUZS != null">YAOWUZS,</if>
                <if test="QINGZHITH != null">QINGZHITH,</if>
                <if test="XINZENGZHONGYIZXID != null">XINZENGZHONGYIZXID,</if>
                <if test="XINZENGZHONGYIBZID != null">XINZENGZHONGYIBZID,</if>
                <if test="TICHUYY != null">TICHUYY,</if>
                <if test="JILUBQ != null">JILUBQ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="ZHONGYIHLJLID != null">#{ZHONGYIHLJLID,jdbcType=VARCHAR},</if>
                <if test="BINGZHONGID != null">#{BINGZHONGID,jdbcType=VARCHAR},</if>
                <if test="ZHONGYIZXID != null">#{ZHONGYIZXID,jdbcType=VARCHAR},</if>
                <if test="ZHONGYIBZID != null">#{ZHONGYIBZID,jdbcType=VARCHAR},</if>
                <if test="XIAOGUOPJ != null">#{XIAOGUOPJ,jdbcType=VARCHAR},</if>
                <if test="ZHUANGTAI != null">#{ZHUANGTAI,jdbcType=VARCHAR},</if>
                <if test="JILUSJ != null">#{JILUSJ,jdbcType=TIMESTAMP},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="NARULJBZ != null">#{NARULJBZ,jdbcType=VARCHAR},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="ZERENHSQM != null">#{ZERENHSQM,jdbcType=VARCHAR},</if>
                <if test="SHANGJIHSQM != null">#{SHANGJIHSQM,jdbcType=VARCHAR},</if>
                <if test="FANGANWCSJ != null">#{FANGANWCSJ,jdbcType=TIMESTAMP},</if>
                <if test="HULIFAPJ != null">#{HULIFAPJ,jdbcType=VARCHAR},</if>
                <if test="PINGJIARENQM != null">#{PINGJIARENQM,jdbcType=VARCHAR},</if>
                <if test="JISHUZC != null">#{JISHUZC,jdbcType=VARCHAR},</if>
                <if test="SHANGJIHSQM2 != null">#{SHANGJIHSQM2,jdbcType=VARCHAR},</if>
                <if test="JIANKANGZD != null">#{JIANKANGZD,jdbcType=VARCHAR},</if>
                <if test="JIANKANGZDFS != null">#{JIANKANGZDFS,jdbcType=VARCHAR},</if>
                <if test="JIBINGZSZW != null">#{JIBINGZSZW,jdbcType=VARCHAR},</if>
                <if test="YINSHITH != null">#{YINSHITH,jdbcType=VARCHAR},</if>
                <if test="YAOWUZS != null">#{YAOWUZS,jdbcType=VARCHAR},</if>
                <if test="QINGZHITH != null">#{QINGZHITH,jdbcType=VARCHAR},</if>
                <if test="XINZENGZHONGYIZXID != null">#{XINZENGZHONGYIZXID,jdbcType=VARCHAR},</if>
                <if test="XINZENGZHONGYIBZID != null">#{XINZENGZHONGYIBZID,jdbcType=VARCHAR},</if>
                <if test="TICHUYY != null">#{TICHUYY,jdbcType=VARCHAR},</if>
                <if test="JILUBQ != null">#{JILUBQ,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.ZyhlZhongyihljl">
        update ZYHL_ZHONGYIHLJL
        <set>
                <if test="ZHONGYIHLJLID != null">
                    ZHONGYIHLJLID = #{ZHONGYIHLJLID,jdbcType=VARCHAR},
                </if>
                <if test="BINGZHONGID != null">
                    BINGZHONGID = #{BINGZHONGID,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYIZXID != null">
                    ZHONGYIZXID = #{ZHONGYIZXID,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGYIBZID != null">
                    ZHONGYIBZID = #{ZHONGYIBZID,jdbcType=VARCHAR},
                </if>
                <if test="XIAOGUOPJ != null">
                    XIAOGUOPJ = #{XIAOGUOPJ,jdbcType=VARCHAR},
                </if>
                <if test="ZHUANGTAI != null">
                    ZHUANGTAI = #{ZHUANGTAI,jdbcType=VARCHAR},
                </if>
                <if test="JILUSJ != null">
                    JILUSJ = #{JILUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="NARULJBZ != null">
                    NARULJBZ = #{NARULJBZ,jdbcType=VARCHAR},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZERENHSQM != null">
                    ZERENHSQM = #{ZERENHSQM,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIHSQM != null">
                    SHANGJIHSQM = #{SHANGJIHSQM,jdbcType=VARCHAR},
                </if>
                <if test="FANGANWCSJ != null">
                    FANGANWCSJ = #{FANGANWCSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="HULIFAPJ != null">
                    HULIFAPJ = #{HULIFAPJ,jdbcType=VARCHAR},
                </if>
                <if test="PINGJIARENQM != null">
                    PINGJIARENQM = #{PINGJIARENQM,jdbcType=VARCHAR},
                </if>
                <if test="JISHUZC != null">
                    JISHUZC = #{JISHUZC,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIHSQM2 != null">
                    SHANGJIHSQM2 = #{SHANGJIHSQM2,jdbcType=VARCHAR},
                </if>
                <if test="JIANKANGZD != null">
                    JIANKANGZD = #{JIANKANGZD,jdbcType=VARCHAR},
                </if>
                <if test="JIANKANGZDFS != null">
                    JIANKANGZDFS = #{JIANKANGZDFS,jdbcType=VARCHAR},
                </if>
                <if test="JIBINGZSZW != null">
                    JIBINGZSZW = #{JIBINGZSZW,jdbcType=VARCHAR},
                </if>
                <if test="YINSHITH != null">
                    YINSHITH = #{YINSHITH,jdbcType=VARCHAR},
                </if>
                <if test="YAOWUZS != null">
                    YAOWUZS = #{YAOWUZS,jdbcType=VARCHAR},
                </if>
                <if test="QINGZHITH != null">
                    QINGZHITH = #{QINGZHITH,jdbcType=VARCHAR},
                </if>
                <if test="XINZENGZHONGYIZXID != null">
                    XINZENGZHONGYIZXID = #{XINZENGZHONGYIZXID,jdbcType=VARCHAR},
                </if>
                <if test="XINZENGZHONGYIBZID != null">
                    XINZENGZHONGYIBZID = #{XINZENGZHONGYIBZID,jdbcType=VARCHAR},
                </if>
                <if test="TICHUYY != null">
                    TICHUYY = #{TICHUYY,jdbcType=VARCHAR},
                </if>
                <if test="JILUBQ != null">
                    JILUBQ = #{JILUBQ,jdbcType=VARCHAR},
                </if>
        </set>
        where  
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.ZyhlZhongyihljl">
        update ZYHL_ZHONGYIHLJL
        set 
            ZHONGYIHLJLID =  #{ZHONGYIHLJLID,jdbcType=VARCHAR},
            BINGZHONGID =  #{BINGZHONGID,jdbcType=VARCHAR},
            ZHONGYIZXID =  #{ZHONGYIZXID,jdbcType=VARCHAR},
            ZHONGYIBZID =  #{ZHONGYIBZID,jdbcType=VARCHAR},
            XIAOGUOPJ =  #{XIAOGUOPJ,jdbcType=VARCHAR},
            ZHUANGTAI =  #{ZHUANGTAI,jdbcType=VARCHAR},
            JILUSJ =  #{JILUSJ,jdbcType=TIMESTAMP},
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            NARULJBZ =  #{NARULJBZ,jdbcType=VARCHAR},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            ZERENHSQM =  #{ZERENHSQM,jdbcType=VARCHAR},
            SHANGJIHSQM =  #{SHANGJIHSQM,jdbcType=VARCHAR},
            FANGANWCSJ =  #{FANGANWCSJ,jdbcType=TIMESTAMP},
            HULIFAPJ =  #{HULIFAPJ,jdbcType=VARCHAR},
            PINGJIARENQM =  #{PINGJIARENQM,jdbcType=VARCHAR},
            JISHUZC =  #{JISHUZC,jdbcType=VARCHAR},
            SHANGJIHSQM2 =  #{SHANGJIHSQM2,jdbcType=VARCHAR},
            JIANKANGZD =  #{JIANKANGZD,jdbcType=VARCHAR},
            JIANKANGZDFS =  #{JIANKANGZDFS,jdbcType=VARCHAR},
            JIBINGZSZW =  #{JIBINGZSZW,jdbcType=VARCHAR},
            YINSHITH =  #{YINSHITH,jdbcType=VARCHAR},
            YAOWUZS =  #{YAOWUZS,jdbcType=VARCHAR},
            QINGZHITH =  #{QINGZHITH,jdbcType=VARCHAR},
            XINZENGZHONGYIZXID =  #{XINZENGZHONGYIZXID,jdbcType=VARCHAR},
            XINZENGZHONGYIBZID =  #{XINZENGZHONGYIBZID,jdbcType=VARCHAR},
            TICHUYY =  #{TICHUYY,jdbcType=VARCHAR},
            JILUBQ =  #{JILUBQ,jdbcType=VARCHAR}
        where  
    </update>
</mapper>
