package com.javazx.batch.config;

import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.core.launch.support.SimpleJobLauncher;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.task.SyncTaskExecutor;

/**
 * Spring Batch优化配置
 * 最小改动解决死锁问题
 */
@Configuration
public class BatchOptimizationConfig {

    @Autowired
    private JobRepository jobRepository;

    /**
     * 优化JobLauncher配置
     * 使用同步执行器，避免并发冲突
     */
/*    @Bean
    @Primary
    public JobLauncher optimizedJobLauncher() throws Exception {
        SimpleJobLauncher jobLauncher = new SimpleJobLauncher();
        jobLauncher.setJobRepository(jobRepository);
        
        // 使用同步执行器，确保任务串行执行，避免死锁
        jobLauncher.setTaskExecutor(new SyncTaskExecutor());
        
        jobLauncher.afterPropertiesSet();
        return jobLauncher;
    }*/
}
