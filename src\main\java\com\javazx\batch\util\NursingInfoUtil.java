package com.javazx.batch.util;

import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import com.javazx.batch.service.NursingTaskDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 患者信息创建工具类
 * 提供通用的患者信息创建方法，供各个处理器使用
 */
@Component
public class NursingInfoUtil {

    private static final Logger log = LoggerFactory.getLogger(NursingInfoUtil.class);
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final NursingTaskDataService nursingTaskDataService;

    private final ThreadPoolTaskExecutor executorService;

    public NursingInfoUtil(NursingTaskDataService nursingTaskDataService, @Qualifier("taskExecutor") ThreadPoolTaskExecutor executorService) {
        this.nursingTaskDataService = nursingTaskDataService;
        this.executorService = executorService;
    }

    /**
     * 多线程收集病区内所有护理任务医嘱数据
     */
    public HashMap<String, List<NursingTaskOrderResp>> collectAllNursingTaskOrders(String wardId) {
        // 使用ConcurrentHashMap保证线程安全
        ConcurrentHashMap<String, List<NursingTaskOrderResp>> allOrders = new ConcurrentHashMap<>();

        log.info("开始多线程收集病区 {} 的护理任务医嘱数据", wardId);

        try {
            // 创建所有查询任务的CompletableFuture列表
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            // RW01 心电监护
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectECGMonitoringOrders(wardId);
                    allOrders.put("RW01", orders);
                    log.debug("病区 {} RW01心电监护数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW01心电监护数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW01", new ArrayList<>());
                }
            }, executorService));

            // RW02-RW05 测血压医嘱（4个时间点）
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodPressure06Orders(wardId);
                    allOrders.put("RW02", orders);
                    log.debug("病区 {} RW02测血压06:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW02测血压06:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW02", new ArrayList<>());
                }
            }, executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodPressure10Orders(wardId);
                    allOrders.put("RW03", orders);
                    log.debug("病区 {} RW03测血压10:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW03测血压10:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW03", new ArrayList<>());
                }
            }, executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodPressure14Orders(wardId);
                    allOrders.put("RW04", orders);
                    log.debug("病区 {} RW04测血压14:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW04测血压14:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW04", new ArrayList<>());
                }
            }, executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodPressure18Orders(wardId);
                    allOrders.put("RW05", orders);
                    log.debug("病区 {} RW05测血压18:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW05测血压18:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW05", new ArrayList<>());
                }
            }, executorService));

            // RW06-RW09 测血糖医嘱（4个时间点）
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodGlucose06Orders(wardId);
                    allOrders.put("RW06", orders);
                    log.debug("病区 {} RW06测血糖06:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW06测血糖06:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW06", new ArrayList<>());
                }
            }, executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodGlucose10Orders(wardId);
                    allOrders.put("RW07", orders);
                    log.debug("病区 {} RW07测血糖10:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW07测血糖10:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW07", new ArrayList<>());
                }
            }, executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodGlucose16Orders(wardId);
                    allOrders.put("RW08", orders);
                    log.debug("病区 {} RW08测血糖16:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW08测血糖16:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW08", new ArrayList<>());
                }
            }, executorService));

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBloodGlucose20Orders(wardId);
                    allOrders.put("RW09", orders);
                    log.debug("病区 {} RW09测血糖20:00数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW09测血糖20:00数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW09", new ArrayList<>());
                }
            }, executorService));

            // 添加其他护理任务类型的查询
            // RW10 雾化吸入
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectNebulizationOrders(wardId);
                    allOrders.put("RW10", orders);
                    log.debug("病区 {} RW10雾化吸入数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW10雾化吸入数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW10", new ArrayList<>());
                }
            }, executorService));

            // RW11 膀胱冲洗
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectBladderIrrigationOrders(wardId);
                    allOrders.put("RW11", orders);
                    log.debug("病区 {} RW11膀胱冲洗数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW11膀胱冲洗数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW11", new ArrayList<>());
                }
            }, executorService));

            // RW12 膀胱持续冲洗
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectContinuousBladderIrrigationOrders(wardId);
                    allOrders.put("RW12", orders);
                    log.debug("病区 {} RW12膀胱持续冲洗数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW12膀胱持续冲洗数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW12", new ArrayList<>());
                }
            }, executorService));

            // RW13 口腔护理
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectOralCareOrders(wardId);
                    allOrders.put("RW13", orders);
                    log.debug("病区 {} RW13口腔护理数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW13口腔护理数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW13", new ArrayList<>());
                }
            }, executorService));

            // RW14 会阴护理
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectPerinealCareOrders(wardId);
                    allOrders.put("RW14", orders);
                    log.debug("病区 {} RW14会阴护理数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW14会阴护理数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW14", new ArrayList<>());
                }
            }, executorService));

            // RW15 记24小时尿量
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectUrineVolumeRecordOrders(wardId);
                    allOrders.put("RW15", orders);
                    log.debug("病区 {} RW15记24小时尿量数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW15记24小时尿量数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW15", new ArrayList<>());
                }
            }, executorService));

            // RW16 更换引流袋
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectDrainageBagChangeOrders(wardId);
                    allOrders.put("RW16", orders);
                    log.debug("病区 {} RW16更换引流袋数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW16更换引流袋数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW16", new ArrayList<>());
                }
            }, executorService));

            // RW17 静脉置管冲洗
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectVenousCatheterFlushOrders(wardId);
                    allOrders.put("RW17", orders);
                    log.debug("病区 {} RW17静脉置管冲洗数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW17静脉置管冲洗数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW17", new ArrayList<>());
                }
            }, executorService));

            // RW18 深静脉置管护理
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectDeepVenousCatheterCareOrders(wardId);
                    allOrders.put("RW18", orders);
                    log.debug("病区 {} RW18深静脉置管护理数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW18深静脉置管护理数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW18", new ArrayList<>());
                }
            }, executorService));

            // RW20 输液港
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectInfusionPortOrders(wardId);
                    allOrders.put("RW20", orders);
                    log.debug("病区 {} RW20输液港数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW20输液港数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW20", new ArrayList<>());
                }
            }, executorService));

            // RW21 PICC置管
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectPICCCatheterOrders(wardId);
                    allOrders.put("RW21", orders);
                    log.debug("病区 {} RW21PICC置管数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW21PICC置管数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW21", new ArrayList<>());
                }
            }, executorService));

            // RW22 肾周引流管护理
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectPerinephricDrainageCareOrders(wardId);
                    allOrders.put("RW22", orders);
                    log.debug("病区 {} RW22肾周引流管护理数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW22肾周引流管护理数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW22", new ArrayList<>());
                }
            }, executorService));

            // RW46 留置导尿
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectUrinaryCatheterizationOrders(wardId);
                    allOrders.put("RW46", orders);
                    log.debug("病区 {} RW46留置导尿数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW46留置导尿数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW46", new ArrayList<>());
                }
            }, executorService));

            // RW47 吸氧
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    List<NursingTaskOrderResp> orders = nursingTaskDataService.selectOxygenTherapyOrders(wardId);
                    allOrders.put("RW47", orders);
                    log.debug("病区 {} RW47吸氧数据收集完成，数量: {}", wardId, orders.size());
                } catch (Exception e) {
                    log.error("病区 {} RW47吸氧数据收集失败: {}", wardId, e.getMessage());
                    allOrders.put("RW47", new ArrayList<>());
                }
            }, executorService));

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计总数据量
            int totalCount = allOrders.values().stream().mapToInt(List::size).sum();
            log.info("病区 {} 护理任务医嘱数据多线程收集完成，共收集 {} 种类型，总计 {} 条数据", wardId, allOrders.size(), totalCount);

        } catch (Exception e) {
            log.error("多线程收集病区 {} 护理任务医嘱数据失败: {}", wardId, e.getMessage(), e);
        }

        return new HashMap<>(allOrders);
    }
}
