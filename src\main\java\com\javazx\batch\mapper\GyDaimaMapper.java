package com.javazx.batch.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.GyDaima;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【GY_DAIMA(公用_代码)】的数据库操作Mapper
* @createDate 2025-06-18 16:23:00
* @Entity com.javazx.batch.po.GyDaima
*/
@Mapper
@DS("hzzyy")
public interface GyDaimaMapper extends BaseMapper<GyDaima> {

    int deleteByPrimaryKey(Long id);

    int insert(GyDaima record);

    int insertSelective(GyDaima record);

    GyDaima selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GyDaima record);

    int updateByPrimaryKey(GyDaima record);

    /**
     * 查询职称代码列表
     * @return 职称代码列表
     */
    @Select("""
            SELECT DAIMAID, DAIMAMC, DAIMALB, ZUOFEIBZ
            FROM HIS6.GY_DAIMA
            WHERE DAIMALB = '0072'
              AND ZUOFEIBZ = 0
            ORDER BY SHUNXUHAO, DAIMAID
            """)
    List<GyDaima> selectJobTitleCodes();

    /**
     * 根据代码ID查询职称信息
     * @param daimaId 代码ID
     * @return 职称信息
     */
    @Select("""
            SELECT DAIMAID, DAIMAMC, DAIMALB, ZUOFEIBZ
            FROM HIS6.GY_DAIMA
            WHERE DAIMALB = '0072'
              AND ZUOFEIBZ = 0
              AND DAIMAID = #{daimaId}
            """)
    GyDaima selectJobTitleByCode(@Param("daimaId") String daimaId);

    /**
     * 根据职称名称查询职称信息
     * @param jobTitle 职称名称
     * @return 职称信息
     */
    @Select("""
            SELECT DAIMAID, DAIMAMC, DAIMALB, ZUOFEIBZ
            FROM HIS6.GY_DAIMA
            WHERE DAIMALB = '0072'
              AND ZUOFEIBZ = 0
              AND DAIMAMC = #{jobTitle}
            """)
    GyDaima selectJobTitleByName(@Param("jobTitle") String jobTitle);

    /**
     * 查询工作类别代码列表
     * @return 工作类别代码列表
     */
    @Select("""
            SELECT DAIMAID, DAIMAMC, DAIMALB, ZUOFEIBZ
            FROM HIS6.GY_DAIMA
            WHERE DAIMALB = '0074'
              AND ZUOFEIBZ = 0
            ORDER BY SHUNXUHAO, DAIMAID
            """)
    List<GyDaima> selectJobTypeCodes();

    /**
     * 根据工作类别代码ID查询工作类别信息
     * @param daimaId 代码ID
     * @return 工作类别信息
     */
    @Select("""
            SELECT DAIMAID, DAIMAMC, DAIMALB, ZUOFEIBZ
            FROM HIS6.GY_DAIMA
            WHERE DAIMALB = '0074'
              AND ZUOFEIBZ = 0
              AND DAIMAID = #{daimaId}
            """)
    GyDaima selectJobTypeByCode(@Param("daimaId") String daimaId);

}
