package com.javazx.batch.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebClientConfig {

    /**
     * 智慧病房系统WebClient
     * BaseURL: http://125.124.28.118:8081/api/smartward
     * 超时: 1分钟
     */
    @Bean("smartwardWebClient")
    public WebClient smartwardWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 30000) // 30秒连接超时
                .responseTimeout(Duration.ofMinutes(1)) // 1分钟响应超时
                .doOnConnected(conn ->
                        conn.addHandlerLast(new ReadTimeoutHandler(60, TimeUnit.SECONDS))
                            .addHandlerLast(new WriteTimeoutHandler(60, TimeUnit.SECONDS)));

        return WebClient.builder()
                .baseUrl("http://125.124.28.118:8081/api/smartward")
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .defaultHeader("tenantId", "100003")
                .build();
    }

    /**
     * 医院信息系统WebClient
     * BaseURL: http://his.hospital.com/api
     * 超时: 30秒
     */
    @Bean("hospitalWebClient")
    public WebClient hospitalWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 15000) // 15秒连接超时
                .responseTimeout(Duration.ofSeconds(30)) // 30秒响应超时
                .doOnConnected(conn ->
                        conn.addHandlerLast(new ReadTimeoutHandler(30, TimeUnit.SECONDS))
                            .addHandlerLast(new WriteTimeoutHandler(30, TimeUnit.SECONDS)));

        return WebClient.builder()
                .baseUrl("http://his.hospital.com/api")
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    /**
     * 第三方接口WebClient
     * BaseURL: http://third-party.service.com/api
     * 超时: 2分钟
     */
    @Bean("thirdPartyWebClient")
    public WebClient thirdPartyWebClient() {
        HttpClient httpClient = HttpClient.create()
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 45000) // 45秒连接超时
                .responseTimeout(Duration.ofMinutes(2)) // 2分钟响应超时
                .doOnConnected(conn ->
                        conn.addHandlerLast(new ReadTimeoutHandler(120, TimeUnit.SECONDS))
                            .addHandlerLast(new WriteTimeoutHandler(120, TimeUnit.SECONDS)));

        return WebClient.builder()
                .baseUrl("http://third-party.service.com/api")
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }
}
