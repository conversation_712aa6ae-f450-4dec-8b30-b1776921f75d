package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 医护人员信息同步请求对象
 * 用于批量同步医护人员信息到外部系统
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserSyncRequest {
    /**
     * 医护人员信息列表
     */
    private List<UserInfoReq> userInfoList;
}
