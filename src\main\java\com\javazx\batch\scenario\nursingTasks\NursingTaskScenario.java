package com.javazx.batch.scenario.nursingTasks;


import com.javazx.batch.mapper.resp.NursingTaskOrderResp;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.vo.NurseTaskReq;
import com.javazx.batch.vo.NurseTaskSyncRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.*;
import java.util.stream.Collectors;

/**
 *  护理任务数据同步场景
 *  从数据库读取床位数据并生成护理任务同步到外部系统
 */

@Component
@Scope("prototype")
public class NursingTaskScenario extends AbstractBatchScenario<NursingTaskOrderResp, NurseTaskReq> {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskScenario.class);

    // 支持多病区配置，可以通过配置动态修改
    private static List<String> wardIds = Arrays.asList("1006");

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private NursingTaskDataService nursingTaskDataService;

    public NursingTaskScenario() {
        super("nursingTask", "护理任务数据同步场景：同步床位护理任务数据");
        this.setCommitInterval(100);
        this.setPageSize(100);
    }

    @Override
    public ItemReader<NursingTaskOrderResp> createReader() {
        log.info("创建护理任务数据读取器，页面大小: {}", getPageSize());

        // 创建护理任务医嘱分页读取器
        GenericPagingItemReader<NursingTaskOrderResp> reader = new GenericPagingItemReader<>(
                "护理任务医嘱信息",
                getPageSize(),
                (offset, limit) -> {
                    List<NursingTaskOrderResp> allOrders = new ArrayList<>();
                    for (String wardId : wardIds) {
                        allOrders.addAll(nursingTaskDataService.getAllNursingTaskOrders(wardId, offset, limit));
                    }
                    return allOrders;
                },
                () -> {
                    int totalCount = 0;
                    for (String wardId : wardIds) {
                        totalCount += nursingTaskDataService.countAllNursingTaskOrders(wardId);
                    }
                    return totalCount;
                }
        );
        return reader;
    }

    @Override
    public ItemProcessor<NursingTaskOrderResp, NurseTaskReq> createProcessor() {
        log.info("创建护理任务数据转换器");
        return new NursingTaskProcessor();
    }

    @Override
    public ItemWriter<NurseTaskReq> createWriter() {
        log.info("创建病区{}护理任务同步数据写入器", wardIds);
        return items -> {
            List<NurseTaskReq> allTasks = items.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (allTasks.isEmpty()) {
                log.info("没有护理任务数据需要同步");
                return;
            }

            // 创建同步请求对象
            NurseTaskSyncRequest request = new NurseTaskSyncRequest();
            request.setNurseTaskList(allTasks);

            try {
                // 发送HTTP请求同步数据
                smartwardWebClient
                        .post()
                        .uri("/sync/nursing_task/sync_nursing_task")
                        .bodyValue(request.getNurseTaskList())
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("护理任务 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("护理任务 Error: " + error.getMessage());
                                }
                        );
            } catch (Exception e) {
                log.error("护理任务数据同步失败: {}", e.getMessage(), e);
                throw new RuntimeException("护理任务数据同步失败", e);
            }
        };
    }

    /**
     * 从护理任务列表中获取床位号
     */
    private List<String> getBedNumbersFromTasks(List<NurseTaskReq> tasks, List<String> wardIds) {
        if (tasks == null || tasks.isEmpty()) {
            return List.of("未知床位");
        }

        return tasks.stream()
                .filter(Objects::nonNull)
                .map(NurseTaskReq::getBedNo)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
}
