/*
package com.javazx.batch.scenario.nursingTasks;

import com.javazx.batch.po.GyChuangwei;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.service.NursingTaskDataService;
import com.javazx.batch.vo.NurseTaskReq;
import com.javazx.batch.vo.NurseTaskSyncRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

*/
/**
 * 护理任务数据同步场景
 * 从数据库读取床位数据并生成护理任务同步到外部系统
 *//*

@Component
@Scope("prototype")
public class NursingTaskScenario extends AbstractBatchScenario<GyChuangwei, List<NurseTaskReq>> {

    private static final Logger log = LoggerFactory.getLogger(NursingTaskScenario.class);

    // 支持多病区配置，可以通过配置动态修改
    private List<String> wardIds = Arrays.asList("1006");

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private NursingTaskDataService nursingTaskDataService;

    public NursingTaskScenario() {
        super("nursingTask", "护理任务数据同步场景：同步床位护理任务数据");
        this.setCommitInterval(50);
        this.setPageSize(100);
    }

    @Override
    public ItemReader<GyChuangwei> createReader() {
        log.info("创建护理任务数据读取器，页面大小: {}", getPageSize());

        // 创建通用分页读取器
        GenericPagingItemReader<GyChuangwei> reader = new GenericPagingItemReader<>(
                "护理任务床位信息",
                getPageSize(),
                (offset, limit) -> {
                    List<GyChuangwei> allBeds = new ArrayList<>();
                    for (String wardId : wardIds) {
                        List<GyChuangwei> wardBeds = nursingTaskDataService.selectBedsByPage(wardId, offset, limit);
                        allBeds.addAll(wardBeds);
                    }
                    return allBeds;
                },
                () -> {
                    int totalCount = 0;
                    for (String wardId : wardIds) {
                        totalCount += nursingTaskDataService.countBeds(wardId);
                    }
                    return totalCount;
                }
        );

        return null;
    }

    @Override
    public ItemProcessor<GyChuangwei, List<NurseTaskReq>> createProcessor() {
        log.info("创建护理任务数据转换器");
        return new NursingTaskProcessor(nursingTaskDataService);
    }

    @Override
    public ItemWriter<List<NurseTaskReq>> createWriter() {
        log.info("创建病区{}护理任务同步数据写入器", wardIds);
        return items -> {
            // 将所有护理任务合并到一个列表中
            List<NurseTaskReq> allTasks = new ArrayList<>();
            for (List<NurseTaskReq> taskList : items) {
                if (taskList != null && !taskList.isEmpty()) {
                    allTasks.addAll(taskList);
                }
            }

            if (allTasks.isEmpty()) {
                log.info("没有护理任务数据需要同步");
                return;
            }

            // 创建同步请求对象
            NurseTaskSyncRequest request = new NurseTaskSyncRequest();
            request.setNurseTaskList(allTasks);

            // 记录同步信息
            List<String> bedNumbers = getBedNumbersFromTasks(allTasks);
            log.info("开始同步护理任务数据 - 床位数量: {}, 任务数量: {}, 床位: {}", 
                    bedNumbers.size(), allTasks.size(), 
                    bedNumbers.size() <= 10 ? bedNumbers : bedNumbers.subList(0, 10) + "...");

            try {
                // 发送HTTP请求同步数据
                String response = smartwardWebClient
                        .post()
                        .uri("/sync/nursing_task/sync_nursing_task")
                        .bodyValue(request)
                        .retrieve()
                        .bodyToMono(String.class)
                        .block();

                log.info("护理任务数据同步完成 - 响应: {}", response);
            } catch (Exception e) {
                log.error("护理任务数据同步失败: {}", e.getMessage(), e);
                throw new RuntimeException("护理任务数据同步失败", e);
            }
        };
    }

    */
/**
     * 设置病区ID列表
     *//*

    public void setWardIds(List<String> wardIds) {
        this.wardIds = wardIds;
        log.info("设置护理任务同步病区: {}", wardIds);
    }

    */
/**
     * 从护理任务列表中获取床位号
     *//*

    private List<String> getBedNumbersFromTasks(List<NurseTaskReq> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return List.of("未知床位");
        }

        return tasks.stream()
                .filter(Objects::nonNull)
                .map(NurseTaskReq::getBedNo)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    */
/**
     * 获取当前配置的病区ID列表
     *//*

    public List<String> getWardIds() {
        return wardIds;
    }
}
*/
