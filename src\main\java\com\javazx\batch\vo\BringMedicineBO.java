package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 携带药品信息
 * 对应 ApifoxModel 中的 BringMedicineBO 结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BringMedicineBO {
    /**
     * 药品名称
     */
    private String medicineName;
    
    /**
     * 药品剂量
     */
    private String medicineDosage;
    
    /**
     * 药品数量
     */
    private Long medicineCount;
}
