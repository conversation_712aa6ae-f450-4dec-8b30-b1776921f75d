package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 患者检查信息请求对象
 * 对应 ApifoxModel 中的主结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientWithCheckReq {
    /**
     * 患者检查信息列表
     */
    private List<PatientCheckInfoReq> patientCheckList;

    /**
     * 患者基本信息
     */
    private PatientInfoReq patientInfo;
}
