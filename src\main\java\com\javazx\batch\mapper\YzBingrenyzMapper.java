package com.javazx.batch.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.mapper.resp.YzChuanCIJingMaiResp;
import com.javazx.batch.po.YzBingrenyz;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【YZ_BINGRENYZ(医嘱_病人医嘱)】的数据库操作Mapper
* @createDate 2025-06-12 15:25:02
* @Entity generator.domain.YzBingrenyz
*/
@Mapper
@DS("hzzyy")
public interface YzBingrenyzMapper extends BaseMapper<YzBingrenyz> {

    int deleteByPrimaryKey(Long id);

    int insert(YzBingrenyz record);

    int insertSelective(YzBingrenyz record);

    YzBingrenyz selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(YzBingrenyz record);

    int updateByPrimaryKey(YzBingrenyz record);

    /**
     * 查询护理等级医嘱项目ID
     * @param bingrenzyid 病人住院ID
     * @param bingquid 病区ID
     * @return 护理等级医嘱项目ID
     */
    @Select("""
            SELECT YIZHUXMID
            FROM HIS6.YZ_BINGRENYZ
            WHERE KAISHISJ > SYSDATE - INTERVAL '30' DAY
              AND BINGQUID = #{bingquid}
              AND YIZHUZT IN (2, 3, 4)
              AND YIZHUXMID IN (1000012080, 1000006513, 15150, 1000006087, 1000005785, 1000005997, 1000006017)
              AND BINGRENZYID = #{bingrenzyid}
            ORDER BY KAISHISJ DESC
            FETCH FIRST 1 ROW ONLY
            """)
    String getNursingLevelOrderId(@Param("bingrenzyid") String bingrenzyid, @Param("bingquid") String bingquid);

    /**
     * 查询危重标签（根据特定医嘱项目ID判断）
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return 危重标签记录数量
     */
    @Select("""
            SELECT count(1)
            FROM HIS6.zy_bingrenxx s, HIS6.YZ_BINGRENYZ m
            WHERE s.bingrenzyid = m.bingrenzyid
              AND m.yizhuxmid = '**********'
              AND s.ZAIYUANZT = 0
              AND s.Yingerbz = 0
              AND s.rukebz = 1
              AND m.YIZHUZT = '3'
              AND s.bingrenzyid = #{bingrenzyid}
              AND s.dangqianbq = #{dangqianbq}
            """)
    Integer getCriticalPatientCount(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);

    /**
     * 查询多重耐药标签（根据接触隔离医嘱判断）
     * @param bingrenzyid 病人住院ID
     * @param dangqianbq 当前病区
     * @return 多重耐药记录数量
     */
    @Select("""
            SELECT count(1)
            FROM HIS6.zy_bingrenxx s, HIS6.yz_bingrenyz d
            WHERE s.ZaiYuanZT = '0'
              AND s.rukebz = 1
              AND s.Yingerbz = 0
              AND s.bingrenzyid = d.bingrenzyid
              AND d.yizhumc = '接触隔离'
              AND d.yizhuzt not in ('4','5')
              AND s.bingrenzyid = #{bingrenzyid}
              AND s.dangqianbq = #{dangqianbq}
            """)
    Integer getContactIsolationCount(@Param("bingrenzyid") String bingrenzyid, @Param("dangqianbq") String dangqianbq);

    /**
     * 查询术中带药医嘱列表
     * @return 术中带药医嘱列表
     */
    @Select("""
                SELECT * 
                FROM his6.yz_bingrenyz
                WHERE PINCI = 'SQS'
                  AND KAISHISJ > TRUNC(SYSDATE)
                  AND YIZHUZT in (2,3,4)
                  AND SHOUSHUYZID = #{shoushuyzid}
            """)
    List<YzBingrenyz> selectIntraoperativeMedication(@Param("shoushuyzid") String shoushuyzid);

    /**
     * 统计术中带药医嘱总数
     * @return 术中带药医嘱总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.yz_bingrenyz
            WHERE PINCI = 'SQS'
              AND KAISHISJ > TRUNC(SYSDATE)
              AND YIZHUZT in (2,3,4)
              AND SHOUSHUYZID = #{shoushuyzid}
            """)
    int countIntraoperativeMedication(@Param("shoushuyzid") String shoushuyzid);

    /**
     * 根据病区分页查询术中带药医嘱列表
     * @param bingquid 病区ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 术中带药医嘱列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT *, ROW_NUMBER() OVER (ORDER BY YIZHUID) AS ROW_NUM
                FROM his6.yz_bingrenyz
                WHERE PINCI = 'SQS'
                  AND KAISHISJ > SYSDATE - 7
                    AND YIZHUZT in (2,3,4)
                  AND BINGQUID = #{bingquid}
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<YzBingrenyz> selectIntraoperativeMedicationByPageAndWard(@Param("bingquid") String bingquid,
                                                                  @Param("offset") int offset,
                                                                  @Param("limit") int limit);

    /**
     * 根据病区统计术中带药医嘱总数
     * @param bingquid 病区ID
     * @return 术中带药医嘱总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.yz_bingrenyz
            WHERE PINCI = 'SQS'
              AND KAISHISJ > SYSDATE - 7
                AND YIZHUZT in (2,3,4)
              AND BINGQUID = #{bingquid}
            """)
    int countIntraoperativeMedicationByWard(@Param("bingquid") String bingquid);

    /**
     * 分页查询医嘱数据（按病区）
     * @param bingquid 病区ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 医嘱列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT *, ROW_NUMBER() OVER (ORDER BY YIZHUID) AS ROW_NUM
                FROM his6.yz_bingrenyz
                WHERE BINGQUID = #{bingquid}
                  AND YIZHUZT IN (2, 3, 4)
                  AND KAISHISJ > SYSDATE - 7
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<YzBingrenyz> selectDoctorAdviceByPage(@Param("bingquid") String bingquid,
                                               @Param("offset") int offset,
                                               @Param("limit") int limit);

    /**
     * 统计医嘱总数（按病区）
     * @param bingquid 病区ID
     * @return 医嘱总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.yz_bingrenyz
            WHERE BINGQUID = #{bingquid}
              AND YIZHUZT IN (2, 3, 4)
              AND KAISHISJ > SYSDATE - 7
            """)
    int countDoctorAdvice(@Param("bingquid") String bingquid);

    /**
     * 根据病人住院ID查询医嘱列表
     * @param bingrenzyid 病人住院ID
     * @return 医嘱列表
     */
    @Select("""
            SELECT *
            FROM his6.yz_bingrenyz
            WHERE BINGRENZYID = #{bingrenzyid}
              AND YIZHUZT IN (2, 3, 4)
              AND KAISHISJ > SYSDATE - 7
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectDoctorAdviceByPatientId(@Param("bingrenzyid") String bingrenzyid);

    /**
     * 根据病人住院ID查询肾囊肿穿刺和静脉抽血医嘱列表
     * @param bingrenzyid 病人住院ID
     * @param bingquid 病区ID
     * @return 肾囊肿穿刺和静脉抽血医嘱列表
     */
    @Select("""
            SELECT *
            FROM his6.yz_bingrenyz
            WHERE BINGRENZYID = #{bingrenzyid}
              AND BINGQUID = #{bingquid}
              AND YIZHUZT IN (2, 3, 4)
              AND KAIZHUSJ >= TRUNC(SYSDATE, 'IW') 
              AND KAIZHUSJ < TRUNC(SYSDATE, 'IW') + 7
              AND (
                  (YIZHUXMID = 1044 AND YIZHUMC LIKE '%肾囊肿穿刺%')
                  OR
                  (YIZHUXMID = **********)
              )
            ORDER BY KAISHISJ DESC
            """)
    List<YzBingrenyz> selectRenalCystPunctureAndVenipunctureByPatientId(@Param("bingrenzyid") String bingrenzyid, @Param("bingquid") String bingquid);

    /**
     * 根据病人住院ID查询肾囊肿穿刺
     */
  /*  @Select("""
            SELECT a.YIZHUMC, a.YIZHUFL, b.YUYUESQRQ, b.DANGQIANZT, a.KAIZHUSJ,
                                 (SELECT dysm.daoyism5
                                  FROM his6.gy_duoyuanqdysm dysm
                                  WHERE dysm.xiangmuid = b.yizhuxmid
                                    AND dysm.zuofeibz = 0
                                    AND dysm.jiajisy = 0) as jianchadz,
                                 z.DANGQIANBQ, z.DANGQIANCW, z.XINGMING, z.ZHUYUANHAO, b.BINGRENZYID
                          FROM his6.yz_bingrenyz a
                          JOIN his6.yj_shenqingdan b ON a.yizhuid = b.yizhuid
                          JOIN his6.zy_bingrenxx z ON b.BINGRENZYID = z.BINGRENZYID
                          WHERE
                            a.BINGQUID = #{bingquid}
                            AND a.BINGRENZYID = #{bingrenzyid}
                            AND (a.YIZHUXMID = 1044 and a.YIZHUMC like '%肾囊肿穿刺%' )
            				AND a.KAIZHUSJ >= TRUNC(SYSDATE)
             """)
    List<YzChuanCIJingMaiResp> selectRenalCystPunctureByPatientId(@Param("bingrenzyid") String bingrenzyid, @Param("bingquid") String bingquid);

*/
    /**
     * 根据病人住院ID查询肾囊肿穿刺
     */
    @Select("""
            SELECT  a.YIZHUID, a.YIZHUMC, a.YIZHUFL, b.YUYUESQRQ, b.DANGQIANZT, a.KAIZHUSJ,
                                 (SELECT dysm.daoyism5
                                  FROM his6.gy_duoyuanqdysm dysm
                                  WHERE dysm.xiangmuid = b.yizhuxmid
                                    AND dysm.zuofeibz = 0
                                    AND dysm.jiajisy = 0) as jianchadz,
                                 z.DANGQIANBQ, z.DANGQIANCW, z.XINGMING, z.ZHUYUANHAO, b.BINGRENZYID
                          FROM his6.yz_bingrenyz a
                          JOIN his6.yj_shenqingdan b ON a.yizhuid = b.yizhuid
                          JOIN his6.zy_bingrenxx z ON b.BINGRENZYID = z.BINGRENZYID
                          WHERE
                            a.BINGRENZYID = #{bingrenzyid}
                            AND (a.YIZHUXMID = 1044 and a.YIZHUMC like '%肾囊肿穿刺%' )
             """)
    List<YzChuanCIJingMaiResp> selectRenalCystPunctureByPatientId(@Param("bingrenzyid") String bingrenzyid, @Param("bingquid") String bingquid);



    /**
     * 根据病人住院ID查询静脉抽血
     */
    @Select("""
          select  a.YIZHUID, a.YIZHUMC, a.YIZHUFL, a.KAIZHUSJ, b.ZHIXINGZT, b.ZHIXINGKSSJ , a.DANGQIANZT
          from his6.YZ_BINGRENYZ a LEFT JOIN his6.YZ_YIZHUZX b on a.YIZHUID = b.YIZHUID 
          where a.BINGQUID = #{bingquid}
          AND a.BINGRENZYID = #{bingrenzyid}
          AND a.YIZHUXMID =  ********** and a.KAIZHUSJ >= TRUNC(SYSDATE, 'IW') 
          AND a.KAIZHUSJ < TRUNC(SYSDATE, 'IW') + 7 and a.YIZHUZT in (2,3,4)
         """)
    List<YzChuanCIJingMaiResp> selectVenipunctureByPatientId(@Param("bingrenzyid") String bingrenzyid, @Param("bingquid") String bingquid);
}
