package com.javazx.batch.mapper;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.javazx.batch.po.SmShoushuxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【SM_SHOUSHUXX(手麻_手术信息)】的数据库操作Mapper
* @createDate 2025-06-12 15:26:18
* @Entity generator.domain.SmShoushuxx
*/
@Mapper
@DS("hzzyy")
public interface SmShoushuxxMapper extends BaseMapper<SmShoushuxx> {

    int deleteByPrimaryKey(Long id);

    int insert(SmShoushuxx record);

    int insertSelective(SmShoushuxx record);

    SmShoushuxx selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmShoushuxx record);

    int updateByPrimaryKey(SmShoushuxx record);

    /**
     * 查询今日手术数量
     * @param bingrenzyid 病人住院ID
     * @param bingquid 病区ID
     * @return 手术数量
     */
    @Select("""
           SELECT COUNT(*)
           FROM HIS6.sm_shoushuxx a, HIS6.yz_bingrenyz b
           WHERE a.shoushudid = b.yizhuid
             AND a.ZHUANGTAIBZ NOT IN (0, 2)
             AND b.BINGQUID = #{bingquid}
             AND b.BINGRENZYID = #{bingrenzyid}
             AND TRUNC(a.ANPAISJ) = TRUNC(SYSDATE)
            """)
    Integer getTodaySurgeryCount(@Param("bingrenzyid") String bingrenzyid, @Param("bingquid") String bingquid);

    /**
     * 分页查询手术信息列表（按病区）
     * @param bingquid 病区ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 手术信息列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT a.*, b.BINGQUID, b.BINGRENZYID, b.KAISHISJ,
                       ROW_NUMBER() OVER (ORDER BY a.SHOUSHUDID) AS ROW_NUM
                FROM his6.sm_shoushuxx a, his6.yz_bingrenyz b
                WHERE a.shoushudid = b.yizhuid
                  AND a.ZHUANGTAIBZ NOT IN (0, 2)
                  AND b.BINGQUID = #{bingquid}
                  AND b.KAISHISJ > SYSDATE - 6
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<SmShoushuxx> selectSurgeryByPage(@Param("bingquid") String bingquid,
                                          @Param("offset") int offset,
                                          @Param("limit") int limit);

    /**
     * 统计手术信息总数（按病区）
     * @param bingquid 病区ID
     * @return 手术信息总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.sm_shoushuxx a, his6.yz_bingrenyz b
            WHERE a.shoushudid = b.yizhuid
              AND a.ZHUANGTAIBZ NOT IN (0, 2)
              AND b.BINGQUID = #{bingquid}
              AND b.KAISHISJ > SYSDATE - 6
            """)
    int countSurgery(@Param("bingquid") String bingquid);

    /**
     * 分页查询当日手术信息列表（按病区）
     * @param bingquid 病区ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 当日手术信息列表
     */
    @Select("""
            SELECT *
            FROM (
                SELECT a.*, b.BINGQUID, b.BINGRENZYID, b.KAISHISJ,
                       ROW_NUMBER() OVER (ORDER BY a.SHOUSHUDID) AS ROW_NUM
                FROM his6.sm_shoushuxx a, his6.yz_bingrenyz b
                WHERE a.shoushudid = b.yizhuid
                  AND a.ZHUANGTAIBZ NOT IN (0, 2)
                  AND b.BINGQUID = #{bingquid}
                  AND b.KAISHISJ > SYSDATE - 6
                  AND TRUNC(a.ANPAISJ) = TRUNC(SYSDATE)
            )
            WHERE ROW_NUM BETWEEN #{offset} + 1 AND #{offset} + #{limit}
            """)
    List<SmShoushuxx> selectTodaySurgeryByPage(@Param("bingquid") String bingquid,
                                               @Param("offset") int offset,
                                               @Param("limit") int limit);

    /**
     * 统计当日手术信息总数（按病区）
     * @param bingquid 病区ID
     * @return 当日手术信息总数
     */
    @Select("""
            SELECT COUNT(*)
            FROM his6.sm_shoushuxx a, his6.yz_bingrenyz b
            WHERE a.shoushudid = b.yizhuid
              AND a.ZHUANGTAIBZ NOT IN (0, 2)
              AND b.BINGQUID = #{bingquid}
              AND b.KAISHISJ > SYSDATE - 6
              AND TRUNC(a.ANPAISJ) = TRUNC(SYSDATE)
            """)
    int countTodaySurgery(@Param("bingquid") String bingquid);

}
