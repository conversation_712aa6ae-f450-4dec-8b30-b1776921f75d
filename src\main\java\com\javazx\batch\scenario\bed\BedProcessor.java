package com.javazx.batch.scenario.bed;

import com.javazx.batch.vo.BedInfoReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 床位数据处理器
 * 将BedInfoReq对象进行处理和验证
 */
@Component
public class BedProcessor implements ItemProcessor<BedInfoReq, BedInfoReq> {

    private static final Logger log = LoggerFactory.getLogger(BedProcessor.class);

    @Override
    public BedInfoReq process(BedInfoReq bedInfoReq) {
        if (bedInfoReq == null) {
            return null;
        }

        log.debug("处理床位数据: 病区{} - 床位{}", bedInfoReq.getWardCode(), bedInfoReq.getBedNo());

        try {
            // 验证床位信息
            if (!isValidBedInfo(bedInfoReq)) {
                log.warn("床位信息无效: {}", bedInfoReq);
                return null;
            }

            // 标准化床位信息
            BedInfoReq processedBed = standardizeBedInfo(bedInfoReq);

            log.debug("完成床位数据处理: 病区{} - 床位{}", processedBed.getWardCode(), processedBed.getBedNo());
            return processedBed;
            
        } catch (Exception e) {
            log.error("处理床位数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证床位信息是否有效
     */
    private boolean isValidBedInfo(BedInfoReq bedInfo) {
        // 检查床位号是否为空
        if (!StringUtils.hasLength(bedInfo.getBedNo())) {
            log.warn("床位号为空");
            return false;
        }

        // 检查病区代码是否为空
        if (!StringUtils.hasLength(bedInfo.getWardCode())) {
            log.warn("病区代码为空");
            return false;
        }

        // 检查床位号格式（可以根据实际规则调整）
        String bedNo = bedInfo.getBedNo().trim();
        if (bedNo.length() > 10) {
            log.warn("床位号过长: {}", bedNo);
            return false;
        }

        // 检查病区代码格式
        String wardCode = bedInfo.getWardCode().trim();
        if (wardCode.length() > 10) {
            log.warn("病区代码过长: {}", wardCode);
            return false;
        }

        return true;
    }

    /**
     * 标准化床位信息
     */
    private BedInfoReq standardizeBedInfo(BedInfoReq bedInfo) {
        BedInfoReq standardized = new BedInfoReq();

        standardized.setBedNo(bedInfo.getBedNo());

        standardized.setWardCode(bedInfo.getWardCode());

        return standardized;
    }
}
