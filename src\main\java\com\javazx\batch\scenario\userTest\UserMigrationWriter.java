package com.javazx.batch.scenario.userTest;

import com.javazx.batch.po.UserTo;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户数据迁移写入器
 * 继承通用的批量写入器，专门用于用户数据写入
 *
 */
@Component
public class UserMigrationWriter implements ItemWriter<UserTo>, InitializingBean {

    /**
     * 初始化提示
     */
    @Override
    public void afterPropertiesSet() {

    }


    @Override
    public void write(List<? extends UserTo> list) {

    }
}
