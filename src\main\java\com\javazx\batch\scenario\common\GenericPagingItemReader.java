package com.javazx.batch.scenario.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStream;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 通用分页读取器（线程安全版本）
 * 支持任意类型的数据分页读取，支持多线程并发访问
 * 实现ItemStream接口以支持Spring Batch生命周期管理
 *
 * @param <T> 数据类型
 */
public class GenericPagingItemReader<T> implements ItemReader<T>, ItemStream {

    private static final Logger log = LoggerFactory.getLogger(GenericPagingItemReader.class);

    private final String readerName;
    private final int pageSize;
    private final List<String> wardIds;
    private final BiFunction<String, Integer, List<T>> dataProvider;
    private final Function<String, Integer> totalCountProvider;

    // 使用原子变量和锁确保线程安全
    private final AtomicInteger globalOffset = new AtomicInteger(0);
    private final ReentrantLock lock = new ReentrantLock();
    private volatile boolean hasMoreData = true;
    
    // 线程本地变量，每个线程维护自己的数据缓存
    private final ThreadLocal<List<T>> threadLocalData = new ThreadLocal<>();
    private final ThreadLocal<Integer> threadLocalIndex = new ThreadLocal<>();

    /**
     * 构造函数
     * 
     * @param readerName 读取器名称（用于日志）
     * @param pageSize 页面大小
     * @param dataProvider 数据提供者函数 (offset, limit) -> List<T>
     * @param totalCountProvider 总数统计提供者函数 () -> Integer
     */
    public GenericPagingItemReader(String readerName, 
                                   int pageSize,
                                   List<String> wardIds,
                                   BiFunction<String, Integer, List<T>> dataProvider,
                                   Function<String, Integer> totalCountProvider) {
        this.readerName = readerName;
        this.pageSize = pageSize;
        this.wardIds = wardIds;
        this.dataProvider = dataProvider;
        this.totalCountProvider = totalCountProvider;
        
        log.info("初始化{}数据读取器（线程安全版本），病区: {}，页面大小: {}", readerName, wardIds, pageSize);
    }

    @Override
    public T read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        // 获取当前线程的数据缓存
        List<T> currentPageData = threadLocalData.get();
        Integer currentIndex = threadLocalIndex.get();
        
        // 初始化线程本地变量
        if (currentIndex == null) {
            currentIndex = 0;
            threadLocalIndex.set(currentIndex);
        }
        
        // 如果当前线程的数据已读完，尝试加载下一批数据
        if (currentPageData == null || currentIndex >= currentPageData.size()) {
            if (!hasMoreData) {
                String threadName = Thread.currentThread().getName();
                log.debug("线程 {} {}数据读取完成", threadName, readerName);
                return null;
            }
            currentPageData = loadNextBatch();
            if (currentPageData == null || currentPageData.isEmpty()) {
                return null;
            }
            currentIndex = 0;
            threadLocalIndex.set(currentIndex);
        }

        // 返回当前数据项
        T item = currentPageData.get(currentIndex);
        threadLocalIndex.set(currentIndex + 1);
        
        String threadName = Thread.currentThread().getName();
        log.debug("线程 {} 读取{}数据: {}", threadName, readerName, item);
        return item;
    }

    /**
     * 线程安全地加载下一批数据
     * 每个线程获取独立的数据批次，避免重复读取
     */
    private List<T> loadNextBatch() {
        lock.lock();
        try {
            if (!hasMoreData) {
                return null;
            }
            
            // 获取当前全局偏移量并递增
            int offset = globalOffset.getAndAdd(pageSize);
            String threadName = Thread.currentThread().getName();
            
            log.debug("线程 {} 开始加载{}数据，偏移: {}, 限制: {}", threadName, readerName, offset, pageSize);
            
            List<T> data = dataProvider.apply(offset, pageSize);
            
            if (data == null || data.size() < pageSize) {
                hasMoreData = false;
                log.info("线程 {} 加载最后一批{}数据，数据量: {}", threadName, readerName,
                        data != null ? data.size() : 0);
            } else {
                log.debug("线程 {} 成功加载{}数据，数据量: {}", threadName, readerName, data.size());
            }
            
            // 将数据存储到线程本地变量
            threadLocalData.set(data);
            return data;
            
        } catch (Exception e) {
            String threadName = Thread.currentThread().getName();
            log.error("线程 {} 加载{}数据失败: {}", threadName, readerName, e.getMessage(), e);
            hasMoreData = false;
            return null;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取总数据量（用于监控和日志）
     */
    public int getTotalCount() {
        try {
            return totalCountProvider.get();
        } catch (Exception e) {
            log.error("获取{}总数失败: {}", readerName, e.getMessage(), e);
            return 0;
        }
    }
    
    /**
     * 清理线程本地变量，防止内存泄漏
     */
    public void cleanup() {
        threadLocalData.remove();
        threadLocalIndex.remove();
    }

    /**
     * 重置读取器状态（用于重新开始读取）
     */
    public void reset() {
        lock.lock();
        try {
            globalOffset.set(0);
            hasMoreData = true;
            cleanup();
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取读取器名称
     */
    public String getReaderName() {
        return readerName;
    }

    /**
     * 获取页面大小
     */
    public int getPageSize() {
        return pageSize;
    }

    // ========== ItemStream接口实现 ==========

    /**
     * 打开资源，在每次Job步骤开始时调用
     * 这里重置读取器状态，确保每次执行都从头开始
     */
    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        log.info("打开{}数据读取器，重置状态", readerName);
        reset();
    }

    /**
     * 更新执行上下文，在检查点时调用
     * 保存当前读取进度到执行上下文
     */
    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        // 保存当前读取进度
        executionContext.putInt(readerName + ".currentWardIndex", currentWardIndex.get());
        executionContext.putInt(readerName + ".currentWardOffset", currentWardOffset.get());
        executionContext.putString(readerName + ".currentWardId", currentWardId);
        executionContext.putString(readerName + ".hasMoreData", String.valueOf(hasMoreData));

        log.debug("更新多病区{}数据读取器执行上下文，当前病区: {}, 病区索引: {}, 病区偏移: {}, 是否有更多数据: {}",
                readerName, currentWardId, currentWardIndex.get(), currentWardOffset.get(), hasMoreData);
    }

    /**
     * 关闭资源，在Job步骤结束时调用
     * 清理线程本地变量，防止内存泄漏
     */
    @Override
    public void close() throws ItemStreamException {
        log.info("关闭{}数据读取器，清理资源", readerName);
        cleanup();
    }
}
