package com.javazx.batch.scenario.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStream;
import org.springframework.batch.item.ItemStreamException;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.NonTransientResourceException;
import org.springframework.batch.item.ParseException;
import org.springframework.batch.item.UnexpectedInputException;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 通用分页读取器（线程安全版本）
 * 支持单病区和多病区两种模式的数据分页读取，支持多线程并发访问
 * 实现ItemStream接口以支持Spring Batch生命周期管理
 *
 * @param <T> 数据类型
 */
public class GenericPagingItemReader<T> implements ItemReader<T>, ItemStream {

    private static final Logger log = LoggerFactory.getLogger(GenericPagingItemReader.class);

    private final String readerName;
    private final int pageSize;
    private final boolean isMultiWard; // 是否为多病区模式

    // 单病区模式的字段
    private final BiFunction<Integer, Integer, List<T>> singleWardDataProvider; // (offset, limit) -> List<T>
    private final Supplier<Integer> singleWardCountProvider; // () -> Integer

    // 多病区模式的字段
    private final List<String> wardIds;
    private final BiFunction<String, Integer, List<T>> multiWardDataProvider; // (wardId, offset) -> List<T>
    private final Function<String, Integer> multiWardCountProvider; // wardId -> Integer

    // 使用原子变量和锁确保线程安全
    private final AtomicInteger globalOffset = new AtomicInteger(0);
    private final AtomicInteger currentWardIndex = new AtomicInteger(0);
    private final AtomicInteger currentWardOffset = new AtomicInteger(0);
    private final ReentrantLock lock = new ReentrantLock();
    private volatile boolean hasMoreData = true;
    private volatile String currentWardId = null;
    private volatile int currentWardTotalCount = 0;

    // 线程本地变量，每个线程维护自己的数据缓存
    private final ThreadLocal<List<T>> threadLocalData = new ThreadLocal<>();
    private final ThreadLocal<Integer> threadLocalIndex = new ThreadLocal<>();

    /**
     * 单病区模式构造函数
     *
     * @param readerName 读取器名称（用于日志）
     * @param pageSize 页面大小
     * @param dataProvider 数据提供者函数 (offset, limit) -> List<T>
     * @param totalCountProvider 总数统计提供者函数 () -> Integer
     */
    public GenericPagingItemReader(String readerName,
                                   int pageSize,
                                   BiFunction<Integer, Integer, List<T>> dataProvider,
                                   Supplier<Integer> totalCountProvider) {
        this.readerName = readerName;
        this.pageSize = pageSize;
        this.isMultiWard = false;

        // 单病区模式字段
        this.singleWardDataProvider = dataProvider;
        this.singleWardCountProvider = totalCountProvider;

        // 多病区模式字段设为null
        this.wardIds = null;
        this.multiWardDataProvider = null;
        this.multiWardCountProvider = null;

        log.info("初始化{}数据读取器（单病区模式，线程安全版本），页面大小: {}", readerName, pageSize);
    }

    /**
     * 多病区模式构造函数
     *
     * @param readerName 读取器名称（用于日志）
     * @param pageSize 页面大小
     * @param wardIds 病区ID列表
     * @param dataProvider 数据提供者函数 (wardId, offset) -> List<T>
     * @param totalCountProvider 总数统计提供者函数 wardId -> Integer
     */
    public GenericPagingItemReader(String readerName,
                                   int pageSize,
                                   List<String> wardIds,
                                   BiFunction<String, Integer, List<T>> dataProvider,
                                   Function<String, Integer> totalCountProvider) {
        this.readerName = readerName;
        this.pageSize = pageSize;
        this.isMultiWard = true;

        // 多病区模式字段
        this.wardIds = wardIds;
        this.multiWardDataProvider = dataProvider;
        this.multiWardCountProvider = totalCountProvider;

        // 单病区模式字段设为null
        this.singleWardDataProvider = null;
        this.singleWardCountProvider = null;

        // 初始化第一个病区
        if (wardIds != null && !wardIds.isEmpty()) {
            this.currentWardId = wardIds.get(0);
            try {
                this.currentWardTotalCount = totalCountProvider.apply(currentWardId);
            } catch (Exception e) {
                log.error("获取病区{}数据总数失败: {}", currentWardId, e.getMessage(), e);
                this.currentWardTotalCount = 0;
            }
        }

        log.info("初始化{}数据读取器（多病区模式，线程安全版本），病区: {}，页面大小: {}", readerName, wardIds, pageSize);
    }

    @Override
    public T read() throws Exception, UnexpectedInputException, ParseException, NonTransientResourceException {
        List<T> currentPageData = threadLocalData.get();
        Integer currentIndex = threadLocalIndex.get();

        if (currentIndex == null) {
            currentIndex = 0;
            threadLocalIndex.set(currentIndex);
        }
        
        // 如果当前线程的数据已读完，尝试加载下一批数据
        if (currentPageData == null || currentIndex >= currentPageData.size()) {
            if (!hasMoreData) {
                String threadName = Thread.currentThread().getName();
                log.debug("线程 {} {}数据读取完成", threadName, readerName);
                return null;
            }
            currentPageData = loadNextBatch();
            if (currentPageData == null || currentPageData.isEmpty()) {
                return null;
            }
            currentIndex = 0;
            threadLocalIndex.set(currentIndex);
        }

        // 返回当前数据项
        T item = currentPageData.get(currentIndex);
        threadLocalIndex.set(currentIndex + 1);
        
        String threadName = Thread.currentThread().getName();
        log.debug("线程 {} 读取{}数据: {}", threadName, readerName, item);
        return item;
    }

    /**
     * 线程安全地加载下一批数据
     * 支持单病区和多病区两种模式
     */
    private List<T> loadNextBatch() {
        lock.lock();
        try {
            if (!hasMoreData) {
                return null;
            }

            String threadName = Thread.currentThread().getName();
            List<T> data = null;

            if (isMultiWard) {
                data = loadNextBatchMultiWard(threadName);
            } else {
                data = loadNextBatchSingleWard(threadName);
            }

            threadLocalData.set(data);
            return data;

        } catch (Exception e) {
            String threadName = Thread.currentThread().getName();
            log.error("线程 {} 加载{}数据失败: {}", threadName, readerName, e.getMessage(), e);
            hasMoreData = false;
            return null;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 单病区模式：加载下一批数据
     */
    private List<T> loadNextBatchSingleWard(String threadName) {
        int offset = globalOffset.getAndAdd(pageSize);

        log.debug("线程 {} 开始加载{}数据，偏移: {}, 限制: {}", threadName, readerName, offset, pageSize);

        List<T> data = singleWardDataProvider.apply(offset, pageSize);

        if (data == null || data.size() < pageSize) {
            hasMoreData = false;
            log.info("线程 {} 加载最后一批{}数据，数据量: {}", threadName, readerName,
                    data != null ? data.size() : 0);
        } else {
            log.debug("线程 {} 成功加载{}数据，数据量: {}", threadName, readerName, data.size());
        }

        return data;
    }

    /**
     * 多病区模式：加载下一批数据
     * 支持多病区顺序处理，当前病区处理完毕后自动切换到下一个病区
     */
    private List<T> loadNextBatchMultiWard(String threadName) {
        while (currentWardIndex.get() < wardIds.size()) {
            String wardId = wardIds.get(currentWardIndex.get());

            if (!wardId.equals(currentWardId)) {
                currentWardId = wardId;
                currentWardOffset.set(0);
                try {
                    currentWardTotalCount = multiWardCountProvider.apply(currentWardId);
                    log.info("线程 {} 切换到病区: {}, 数据总数: {}", threadName, currentWardId, currentWardTotalCount);
                } catch (Exception e) {
                    log.error("线程 {} 获取病区{}数据总数失败: {}", threadName, currentWardId, e.getMessage(), e);
                    currentWardTotalCount = 0;
                }
            }

            int offset = currentWardOffset.getAndAdd(pageSize);

            log.info("线程 {} 开始加载病区{}{}数据，偏移: {}, 限制: {}",
                     threadName, currentWardId, readerName, offset, pageSize);

            List<T> data = multiWardDataProvider.apply(currentWardId, offset);

            if (data != null && !data.isEmpty()) {
                log.info("线程 {} 成功加载病区{}{}数据，数据量: {}",
                         threadName, currentWardId, readerName, data.size());
                return data;
            } else {
                log.info("线程 {} 病区{}{}数据处理完毕，切换到下一个病区", threadName, currentWardId, readerName);
                currentWardIndex.incrementAndGet();
                currentWardOffset.set(0);
            }
        }

        hasMoreData = false;
        log.info("线程 {} 所有病区{}{}数据读取完毕", threadName, wardIds, readerName);
        return null;
    }

    /**
     * 获取总数据量（用于监控和日志）
     */
    public int getTotalCount() {
        try {
            if (isMultiWard) {
                int totalCount = 0;
                for (String wardId : wardIds) {
                    totalCount += multiWardCountProvider.apply(wardId);
                }
                return totalCount;
            } else {
                return singleWardCountProvider.get();
            }
        } catch (Exception e) {
            log.error("获取{}总数失败: {}", readerName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取当前病区的总数据量（仅多病区模式有效）
     */
    public int getCurrentWardTotalCount() {
        return currentWardTotalCount;
    }

    /**
     * 获取当前处理的病区ID（仅多病区模式有效）
     */
    public String getCurrentWardId() {
        return currentWardId;
    }

    /**
     * 获取病区列表（仅多病区模式有效）
     */
    public List<String> getWardIds() {
        return wardIds;
    }

    /**
     * 获取当前病区的偏移量（仅多病区模式有效）
     */
    public int getCurrentWardOffset() {
        return currentWardOffset.get();
    }

    /**
     * 是否为多病区模式
     */
    public boolean isMultiWard() {
        return isMultiWard;
    }
    
    /**
     * 清理线程本地变量，防止内存泄漏
     */
    public void cleanup() {
        threadLocalData.remove();
        threadLocalIndex.remove();
    }

    /**
     * 重置读取器状态（用于重新开始读取）
     */
    public void reset() {
        lock.lock();
        try {
            globalOffset.set(0);
            hasMoreData = true;
            currentWardIndex.set(0);
            currentWardOffset.set(0);
            if (wardIds != null && !wardIds.isEmpty()) {
                currentWardId = wardIds.get(0);
                try {
                    currentWardTotalCount = multiWardCountProvider.apply(currentWardId);
                } catch (Exception e) {
                    log.error("重置时获取病区{}数据总数失败: {}", currentWardId, e.getMessage(), e);
                    currentWardTotalCount = 0;
                }
            }
            cleanup();
            log.info("重置{}数据读取器状态）", readerName);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取读取器名称
     */
    public String getReaderName() {
        return readerName;
    }

    /**
     * 获取页面大小
     */
    public int getPageSize() {
        return pageSize;
    }

    // ========== ItemStream接口实现 ==========

    /**
     * 打开资源，在每次Job步骤开始时调用
     * 这里重置读取器状态，确保每次执行都从头开始
     */
    @Override
    public void open(ExecutionContext executionContext) throws ItemStreamException {
        synchronized (this) {
            log.info("打开{}数据读取器，重置状态", readerName);
            reset();
        }
    }

    /**
     * 更新执行上下文，在检查点时调用
     * 保存当前读取进度到执行上下文
     */
    @Override
    public void update(ExecutionContext executionContext) throws ItemStreamException {
        synchronized (this) {
            String currentThread = Thread.currentThread().getName();
            if (isMultiWard) {
                Integer lastWardIndex = (Integer) executionContext.get(readerName + ".currentWardIndex");
                Integer lastWardOffset = (Integer) executionContext.get(readerName + ".currentWardOffset");
                String lastWardId = executionContext.getString(readerName + ".currentWardId");

                int currentWardIndexValue = currentWardIndex.get();
                int currentWardOffsetValue = currentWardOffset.get();

                if (lastWardIndex == null || lastWardIndex != currentWardIndexValue || lastWardOffset == null || lastWardOffset != currentWardOffsetValue || !lastWardId.equals(currentWardId)) {

                    executionContext.putInt(readerName + ".currentWardIndex", currentWardIndexValue);
                    executionContext.putInt(readerName + ".currentWardOffset", currentWardOffsetValue);
                    executionContext.putString(readerName + ".currentWardId", currentWardId);
                    executionContext.putString(readerName + ".hasMoreData", String.valueOf(hasMoreData));

                    log.debug("线程 {} 更新多病区{}数据读取器执行上下文，当前病区: {}, 病区索引: {}, 病区偏移: {}, 是否有更多数据: {}",
                            currentThread, readerName, currentWardId, currentWardIndexValue, currentWardOffsetValue, hasMoreData);
                }
            } else {
                Integer lastGlobalOffset = (Integer) executionContext.get(readerName + ".globalOffset");
                int currentGlobalOffsetValue = globalOffset.get();

                if (lastGlobalOffset == null || lastGlobalOffset != currentGlobalOffsetValue) {
                    executionContext.putInt(readerName + ".globalOffset", currentGlobalOffsetValue);
                    executionContext.putString(readerName + ".hasMoreData", String.valueOf(hasMoreData));

                    log.debug("线程 {} 更新单病区{}数据读取器执行上下文，全局偏移: {}, 是否有更多数据: {}",
                            currentThread, readerName, currentGlobalOffsetValue, hasMoreData);
                }
            }
        }
    }

    /**
     * 关闭资源，在Job步骤结束时调用
     * 清理线程本地变量，防止内存泄漏
     */
    @Override
    public void close() throws ItemStreamException {
        synchronized (this) {
            log.info("关闭{}数据读取器，清理资源", readerName);
            cleanup();
        }
    }
}
