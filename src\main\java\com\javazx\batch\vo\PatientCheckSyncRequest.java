package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * 患者检查信息同步请求对象
 * 用于批量同步患者检查信息到外部系统
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientCheckSyncRequest {
    /**
     * 患者检查信息列表
     */
    private List<PatientWithCheckReq> patientWithCheckReqList;
}
