package com.javazx.batch.service;

import com.javazx.batch.mapper.GyZhigongxxMapper;
import com.javazx.batch.po.GyZhigongxx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 医护人员数据服务类
 * 专门处理医护人员数据访问
 */
@Service
public class DoctorDataService {

    private static final Logger log = LoggerFactory.getLogger(DoctorDataService.class);

    @Autowired
    private GyZhigongxxMapper gyZhigongxxMapper;

    /**
     * 分页查询医护人员数据（所有病区）
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 医护人员列表
     */
    public List<GyZhigongxx> selectDoctorsByPage(int offset, int limit) {
        try {
            log.debug("查询医护人员数据 - 偏移: {}, 限制: {}", offset, limit);
            return gyZhigongxxMapper.selectDoctorsByPage(offset, limit, null);
        } catch (Exception e) {
            log.error("查询医护人员数据失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 分页查询指定病区的医护人员数据
     * @param offset 偏移量
     * @param limit 限制数量
     * @param wardId 病区ID
     * @return 医护人员列表
     */
    public List<GyZhigongxx> selectDoctorsByPageAndWard(int offset, int limit, String wardId) {
        try {
            log.debug("查询病区{}医护人员数据 - 偏移: {}, 限制: {}", wardId, offset, limit);
            return gyZhigongxxMapper.selectDoctorsByPageAndWard(offset, limit, wardId);
        } catch (Exception e) {
            log.error("查询病区{}医护人员数据失败: {}", wardId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计医护人员总数（所有病区）
     * @return 医护人员总数
     */
    public int countDoctors() {
        try {
            return gyZhigongxxMapper.countDoctors(null);
        } catch (Exception e) {
            log.error("统计医护人员总数失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 统计指定病区的医护人员总数
     * @param wardId 病区ID
     * @return 医护人员总数
     */
    public int countDoctorsByWard(String wardId) {
        try {
            log.debug("统计病区{}医护人员总数", wardId);
            return gyZhigongxxMapper.countDoctorsByWard(wardId);
        } catch (Exception e) {
            log.error("统计病区{}医护人员总数失败: {}", wardId, e.getMessage(), e);
            throw e;
        }
    }
}
