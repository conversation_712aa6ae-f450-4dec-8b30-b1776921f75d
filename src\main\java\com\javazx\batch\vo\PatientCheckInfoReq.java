package com.javazx.batch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 患者检查信息请求对象
 * 对应 ApifoxModel 中的 PatientCheckInfoReq 结构
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PatientCheckInfoReq {
    /**
     * 预约状态(0:未预约,1:已预约,2:已完成,3:已取消)
     */
    private Long appointmentStatus;
    
    /**
     * 床位号
     */
    private String bedNo;
    
    /**
     * 预约结束时间
     */
    private String appointEndTime;
    
    /**
     * 检查地点
     */
    private String checkLocation;
    
    /**
     * 检查名
     */
    private String checkName;
    
    /**
     * 预约开始时间
     */
    private String appointStartTime;

    /**
     * 显示状态(0:不显示,1:显示)
     */
    private Long isDisplay;
    
    /**
     * 是否空腹(0:否,1:是)
     */
    private Long isFasting;
    
    /**
     * 是否完成(0:否,1:是)
     */
    private Long isFinish;
    
    /**
     * 是否特殊检查(0:否,1:是)
     */
    private Long isSpecialCheck;
    
    /**
     * 病人名称
     */
    private String patientName;
    
    /**
     * 住院号
     */
    private String zyh;
}
