package com.javazx.batch.scenario.patientInfor;

import com.javazx.batch.service.PatientDataService;
import com.javazx.batch.po.ZyBingrenxx;
import com.javazx.batch.scenario.AbstractBatchScenario;
import com.javazx.batch.scenario.common.GenericPagingItemReader;
import com.javazx.batch.vo.PatientInfoReq;
import com.javazx.batch.vo.PatientLabelSyncRequest;
import com.javazx.batch.vo.PatientWithLabelReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 患者数据同步场景
 * 从数据库读取患者数据并同步到外部系统
 * 支持动态配置病区ID
 */
@Component
@Scope("prototype")
public class PatientScenario extends AbstractBatchScenario<ZyBingrenxx, PatientWithLabelReq> {

    private static final Logger log = LoggerFactory.getLogger(PatientScenario.class);

    // 支持多病区配置，可以通过配置动态修改
    private List<String> wardIds = Arrays.asList("1006");

    @Autowired
    @Qualifier("smartwardWebClient")
    private WebClient smartwardWebClient;

    @Autowired
    private PatientDataService patientDataService;

    public PatientScenario() {
        super("patient", "患者数据同步场景：同步患者标签检查数据");
        this.setCommitInterval(100);
        this.setPageSize(50);
    }

    @Override
    public ItemReader<ZyBingrenxx> createReader() {
        log.info("创建多病区{}患者数据读取器，病区: {}, 页面大小: {}", wardIds, wardIds, getPageSize());

        GenericPagingItemReader<ZyBingrenxx> reader = new GenericPagingItemReader<>(
                "患者",
                getPageSize(),
                wardIds,
                // 病区数据提供者：根据病区ID和偏移量获取患者数据
                (wardId, offset) -> {
                    try {
                        return patientDataService.selectPatientsByPage(wardId, offset, getPageSize());
                    } catch (Exception e) {
                        log.error("查询病区{}患者数据失败: {}", wardId, e.getMessage(), e);
                        return null;
                    }
                },
                wardId -> {
                    try {
                        return patientDataService.countPatients(wardId);
                    } catch (Exception e) {
                        log.error("统计病区{}患者总数失败: {}", wardId, e.getMessage(), e);
                        return 0;
                    }
                }
        );

        // 记录总患者数量
        int totalCount = reader.getTotalCount();
        log.info("所有病区{}患者总数: {}", wardIds, totalCount);

        return reader;
    }

    @Override
    public ItemProcessor<ZyBingrenxx, PatientWithLabelReq> createProcessor() {
        log.info("创建患者数据转换器");
        return new PatientProcessor(patientDataService);
    }
    
    @Override
    public ItemWriter<PatientWithLabelReq> createWriter() {
        log.info("创建病区{}患者标签同步数据写入器", wardIds);
        return items -> {
            // 设置所有标签编码列表（包含默认标签和患者特定标签）
            List<String> allLabelCodes = new ArrayList<>();
            // 添加默认标签
            allLabelCodes.addAll(getDefaultLabelCodes());

            PatientLabelSyncRequest request = new PatientLabelSyncRequest();
            request.setAllLabelCodeList(allLabelCodes);

            List<PatientWithLabelReq> patientList = new ArrayList<>();
            for (PatientWithLabelReq item : items) {
                patientList.add(item);
            }
            request.setPatientWithLabelReqList(patientList);

            String threadName = Thread.currentThread().getName();
            log.info("线程: {}, 开始处理病区{}患者数据量: {}", threadName, wardIds, items.size());

            try {
                List<String> patientNames = getPatientNameFromRequest(patientList);
                String patientNamesStr = String.join(", ", patientNames);
                smartwardWebClient
                        .post()
                        .uri("/sync/patient/sync_patient_label")
                        .contentType(MediaType.APPLICATION_JSON)
                        .bodyValue(request)
                        .retrieve()
                        .bodyToMono(String.class)
                        .subscribe(
                                response -> {
                                    // 处理响应数据
                                    System.out.println("病区" + wardIds + "患者 Response: " + response);
                                },
                                error -> {
                                    // 处理错误
                                    System.err.println("病区" + wardIds + "患者 Error: " + error.getMessage());
                                }
                        );

                log.info("已发送病区{}患者标签数据: {} (线程: {})", wardIds, patientNamesStr, threadName);
            } catch (Exception e) {
                log.error("病区{}患者标签数据时发生异常: {}", wardIds, e.getMessage(), e);
            }

            log.info("线程: {}, 完成处理病区{}患者数据量: {}", threadName, wardIds, items.size());
        };
    }


    /**
     * 从请求对象中获取患者姓名
     */
    private List<String> getPatientNameFromRequest(List<PatientWithLabelReq> requests) {
        if (requests == null || requests.isEmpty()) {
            return List.of("未知患者");
        }

        return requests.stream()
                .filter(Objects::nonNull)
                .map(PatientWithLabelReq::getPatientInfo)
                .filter(Objects::nonNull)
                .map(PatientInfoReq::getPatientName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 获取默认标签编码列表
     */
    private List<String> getDefaultLabelCodes() {
        List<String> defaultCodes = new ArrayList<>();
        defaultCodes.add("FL01"); // 全部
        defaultCodes.add("FL02"); // 在院
        defaultCodes.add("FL05"); // 一级护理
        defaultCodes.add("FL06"); // 二级护理
        defaultCodes.add("FL07"); // 危重
        defaultCodes.add("FL08"); // 三级护理
        defaultCodes.add("FL09"); // 今日入院
        defaultCodes.add("FL10"); // 今日手术
        defaultCodes.add("FL11"); // 今日出院
        defaultCodes.add("FL12"); // 转入
        defaultCodes.add("FL13"); // 转出
        defaultCodes.add("FL14"); // 借床
        defaultCodes.add("FL15"); // 多重耐药
        defaultCodes.add("FL16"); // 过敏
        defaultCodes.add("FL17"); // 高危VTE
        defaultCodes.add("FL18"); // 高危跌倒
        defaultCodes.add("FL19"); // 高危压力性损伤
        defaultCodes.add("FL20"); // 疼痛
        defaultCodes.add("FL21"); // 中医护理方案
        return defaultCodes;
    }
}
