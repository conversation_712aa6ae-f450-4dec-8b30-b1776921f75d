<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.ZyBingrenxxMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.ZyBingrenxx">
            <id property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="SHEBAOBH" column="SHEBAOBH" jdbcType="VARCHAR"/>
            <result property="YIBAOKH" column="YIBAOKH" jdbcType="VARCHAR"/>
            <result property="JIUZHENKH" column="JIUZHENKH" jdbcType="VARCHAR"/>
            <result property="SHEQUBH" column="SHEQUBH" jdbcType="VARCHAR"/>
            <result property="FEIYONGLB" column="FEIYONGLB" jdbcType="VARCHAR"/>
            <result property="FEIYONGXZ" column="FEIYONGXZ" jdbcType="VARCHAR"/>
            <result property="YOUHUILB" column="YOUHUILB" jdbcType="VARCHAR"/>
            <result property="GONGFEIZH" column="GONGFEIZH" jdbcType="VARCHAR"/>
            <result property="GONGFEIDW" column="GONGFEIDW" jdbcType="VARCHAR"/>
            <result property="GONGFEIDWMC" column="GONGFEIDWMC" jdbcType="VARCHAR"/>
            <result property="XINGMING" column="XINGMING" jdbcType="VARCHAR"/>
            <result property="SHURUMA1" column="SHURUMA1" jdbcType="VARCHAR"/>
            <result property="SHURUMA2" column="SHURUMA2" jdbcType="VARCHAR"/>
            <result property="SHURUMA3" column="SHURUMA3" jdbcType="VARCHAR"/>
            <result property="XINGBIE" column="XINGBIE" jdbcType="VARCHAR"/>
            <result property="NIANLINGDW" column="NIANLINGDW" jdbcType="VARCHAR"/>
            <result property="SHENFENZH" column="SHENFENZH" jdbcType="VARCHAR"/>
            <result property="CHUSHENGRQ" column="CHUSHENGRQ" jdbcType="TIMESTAMP"/>
            <result property="GONGZUODW" column="GONGZUODW" jdbcType="VARCHAR"/>
            <result property="DANWEIDH" column="DANWEIDH" jdbcType="VARCHAR"/>
            <result property="DANWEIYB" column="DANWEIYB" jdbcType="VARCHAR"/>
            <result property="JIATINGDZ" column="JIATINGDZ" jdbcType="VARCHAR"/>
            <result property="JIATINGDH" column="JIATINGDH" jdbcType="VARCHAR"/>
            <result property="JIATINGYB" column="JIATINGYB" jdbcType="VARCHAR"/>
            <result property="XUEXING" column="XUEXING" jdbcType="VARCHAR"/>
            <result property="HUNYIN" column="HUNYIN" jdbcType="VARCHAR"/>
            <result property="ZHIYE" column="ZHIYE" jdbcType="VARCHAR"/>
            <result property="GUOJI" column="GUOJI" jdbcType="VARCHAR"/>
            <result property="MINZU" column="MINZU" jdbcType="VARCHAR"/>
            <result property="SHENGFEN" column="SHENGFEN" jdbcType="VARCHAR"/>
            <result property="XIANGZHENJD" column="XIANGZHENJD" jdbcType="VARCHAR"/>
            <result property="SHIDIQU" column="SHIDIQU" jdbcType="VARCHAR"/>
            <result property="JIGUAN" column="JIGUAN" jdbcType="VARCHAR"/>
            <result property="CHUSHENGDI" column="CHUSHENGDI" jdbcType="VARCHAR"/>
            <result property="YOUBIAN" column="YOUBIAN" jdbcType="VARCHAR"/>
            <result property="LIANXIREN" column="LIANXIREN" jdbcType="VARCHAR"/>
            <result property="GUANXI" column="GUANXI" jdbcType="VARCHAR"/>
            <result property="LIANXIRDZ" column="LIANXIRDZ" jdbcType="VARCHAR"/>
            <result property="LIANXIRDH" column="LIANXIRDH" jdbcType="VARCHAR"/>
            <result property="LIANXIRYB" column="LIANXIRYB" jdbcType="VARCHAR"/>
            <result property="JIWANGSHI" column="JIWANGSHI" jdbcType="VARCHAR"/>
            <result property="GUOMINSHI" column="GUOMINSHI" jdbcType="VARCHAR"/>
            <result property="QUYU" column="QUYU" jdbcType="VARCHAR"/>
            <result property="WAIDIBRBZ" column="WAIDIBRBZ" jdbcType="DECIMAL"/>
            <result property="JIANDANGREN" column="JIANDANGREN" jdbcType="VARCHAR"/>
            <result property="JIANDANGRQ" column="JIANDANGRQ" jdbcType="TIMESTAMP"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="YINGERBZ" column="YINGERBZ" jdbcType="DECIMAL"/>
            <result property="ZHUYUANCS" column="ZHUYUANCS" jdbcType="DECIMAL"/>
            <result property="RUYUANRQ" column="RUYUANRQ" jdbcType="TIMESTAMP"/>
            <result property="YUCHUYRQ" column="YUCHUYRQ" jdbcType="TIMESTAMP"/>
            <result property="CHUYUANRQ" column="CHUYUANRQ" jdbcType="TIMESTAMP"/>
            <result property="ZAIYUANZT" column="ZAIYUANZT" jdbcType="VARCHAR"/>
            <result property="RUYUANTJ" column="RUYUANTJ" jdbcType="VARCHAR"/>
            <result property="RUYUANKS" column="RUYUANKS" jdbcType="VARCHAR"/>
            <result property="RUYUANBQ" column="RUYUANBQ" jdbcType="VARCHAR"/>
            <result property="RUYUANCW" column="RUYUANCW" jdbcType="VARCHAR"/>
            <result property="DANGQIANKS" column="DANGQIANKS" jdbcType="VARCHAR"/>
            <result property="DANGQIANBQ" column="DANGQIANBQ" jdbcType="VARCHAR"/>
            <result property="DANGQIANCW" column="DANGQIANCW" jdbcType="VARCHAR"/>
            <result property="GUANLIKS" column="GUANLIKS" jdbcType="VARCHAR"/>
            <result property="JIECHUANGBZ" column="JIECHUANGBZ" jdbcType="DECIMAL"/>
            <result property="LIYUANQX" column="LIYUANQX" jdbcType="VARCHAR"/>
            <result property="MENZHENZDDM" column="MENZHENZDDM" jdbcType="VARCHAR"/>
            <result property="MENZHENZDMC" column="MENZHENZDMC" jdbcType="VARCHAR"/>
            <result property="RUYUANZDDM" column="RUYUANZDDM" jdbcType="VARCHAR"/>
            <result property="RUYUANZDMC" column="RUYUANZDMC" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDDM" column="CHUYUANZDDM" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDMC" column="CHUYUANZDMC" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDDM2" column="CHUYUANZDDM2" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDMC2" column="CHUYUANZDMC2" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDMC3" column="CHUYUANZDMC3" jdbcType="VARCHAR"/>
            <result property="CHUYUANZDDM3" column="CHUYUANZDDM3" jdbcType="VARCHAR"/>
            <result property="JIATINGBCBZ" column="JIATINGBCBZ" jdbcType="DECIMAL"/>
            <result property="BINGQING" column="BINGQING" jdbcType="VARCHAR"/>
            <result property="FENMIAN" column="FENMIAN" jdbcType="VARCHAR"/>
            <result property="SHANGCHUANBZ" column="SHANGCHUANBZ" jdbcType="DECIMAL"/>
            <result property="SHANGCHUANRQ" column="SHANGCHUANRQ" jdbcType="TIMESTAMP"/>
            <result property="DANBAOREN" column="DANBAOREN" jdbcType="VARCHAR"/>
            <result property="DANBAOJE" column="DANBAOJE" jdbcType="DECIMAL"/>
            <result property="JIAZHANGXM" column="JIAZHANGXM" jdbcType="VARCHAR"/>
            <result property="ZHUYUANHAO" column="ZHUYUANHAO" jdbcType="VARCHAR"/>
            <result property="BINGANHAO" column="BINGANHAO" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="JIESUANXH" column="JIESUANXH" jdbcType="DECIMAL"/>
            <result property="NIANLING" column="NIANLING" jdbcType="DECIMAL"/>
            <result property="MENZHENYS" column="MENZHENYS" jdbcType="VARCHAR"/>
            <result property="SHOUZHIYS" column="SHOUZHIYS" jdbcType="VARCHAR"/>
            <result property="ZHUYUANYS" column="ZHUYUANYS" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYS" column="ZHUZHIYS" jdbcType="VARCHAR"/>
            <result property="YUYUEID" column="YUYUEID" jdbcType="VARCHAR"/>
            <result property="SHENHEBZ" column="SHENHEBZ" jdbcType="DECIMAL"/>
            <result property="QIGUANYZBZ" column="QIGUANYZBZ" jdbcType="DECIMAL"/>
            <result property="JIAOYILSH" column="JIAOYILSH" jdbcType="VARCHAR"/>
            <result property="GERENBH" column="GERENBH" jdbcType="VARCHAR"/>
            <result property="IC" column="IC" jdbcType="VARCHAR"/>
            <result property="SHIMINKWKH" column="SHIMINKWKH" jdbcType="VARCHAR"/>
            <result property="YILIAOZID" column="YILIAOZID" jdbcType="VARCHAR"/>
            <result property="YILIAOZM" column="YILIAOZM" jdbcType="VARCHAR"/>
            <result property="RUKERQ" column="RUKERQ" jdbcType="TIMESTAMP"/>
            <result property="RUKEBZ" column="RUKEBZ" jdbcType="DECIMAL"/>
            <result property="ZHANCHUANGBZ" column="ZHANCHUANGBZ" jdbcType="DECIMAL"/>
            <result property="RUYUANKSMC" column="RUYUANKSMC" jdbcType="VARCHAR"/>
            <result property="RUYUANBQMC" column="RUYUANBQMC" jdbcType="VARCHAR"/>
            <result property="DANGQIANKSMC" column="DANGQIANKSMC" jdbcType="VARCHAR"/>
            <result property="DANGQIANBQMC" column="DANGQIANBQMC" jdbcType="VARCHAR"/>
            <result property="GUANLIKSMC" column="GUANLIKSMC" jdbcType="VARCHAR"/>
            <result property="MENZHENYSXM" column="MENZHENYSXM" jdbcType="VARCHAR"/>
            <result property="SHOUZHIYSXM" column="SHOUZHIYSXM" jdbcType="VARCHAR"/>
            <result property="ZHUYUANYSXM" column="ZHUYUANYSXM" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYSXM" column="ZHUZHIYSXM" jdbcType="VARCHAR"/>
            <result property="BINGLISLZT" column="BINGLISLZT" jdbcType="VARCHAR"/>
            <result property="FEIYONGXZZHBZ" column="FEIYONGXZZHBZ" jdbcType="DECIMAL"/>
            <result property="FEIYONGXZZHR" column="FEIYONGXZZHR" jdbcType="VARCHAR"/>
            <result property="FEIYONGXZZHRQ" column="FEIYONGXZZHRQ" jdbcType="TIMESTAMP"/>
            <result property="YIBAOYLLB" column="YIBAOYLLB" jdbcType="VARCHAR"/>
            <result property="TESHUBZBZ" column="TESHUBZBZ" jdbcType="DECIMAL"/>
            <result property="TESHUBZMC" column="TESHUBZMC" jdbcType="VARCHAR"/>
            <result property="TESHUBZBM" column="TESHUBZBM" jdbcType="VARCHAR"/>
            <result property="MENZHENZYZDDM" column="MENZHENZYZDDM" jdbcType="VARCHAR"/>
            <result property="MENZHENZYZDMC" column="MENZHENZYZDMC" jdbcType="VARCHAR"/>
            <result property="RUYUANZYZDDM" column="RUYUANZYZDDM" jdbcType="VARCHAR"/>
            <result property="RUYUANZYZDMC" column="RUYUANZYZDMC" jdbcType="VARCHAR"/>
            <result property="WAIYUANZDBZ" column="WAIYUANZDBZ" jdbcType="DECIMAL"/>
            <result property="RUYUANZYZHDM" column="RUYUANZYZHDM" jdbcType="VARCHAR"/>
            <result property="RUYUANZYZHMC" column="RUYUANZYZHMC" jdbcType="VARCHAR"/>
            <result property="YAOPINBZYBZ" column="YAOPINBZYBZ" jdbcType="DECIMAL"/>
            <result property="ZHUANBINGQBZ" column="ZHUANBINGQBZ" jdbcType="DECIMAL"/>
            <result property="MUQINZYID" column="MUQINZYID" jdbcType="VARCHAR"/>
            <result property="CHANFUBZ" column="CHANFUBZ" jdbcType="DECIMAL"/>
            <result property="LINCHUANGLJBZ" column="LINCHUANGLJBZ" jdbcType="DECIMAL"/>
            <result property="QUXIAORYBZ" column="QUXIAORYBZ" jdbcType="DECIMAL"/>
            <result property="QINGJIABZ" column="QINGJIABZ" jdbcType="DECIMAL"/>
            <result property="YUANBINGRZYID" column="YUANBINGRZYID" jdbcType="VARCHAR"/>
            <result property="LVSETDBZ" column="LVSETDBZ" jdbcType="DECIMAL"/>
            <result property="LVSETDKQRQ" column="LVSETDKQRQ" jdbcType="TIMESTAMP"/>
            <result property="YIBAOMXFJYJLS" column="YIBAOMXFJYJLS" jdbcType="DECIMAL"/>
            <result property="TESHUBZ" column="TESHUBZ" jdbcType="DECIMAL"/>
            <result property="YILIAOKH" column="YILIAOKH" jdbcType="VARCHAR"/>
            <result property="JIESHAOREN" column="JIESHAOREN" jdbcType="VARCHAR"/>
            <result property="YINGERSL" column="YINGERSL" jdbcType="DECIMAL"/>
            <result property="YOUHUILBLB" column="YOUHUILBLB" jdbcType="VARCHAR"/>
            <result property="QUXIAOYCYRQ" column="QUXIAOYCYRQ" jdbcType="TIMESTAMP"/>
            <result property="RUYUANZDDM2" column="RUYUANZDDM2" jdbcType="VARCHAR"/>
            <result property="RUYUANZDMC2" column="RUYUANZDMC2" jdbcType="VARCHAR"/>
            <result property="RUYUANZDDM3" column="RUYUANZDDM3" jdbcType="VARCHAR"/>
            <result property="RUYUANZDMC3" column="RUYUANZDMC3" jdbcType="VARCHAR"/>
            <result property="YIBAOBRXX" column="YIBAOBRXX" jdbcType="VARCHAR"/>
            <result property="FEIYONGLB2" column="FEIYONGLB2" jdbcType="VARCHAR"/>
            <result property="FEIYONGXZ2" column="FEIYONGXZ2" jdbcType="VARCHAR"/>
            <result property="YILIAOJZDJBZ" column="YILIAOJZDJBZ" jdbcType="DECIMAL"/>
            <result property="ZHIFU1YB" column="ZHIFU1YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU2YB" column="ZHIFU2YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU3YB" column="ZHIFU3YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU4YB" column="ZHIFU4YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU5YB" column="ZHIFU5YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU6YB" column="ZHIFU6YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU7YB" column="ZHIFU7YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU8YB" column="ZHIFU8YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU9YB" column="ZHIFU9YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU10YB" column="ZHIFU10YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU11YB" column="ZHIFU11YB" jdbcType="VARCHAR"/>
            <result property="ZHIFU12YB" column="ZHIFU12YB" jdbcType="VARCHAR"/>
            <result property="SHUZHI1YB" column="SHUZHI1YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI2YB" column="SHUZHI2YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI3YB" column="SHUZHI3YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI4YB" column="SHUZHI4YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI5YB" column="SHUZHI5YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI6YB" column="SHUZHI6YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI7YB" column="SHUZHI7YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI8YB" column="SHUZHI8YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI9YB" column="SHUZHI9YB" jdbcType="DECIMAL"/>
            <result property="SHUZHI10YB" column="SHUZHI10YB" jdbcType="DECIMAL"/>
            <result property="YIBAOCYDJBZ" column="YIBAOCYDJBZ" jdbcType="DECIMAL"/>
            <result property="YIZHONGYLBZ" column="YIZHONGYLBZ" jdbcType="DECIMAL"/>
            <result property="YIZHONGYLLB" column="YIZHONGYLLB" jdbcType="VARCHAR"/>
            <result property="YIZHONGYLXYJE" column="YIZHONGYLXYJE" jdbcType="DECIMAL"/>
            <result property="YIZHONGYLBZSM" column="YIZHONGYLBZSM" jdbcType="VARCHAR"/>
            <result property="FENMIANRQ" column="FENMIANRQ" jdbcType="TIMESTAMP"/>
            <result property="BINGFAZHENG" column="BINGFAZHENG" jdbcType="VARCHAR"/>
            <result property="BUZHUKBH" column="BUZHUKBH" jdbcType="VARCHAR"/>
            <result property="YIBAOKH2" column="YIBAOKH2" jdbcType="VARCHAR"/>
            <result property="IC2" column="IC2" jdbcType="VARCHAR"/>
            <result property="QUXIAOYCYYY" column="QUXIAOYCYYY" jdbcType="VARCHAR"/>
            <result property="BEIZHU" column="BEIZHU" jdbcType="VARCHAR"/>
            <result property="ZERENHS" column="ZERENHS" jdbcType="VARCHAR"/>
            <result property="ZERENHSXM" column="ZERENHSXM" jdbcType="VARCHAR"/>
            <result property="JIUFENBRBZ" column="JIUFENBRBZ" jdbcType="DECIMAL"/>
            <result property="ZHENGJIANLX" column="ZHENGJIANLX" jdbcType="VARCHAR"/>
            <result property="JIANHURENSFZH" column="JIANHURENSFZH" jdbcType="VARCHAR"/>
            <result property="GELILX" column="GELILX" jdbcType="VARCHAR"/>
            <result property="LASTYUCHUYRQ" column="LASTYUCHUYRQ" jdbcType="TIMESTAMP"/>
            <result property="LASTCHUYUANRQ" column="LASTCHUYUANRQ" jdbcType="TIMESTAMP"/>
            <result property="YURUYUANBZ" column="YURUYUANBZ" jdbcType="DECIMAL"/>
            <result property="RUYUANDJLX" column="RUYUANDJLX" jdbcType="VARCHAR"/>
            <result property="XIANQU" column="XIANQU" jdbcType="VARCHAR"/>
            <result property="NIANLING1" column="NIANLING1" jdbcType="DECIMAL"/>
            <result property="NIANLINGDW1" column="NIANLINGDW1" jdbcType="VARCHAR"/>
            <result property="GONGSHANGKFDJH" column="GONGSHANGKFDJH" jdbcType="VARCHAR"/>
            <result property="GONGSHANGKFRQ" column="GONGSHANGKFRQ" jdbcType="TIMESTAMP"/>
            <result property="LINCHUANGLJDBZBZ" column="LINCHUANGLJDBZBZ" jdbcType="DECIMAL"/>
            <result property="SHENGAO" column="SHENGAO" jdbcType="DECIMAL"/>
            <result property="TIZHONG" column="TIZHONG" jdbcType="DECIMAL"/>
            <result property="ADLSCORE" column="ADLSCORE" jdbcType="VARCHAR"/>
            <result property="SHENHEREN" column="SHENHEREN" jdbcType="VARCHAR"/>
            <result property="SHENHERQ" column="SHENHERQ" jdbcType="TIMESTAMP"/>
            <result property="GUAZHANGBZ" column="GUAZHANGBZ" jdbcType="DECIMAL"/>
            <result property="ZHENGJIANSMWJ" column="ZHENGJIANSMWJ" jdbcType="VARCHAR"/>
            <result property="BAOMIJB" column="BAOMIJB" jdbcType="VARCHAR"/>
            <result property="HUKOUDZ" column="HUKOUDZ" jdbcType="VARCHAR"/>
            <result property="HUKOUSF" column="HUKOUSF" jdbcType="VARCHAR"/>
            <result property="HUKOUSDQ" column="HUKOUSDQ" jdbcType="VARCHAR"/>
            <result property="HUKOUXQ" column="HUKOUXQ" jdbcType="VARCHAR"/>
            <result property="HUKOUXZJD" column="HUKOUXZJD" jdbcType="VARCHAR"/>
            <result property="JIATINGDZLB" column="JIATINGDZLB" jdbcType="VARCHAR"/>
            <result property="HUKOUDZLB" column="HUKOUDZLB" jdbcType="VARCHAR"/>
            <result property="JIANKANGKH" column="JIANKANGKH" jdbcType="VARCHAR"/>
            <result property="ZHAOSHIZHBZ" column="ZHAOSHIZHBZ" jdbcType="DECIMAL"/>
            <result property="JIANKANGKXX" column="JIANKANGKXX" jdbcType="VARCHAR"/>
            <result property="RIJIANZYBZ" column="RIJIANZYBZ" jdbcType="DECIMAL"/>
            <result property="CHUANGWEIZYBZ" column="CHUANGWEIZYBZ" jdbcType="DECIMAL"/>
            <result property="HUSHIZHANG" column="HUSHIZHANG" jdbcType="VARCHAR"/>
            <result property="HUSHIZXM" column="HUSHIZXM" jdbcType="VARCHAR"/>
            <result property="JIZHENYXBZ" column="JIZHENYXBZ" jdbcType="DECIMAL"/>
            <result property="YUCHUYXTSJ" column="YUCHUYXTSJ" jdbcType="TIMESTAMP"/>
            <result property="TIBIAOMJ" column="TIBIAOMJ" jdbcType="DECIMAL"/>
            <result property="YIWAISHBZ" column="YIWAISHBZ" jdbcType="VARCHAR"/>
            <result property="CHUSHENGDSF" column="CHUSHENGDSF" jdbcType="VARCHAR"/>
            <result property="CHUSHENGDSDQ" column="CHUSHENGDSDQ" jdbcType="VARCHAR"/>
            <result property="CHUSHENGDXQ" column="CHUSHENGDXQ" jdbcType="VARCHAR"/>
            <result property="JIGUANSF" column="JIGUANSF" jdbcType="VARCHAR"/>
            <result property="JIGUANSDQ" column="JIGUANSDQ" jdbcType="VARCHAR"/>
            <result property="XIANZHUZSF" column="XIANZHUZSF" jdbcType="VARCHAR"/>
            <result property="XIANZHUZSDQ" column="XIANZHUZSDQ" jdbcType="VARCHAR"/>
            <result property="XIANZHUZXQ" column="XIANZHUZXQ" jdbcType="VARCHAR"/>
            <result property="XIANZHUZQT" column="XIANZHUZQT" jdbcType="VARCHAR"/>
            <result property="HUKOUDZQT" column="HUKOUDZQT" jdbcType="VARCHAR"/>
            <result property="XIAOXILY" column="XIAOXILY" jdbcType="VARCHAR"/>
            <result property="WANGLUOFWBZ" column="WANGLUOFWBZ" jdbcType="DECIMAL"/>
            <result property="DABINGBZ" column="DABINGBZ" jdbcType="DECIMAL"/>
            <result property="SHIFOUCSSFZ" column="SHIFOUCSSFZ" jdbcType="DECIMAL"/>
            <result property="YUNCI" column="YUNCI" jdbcType="VARCHAR"/>
            <result property="CHANCI" column="CHANCI" jdbcType="VARCHAR"/>
            <result property="TAICI" column="TAICI" jdbcType="VARCHAR"/>
            <result property="YUCHANQI" column="YUCHANQI" jdbcType="VARCHAR"/>
            <result property="QUYUBH" column="QUYUBH" jdbcType="VARCHAR"/>
            <result property="MOCIYJ" column="MOCIYJ" jdbcType="VARCHAR"/>
            <result property="MIANFEIQLX" column="MIANFEIQLX" jdbcType="VARCHAR"/>
            <result property="HUKOUYB" column="HUKOUYB" jdbcType="VARCHAR"/>
            <result property="SHIBAZDBDM" column="SHIBAZDBDM" jdbcType="VARCHAR"/>
            <result property="MOCIYUEJINGSJ" column="MOCIYUEJINGSJ" jdbcType="TIMESTAMP"/>
            <result property="JSMOCIYJSJ" column="JSMOCIYJSJ" jdbcType="TIMESTAMP"/>
            <result property="YUNZHOU" column="YUNZHOU" jdbcType="DECIMAL"/>
            <result property="YUNTIAN" column="YUNTIAN" jdbcType="DECIMAL"/>
            <result property="TAICHANCI" column="TAICHANCI" jdbcType="VARCHAR"/>
            <result property="BUSUIFANGBZ" column="BUSUIFANGBZ" jdbcType="DECIMAL"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="VARCHAR"/>
            <result property="SHIFOUSQQFX" column="SHIFOUSQQFX" jdbcType="VARCHAR"/>
            <result property="SHANSHIFYBZ" column="SHANSHIFYBZ" jdbcType="DECIMAL"/>
            <result property="SFJSFLZBZ" column="SFJSFLZBZ" jdbcType="DECIMAL"/>
            <result property="BURUJINGLY" column="BURUJINGLY" jdbcType="VARCHAR"/>
            <result property="YILIANTBZ" column="YILIANTBZ" jdbcType="VARCHAR"/>
            <result property="YILIANTBH" column="YILIANTBH" jdbcType="VARCHAR"/>
            <result property="YILIANTSQDID" column="YILIANTSQDID" jdbcType="VARCHAR"/>
            <result property="ZHURENYS" column="ZHURENYS" jdbcType="VARCHAR"/>
            <result property="ZHURENYSXM" column="ZHURENYSXM" jdbcType="VARCHAR"/>
            <result property="SHENGYUBZ" column="SHENGYUBZ" jdbcType="DECIMAL"/>
            <result property="BIAOZHI120" column="BIAOZHI120" jdbcType="DECIMAL"/>
            <result property="YINANBRBZ" column="YINANBRBZ" jdbcType="DECIMAL"/>
            <result property="RUYUANYY" column="RUYUANYY" jdbcType="VARCHAR"/>
            <result property="ZAICIRYLX" column="ZAICIRYLX" jdbcType="DECIMAL"/>
            <result property="ZHUANZHENDH" column="ZHUANZHENDH" jdbcType="VARCHAR"/>
            <result property="YUANRUKERQ" column="YUANRUKERQ" jdbcType="TIMESTAMP"/>
            <result property="YINGERCWFJZZT" column="YINGERCWFJZZT" jdbcType="DECIMAL"/>
            <result property="YINGERCWFJZTZRQ" column="YINGERCWFJZTZRQ" jdbcType="TIMESTAMP"/>
            <result property="YINGERCWFJZTZR" column="YINGERCWFJZTZR" jdbcType="VARCHAR"/>
            <result property="KUAISURYBZ" column="KUAISURYBZ" jdbcType="DECIMAL"/>
            <result property="SHENFENZBXYY" column="SHENFENZBXYY" jdbcType="VARCHAR"/>
            <result property="YDRYBZ" column="YDRYBZ" jdbcType="VARCHAR"/>
            <result property="TCQBZ" column="TCQBZ" jdbcType="VARCHAR"/>
            <result property="SENLINFHBRBZ" column="SENLINFHBRBZ" jdbcType="DECIMAL"/>
            <result property="SENLINFHSPRQ" column="SENLINFHSPRQ" jdbcType="TIMESTAMP"/>
            <result property="SENLINFHBZ" column="SENLINFHBZ" jdbcType="VARCHAR"/>
            <result property="MPI" column="MPI" jdbcType="VARCHAR"/>
            <result property="SHOUCIRKR" column="SHOUCIRKR" jdbcType="VARCHAR"/>
            <result property="SHOUCIRKRXM" column="SHOUCIRKRXM" jdbcType="VARCHAR"/>
            <result property="JIUZHENQRRQ" column="JIUZHENQRRQ" jdbcType="TIMESTAMP"/>
            <result property="JIUZHENQRBZ" column="JIUZHENQRBZ" jdbcType="DECIMAL"/>
            <result property="QUERENSFRQ_HIS" column="QUERENSFRQ_HIS" jdbcType="TIMESTAMP"/>
            <result property="QUERENSFBZ_HIS" column="QUERENSFBZ_HIS" jdbcType="DECIMAL"/>
            <result property="DENGJISY" column="DENGJISY" jdbcType="VARCHAR"/>
            <result property="YUNYIXYED" column="YUNYIXYED" jdbcType="VARCHAR"/>
            <result property="YUNYIQYBZ" column="YUNYIQYBZ" jdbcType="DECIMAL"/>
            <result property="ZHUANDRYELB" column="ZHUANDRYELB" jdbcType="VARCHAR"/>
            <result property="LIUGUANBZ" column="LIUGUANBZ" jdbcType="DECIMAL"/>
            <result property="LIUGUANSQDID" column="LIUGUANSQDID" jdbcType="VARCHAR"/>
            <result property="JIUZHENID" column="JIUZHENID" jdbcType="VARCHAR"/>
            <result property="FENZHENSJ" column="FENZHENSJ" jdbcType="TIMESTAMP"/>
            <result property="LAIYUAN" column="LAIYUAN" jdbcType="VARCHAR"/>
            <result property="FENZHENYUAN" column="FENZHENYUAN" jdbcType="VARCHAR"/>
            <result property="FENZHENYUANID" column="FENZHENYUANID" jdbcType="VARCHAR"/>
            <result property="FENZHENLB" column="FENZHENLB" jdbcType="VARCHAR"/>
            <result property="SHIFUTZRQ" column="SHIFUTZRQ" jdbcType="DECIMAL"/>
            <result property="DIANHUA" column="DIANHUA" jdbcType="VARCHAR"/>
            <result property="XINGUANBZ" column="XINGUANBZ" jdbcType="DECIMAL"/>
            <result property="SANDAZXBZ" column="SANDAZXBZ" jdbcType="DECIMAL"/>
            <result property="QITABZNR" column="QITABZNR" jdbcType="VARCHAR"/>
            <result property="XINBINGRENZYID" column="XINBINGRENZYID" jdbcType="VARCHAR"/>
            <result property="LIUGUANBRZYID" column="LIUGUANBRZYID" jdbcType="VARCHAR"/>
            <result property="XIANZHUZHI" column="XIANZHUZHI" jdbcType="VARCHAR"/>
            <result property="BINGANSBBZ" column="BINGANSBBZ" jdbcType="DECIMAL"/>
            <result property="SHUANGXIANGJZBZ" column="SHUANGXIANGJZBZ" jdbcType="DECIMAL"/>
            <result property="ZHONGZHUANKS" column="ZHONGZHUANKS" jdbcType="VARCHAR"/>
            <result property="ZHONGZHUANKEMC" column="ZHONGZHUANKEMC" jdbcType="VARCHAR"/>
            <result property="DIEDAOPGBZ" column="DIEDAOPGBZ" jdbcType="DECIMAL"/>
            <result property="DISANFBZJ" column="DISANFBZJ" jdbcType="VARCHAR"/>
            <result property="BINGRENQX" column="BINGRENQX" jdbcType="VARCHAR"/>
            <result property="FENZHENJB" column="FENZHENJB" jdbcType="VARCHAR"/>
            <result property="YOUXIAOZDMC" column="YOUXIAOZDMC" jdbcType="VARCHAR"/>
            <result property="MINZHENGXX" column="MINZHENGXX" jdbcType="VARCHAR"/>
            <result property="BINGQINGZY" column="BINGQINGZY" jdbcType="VARCHAR"/>
            <result property="YANKECS" column="YANKECS" jdbcType="DECIMAL"/>
            <result property="QITAYLXTBRBZ" column="QITAYLXTBRBZ" jdbcType="DECIMAL"/>
            <result property="GCPLX" column="GCPLX" jdbcType="VARCHAR"/>
            <result property="BUSHIYDBZYY" column="BUSHIYDBZYY" jdbcType="VARCHAR"/>
            <result property="SHIFOUGZ" column="SHIFOUGZ" jdbcType="DECIMAL"/>
            <result property="EMPI_YJ" column="EMPI_YJ" jdbcType="VARCHAR"/>
            <result property="SANJIYS" column="SANJIYS" jdbcType="VARCHAR"/>
            <result property="SANJIYSMC" column="SANJIYSMC" jdbcType="VARCHAR"/>
            <result property="KEZHURYS" column="KEZHURYS" jdbcType="VARCHAR"/>
            <result property="KEZHURYSXM" column="KEZHURYSXM" jdbcType="VARCHAR"/>
            <result property="ERJIYS" column="ERJIYS" jdbcType="VARCHAR"/>
            <result property="ERJIYSMC" column="ERJIYSMC" jdbcType="VARCHAR"/>
            <result property="YIJIYS" column="YIJIYS" jdbcType="VARCHAR"/>
            <result property="YIJIYSMC" column="YIJIYSMC" jdbcType="VARCHAR"/>
            <result property="SHANGCICKSJ" column="SHANGCICKSJ" jdbcType="TIMESTAMP"/>
            <result property="ZONGHEBFBZ" column="ZONGHEBFBZ" jdbcType="DECIMAL"/>
            <result property="ZONGHEBQ" column="ZONGHEBQ" jdbcType="VARCHAR"/>
            <result property="ZONGHEBQCW" column="ZONGHEBQCW" jdbcType="VARCHAR"/>
            <result property="ZONGHEBFJRSJ" column="ZONGHEBFJRSJ" jdbcType="TIMESTAMP"/>
            <result property="ZONGHEBQMC" column="ZONGHEBQMC" jdbcType="VARCHAR"/>
            <result property="QIANTIANJH" column="QIANTIANJH" jdbcType="DECIMAL"/>
            <result property="ZHOUQI" column="ZHOUQI" jdbcType="VARCHAR"/>
            <result property="JINGQI" column="JINGQI" jdbcType="VARCHAR"/>
            <result property="YXJM" column="YXJM" jdbcType="DECIMAL"/>
            <result property="XINXITBZ" column="XINXITBZ" jdbcType="DECIMAL"/>
            <result property="CHUANGTOUKDYBZ" column="CHUANGTOUKDYBZ" jdbcType="DECIMAL"/>
            <result property="FLSBZ" column="FLSBZ" jdbcType="DECIMAL"/>
            <result property="XINGBIEDM" column="XINGBIEDM" jdbcType="VARCHAR"/>
            <result property="HUNYINDM" column="HUNYINDM" jdbcType="VARCHAR"/>
            <result property="ZHIYEDM" column="ZHIYEDM" jdbcType="VARCHAR"/>
            <result property="GUOJIDM" column="GUOJIDM" jdbcType="VARCHAR"/>
            <result property="MINZUDM" column="MINZUDM" jdbcType="VARCHAR"/>
            <result property="SHENGFENDM" column="SHENGFENDM" jdbcType="VARCHAR"/>
            <result property="JIGUANDM" column="JIGUANDM" jdbcType="VARCHAR"/>
            <result property="CHUSHENGDDM" column="CHUSHENGDDM" jdbcType="VARCHAR"/>
            <result property="JIHUASYFWZH" column="JIHUASYFWZH" jdbcType="VARCHAR"/>
            <result property="SHENGYULB" column="SHENGYULB" jdbcType="VARCHAR"/>
            <result property="WANYUBZ" column="WANYUBZ" jdbcType="DECIMAL"/>
            <result property="ZAOCHANBZ" column="ZAOCHANBZ" jdbcType="DECIMAL"/>
            <result property="JIHUASYSSLB" column="JIHUASYSSLB" jdbcType="VARCHAR"/>
            <result property="XUELI" column="XUELI" jdbcType="VARCHAR"/>
            <result property="ZAIRUYHZBZ" column="ZAIRUYHZBZ" jdbcType="DECIMAL"/>
            <result property="XUETANGSQZ" column="XUETANGSQZ" jdbcType="VARCHAR"/>
            <result property="XUETANGSQZT" column="XUETANGSQZT" jdbcType="VARCHAR"/>
            <result property="SHAICHADQ" column="SHAICHADQ" jdbcType="VARCHAR"/>
            <result property="XIANXUEZBZ" column="XIANXUEZBZ" jdbcType="DECIMAL"/>
            <result property="JCPTEMPI" column="JCPTEMPI" jdbcType="VARCHAR"/>
            <result property="IPT_PSN_SP_FLAG_DETL_ID" column="IPT_PSN_SP_FLAG_DETL_ID" jdbcType="VARCHAR"/>
            <result property="LINCHUANGLJID" column="LINCHUANGLJID" jdbcType="VARCHAR"/>
            <result property="SHIFOUYXZZJS" column="SHIFOUYXZZJS" jdbcType="VARCHAR"/>
            <result property="PEIKEKH" column="PEIKEKH" jdbcType="VARCHAR"/>
            <result property="KANGFUSQZT" column="KANGFUSQZT" jdbcType="VARCHAR"/>
            <result property="KANGFUZLS" column="KANGFUZLS" jdbcType="VARCHAR"/>
            <result property="KANGFUZLSXM" column="KANGFUZLSXM" jdbcType="VARCHAR"/>
            <result property="CHANGHUXBRBZ" column="CHANGHUXBRBZ" jdbcType="DECIMAL"/>
            <result property="YOUFUBRBZ" column="YOUFUBRBZ" jdbcType="DECIMAL"/>
            <result property="LIANXIREN2" column="LIANXIREN2" jdbcType="VARCHAR"/>
            <result property="LIANXIRDH2" column="LIANXIRDH2" jdbcType="VARCHAR"/>
            <result property="GUANXI2" column="GUANXI2" jdbcType="VARCHAR"/>
            <result property="YUANQIANBQID" column="YUANQIANBQID" jdbcType="VARCHAR"/>
            <result property="ZHUGUANYS" column="ZHUGUANYS" jdbcType="VARCHAR"/>
            <result property="LIUQIANGZKBZ" column="LIUQIANGZKBZ" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        BINGRENZYID,SHEBAOBH,YIBAOKH,
        JIUZHENKH,SHEQUBH,FEIYONGLB,
        FEIYONGXZ,YOUHUILB,GONGFEIZH,
        GONGFEIDW,GONGFEIDWMC,XINGMING,
        SHURUMA1,SHURUMA2,SHURUMA3,
        XINGBIE,NIANLINGDW,SHENFENZH,
        CHUSHENGRQ,GONGZUODW,DANWEIDH,
        DANWEIYB,JIATINGDZ,JIATINGDH,
        JIATINGYB,XUEXING,HUNYIN,
        ZHIYE,GUOJI,MINZU,
        SHENGFEN,XIANGZHENJD,SHIDIQU,
        JIGUAN,CHUSHENGDI,YOUBIAN,
        LIANXIREN,GUANXI,LIANXIRDZ,
        LIANXIRDH,LIANXIRYB,JIWANGSHI,
        GUOMINSHI,QUYU,WAIDIBRBZ,
        JIANDANGREN,JIANDANGRQ,XIUGAIREN,
        XIUGAISJ,YINGERBZ,ZHUYUANCS,
        RUYUANRQ,YUCHUYRQ,CHUYUANRQ,
        ZAIYUANZT,RUYUANTJ,RUYUANKS,
        RUYUANBQ,RUYUANCW,DANGQIANKS,
        DANGQIANBQ,DANGQIANCW,GUANLIKS,
        JIECHUANGBZ,LIYUANQX,MENZHENZDDM,
        MENZHENZDMC,RUYUANZDDM,RUYUANZDMC,
        CHUYUANZDDM,CHUYUANZDMC,CHUYUANZDDM2,
        CHUYUANZDMC2,CHUYUANZDMC3,CHUYUANZDDM3,
        JIATINGBCBZ,BINGQING,FENMIAN,
        SHANGCHUANBZ,SHANGCHUANRQ,DANBAOREN,
        DANBAOJE,JIAZHANGXM,ZHUYUANHAO,
        BINGANHAO,BINGRENID,JIESUANXH,
        NIANLING,MENZHENYS,SHOUZHIYS,
        ZHUYUANYS,ZHUZHIYS,YUYUEID,
        SHENHEBZ,QIGUANYZBZ,JIAOYILSH,
        GERENBH,IC,SHIMINKWKH,
        YILIAOZID,YILIAOZM,RUKERQ,
        RUKEBZ,ZHANCHUANGBZ,RUYUANKSMC,
        RUYUANBQMC,DANGQIANKSMC,DANGQIANBQMC,
        GUANLIKSMC,MENZHENYSXM,SHOUZHIYSXM,
        ZHUYUANYSXM,ZHUZHIYSXM,BINGLISLZT,
        FEIYONGXZZHBZ,FEIYONGXZZHR,FEIYONGXZZHRQ,
        YIBAOYLLB,TESHUBZBZ,TESHUBZMC,
        TESHUBZBM,MENZHENZYZDDM,MENZHENZYZDMC,
        RUYUANZYZDDM,RUYUANZYZDMC,WAIYUANZDBZ,
        RUYUANZYZHDM,RUYUANZYZHMC,YAOPINBZYBZ,
        ZHUANBINGQBZ,MUQINZYID,CHANFUBZ,
        LINCHUANGLJBZ,QUXIAORYBZ,QINGJIABZ,
        YUANBINGRZYID,LVSETDBZ,LVSETDKQRQ,
        YIBAOMXFJYJLS,TESHUBZ,YILIAOKH,
        JIESHAOREN,YINGERSL,YOUHUILBLB,
        QUXIAOYCYRQ,RUYUANZDDM2,RUYUANZDMC2,
        RUYUANZDDM3,RUYUANZDMC3,YIBAOBRXX,
        FEIYONGLB2,FEIYONGXZ2,YILIAOJZDJBZ,
        ZHIFU1YB,ZHIFU2YB,ZHIFU3YB,
        ZHIFU4YB,ZHIFU5YB,ZHIFU6YB,
        ZHIFU7YB,ZHIFU8YB,ZHIFU9YB,
        ZHIFU10YB,ZHIFU11YB,ZHIFU12YB,
        SHUZHI1YB,SHUZHI2YB,SHUZHI3YB,
        SHUZHI4YB,SHUZHI5YB,SHUZHI6YB,
        SHUZHI7YB,SHUZHI8YB,SHUZHI9YB,
        SHUZHI10YB,YIBAOCYDJBZ,YIZHONGYLBZ,
        YIZHONGYLLB,YIZHONGYLXYJE,YIZHONGYLBZSM,
        FENMIANRQ,BINGFAZHENG,BUZHUKBH,
        YIBAOKH2,IC2,QUXIAOYCYYY,
        BEIZHU,ZERENHS,ZERENHSXM,
        JIUFENBRBZ,ZHENGJIANLX,JIANHURENSFZH,
        GELILX,LASTYUCHUYRQ,LASTCHUYUANRQ,
        YURUYUANBZ,RUYUANDJLX,XIANQU,
        NIANLING1,NIANLINGDW1,GONGSHANGKFDJH,
        GONGSHANGKFRQ,LINCHUANGLJDBZBZ,SHENGAO,
        TIZHONG,ADLSCORE,SHENHEREN,
        SHENHERQ,GUAZHANGBZ,ZHENGJIANSMWJ,
        BAOMIJB,HUKOUDZ,HUKOUSF,
        HUKOUSDQ,HUKOUXQ,HUKOUXZJD,
        JIATINGDZLB,HUKOUDZLB,JIANKANGKH,
        ZHAOSHIZHBZ,JIANKANGKXX,RIJIANZYBZ,
        CHUANGWEIZYBZ,HUSHIZHANG,HUSHIZXM,
        JIZHENYXBZ,YUCHUYXTSJ,TIBIAOMJ,
        YIWAISHBZ,CHUSHENGDSF,CHUSHENGDSDQ,
        CHUSHENGDXQ,JIGUANSF,JIGUANSDQ,
        XIANZHUZSF,XIANZHUZSDQ,XIANZHUZXQ,
        XIANZHUZQT,HUKOUDZQT,XIAOXILY,
        WANGLUOFWBZ,DABINGBZ,SHIFOUCSSFZ,
        YUNCI,CHANCI,TAICI,
        YUCHANQI,QUYUBH,MOCIYJ,
        MIANFEIQLX,HUKOUYB,SHIBAZDBDM,
        MOCIYUEJINGSJ,JSMOCIYJSJ,YUNZHOU,
        YUNTIAN,TAICHANCI,BUSUIFANGBZ,
        YUANQUID,SHIFOUSQQFX,SHANSHIFYBZ,
        SFJSFLZBZ,BURUJINGLY,YILIANTBZ,
        YILIANTBH,YILIANTSQDID,ZHURENYS,
        ZHURENYSXM,SHENGYUBZ,BIAOZHI120,
        YINANBRBZ,RUYUANYY,ZAICIRYLX,
        ZHUANZHENDH,YUANRUKERQ,YINGERCWFJZZT,
        YINGERCWFJZTZRQ,YINGERCWFJZTZR,KUAISURYBZ,
        SHENFENZBXYY,YDRYBZ,TCQBZ,
        SENLINFHBRBZ,SENLINFHSPRQ,SENLINFHBZ,
        MPI,SHOUCIRKR,SHOUCIRKRXM,
        JIUZHENQRRQ,JIUZHENQRBZ,QUERENSFRQ_HIS,
        QUERENSFBZ_HIS,DENGJISY,YUNYIXYED,
        YUNYIQYBZ,ZHUANDRYELB,LIUGUANBZ,
        LIUGUANSQDID,JIUZHENID,FENZHENSJ,
        LAIYUAN,FENZHENYUAN,FENZHENYUANID,
        FENZHENLB,SHIFUTZRQ,DIANHUA,
        XINGUANBZ,SANDAZXBZ,QITABZNR,
        XINBINGRENZYID,LIUGUANBRZYID,XIANZHUZHI,
        BINGANSBBZ,SHUANGXIANGJZBZ,ZHONGZHUANKS,
        ZHONGZHUANKEMC,DIEDAOPGBZ,DISANFBZJ,
        BINGRENQX,FENZHENJB,YOUXIAOZDMC,
        MINZHENGXX,BINGQINGZY,YANKECS,
        QITAYLXTBRBZ,GCPLX,BUSHIYDBZYY,
        SHIFOUGZ,EMPI_YJ,SANJIYS,
        SANJIYSMC,KEZHURYS,KEZHURYSXM,
        ERJIYS,ERJIYSMC,YIJIYS,
        YIJIYSMC,SHANGCICKSJ,ZONGHEBFBZ,
        ZONGHEBQ,ZONGHEBQCW,ZONGHEBFJRSJ,
        ZONGHEBQMC,QIANTIANJH,ZHOUQI,
        JINGQI,YXJM,XINXITBZ,
        CHUANGTOUKDYBZ,FLSBZ,XINGBIEDM,
        HUNYINDM,ZHIYEDM,GUOJIDM,
        MINZUDM,SHENGFENDM,JIGUANDM,
        CHUSHENGDDM,JIHUASYFWZH,SHENGYULB,
        WANYUBZ,ZAOCHANBZ,JIHUASYSSLB,
        XUELI,ZAIRUYHZBZ,XUETANGSQZ,
        XUETANGSQZT,SHAICHADQ,XIANXUEZBZ,
        JCPTEMPI,IPT_PSN_SP_FLAG_DETL_ID,LINCHUANGLJID,
        SHIFOUYXZZJS,PEIKEKH,KANGFUSQZT,
        KANGFUZLS,KANGFUZLSXM,CHANGHUXBRBZ,
        YOUFUBRBZ,LIANXIREN2,LIANXIRDH2,
        GUANXI2,YUANQIANBQID,ZHUGUANYS,
        LIUQIANGZKBZ
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ZY_BINGRENXX
        where  BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ZY_BINGRENXX
        where  BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="BINGRENZYID" keyProperty="BINGRENZYID" parameterType="com.javazx.batch.po.ZyBingrenxx" useGeneratedKeys="true">
        insert into ZY_BINGRENXX
        ( BINGRENZYID,SHEBAOBH,YIBAOKH
        ,JIUZHENKH,SHEQUBH,FEIYONGLB
        ,FEIYONGXZ,YOUHUILB,GONGFEIZH
        ,GONGFEIDW,GONGFEIDWMC,XINGMING
        ,SHURUMA1,SHURUMA2,SHURUMA3
        ,XINGBIE,NIANLINGDW,SHENFENZH
        ,CHUSHENGRQ,GONGZUODW,DANWEIDH
        ,DANWEIYB,JIATINGDZ,JIATINGDH
        ,JIATINGYB,XUEXING,HUNYIN
        ,ZHIYE,GUOJI,MINZU
        ,SHENGFEN,XIANGZHENJD,SHIDIQU
        ,JIGUAN,CHUSHENGDI,YOUBIAN
        ,LIANXIREN,GUANXI,LIANXIRDZ
        ,LIANXIRDH,LIANXIRYB,JIWANGSHI
        ,GUOMINSHI,QUYU,WAIDIBRBZ
        ,JIANDANGREN,JIANDANGRQ,XIUGAIREN
        ,XIUGAISJ,YINGERBZ,ZHUYUANCS
        ,RUYUANRQ,YUCHUYRQ,CHUYUANRQ
        ,ZAIYUANZT,RUYUANTJ,RUYUANKS
        ,RUYUANBQ,RUYUANCW,DANGQIANKS
        ,DANGQIANBQ,DANGQIANCW,GUANLIKS
        ,JIECHUANGBZ,LIYUANQX,MENZHENZDDM
        ,MENZHENZDMC,RUYUANZDDM,RUYUANZDMC
        ,CHUYUANZDDM,CHUYUANZDMC,CHUYUANZDDM2
        ,CHUYUANZDMC2,CHUYUANZDMC3,CHUYUANZDDM3
        ,JIATINGBCBZ,BINGQING,FENMIAN
        ,SHANGCHUANBZ,SHANGCHUANRQ,DANBAOREN
        ,DANBAOJE,JIAZHANGXM,ZHUYUANHAO
        ,BINGANHAO,BINGRENID,JIESUANXH
        ,NIANLING,MENZHENYS,SHOUZHIYS
        ,ZHUYUANYS,ZHUZHIYS,YUYUEID
        ,SHENHEBZ,QIGUANYZBZ,JIAOYILSH
        ,GERENBH,IC,SHIMINKWKH
        ,YILIAOZID,YILIAOZM,RUKERQ
        ,RUKEBZ,ZHANCHUANGBZ,RUYUANKSMC
        ,RUYUANBQMC,DANGQIANKSMC,DANGQIANBQMC
        ,GUANLIKSMC,MENZHENYSXM,SHOUZHIYSXM
        ,ZHUYUANYSXM,ZHUZHIYSXM,BINGLISLZT
        ,FEIYONGXZZHBZ,FEIYONGXZZHR,FEIYONGXZZHRQ
        ,YIBAOYLLB,TESHUBZBZ,TESHUBZMC
        ,TESHUBZBM,MENZHENZYZDDM,MENZHENZYZDMC
        ,RUYUANZYZDDM,RUYUANZYZDMC,WAIYUANZDBZ
        ,RUYUANZYZHDM,RUYUANZYZHMC,YAOPINBZYBZ
        ,ZHUANBINGQBZ,MUQINZYID,CHANFUBZ
        ,LINCHUANGLJBZ,QUXIAORYBZ,QINGJIABZ
        ,YUANBINGRZYID,LVSETDBZ,LVSETDKQRQ
        ,YIBAOMXFJYJLS,TESHUBZ,YILIAOKH
        ,JIESHAOREN,YINGERSL,YOUHUILBLB
        ,QUXIAOYCYRQ,RUYUANZDDM2,RUYUANZDMC2
        ,RUYUANZDDM3,RUYUANZDMC3,YIBAOBRXX
        ,FEIYONGLB2,FEIYONGXZ2,YILIAOJZDJBZ
        ,ZHIFU1YB,ZHIFU2YB,ZHIFU3YB
        ,ZHIFU4YB,ZHIFU5YB,ZHIFU6YB
        ,ZHIFU7YB,ZHIFU8YB,ZHIFU9YB
        ,ZHIFU10YB,ZHIFU11YB,ZHIFU12YB
        ,SHUZHI1YB,SHUZHI2YB,SHUZHI3YB
        ,SHUZHI4YB,SHUZHI5YB,SHUZHI6YB
        ,SHUZHI7YB,SHUZHI8YB,SHUZHI9YB
        ,SHUZHI10YB,YIBAOCYDJBZ,YIZHONGYLBZ
        ,YIZHONGYLLB,YIZHONGYLXYJE,YIZHONGYLBZSM
        ,FENMIANRQ,BINGFAZHENG,BUZHUKBH
        ,YIBAOKH2,IC2,QUXIAOYCYYY
        ,BEIZHU,ZERENHS,ZERENHSXM
        ,JIUFENBRBZ,ZHENGJIANLX,JIANHURENSFZH
        ,GELILX,LASTYUCHUYRQ,LASTCHUYUANRQ
        ,YURUYUANBZ,RUYUANDJLX,XIANQU
        ,NIANLING1,NIANLINGDW1,GONGSHANGKFDJH
        ,GONGSHANGKFRQ,LINCHUANGLJDBZBZ,SHENGAO
        ,TIZHONG,ADLSCORE,SHENHEREN
        ,SHENHERQ,GUAZHANGBZ,ZHENGJIANSMWJ
        ,BAOMIJB,HUKOUDZ,HUKOUSF
        ,HUKOUSDQ,HUKOUXQ,HUKOUXZJD
        ,JIATINGDZLB,HUKOUDZLB,JIANKANGKH
        ,ZHAOSHIZHBZ,JIANKANGKXX,RIJIANZYBZ
        ,CHUANGWEIZYBZ,HUSHIZHANG,HUSHIZXM
        ,JIZHENYXBZ,YUCHUYXTSJ,TIBIAOMJ
        ,YIWAISHBZ,CHUSHENGDSF,CHUSHENGDSDQ
        ,CHUSHENGDXQ,JIGUANSF,JIGUANSDQ
        ,XIANZHUZSF,XIANZHUZSDQ,XIANZHUZXQ
        ,XIANZHUZQT,HUKOUDZQT,XIAOXILY
        ,WANGLUOFWBZ,DABINGBZ,SHIFOUCSSFZ
        ,YUNCI,CHANCI,TAICI
        ,YUCHANQI,QUYUBH,MOCIYJ
        ,MIANFEIQLX,HUKOUYB,SHIBAZDBDM
        ,MOCIYUEJINGSJ,JSMOCIYJSJ,YUNZHOU
        ,YUNTIAN,TAICHANCI,BUSUIFANGBZ
        ,YUANQUID,SHIFOUSQQFX,SHANSHIFYBZ
        ,SFJSFLZBZ,BURUJINGLY,YILIANTBZ
        ,YILIANTBH,YILIANTSQDID,ZHURENYS
        ,ZHURENYSXM,SHENGYUBZ,BIAOZHI120
        ,YINANBRBZ,RUYUANYY,ZAICIRYLX
        ,ZHUANZHENDH,YUANRUKERQ,YINGERCWFJZZT
        ,YINGERCWFJZTZRQ,YINGERCWFJZTZR,KUAISURYBZ
        ,SHENFENZBXYY,YDRYBZ,TCQBZ
        ,SENLINFHBRBZ,SENLINFHSPRQ,SENLINFHBZ
        ,MPI,SHOUCIRKR,SHOUCIRKRXM
        ,JIUZHENQRRQ,JIUZHENQRBZ,QUERENSFRQ_HIS
        ,QUERENSFBZ_HIS,DENGJISY,YUNYIXYED
        ,YUNYIQYBZ,ZHUANDRYELB,LIUGUANBZ
        ,LIUGUANSQDID,JIUZHENID,FENZHENSJ
        ,LAIYUAN,FENZHENYUAN,FENZHENYUANID
        ,FENZHENLB,SHIFUTZRQ,DIANHUA
        ,XINGUANBZ,SANDAZXBZ,QITABZNR
        ,XINBINGRENZYID,LIUGUANBRZYID,XIANZHUZHI
        ,BINGANSBBZ,SHUANGXIANGJZBZ,ZHONGZHUANKS
        ,ZHONGZHUANKEMC,DIEDAOPGBZ,DISANFBZJ
        ,BINGRENQX,FENZHENJB,YOUXIAOZDMC
        ,MINZHENGXX,BINGQINGZY,YANKECS
        ,QITAYLXTBRBZ,GCPLX,BUSHIYDBZYY
        ,SHIFOUGZ,EMPI_YJ,SANJIYS
        ,SANJIYSMC,KEZHURYS,KEZHURYSXM
        ,ERJIYS,ERJIYSMC,YIJIYS
        ,YIJIYSMC,SHANGCICKSJ,ZONGHEBFBZ
        ,ZONGHEBQ,ZONGHEBQCW,ZONGHEBFJRSJ
        ,ZONGHEBQMC,QIANTIANJH,ZHOUQI
        ,JINGQI,YXJM,XINXITBZ
        ,CHUANGTOUKDYBZ,FLSBZ,XINGBIEDM
        ,HUNYINDM,ZHIYEDM,GUOJIDM
        ,MINZUDM,SHENGFENDM,JIGUANDM
        ,CHUSHENGDDM,JIHUASYFWZH,SHENGYULB
        ,WANYUBZ,ZAOCHANBZ,JIHUASYSSLB
        ,XUELI,ZAIRUYHZBZ,XUETANGSQZ
        ,XUETANGSQZT,SHAICHADQ,XIANXUEZBZ
        ,JCPTEMPI,IPT_PSN_SP_FLAG_DETL_ID,LINCHUANGLJID
        ,SHIFOUYXZZJS,PEIKEKH,KANGFUSQZT
        ,KANGFUZLS,KANGFUZLSXM,CHANGHUXBRBZ
        ,YOUFUBRBZ,LIANXIREN2,LIANXIRDH2
        ,GUANXI2,YUANQIANBQID,ZHUGUANYS
        ,LIUQIANGZKBZ)
        values (#{BINGRENZYID,jdbcType=VARCHAR},#{SHEBAOBH,jdbcType=VARCHAR},#{YIBAOKH,jdbcType=VARCHAR}
        ,#{JIUZHENKH,jdbcType=VARCHAR},#{SHEQUBH,jdbcType=VARCHAR},#{FEIYONGLB,jdbcType=VARCHAR}
        ,#{FEIYONGXZ,jdbcType=VARCHAR},#{YOUHUILB,jdbcType=VARCHAR},#{GONGFEIZH,jdbcType=VARCHAR}
        ,#{GONGFEIDW,jdbcType=VARCHAR},#{GONGFEIDWMC,jdbcType=VARCHAR},#{XINGMING,jdbcType=VARCHAR}
        ,#{SHURUMA1,jdbcType=VARCHAR},#{SHURUMA2,jdbcType=VARCHAR},#{SHURUMA3,jdbcType=VARCHAR}
        ,#{XINGBIE,jdbcType=VARCHAR},#{NIANLINGDW,jdbcType=VARCHAR},#{SHENFENZH,jdbcType=VARCHAR}
        ,#{CHUSHENGRQ,jdbcType=TIMESTAMP},#{GONGZUODW,jdbcType=VARCHAR},#{DANWEIDH,jdbcType=VARCHAR}
        ,#{DANWEIYB,jdbcType=VARCHAR},#{JIATINGDZ,jdbcType=VARCHAR},#{JIATINGDH,jdbcType=VARCHAR}
        ,#{JIATINGYB,jdbcType=VARCHAR},#{XUEXING,jdbcType=VARCHAR},#{HUNYIN,jdbcType=VARCHAR}
        ,#{ZHIYE,jdbcType=VARCHAR},#{GUOJI,jdbcType=VARCHAR},#{MINZU,jdbcType=VARCHAR}
        ,#{SHENGFEN,jdbcType=VARCHAR},#{XIANGZHENJD,jdbcType=VARCHAR},#{SHIDIQU,jdbcType=VARCHAR}
        ,#{JIGUAN,jdbcType=VARCHAR},#{CHUSHENGDI,jdbcType=VARCHAR},#{YOUBIAN,jdbcType=VARCHAR}
        ,#{LIANXIREN,jdbcType=VARCHAR},#{GUANXI,jdbcType=VARCHAR},#{LIANXIRDZ,jdbcType=VARCHAR}
        ,#{LIANXIRDH,jdbcType=VARCHAR},#{LIANXIRYB,jdbcType=VARCHAR},#{JIWANGSHI,jdbcType=VARCHAR}
        ,#{GUOMINSHI,jdbcType=VARCHAR},#{QUYU,jdbcType=VARCHAR},#{WAIDIBRBZ,jdbcType=DECIMAL}
        ,#{JIANDANGREN,jdbcType=VARCHAR},#{JIANDANGRQ,jdbcType=TIMESTAMP},#{XIUGAIREN,jdbcType=VARCHAR}
        ,#{XIUGAISJ,jdbcType=TIMESTAMP},#{YINGERBZ,jdbcType=DECIMAL},#{ZHUYUANCS,jdbcType=DECIMAL}
        ,#{RUYUANRQ,jdbcType=TIMESTAMP},#{YUCHUYRQ,jdbcType=TIMESTAMP},#{CHUYUANRQ,jdbcType=TIMESTAMP}
        ,#{ZAIYUANZT,jdbcType=VARCHAR},#{RUYUANTJ,jdbcType=VARCHAR},#{RUYUANKS,jdbcType=VARCHAR}
        ,#{RUYUANBQ,jdbcType=VARCHAR},#{RUYUANCW,jdbcType=VARCHAR},#{DANGQIANKS,jdbcType=VARCHAR}
        ,#{DANGQIANBQ,jdbcType=VARCHAR},#{DANGQIANCW,jdbcType=VARCHAR},#{GUANLIKS,jdbcType=VARCHAR}
        ,#{JIECHUANGBZ,jdbcType=DECIMAL},#{LIYUANQX,jdbcType=VARCHAR},#{MENZHENZDDM,jdbcType=VARCHAR}
        ,#{MENZHENZDMC,jdbcType=VARCHAR},#{RUYUANZDDM,jdbcType=VARCHAR},#{RUYUANZDMC,jdbcType=VARCHAR}
        ,#{CHUYUANZDDM,jdbcType=VARCHAR},#{CHUYUANZDMC,jdbcType=VARCHAR},#{CHUYUANZDDM2,jdbcType=VARCHAR}
        ,#{CHUYUANZDMC2,jdbcType=VARCHAR},#{CHUYUANZDMC3,jdbcType=VARCHAR},#{CHUYUANZDDM3,jdbcType=VARCHAR}
        ,#{JIATINGBCBZ,jdbcType=DECIMAL},#{BINGQING,jdbcType=VARCHAR},#{FENMIAN,jdbcType=VARCHAR}
        ,#{SHANGCHUANBZ,jdbcType=DECIMAL},#{SHANGCHUANRQ,jdbcType=TIMESTAMP},#{DANBAOREN,jdbcType=VARCHAR}
        ,#{DANBAOJE,jdbcType=DECIMAL},#{JIAZHANGXM,jdbcType=VARCHAR},#{ZHUYUANHAO,jdbcType=VARCHAR}
        ,#{BINGANHAO,jdbcType=VARCHAR},#{BINGRENID,jdbcType=VARCHAR},#{JIESUANXH,jdbcType=DECIMAL}
        ,#{NIANLING,jdbcType=DECIMAL},#{MENZHENYS,jdbcType=VARCHAR},#{SHOUZHIYS,jdbcType=VARCHAR}
        ,#{ZHUYUANYS,jdbcType=VARCHAR},#{ZHUZHIYS,jdbcType=VARCHAR},#{YUYUEID,jdbcType=VARCHAR}
        ,#{SHENHEBZ,jdbcType=DECIMAL},#{QIGUANYZBZ,jdbcType=DECIMAL},#{JIAOYILSH,jdbcType=VARCHAR}
        ,#{GERENBH,jdbcType=VARCHAR},#{IC,jdbcType=VARCHAR},#{SHIMINKWKH,jdbcType=VARCHAR}
        ,#{YILIAOZID,jdbcType=VARCHAR},#{YILIAOZM,jdbcType=VARCHAR},#{RUKERQ,jdbcType=TIMESTAMP}
        ,#{RUKEBZ,jdbcType=DECIMAL},#{ZHANCHUANGBZ,jdbcType=DECIMAL},#{RUYUANKSMC,jdbcType=VARCHAR}
        ,#{RUYUANBQMC,jdbcType=VARCHAR},#{DANGQIANKSMC,jdbcType=VARCHAR},#{DANGQIANBQMC,jdbcType=VARCHAR}
        ,#{GUANLIKSMC,jdbcType=VARCHAR},#{MENZHENYSXM,jdbcType=VARCHAR},#{SHOUZHIYSXM,jdbcType=VARCHAR}
        ,#{ZHUYUANYSXM,jdbcType=VARCHAR},#{ZHUZHIYSXM,jdbcType=VARCHAR},#{BINGLISLZT,jdbcType=VARCHAR}
        ,#{FEIYONGXZZHBZ,jdbcType=DECIMAL},#{FEIYONGXZZHR,jdbcType=VARCHAR},#{FEIYONGXZZHRQ,jdbcType=TIMESTAMP}
        ,#{YIBAOYLLB,jdbcType=VARCHAR},#{TESHUBZBZ,jdbcType=DECIMAL},#{TESHUBZMC,jdbcType=VARCHAR}
        ,#{TESHUBZBM,jdbcType=VARCHAR},#{MENZHENZYZDDM,jdbcType=VARCHAR},#{MENZHENZYZDMC,jdbcType=VARCHAR}
        ,#{RUYUANZYZDDM,jdbcType=VARCHAR},#{RUYUANZYZDMC,jdbcType=VARCHAR},#{WAIYUANZDBZ,jdbcType=DECIMAL}
        ,#{RUYUANZYZHDM,jdbcType=VARCHAR},#{RUYUANZYZHMC,jdbcType=VARCHAR},#{YAOPINBZYBZ,jdbcType=DECIMAL}
        ,#{ZHUANBINGQBZ,jdbcType=DECIMAL},#{MUQINZYID,jdbcType=VARCHAR},#{CHANFUBZ,jdbcType=DECIMAL}
        ,#{LINCHUANGLJBZ,jdbcType=DECIMAL},#{QUXIAORYBZ,jdbcType=DECIMAL},#{QINGJIABZ,jdbcType=DECIMAL}
        ,#{YUANBINGRZYID,jdbcType=VARCHAR},#{LVSETDBZ,jdbcType=DECIMAL},#{LVSETDKQRQ,jdbcType=TIMESTAMP}
        ,#{YIBAOMXFJYJLS,jdbcType=DECIMAL},#{TESHUBZ,jdbcType=DECIMAL},#{YILIAOKH,jdbcType=VARCHAR}
        ,#{JIESHAOREN,jdbcType=VARCHAR},#{YINGERSL,jdbcType=DECIMAL},#{YOUHUILBLB,jdbcType=VARCHAR}
        ,#{QUXIAOYCYRQ,jdbcType=TIMESTAMP},#{RUYUANZDDM2,jdbcType=VARCHAR},#{RUYUANZDMC2,jdbcType=VARCHAR}
        ,#{RUYUANZDDM3,jdbcType=VARCHAR},#{RUYUANZDMC3,jdbcType=VARCHAR},#{YIBAOBRXX,jdbcType=VARCHAR}
        ,#{FEIYONGLB2,jdbcType=VARCHAR},#{FEIYONGXZ2,jdbcType=VARCHAR},#{YILIAOJZDJBZ,jdbcType=DECIMAL}
        ,#{ZHIFU1YB,jdbcType=VARCHAR},#{ZHIFU2YB,jdbcType=VARCHAR},#{ZHIFU3YB,jdbcType=VARCHAR}
        ,#{ZHIFU4YB,jdbcType=VARCHAR},#{ZHIFU5YB,jdbcType=VARCHAR},#{ZHIFU6YB,jdbcType=VARCHAR}
        ,#{ZHIFU7YB,jdbcType=VARCHAR},#{ZHIFU8YB,jdbcType=VARCHAR},#{ZHIFU9YB,jdbcType=VARCHAR}
        ,#{ZHIFU10YB,jdbcType=VARCHAR},#{ZHIFU11YB,jdbcType=VARCHAR},#{ZHIFU12YB,jdbcType=VARCHAR}
        ,#{SHUZHI1YB,jdbcType=DECIMAL},#{SHUZHI2YB,jdbcType=DECIMAL},#{SHUZHI3YB,jdbcType=DECIMAL}
        ,#{SHUZHI4YB,jdbcType=DECIMAL},#{SHUZHI5YB,jdbcType=DECIMAL},#{SHUZHI6YB,jdbcType=DECIMAL}
        ,#{SHUZHI7YB,jdbcType=DECIMAL},#{SHUZHI8YB,jdbcType=DECIMAL},#{SHUZHI9YB,jdbcType=DECIMAL}
        ,#{SHUZHI10YB,jdbcType=DECIMAL},#{YIBAOCYDJBZ,jdbcType=DECIMAL},#{YIZHONGYLBZ,jdbcType=DECIMAL}
        ,#{YIZHONGYLLB,jdbcType=VARCHAR},#{YIZHONGYLXYJE,jdbcType=DECIMAL},#{YIZHONGYLBZSM,jdbcType=VARCHAR}
        ,#{FENMIANRQ,jdbcType=TIMESTAMP},#{BINGFAZHENG,jdbcType=VARCHAR},#{BUZHUKBH,jdbcType=VARCHAR}
        ,#{YIBAOKH2,jdbcType=VARCHAR},#{IC2,jdbcType=VARCHAR},#{QUXIAOYCYYY,jdbcType=VARCHAR}
        ,#{BEIZHU,jdbcType=VARCHAR},#{ZERENHS,jdbcType=VARCHAR},#{ZERENHSXM,jdbcType=VARCHAR}
        ,#{JIUFENBRBZ,jdbcType=DECIMAL},#{ZHENGJIANLX,jdbcType=VARCHAR},#{JIANHURENSFZH,jdbcType=VARCHAR}
        ,#{GELILX,jdbcType=VARCHAR},#{LASTYUCHUYRQ,jdbcType=TIMESTAMP},#{LASTCHUYUANRQ,jdbcType=TIMESTAMP}
        ,#{YURUYUANBZ,jdbcType=DECIMAL},#{RUYUANDJLX,jdbcType=VARCHAR},#{XIANQU,jdbcType=VARCHAR}
        ,#{NIANLING1,jdbcType=DECIMAL},#{NIANLINGDW1,jdbcType=VARCHAR},#{GONGSHANGKFDJH,jdbcType=VARCHAR}
        ,#{GONGSHANGKFRQ,jdbcType=TIMESTAMP},#{LINCHUANGLJDBZBZ,jdbcType=DECIMAL},#{SHENGAO,jdbcType=DECIMAL}
        ,#{TIZHONG,jdbcType=DECIMAL},#{ADLSCORE,jdbcType=VARCHAR},#{SHENHEREN,jdbcType=VARCHAR}
        ,#{SHENHERQ,jdbcType=TIMESTAMP},#{GUAZHANGBZ,jdbcType=DECIMAL},#{ZHENGJIANSMWJ,jdbcType=VARCHAR}
        ,#{BAOMIJB,jdbcType=VARCHAR},#{HUKOUDZ,jdbcType=VARCHAR},#{HUKOUSF,jdbcType=VARCHAR}
        ,#{HUKOUSDQ,jdbcType=VARCHAR},#{HUKOUXQ,jdbcType=VARCHAR},#{HUKOUXZJD,jdbcType=VARCHAR}
        ,#{JIATINGDZLB,jdbcType=VARCHAR},#{HUKOUDZLB,jdbcType=VARCHAR},#{JIANKANGKH,jdbcType=VARCHAR}
        ,#{ZHAOSHIZHBZ,jdbcType=DECIMAL},#{JIANKANGKXX,jdbcType=VARCHAR},#{RIJIANZYBZ,jdbcType=DECIMAL}
        ,#{CHUANGWEIZYBZ,jdbcType=DECIMAL},#{HUSHIZHANG,jdbcType=VARCHAR},#{HUSHIZXM,jdbcType=VARCHAR}
        ,#{JIZHENYXBZ,jdbcType=DECIMAL},#{YUCHUYXTSJ,jdbcType=TIMESTAMP},#{TIBIAOMJ,jdbcType=DECIMAL}
        ,#{YIWAISHBZ,jdbcType=VARCHAR},#{CHUSHENGDSF,jdbcType=VARCHAR},#{CHUSHENGDSDQ,jdbcType=VARCHAR}
        ,#{CHUSHENGDXQ,jdbcType=VARCHAR},#{JIGUANSF,jdbcType=VARCHAR},#{JIGUANSDQ,jdbcType=VARCHAR}
        ,#{XIANZHUZSF,jdbcType=VARCHAR},#{XIANZHUZSDQ,jdbcType=VARCHAR},#{XIANZHUZXQ,jdbcType=VARCHAR}
        ,#{XIANZHUZQT,jdbcType=VARCHAR},#{HUKOUDZQT,jdbcType=VARCHAR},#{XIAOXILY,jdbcType=VARCHAR}
        ,#{WANGLUOFWBZ,jdbcType=DECIMAL},#{DABINGBZ,jdbcType=DECIMAL},#{SHIFOUCSSFZ,jdbcType=DECIMAL}
        ,#{YUNCI,jdbcType=VARCHAR},#{CHANCI,jdbcType=VARCHAR},#{TAICI,jdbcType=VARCHAR}
        ,#{YUCHANQI,jdbcType=VARCHAR},#{QUYUBH,jdbcType=VARCHAR},#{MOCIYJ,jdbcType=VARCHAR}
        ,#{MIANFEIQLX,jdbcType=VARCHAR},#{HUKOUYB,jdbcType=VARCHAR},#{SHIBAZDBDM,jdbcType=VARCHAR}
        ,#{MOCIYUEJINGSJ,jdbcType=TIMESTAMP},#{JSMOCIYJSJ,jdbcType=TIMESTAMP},#{YUNZHOU,jdbcType=DECIMAL}
        ,#{YUNTIAN,jdbcType=DECIMAL},#{TAICHANCI,jdbcType=VARCHAR},#{BUSUIFANGBZ,jdbcType=DECIMAL}
        ,#{YUANQUID,jdbcType=VARCHAR},#{SHIFOUSQQFX,jdbcType=VARCHAR},#{SHANSHIFYBZ,jdbcType=DECIMAL}
        ,#{SFJSFLZBZ,jdbcType=DECIMAL},#{BURUJINGLY,jdbcType=VARCHAR},#{YILIANTBZ,jdbcType=VARCHAR}
        ,#{YILIANTBH,jdbcType=VARCHAR},#{YILIANTSQDID,jdbcType=VARCHAR},#{ZHURENYS,jdbcType=VARCHAR}
        ,#{ZHURENYSXM,jdbcType=VARCHAR},#{SHENGYUBZ,jdbcType=DECIMAL},#{BIAOZHI120,jdbcType=DECIMAL}
        ,#{YINANBRBZ,jdbcType=DECIMAL},#{RUYUANYY,jdbcType=VARCHAR},#{ZAICIRYLX,jdbcType=DECIMAL}
        ,#{ZHUANZHENDH,jdbcType=VARCHAR},#{YUANRUKERQ,jdbcType=TIMESTAMP},#{YINGERCWFJZZT,jdbcType=DECIMAL}
        ,#{YINGERCWFJZTZRQ,jdbcType=TIMESTAMP},#{YINGERCWFJZTZR,jdbcType=VARCHAR},#{KUAISURYBZ,jdbcType=DECIMAL}
        ,#{SHENFENZBXYY,jdbcType=VARCHAR},#{YDRYBZ,jdbcType=VARCHAR},#{TCQBZ,jdbcType=VARCHAR}
        ,#{SENLINFHBRBZ,jdbcType=DECIMAL},#{SENLINFHSPRQ,jdbcType=TIMESTAMP},#{SENLINFHBZ,jdbcType=VARCHAR}
        ,#{MPI,jdbcType=VARCHAR},#{SHOUCIRKR,jdbcType=VARCHAR},#{SHOUCIRKRXM,jdbcType=VARCHAR}
        ,#{JIUZHENQRRQ,jdbcType=TIMESTAMP},#{JIUZHENQRBZ,jdbcType=DECIMAL},#{QUERENSFRQ_HIS,jdbcType=TIMESTAMP}
        ,#{QUERENSFBZ_HIS,jdbcType=DECIMAL},#{DENGJISY,jdbcType=VARCHAR},#{YUNYIXYED,jdbcType=VARCHAR}
        ,#{YUNYIQYBZ,jdbcType=DECIMAL},#{ZHUANDRYELB,jdbcType=VARCHAR},#{LIUGUANBZ,jdbcType=DECIMAL}
        ,#{LIUGUANSQDID,jdbcType=VARCHAR},#{JIUZHENID,jdbcType=VARCHAR},#{FENZHENSJ,jdbcType=TIMESTAMP}
        ,#{LAIYUAN,jdbcType=VARCHAR},#{FENZHENYUAN,jdbcType=VARCHAR},#{FENZHENYUANID,jdbcType=VARCHAR}
        ,#{FENZHENLB,jdbcType=VARCHAR},#{SHIFUTZRQ,jdbcType=DECIMAL},#{DIANHUA,jdbcType=VARCHAR}
        ,#{XINGUANBZ,jdbcType=DECIMAL},#{SANDAZXBZ,jdbcType=DECIMAL},#{QITABZNR,jdbcType=VARCHAR}
        ,#{XINBINGRENZYID,jdbcType=VARCHAR},#{LIUGUANBRZYID,jdbcType=VARCHAR},#{XIANZHUZHI,jdbcType=VARCHAR}
        ,#{BINGANSBBZ,jdbcType=DECIMAL},#{SHUANGXIANGJZBZ,jdbcType=DECIMAL},#{ZHONGZHUANKS,jdbcType=VARCHAR}
        ,#{ZHONGZHUANKEMC,jdbcType=VARCHAR},#{DIEDAOPGBZ,jdbcType=DECIMAL},#{DISANFBZJ,jdbcType=VARCHAR}
        ,#{BINGRENQX,jdbcType=VARCHAR},#{FENZHENJB,jdbcType=VARCHAR},#{YOUXIAOZDMC,jdbcType=VARCHAR}
        ,#{MINZHENGXX,jdbcType=VARCHAR},#{BINGQINGZY,jdbcType=VARCHAR},#{YANKECS,jdbcType=DECIMAL}
        ,#{QITAYLXTBRBZ,jdbcType=DECIMAL},#{GCPLX,jdbcType=VARCHAR},#{BUSHIYDBZYY,jdbcType=VARCHAR}
        ,#{SHIFOUGZ,jdbcType=DECIMAL},#{EMPI_YJ,jdbcType=VARCHAR},#{SANJIYS,jdbcType=VARCHAR}
        ,#{SANJIYSMC,jdbcType=VARCHAR},#{KEZHURYS,jdbcType=VARCHAR},#{KEZHURYSXM,jdbcType=VARCHAR}
        ,#{ERJIYS,jdbcType=VARCHAR},#{ERJIYSMC,jdbcType=VARCHAR},#{YIJIYS,jdbcType=VARCHAR}
        ,#{YIJIYSMC,jdbcType=VARCHAR},#{SHANGCICKSJ,jdbcType=TIMESTAMP},#{ZONGHEBFBZ,jdbcType=DECIMAL}
        ,#{ZONGHEBQ,jdbcType=VARCHAR},#{ZONGHEBQCW,jdbcType=VARCHAR},#{ZONGHEBFJRSJ,jdbcType=TIMESTAMP}
        ,#{ZONGHEBQMC,jdbcType=VARCHAR},#{QIANTIANJH,jdbcType=DECIMAL},#{ZHOUQI,jdbcType=VARCHAR}
        ,#{JINGQI,jdbcType=VARCHAR},#{YXJM,jdbcType=DECIMAL},#{XINXITBZ,jdbcType=DECIMAL}
        ,#{CHUANGTOUKDYBZ,jdbcType=DECIMAL},#{FLSBZ,jdbcType=DECIMAL},#{XINGBIEDM,jdbcType=VARCHAR}
        ,#{HUNYINDM,jdbcType=VARCHAR},#{ZHIYEDM,jdbcType=VARCHAR},#{GUOJIDM,jdbcType=VARCHAR}
        ,#{MINZUDM,jdbcType=VARCHAR},#{SHENGFENDM,jdbcType=VARCHAR},#{JIGUANDM,jdbcType=VARCHAR}
        ,#{CHUSHENGDDM,jdbcType=VARCHAR},#{JIHUASYFWZH,jdbcType=VARCHAR},#{SHENGYULB,jdbcType=VARCHAR}
        ,#{WANYUBZ,jdbcType=DECIMAL},#{ZAOCHANBZ,jdbcType=DECIMAL},#{JIHUASYSSLB,jdbcType=VARCHAR}
        ,#{XUELI,jdbcType=VARCHAR},#{ZAIRUYHZBZ,jdbcType=DECIMAL},#{XUETANGSQZ,jdbcType=VARCHAR}
        ,#{XUETANGSQZT,jdbcType=VARCHAR},#{SHAICHADQ,jdbcType=VARCHAR},#{XIANXUEZBZ,jdbcType=DECIMAL}
        ,#{JCPTEMPI,jdbcType=VARCHAR},#{IPT_PSN_SP_FLAG_DETL_ID,jdbcType=VARCHAR},#{LINCHUANGLJID,jdbcType=VARCHAR}
        ,#{SHIFOUYXZZJS,jdbcType=VARCHAR},#{PEIKEKH,jdbcType=VARCHAR},#{KANGFUSQZT,jdbcType=VARCHAR}
        ,#{KANGFUZLS,jdbcType=VARCHAR},#{KANGFUZLSXM,jdbcType=VARCHAR},#{CHANGHUXBRBZ,jdbcType=DECIMAL}
        ,#{YOUFUBRBZ,jdbcType=DECIMAL},#{LIANXIREN2,jdbcType=VARCHAR},#{LIANXIRDH2,jdbcType=VARCHAR}
        ,#{GUANXI2,jdbcType=VARCHAR},#{YUANQIANBQID,jdbcType=VARCHAR},#{ZHUGUANYS,jdbcType=VARCHAR}
        ,#{LIUQIANGZKBZ,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="BINGRENZYID" keyProperty="BINGRENZYID" parameterType="com.javazx.batch.po.ZyBingrenxx" useGeneratedKeys="true">
        insert into ZY_BINGRENXX
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="SHEBAOBH != null">SHEBAOBH,</if>
                <if test="YIBAOKH != null">YIBAOKH,</if>
                <if test="JIUZHENKH != null">JIUZHENKH,</if>
                <if test="SHEQUBH != null">SHEQUBH,</if>
                <if test="FEIYONGLB != null">FEIYONGLB,</if>
                <if test="FEIYONGXZ != null">FEIYONGXZ,</if>
                <if test="YOUHUILB != null">YOUHUILB,</if>
                <if test="GONGFEIZH != null">GONGFEIZH,</if>
                <if test="GONGFEIDW != null">GONGFEIDW,</if>
                <if test="GONGFEIDWMC != null">GONGFEIDWMC,</if>
                <if test="XINGMING != null">XINGMING,</if>
                <if test="SHURUMA1 != null">SHURUMA1,</if>
                <if test="SHURUMA2 != null">SHURUMA2,</if>
                <if test="SHURUMA3 != null">SHURUMA3,</if>
                <if test="XINGBIE != null">XINGBIE,</if>
                <if test="NIANLINGDW != null">NIANLINGDW,</if>
                <if test="SHENFENZH != null">SHENFENZH,</if>
                <if test="CHUSHENGRQ != null">CHUSHENGRQ,</if>
                <if test="GONGZUODW != null">GONGZUODW,</if>
                <if test="DANWEIDH != null">DANWEIDH,</if>
                <if test="DANWEIYB != null">DANWEIYB,</if>
                <if test="JIATINGDZ != null">JIATINGDZ,</if>
                <if test="JIATINGDH != null">JIATINGDH,</if>
                <if test="JIATINGYB != null">JIATINGYB,</if>
                <if test="XUEXING != null">XUEXING,</if>
                <if test="HUNYIN != null">HUNYIN,</if>
                <if test="ZHIYE != null">ZHIYE,</if>
                <if test="GUOJI != null">GUOJI,</if>
                <if test="MINZU != null">MINZU,</if>
                <if test="SHENGFEN != null">SHENGFEN,</if>
                <if test="XIANGZHENJD != null">XIANGZHENJD,</if>
                <if test="SHIDIQU != null">SHIDIQU,</if>
                <if test="JIGUAN != null">JIGUAN,</if>
                <if test="CHUSHENGDI != null">CHUSHENGDI,</if>
                <if test="YOUBIAN != null">YOUBIAN,</if>
                <if test="LIANXIREN != null">LIANXIREN,</if>
                <if test="GUANXI != null">GUANXI,</if>
                <if test="LIANXIRDZ != null">LIANXIRDZ,</if>
                <if test="LIANXIRDH != null">LIANXIRDH,</if>
                <if test="LIANXIRYB != null">LIANXIRYB,</if>
                <if test="JIWANGSHI != null">JIWANGSHI,</if>
                <if test="GUOMINSHI != null">GUOMINSHI,</if>
                <if test="QUYU != null">QUYU,</if>
                <if test="WAIDIBRBZ != null">WAIDIBRBZ,</if>
                <if test="JIANDANGREN != null">JIANDANGREN,</if>
                <if test="JIANDANGRQ != null">JIANDANGRQ,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="YINGERBZ != null">YINGERBZ,</if>
                <if test="ZHUYUANCS != null">ZHUYUANCS,</if>
                <if test="RUYUANRQ != null">RUYUANRQ,</if>
                <if test="YUCHUYRQ != null">YUCHUYRQ,</if>
                <if test="CHUYUANRQ != null">CHUYUANRQ,</if>
                <if test="ZAIYUANZT != null">ZAIYUANZT,</if>
                <if test="RUYUANTJ != null">RUYUANTJ,</if>
                <if test="RUYUANKS != null">RUYUANKS,</if>
                <if test="RUYUANBQ != null">RUYUANBQ,</if>
                <if test="RUYUANCW != null">RUYUANCW,</if>
                <if test="DANGQIANKS != null">DANGQIANKS,</if>
                <if test="DANGQIANBQ != null">DANGQIANBQ,</if>
                <if test="DANGQIANCW != null">DANGQIANCW,</if>
                <if test="GUANLIKS != null">GUANLIKS,</if>
                <if test="JIECHUANGBZ != null">JIECHUANGBZ,</if>
                <if test="LIYUANQX != null">LIYUANQX,</if>
                <if test="MENZHENZDDM != null">MENZHENZDDM,</if>
                <if test="MENZHENZDMC != null">MENZHENZDMC,</if>
                <if test="RUYUANZDDM != null">RUYUANZDDM,</if>
                <if test="RUYUANZDMC != null">RUYUANZDMC,</if>
                <if test="CHUYUANZDDM != null">CHUYUANZDDM,</if>
                <if test="CHUYUANZDMC != null">CHUYUANZDMC,</if>
                <if test="CHUYUANZDDM2 != null">CHUYUANZDDM2,</if>
                <if test="CHUYUANZDMC2 != null">CHUYUANZDMC2,</if>
                <if test="CHUYUANZDMC3 != null">CHUYUANZDMC3,</if>
                <if test="CHUYUANZDDM3 != null">CHUYUANZDDM3,</if>
                <if test="JIATINGBCBZ != null">JIATINGBCBZ,</if>
                <if test="BINGQING != null">BINGQING,</if>
                <if test="FENMIAN != null">FENMIAN,</if>
                <if test="SHANGCHUANBZ != null">SHANGCHUANBZ,</if>
                <if test="SHANGCHUANRQ != null">SHANGCHUANRQ,</if>
                <if test="DANBAOREN != null">DANBAOREN,</if>
                <if test="DANBAOJE != null">DANBAOJE,</if>
                <if test="JIAZHANGXM != null">JIAZHANGXM,</if>
                <if test="ZHUYUANHAO != null">ZHUYUANHAO,</if>
                <if test="BINGANHAO != null">BINGANHAO,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="JIESUANXH != null">JIESUANXH,</if>
                <if test="NIANLING != null">NIANLING,</if>
                <if test="MENZHENYS != null">MENZHENYS,</if>
                <if test="SHOUZHIYS != null">SHOUZHIYS,</if>
                <if test="ZHUYUANYS != null">ZHUYUANYS,</if>
                <if test="ZHUZHIYS != null">ZHUZHIYS,</if>
                <if test="YUYUEID != null">YUYUEID,</if>
                <if test="SHENHEBZ != null">SHENHEBZ,</if>
                <if test="QIGUANYZBZ != null">QIGUANYZBZ,</if>
                <if test="JIAOYILSH != null">JIAOYILSH,</if>
                <if test="GERENBH != null">GERENBH,</if>
                <if test="IC != null">IC,</if>
                <if test="SHIMINKWKH != null">SHIMINKWKH,</if>
                <if test="YILIAOZID != null">YILIAOZID,</if>
                <if test="YILIAOZM != null">YILIAOZM,</if>
                <if test="RUKERQ != null">RUKERQ,</if>
                <if test="RUKEBZ != null">RUKEBZ,</if>
                <if test="ZHANCHUANGBZ != null">ZHANCHUANGBZ,</if>
                <if test="RUYUANKSMC != null">RUYUANKSMC,</if>
                <if test="RUYUANBQMC != null">RUYUANBQMC,</if>
                <if test="DANGQIANKSMC != null">DANGQIANKSMC,</if>
                <if test="DANGQIANBQMC != null">DANGQIANBQMC,</if>
                <if test="GUANLIKSMC != null">GUANLIKSMC,</if>
                <if test="MENZHENYSXM != null">MENZHENYSXM,</if>
                <if test="SHOUZHIYSXM != null">SHOUZHIYSXM,</if>
                <if test="ZHUYUANYSXM != null">ZHUYUANYSXM,</if>
                <if test="ZHUZHIYSXM != null">ZHUZHIYSXM,</if>
                <if test="BINGLISLZT != null">BINGLISLZT,</if>
                <if test="FEIYONGXZZHBZ != null">FEIYONGXZZHBZ,</if>
                <if test="FEIYONGXZZHR != null">FEIYONGXZZHR,</if>
                <if test="FEIYONGXZZHRQ != null">FEIYONGXZZHRQ,</if>
                <if test="YIBAOYLLB != null">YIBAOYLLB,</if>
                <if test="TESHUBZBZ != null">TESHUBZBZ,</if>
                <if test="TESHUBZMC != null">TESHUBZMC,</if>
                <if test="TESHUBZBM != null">TESHUBZBM,</if>
                <if test="MENZHENZYZDDM != null">MENZHENZYZDDM,</if>
                <if test="MENZHENZYZDMC != null">MENZHENZYZDMC,</if>
                <if test="RUYUANZYZDDM != null">RUYUANZYZDDM,</if>
                <if test="RUYUANZYZDMC != null">RUYUANZYZDMC,</if>
                <if test="WAIYUANZDBZ != null">WAIYUANZDBZ,</if>
                <if test="RUYUANZYZHDM != null">RUYUANZYZHDM,</if>
                <if test="RUYUANZYZHMC != null">RUYUANZYZHMC,</if>
                <if test="YAOPINBZYBZ != null">YAOPINBZYBZ,</if>
                <if test="ZHUANBINGQBZ != null">ZHUANBINGQBZ,</if>
                <if test="MUQINZYID != null">MUQINZYID,</if>
                <if test="CHANFUBZ != null">CHANFUBZ,</if>
                <if test="LINCHUANGLJBZ != null">LINCHUANGLJBZ,</if>
                <if test="QUXIAORYBZ != null">QUXIAORYBZ,</if>
                <if test="QINGJIABZ != null">QINGJIABZ,</if>
                <if test="YUANBINGRZYID != null">YUANBINGRZYID,</if>
                <if test="LVSETDBZ != null">LVSETDBZ,</if>
                <if test="LVSETDKQRQ != null">LVSETDKQRQ,</if>
                <if test="YIBAOMXFJYJLS != null">YIBAOMXFJYJLS,</if>
                <if test="TESHUBZ != null">TESHUBZ,</if>
                <if test="YILIAOKH != null">YILIAOKH,</if>
                <if test="JIESHAOREN != null">JIESHAOREN,</if>
                <if test="YINGERSL != null">YINGERSL,</if>
                <if test="YOUHUILBLB != null">YOUHUILBLB,</if>
                <if test="QUXIAOYCYRQ != null">QUXIAOYCYRQ,</if>
                <if test="RUYUANZDDM2 != null">RUYUANZDDM2,</if>
                <if test="RUYUANZDMC2 != null">RUYUANZDMC2,</if>
                <if test="RUYUANZDDM3 != null">RUYUANZDDM3,</if>
                <if test="RUYUANZDMC3 != null">RUYUANZDMC3,</if>
                <if test="YIBAOBRXX != null">YIBAOBRXX,</if>
                <if test="FEIYONGLB2 != null">FEIYONGLB2,</if>
                <if test="FEIYONGXZ2 != null">FEIYONGXZ2,</if>
                <if test="YILIAOJZDJBZ != null">YILIAOJZDJBZ,</if>
                <if test="ZHIFU1YB != null">ZHIFU1YB,</if>
                <if test="ZHIFU2YB != null">ZHIFU2YB,</if>
                <if test="ZHIFU3YB != null">ZHIFU3YB,</if>
                <if test="ZHIFU4YB != null">ZHIFU4YB,</if>
                <if test="ZHIFU5YB != null">ZHIFU5YB,</if>
                <if test="ZHIFU6YB != null">ZHIFU6YB,</if>
                <if test="ZHIFU7YB != null">ZHIFU7YB,</if>
                <if test="ZHIFU8YB != null">ZHIFU8YB,</if>
                <if test="ZHIFU9YB != null">ZHIFU9YB,</if>
                <if test="ZHIFU10YB != null">ZHIFU10YB,</if>
                <if test="ZHIFU11YB != null">ZHIFU11YB,</if>
                <if test="ZHIFU12YB != null">ZHIFU12YB,</if>
                <if test="SHUZHI1YB != null">SHUZHI1YB,</if>
                <if test="SHUZHI2YB != null">SHUZHI2YB,</if>
                <if test="SHUZHI3YB != null">SHUZHI3YB,</if>
                <if test="SHUZHI4YB != null">SHUZHI4YB,</if>
                <if test="SHUZHI5YB != null">SHUZHI5YB,</if>
                <if test="SHUZHI6YB != null">SHUZHI6YB,</if>
                <if test="SHUZHI7YB != null">SHUZHI7YB,</if>
                <if test="SHUZHI8YB != null">SHUZHI8YB,</if>
                <if test="SHUZHI9YB != null">SHUZHI9YB,</if>
                <if test="SHUZHI10YB != null">SHUZHI10YB,</if>
                <if test="YIBAOCYDJBZ != null">YIBAOCYDJBZ,</if>
                <if test="YIZHONGYLBZ != null">YIZHONGYLBZ,</if>
                <if test="YIZHONGYLLB != null">YIZHONGYLLB,</if>
                <if test="YIZHONGYLXYJE != null">YIZHONGYLXYJE,</if>
                <if test="YIZHONGYLBZSM != null">YIZHONGYLBZSM,</if>
                <if test="FENMIANRQ != null">FENMIANRQ,</if>
                <if test="BINGFAZHENG != null">BINGFAZHENG,</if>
                <if test="BUZHUKBH != null">BUZHUKBH,</if>
                <if test="YIBAOKH2 != null">YIBAOKH2,</if>
                <if test="IC2 != null">IC2,</if>
                <if test="QUXIAOYCYYY != null">QUXIAOYCYYY,</if>
                <if test="BEIZHU != null">BEIZHU,</if>
                <if test="ZERENHS != null">ZERENHS,</if>
                <if test="ZERENHSXM != null">ZERENHSXM,</if>
                <if test="JIUFENBRBZ != null">JIUFENBRBZ,</if>
                <if test="ZHENGJIANLX != null">ZHENGJIANLX,</if>
                <if test="JIANHURENSFZH != null">JIANHURENSFZH,</if>
                <if test="GELILX != null">GELILX,</if>
                <if test="LASTYUCHUYRQ != null">LASTYUCHUYRQ,</if>
                <if test="LASTCHUYUANRQ != null">LASTCHUYUANRQ,</if>
                <if test="YURUYUANBZ != null">YURUYUANBZ,</if>
                <if test="RUYUANDJLX != null">RUYUANDJLX,</if>
                <if test="XIANQU != null">XIANQU,</if>
                <if test="NIANLING1 != null">NIANLING1,</if>
                <if test="NIANLINGDW1 != null">NIANLINGDW1,</if>
                <if test="GONGSHANGKFDJH != null">GONGSHANGKFDJH,</if>
                <if test="GONGSHANGKFRQ != null">GONGSHANGKFRQ,</if>
                <if test="LINCHUANGLJDBZBZ != null">LINCHUANGLJDBZBZ,</if>
                <if test="SHENGAO != null">SHENGAO,</if>
                <if test="TIZHONG != null">TIZHONG,</if>
                <if test="ADLSCORE != null">ADLSCORE,</if>
                <if test="SHENHEREN != null">SHENHEREN,</if>
                <if test="SHENHERQ != null">SHENHERQ,</if>
                <if test="GUAZHANGBZ != null">GUAZHANGBZ,</if>
                <if test="ZHENGJIANSMWJ != null">ZHENGJIANSMWJ,</if>
                <if test="BAOMIJB != null">BAOMIJB,</if>
                <if test="HUKOUDZ != null">HUKOUDZ,</if>
                <if test="HUKOUSF != null">HUKOUSF,</if>
                <if test="HUKOUSDQ != null">HUKOUSDQ,</if>
                <if test="HUKOUXQ != null">HUKOUXQ,</if>
                <if test="HUKOUXZJD != null">HUKOUXZJD,</if>
                <if test="JIATINGDZLB != null">JIATINGDZLB,</if>
                <if test="HUKOUDZLB != null">HUKOUDZLB,</if>
                <if test="JIANKANGKH != null">JIANKANGKH,</if>
                <if test="ZHAOSHIZHBZ != null">ZHAOSHIZHBZ,</if>
                <if test="JIANKANGKXX != null">JIANKANGKXX,</if>
                <if test="RIJIANZYBZ != null">RIJIANZYBZ,</if>
                <if test="CHUANGWEIZYBZ != null">CHUANGWEIZYBZ,</if>
                <if test="HUSHIZHANG != null">HUSHIZHANG,</if>
                <if test="HUSHIZXM != null">HUSHIZXM,</if>
                <if test="JIZHENYXBZ != null">JIZHENYXBZ,</if>
                <if test="YUCHUYXTSJ != null">YUCHUYXTSJ,</if>
                <if test="TIBIAOMJ != null">TIBIAOMJ,</if>
                <if test="YIWAISHBZ != null">YIWAISHBZ,</if>
                <if test="CHUSHENGDSF != null">CHUSHENGDSF,</if>
                <if test="CHUSHENGDSDQ != null">CHUSHENGDSDQ,</if>
                <if test="CHUSHENGDXQ != null">CHUSHENGDXQ,</if>
                <if test="JIGUANSF != null">JIGUANSF,</if>
                <if test="JIGUANSDQ != null">JIGUANSDQ,</if>
                <if test="XIANZHUZSF != null">XIANZHUZSF,</if>
                <if test="XIANZHUZSDQ != null">XIANZHUZSDQ,</if>
                <if test="XIANZHUZXQ != null">XIANZHUZXQ,</if>
                <if test="XIANZHUZQT != null">XIANZHUZQT,</if>
                <if test="HUKOUDZQT != null">HUKOUDZQT,</if>
                <if test="XIAOXILY != null">XIAOXILY,</if>
                <if test="WANGLUOFWBZ != null">WANGLUOFWBZ,</if>
                <if test="DABINGBZ != null">DABINGBZ,</if>
                <if test="SHIFOUCSSFZ != null">SHIFOUCSSFZ,</if>
                <if test="YUNCI != null">YUNCI,</if>
                <if test="CHANCI != null">CHANCI,</if>
                <if test="TAICI != null">TAICI,</if>
                <if test="YUCHANQI != null">YUCHANQI,</if>
                <if test="QUYUBH != null">QUYUBH,</if>
                <if test="MOCIYJ != null">MOCIYJ,</if>
                <if test="MIANFEIQLX != null">MIANFEIQLX,</if>
                <if test="HUKOUYB != null">HUKOUYB,</if>
                <if test="SHIBAZDBDM != null">SHIBAZDBDM,</if>
                <if test="MOCIYUEJINGSJ != null">MOCIYUEJINGSJ,</if>
                <if test="JSMOCIYJSJ != null">JSMOCIYJSJ,</if>
                <if test="YUNZHOU != null">YUNZHOU,</if>
                <if test="YUNTIAN != null">YUNTIAN,</if>
                <if test="TAICHANCI != null">TAICHANCI,</if>
                <if test="BUSUIFANGBZ != null">BUSUIFANGBZ,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
                <if test="SHIFOUSQQFX != null">SHIFOUSQQFX,</if>
                <if test="SHANSHIFYBZ != null">SHANSHIFYBZ,</if>
                <if test="SFJSFLZBZ != null">SFJSFLZBZ,</if>
                <if test="BURUJINGLY != null">BURUJINGLY,</if>
                <if test="YILIANTBZ != null">YILIANTBZ,</if>
                <if test="YILIANTBH != null">YILIANTBH,</if>
                <if test="YILIANTSQDID != null">YILIANTSQDID,</if>
                <if test="ZHURENYS != null">ZHURENYS,</if>
                <if test="ZHURENYSXM != null">ZHURENYSXM,</if>
                <if test="SHENGYUBZ != null">SHENGYUBZ,</if>
                <if test="BIAOZHI120 != null">BIAOZHI120,</if>
                <if test="YINANBRBZ != null">YINANBRBZ,</if>
                <if test="RUYUANYY != null">RUYUANYY,</if>
                <if test="ZAICIRYLX != null">ZAICIRYLX,</if>
                <if test="ZHUANZHENDH != null">ZHUANZHENDH,</if>
                <if test="YUANRUKERQ != null">YUANRUKERQ,</if>
                <if test="YINGERCWFJZZT != null">YINGERCWFJZZT,</if>
                <if test="YINGERCWFJZTZRQ != null">YINGERCWFJZTZRQ,</if>
                <if test="YINGERCWFJZTZR != null">YINGERCWFJZTZR,</if>
                <if test="KUAISURYBZ != null">KUAISURYBZ,</if>
                <if test="SHENFENZBXYY != null">SHENFENZBXYY,</if>
                <if test="YDRYBZ != null">YDRYBZ,</if>
                <if test="TCQBZ != null">TCQBZ,</if>
                <if test="SENLINFHBRBZ != null">SENLINFHBRBZ,</if>
                <if test="SENLINFHSPRQ != null">SENLINFHSPRQ,</if>
                <if test="SENLINFHBZ != null">SENLINFHBZ,</if>
                <if test="MPI != null">MPI,</if>
                <if test="SHOUCIRKR != null">SHOUCIRKR,</if>
                <if test="SHOUCIRKRXM != null">SHOUCIRKRXM,</if>
                <if test="JIUZHENQRRQ != null">JIUZHENQRRQ,</if>
                <if test="JIUZHENQRBZ != null">JIUZHENQRBZ,</if>
                <if test="QUERENSFRQ_HIS != null">QUERENSFRQ_HIS,</if>
                <if test="QUERENSFBZ_HIS != null">QUERENSFBZ_HIS,</if>
                <if test="DENGJISY != null">DENGJISY,</if>
                <if test="YUNYIXYED != null">YUNYIXYED,</if>
                <if test="YUNYIQYBZ != null">YUNYIQYBZ,</if>
                <if test="ZHUANDRYELB != null">ZHUANDRYELB,</if>
                <if test="LIUGUANBZ != null">LIUGUANBZ,</if>
                <if test="LIUGUANSQDID != null">LIUGUANSQDID,</if>
                <if test="JIUZHENID != null">JIUZHENID,</if>
                <if test="FENZHENSJ != null">FENZHENSJ,</if>
                <if test="LAIYUAN != null">LAIYUAN,</if>
                <if test="FENZHENYUAN != null">FENZHENYUAN,</if>
                <if test="FENZHENYUANID != null">FENZHENYUANID,</if>
                <if test="FENZHENLB != null">FENZHENLB,</if>
                <if test="SHIFUTZRQ != null">SHIFUTZRQ,</if>
                <if test="DIANHUA != null">DIANHUA,</if>
                <if test="XINGUANBZ != null">XINGUANBZ,</if>
                <if test="SANDAZXBZ != null">SANDAZXBZ,</if>
                <if test="QITABZNR != null">QITABZNR,</if>
                <if test="XINBINGRENZYID != null">XINBINGRENZYID,</if>
                <if test="LIUGUANBRZYID != null">LIUGUANBRZYID,</if>
                <if test="XIANZHUZHI != null">XIANZHUZHI,</if>
                <if test="BINGANSBBZ != null">BINGANSBBZ,</if>
                <if test="SHUANGXIANGJZBZ != null">SHUANGXIANGJZBZ,</if>
                <if test="ZHONGZHUANKS != null">ZHONGZHUANKS,</if>
                <if test="ZHONGZHUANKEMC != null">ZHONGZHUANKEMC,</if>
                <if test="DIEDAOPGBZ != null">DIEDAOPGBZ,</if>
                <if test="DISANFBZJ != null">DISANFBZJ,</if>
                <if test="BINGRENQX != null">BINGRENQX,</if>
                <if test="FENZHENJB != null">FENZHENJB,</if>
                <if test="YOUXIAOZDMC != null">YOUXIAOZDMC,</if>
                <if test="MINZHENGXX != null">MINZHENGXX,</if>
                <if test="BINGQINGZY != null">BINGQINGZY,</if>
                <if test="YANKECS != null">YANKECS,</if>
                <if test="QITAYLXTBRBZ != null">QITAYLXTBRBZ,</if>
                <if test="GCPLX != null">GCPLX,</if>
                <if test="BUSHIYDBZYY != null">BUSHIYDBZYY,</if>
                <if test="SHIFOUGZ != null">SHIFOUGZ,</if>
                <if test="EMPI_YJ != null">EMPI_YJ,</if>
                <if test="SANJIYS != null">SANJIYS,</if>
                <if test="SANJIYSMC != null">SANJIYSMC,</if>
                <if test="KEZHURYS != null">KEZHURYS,</if>
                <if test="KEZHURYSXM != null">KEZHURYSXM,</if>
                <if test="ERJIYS != null">ERJIYS,</if>
                <if test="ERJIYSMC != null">ERJIYSMC,</if>
                <if test="YIJIYS != null">YIJIYS,</if>
                <if test="YIJIYSMC != null">YIJIYSMC,</if>
                <if test="SHANGCICKSJ != null">SHANGCICKSJ,</if>
                <if test="ZONGHEBFBZ != null">ZONGHEBFBZ,</if>
                <if test="ZONGHEBQ != null">ZONGHEBQ,</if>
                <if test="ZONGHEBQCW != null">ZONGHEBQCW,</if>
                <if test="ZONGHEBFJRSJ != null">ZONGHEBFJRSJ,</if>
                <if test="ZONGHEBQMC != null">ZONGHEBQMC,</if>
                <if test="QIANTIANJH != null">QIANTIANJH,</if>
                <if test="ZHOUQI != null">ZHOUQI,</if>
                <if test="JINGQI != null">JINGQI,</if>
                <if test="YXJM != null">YXJM,</if>
                <if test="XINXITBZ != null">XINXITBZ,</if>
                <if test="CHUANGTOUKDYBZ != null">CHUANGTOUKDYBZ,</if>
                <if test="FLSBZ != null">FLSBZ,</if>
                <if test="XINGBIEDM != null">XINGBIEDM,</if>
                <if test="HUNYINDM != null">HUNYINDM,</if>
                <if test="ZHIYEDM != null">ZHIYEDM,</if>
                <if test="GUOJIDM != null">GUOJIDM,</if>
                <if test="MINZUDM != null">MINZUDM,</if>
                <if test="SHENGFENDM != null">SHENGFENDM,</if>
                <if test="JIGUANDM != null">JIGUANDM,</if>
                <if test="CHUSHENGDDM != null">CHUSHENGDDM,</if>
                <if test="JIHUASYFWZH != null">JIHUASYFWZH,</if>
                <if test="SHENGYULB != null">SHENGYULB,</if>
                <if test="WANYUBZ != null">WANYUBZ,</if>
                <if test="ZAOCHANBZ != null">ZAOCHANBZ,</if>
                <if test="JIHUASYSSLB != null">JIHUASYSSLB,</if>
                <if test="XUELI != null">XUELI,</if>
                <if test="ZAIRUYHZBZ != null">ZAIRUYHZBZ,</if>
                <if test="XUETANGSQZ != null">XUETANGSQZ,</if>
                <if test="XUETANGSQZT != null">XUETANGSQZT,</if>
                <if test="SHAICHADQ != null">SHAICHADQ,</if>
                <if test="XIANXUEZBZ != null">XIANXUEZBZ,</if>
                <if test="JCPTEMPI != null">JCPTEMPI,</if>
                <if test="IPT_PSN_SP_FLAG_DETL_ID != null">IPT_PSN_SP_FLAG_DETL_ID,</if>
                <if test="LINCHUANGLJID != null">LINCHUANGLJID,</if>
                <if test="SHIFOUYXZZJS != null">SHIFOUYXZZJS,</if>
                <if test="PEIKEKH != null">PEIKEKH,</if>
                <if test="KANGFUSQZT != null">KANGFUSQZT,</if>
                <if test="KANGFUZLS != null">KANGFUZLS,</if>
                <if test="KANGFUZLSXM != null">KANGFUZLSXM,</if>
                <if test="CHANGHUXBRBZ != null">CHANGHUXBRBZ,</if>
                <if test="YOUFUBRBZ != null">YOUFUBRBZ,</if>
                <if test="LIANXIREN2 != null">LIANXIREN2,</if>
                <if test="LIANXIRDH2 != null">LIANXIRDH2,</if>
                <if test="GUANXI2 != null">GUANXI2,</if>
                <if test="YUANQIANBQID != null">YUANQIANBQID,</if>
                <if test="ZHUGUANYS != null">ZHUGUANYS,</if>
                <if test="LIUQIANGZKBZ != null">LIUQIANGZKBZ,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="SHEBAOBH != null">#{SHEBAOBH,jdbcType=VARCHAR},</if>
                <if test="YIBAOKH != null">#{YIBAOKH,jdbcType=VARCHAR},</if>
                <if test="JIUZHENKH != null">#{JIUZHENKH,jdbcType=VARCHAR},</if>
                <if test="SHEQUBH != null">#{SHEQUBH,jdbcType=VARCHAR},</if>
                <if test="FEIYONGLB != null">#{FEIYONGLB,jdbcType=VARCHAR},</if>
                <if test="FEIYONGXZ != null">#{FEIYONGXZ,jdbcType=VARCHAR},</if>
                <if test="YOUHUILB != null">#{YOUHUILB,jdbcType=VARCHAR},</if>
                <if test="GONGFEIZH != null">#{GONGFEIZH,jdbcType=VARCHAR},</if>
                <if test="GONGFEIDW != null">#{GONGFEIDW,jdbcType=VARCHAR},</if>
                <if test="GONGFEIDWMC != null">#{GONGFEIDWMC,jdbcType=VARCHAR},</if>
                <if test="XINGMING != null">#{XINGMING,jdbcType=VARCHAR},</if>
                <if test="SHURUMA1 != null">#{SHURUMA1,jdbcType=VARCHAR},</if>
                <if test="SHURUMA2 != null">#{SHURUMA2,jdbcType=VARCHAR},</if>
                <if test="SHURUMA3 != null">#{SHURUMA3,jdbcType=VARCHAR},</if>
                <if test="XINGBIE != null">#{XINGBIE,jdbcType=VARCHAR},</if>
                <if test="NIANLINGDW != null">#{NIANLINGDW,jdbcType=VARCHAR},</if>
                <if test="SHENFENZH != null">#{SHENFENZH,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGRQ != null">#{CHUSHENGRQ,jdbcType=TIMESTAMP},</if>
                <if test="GONGZUODW != null">#{GONGZUODW,jdbcType=VARCHAR},</if>
                <if test="DANWEIDH != null">#{DANWEIDH,jdbcType=VARCHAR},</if>
                <if test="DANWEIYB != null">#{DANWEIYB,jdbcType=VARCHAR},</if>
                <if test="JIATINGDZ != null">#{JIATINGDZ,jdbcType=VARCHAR},</if>
                <if test="JIATINGDH != null">#{JIATINGDH,jdbcType=VARCHAR},</if>
                <if test="JIATINGYB != null">#{JIATINGYB,jdbcType=VARCHAR},</if>
                <if test="XUEXING != null">#{XUEXING,jdbcType=VARCHAR},</if>
                <if test="HUNYIN != null">#{HUNYIN,jdbcType=VARCHAR},</if>
                <if test="ZHIYE != null">#{ZHIYE,jdbcType=VARCHAR},</if>
                <if test="GUOJI != null">#{GUOJI,jdbcType=VARCHAR},</if>
                <if test="MINZU != null">#{MINZU,jdbcType=VARCHAR},</if>
                <if test="SHENGFEN != null">#{SHENGFEN,jdbcType=VARCHAR},</if>
                <if test="XIANGZHENJD != null">#{XIANGZHENJD,jdbcType=VARCHAR},</if>
                <if test="SHIDIQU != null">#{SHIDIQU,jdbcType=VARCHAR},</if>
                <if test="JIGUAN != null">#{JIGUAN,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGDI != null">#{CHUSHENGDI,jdbcType=VARCHAR},</if>
                <if test="YOUBIAN != null">#{YOUBIAN,jdbcType=VARCHAR},</if>
                <if test="LIANXIREN != null">#{LIANXIREN,jdbcType=VARCHAR},</if>
                <if test="GUANXI != null">#{GUANXI,jdbcType=VARCHAR},</if>
                <if test="LIANXIRDZ != null">#{LIANXIRDZ,jdbcType=VARCHAR},</if>
                <if test="LIANXIRDH != null">#{LIANXIRDH,jdbcType=VARCHAR},</if>
                <if test="LIANXIRYB != null">#{LIANXIRYB,jdbcType=VARCHAR},</if>
                <if test="JIWANGSHI != null">#{JIWANGSHI,jdbcType=VARCHAR},</if>
                <if test="GUOMINSHI != null">#{GUOMINSHI,jdbcType=VARCHAR},</if>
                <if test="QUYU != null">#{QUYU,jdbcType=VARCHAR},</if>
                <if test="WAIDIBRBZ != null">#{WAIDIBRBZ,jdbcType=DECIMAL},</if>
                <if test="JIANDANGREN != null">#{JIANDANGREN,jdbcType=VARCHAR},</if>
                <if test="JIANDANGRQ != null">#{JIANDANGRQ,jdbcType=TIMESTAMP},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="YINGERBZ != null">#{YINGERBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUYUANCS != null">#{ZHUYUANCS,jdbcType=DECIMAL},</if>
                <if test="RUYUANRQ != null">#{RUYUANRQ,jdbcType=TIMESTAMP},</if>
                <if test="YUCHUYRQ != null">#{YUCHUYRQ,jdbcType=TIMESTAMP},</if>
                <if test="CHUYUANRQ != null">#{CHUYUANRQ,jdbcType=TIMESTAMP},</if>
                <if test="ZAIYUANZT != null">#{ZAIYUANZT,jdbcType=VARCHAR},</if>
                <if test="RUYUANTJ != null">#{RUYUANTJ,jdbcType=VARCHAR},</if>
                <if test="RUYUANKS != null">#{RUYUANKS,jdbcType=VARCHAR},</if>
                <if test="RUYUANBQ != null">#{RUYUANBQ,jdbcType=VARCHAR},</if>
                <if test="RUYUANCW != null">#{RUYUANCW,jdbcType=VARCHAR},</if>
                <if test="DANGQIANKS != null">#{DANGQIANKS,jdbcType=VARCHAR},</if>
                <if test="DANGQIANBQ != null">#{DANGQIANBQ,jdbcType=VARCHAR},</if>
                <if test="DANGQIANCW != null">#{DANGQIANCW,jdbcType=VARCHAR},</if>
                <if test="GUANLIKS != null">#{GUANLIKS,jdbcType=VARCHAR},</if>
                <if test="JIECHUANGBZ != null">#{JIECHUANGBZ,jdbcType=DECIMAL},</if>
                <if test="LIYUANQX != null">#{LIYUANQX,jdbcType=VARCHAR},</if>
                <if test="MENZHENZDDM != null">#{MENZHENZDDM,jdbcType=VARCHAR},</if>
                <if test="MENZHENZDMC != null">#{MENZHENZDMC,jdbcType=VARCHAR},</if>
                <if test="RUYUANZDDM != null">#{RUYUANZDDM,jdbcType=VARCHAR},</if>
                <if test="RUYUANZDMC != null">#{RUYUANZDMC,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDDM != null">#{CHUYUANZDDM,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDMC != null">#{CHUYUANZDMC,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDDM2 != null">#{CHUYUANZDDM2,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDMC2 != null">#{CHUYUANZDMC2,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDMC3 != null">#{CHUYUANZDMC3,jdbcType=VARCHAR},</if>
                <if test="CHUYUANZDDM3 != null">#{CHUYUANZDDM3,jdbcType=VARCHAR},</if>
                <if test="JIATINGBCBZ != null">#{JIATINGBCBZ,jdbcType=DECIMAL},</if>
                <if test="BINGQING != null">#{BINGQING,jdbcType=VARCHAR},</if>
                <if test="FENMIAN != null">#{FENMIAN,jdbcType=VARCHAR},</if>
                <if test="SHANGCHUANBZ != null">#{SHANGCHUANBZ,jdbcType=DECIMAL},</if>
                <if test="SHANGCHUANRQ != null">#{SHANGCHUANRQ,jdbcType=TIMESTAMP},</if>
                <if test="DANBAOREN != null">#{DANBAOREN,jdbcType=VARCHAR},</if>
                <if test="DANBAOJE != null">#{DANBAOJE,jdbcType=DECIMAL},</if>
                <if test="JIAZHANGXM != null">#{JIAZHANGXM,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANHAO != null">#{ZHUYUANHAO,jdbcType=VARCHAR},</if>
                <if test="BINGANHAO != null">#{BINGANHAO,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="JIESUANXH != null">#{JIESUANXH,jdbcType=DECIMAL},</if>
                <if test="NIANLING != null">#{NIANLING,jdbcType=DECIMAL},</if>
                <if test="MENZHENYS != null">#{MENZHENYS,jdbcType=VARCHAR},</if>
                <if test="SHOUZHIYS != null">#{SHOUZHIYS,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANYS != null">#{ZHUYUANYS,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYS != null">#{ZHUZHIYS,jdbcType=VARCHAR},</if>
                <if test="YUYUEID != null">#{YUYUEID,jdbcType=VARCHAR},</if>
                <if test="SHENHEBZ != null">#{SHENHEBZ,jdbcType=DECIMAL},</if>
                <if test="QIGUANYZBZ != null">#{QIGUANYZBZ,jdbcType=DECIMAL},</if>
                <if test="JIAOYILSH != null">#{JIAOYILSH,jdbcType=VARCHAR},</if>
                <if test="GERENBH != null">#{GERENBH,jdbcType=VARCHAR},</if>
                <if test="IC != null">#{IC,jdbcType=VARCHAR},</if>
                <if test="SHIMINKWKH != null">#{SHIMINKWKH,jdbcType=VARCHAR},</if>
                <if test="YILIAOZID != null">#{YILIAOZID,jdbcType=VARCHAR},</if>
                <if test="YILIAOZM != null">#{YILIAOZM,jdbcType=VARCHAR},</if>
                <if test="RUKERQ != null">#{RUKERQ,jdbcType=TIMESTAMP},</if>
                <if test="RUKEBZ != null">#{RUKEBZ,jdbcType=DECIMAL},</if>
                <if test="ZHANCHUANGBZ != null">#{ZHANCHUANGBZ,jdbcType=DECIMAL},</if>
                <if test="RUYUANKSMC != null">#{RUYUANKSMC,jdbcType=VARCHAR},</if>
                <if test="RUYUANBQMC != null">#{RUYUANBQMC,jdbcType=VARCHAR},</if>
                <if test="DANGQIANKSMC != null">#{DANGQIANKSMC,jdbcType=VARCHAR},</if>
                <if test="DANGQIANBQMC != null">#{DANGQIANBQMC,jdbcType=VARCHAR},</if>
                <if test="GUANLIKSMC != null">#{GUANLIKSMC,jdbcType=VARCHAR},</if>
                <if test="MENZHENYSXM != null">#{MENZHENYSXM,jdbcType=VARCHAR},</if>
                <if test="SHOUZHIYSXM != null">#{SHOUZHIYSXM,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANYSXM != null">#{ZHUYUANYSXM,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYSXM != null">#{ZHUZHIYSXM,jdbcType=VARCHAR},</if>
                <if test="BINGLISLZT != null">#{BINGLISLZT,jdbcType=VARCHAR},</if>
                <if test="FEIYONGXZZHBZ != null">#{FEIYONGXZZHBZ,jdbcType=DECIMAL},</if>
                <if test="FEIYONGXZZHR != null">#{FEIYONGXZZHR,jdbcType=VARCHAR},</if>
                <if test="FEIYONGXZZHRQ != null">#{FEIYONGXZZHRQ,jdbcType=TIMESTAMP},</if>
                <if test="YIBAOYLLB != null">#{YIBAOYLLB,jdbcType=VARCHAR},</if>
                <if test="TESHUBZBZ != null">#{TESHUBZBZ,jdbcType=DECIMAL},</if>
                <if test="TESHUBZMC != null">#{TESHUBZMC,jdbcType=VARCHAR},</if>
                <if test="TESHUBZBM != null">#{TESHUBZBM,jdbcType=VARCHAR},</if>
                <if test="MENZHENZYZDDM != null">#{MENZHENZYZDDM,jdbcType=VARCHAR},</if>
                <if test="MENZHENZYZDMC != null">#{MENZHENZYZDMC,jdbcType=VARCHAR},</if>
                <if test="RUYUANZYZDDM != null">#{RUYUANZYZDDM,jdbcType=VARCHAR},</if>
                <if test="RUYUANZYZDMC != null">#{RUYUANZYZDMC,jdbcType=VARCHAR},</if>
                <if test="WAIYUANZDBZ != null">#{WAIYUANZDBZ,jdbcType=DECIMAL},</if>
                <if test="RUYUANZYZHDM != null">#{RUYUANZYZHDM,jdbcType=VARCHAR},</if>
                <if test="RUYUANZYZHMC != null">#{RUYUANZYZHMC,jdbcType=VARCHAR},</if>
                <if test="YAOPINBZYBZ != null">#{YAOPINBZYBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUANBINGQBZ != null">#{ZHUANBINGQBZ,jdbcType=DECIMAL},</if>
                <if test="MUQINZYID != null">#{MUQINZYID,jdbcType=VARCHAR},</if>
                <if test="CHANFUBZ != null">#{CHANFUBZ,jdbcType=DECIMAL},</if>
                <if test="LINCHUANGLJBZ != null">#{LINCHUANGLJBZ,jdbcType=DECIMAL},</if>
                <if test="QUXIAORYBZ != null">#{QUXIAORYBZ,jdbcType=DECIMAL},</if>
                <if test="QINGJIABZ != null">#{QINGJIABZ,jdbcType=DECIMAL},</if>
                <if test="YUANBINGRZYID != null">#{YUANBINGRZYID,jdbcType=VARCHAR},</if>
                <if test="LVSETDBZ != null">#{LVSETDBZ,jdbcType=DECIMAL},</if>
                <if test="LVSETDKQRQ != null">#{LVSETDKQRQ,jdbcType=TIMESTAMP},</if>
                <if test="YIBAOMXFJYJLS != null">#{YIBAOMXFJYJLS,jdbcType=DECIMAL},</if>
                <if test="TESHUBZ != null">#{TESHUBZ,jdbcType=DECIMAL},</if>
                <if test="YILIAOKH != null">#{YILIAOKH,jdbcType=VARCHAR},</if>
                <if test="JIESHAOREN != null">#{JIESHAOREN,jdbcType=VARCHAR},</if>
                <if test="YINGERSL != null">#{YINGERSL,jdbcType=DECIMAL},</if>
                <if test="YOUHUILBLB != null">#{YOUHUILBLB,jdbcType=VARCHAR},</if>
                <if test="QUXIAOYCYRQ != null">#{QUXIAOYCYRQ,jdbcType=TIMESTAMP},</if>
                <if test="RUYUANZDDM2 != null">#{RUYUANZDDM2,jdbcType=VARCHAR},</if>
                <if test="RUYUANZDMC2 != null">#{RUYUANZDMC2,jdbcType=VARCHAR},</if>
                <if test="RUYUANZDDM3 != null">#{RUYUANZDDM3,jdbcType=VARCHAR},</if>
                <if test="RUYUANZDMC3 != null">#{RUYUANZDMC3,jdbcType=VARCHAR},</if>
                <if test="YIBAOBRXX != null">#{YIBAOBRXX,jdbcType=VARCHAR},</if>
                <if test="FEIYONGLB2 != null">#{FEIYONGLB2,jdbcType=VARCHAR},</if>
                <if test="FEIYONGXZ2 != null">#{FEIYONGXZ2,jdbcType=VARCHAR},</if>
                <if test="YILIAOJZDJBZ != null">#{YILIAOJZDJBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIFU1YB != null">#{ZHIFU1YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU2YB != null">#{ZHIFU2YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU3YB != null">#{ZHIFU3YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU4YB != null">#{ZHIFU4YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU5YB != null">#{ZHIFU5YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU6YB != null">#{ZHIFU6YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU7YB != null">#{ZHIFU7YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU8YB != null">#{ZHIFU8YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU9YB != null">#{ZHIFU9YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU10YB != null">#{ZHIFU10YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU11YB != null">#{ZHIFU11YB,jdbcType=VARCHAR},</if>
                <if test="ZHIFU12YB != null">#{ZHIFU12YB,jdbcType=VARCHAR},</if>
                <if test="SHUZHI1YB != null">#{SHUZHI1YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI2YB != null">#{SHUZHI2YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI3YB != null">#{SHUZHI3YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI4YB != null">#{SHUZHI4YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI5YB != null">#{SHUZHI5YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI6YB != null">#{SHUZHI6YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI7YB != null">#{SHUZHI7YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI8YB != null">#{SHUZHI8YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI9YB != null">#{SHUZHI9YB,jdbcType=DECIMAL},</if>
                <if test="SHUZHI10YB != null">#{SHUZHI10YB,jdbcType=DECIMAL},</if>
                <if test="YIBAOCYDJBZ != null">#{YIBAOCYDJBZ,jdbcType=DECIMAL},</if>
                <if test="YIZHONGYLBZ != null">#{YIZHONGYLBZ,jdbcType=DECIMAL},</if>
                <if test="YIZHONGYLLB != null">#{YIZHONGYLLB,jdbcType=VARCHAR},</if>
                <if test="YIZHONGYLXYJE != null">#{YIZHONGYLXYJE,jdbcType=DECIMAL},</if>
                <if test="YIZHONGYLBZSM != null">#{YIZHONGYLBZSM,jdbcType=VARCHAR},</if>
                <if test="FENMIANRQ != null">#{FENMIANRQ,jdbcType=TIMESTAMP},</if>
                <if test="BINGFAZHENG != null">#{BINGFAZHENG,jdbcType=VARCHAR},</if>
                <if test="BUZHUKBH != null">#{BUZHUKBH,jdbcType=VARCHAR},</if>
                <if test="YIBAOKH2 != null">#{YIBAOKH2,jdbcType=VARCHAR},</if>
                <if test="IC2 != null">#{IC2,jdbcType=VARCHAR},</if>
                <if test="QUXIAOYCYYY != null">#{QUXIAOYCYYY,jdbcType=VARCHAR},</if>
                <if test="BEIZHU != null">#{BEIZHU,jdbcType=VARCHAR},</if>
                <if test="ZERENHS != null">#{ZERENHS,jdbcType=VARCHAR},</if>
                <if test="ZERENHSXM != null">#{ZERENHSXM,jdbcType=VARCHAR},</if>
                <if test="JIUFENBRBZ != null">#{JIUFENBRBZ,jdbcType=DECIMAL},</if>
                <if test="ZHENGJIANLX != null">#{ZHENGJIANLX,jdbcType=VARCHAR},</if>
                <if test="JIANHURENSFZH != null">#{JIANHURENSFZH,jdbcType=VARCHAR},</if>
                <if test="GELILX != null">#{GELILX,jdbcType=VARCHAR},</if>
                <if test="LASTYUCHUYRQ != null">#{LASTYUCHUYRQ,jdbcType=TIMESTAMP},</if>
                <if test="LASTCHUYUANRQ != null">#{LASTCHUYUANRQ,jdbcType=TIMESTAMP},</if>
                <if test="YURUYUANBZ != null">#{YURUYUANBZ,jdbcType=DECIMAL},</if>
                <if test="RUYUANDJLX != null">#{RUYUANDJLX,jdbcType=VARCHAR},</if>
                <if test="XIANQU != null">#{XIANQU,jdbcType=VARCHAR},</if>
                <if test="NIANLING1 != null">#{NIANLING1,jdbcType=DECIMAL},</if>
                <if test="NIANLINGDW1 != null">#{NIANLINGDW1,jdbcType=VARCHAR},</if>
                <if test="GONGSHANGKFDJH != null">#{GONGSHANGKFDJH,jdbcType=VARCHAR},</if>
                <if test="GONGSHANGKFRQ != null">#{GONGSHANGKFRQ,jdbcType=TIMESTAMP},</if>
                <if test="LINCHUANGLJDBZBZ != null">#{LINCHUANGLJDBZBZ,jdbcType=DECIMAL},</if>
                <if test="SHENGAO != null">#{SHENGAO,jdbcType=DECIMAL},</if>
                <if test="TIZHONG != null">#{TIZHONG,jdbcType=DECIMAL},</if>
                <if test="ADLSCORE != null">#{ADLSCORE,jdbcType=VARCHAR},</if>
                <if test="SHENHEREN != null">#{SHENHEREN,jdbcType=VARCHAR},</if>
                <if test="SHENHERQ != null">#{SHENHERQ,jdbcType=TIMESTAMP},</if>
                <if test="GUAZHANGBZ != null">#{GUAZHANGBZ,jdbcType=DECIMAL},</if>
                <if test="ZHENGJIANSMWJ != null">#{ZHENGJIANSMWJ,jdbcType=VARCHAR},</if>
                <if test="BAOMIJB != null">#{BAOMIJB,jdbcType=VARCHAR},</if>
                <if test="HUKOUDZ != null">#{HUKOUDZ,jdbcType=VARCHAR},</if>
                <if test="HUKOUSF != null">#{HUKOUSF,jdbcType=VARCHAR},</if>
                <if test="HUKOUSDQ != null">#{HUKOUSDQ,jdbcType=VARCHAR},</if>
                <if test="HUKOUXQ != null">#{HUKOUXQ,jdbcType=VARCHAR},</if>
                <if test="HUKOUXZJD != null">#{HUKOUXZJD,jdbcType=VARCHAR},</if>
                <if test="JIATINGDZLB != null">#{JIATINGDZLB,jdbcType=VARCHAR},</if>
                <if test="HUKOUDZLB != null">#{HUKOUDZLB,jdbcType=VARCHAR},</if>
                <if test="JIANKANGKH != null">#{JIANKANGKH,jdbcType=VARCHAR},</if>
                <if test="ZHAOSHIZHBZ != null">#{ZHAOSHIZHBZ,jdbcType=DECIMAL},</if>
                <if test="JIANKANGKXX != null">#{JIANKANGKXX,jdbcType=VARCHAR},</if>
                <if test="RIJIANZYBZ != null">#{RIJIANZYBZ,jdbcType=DECIMAL},</if>
                <if test="CHUANGWEIZYBZ != null">#{CHUANGWEIZYBZ,jdbcType=DECIMAL},</if>
                <if test="HUSHIZHANG != null">#{HUSHIZHANG,jdbcType=VARCHAR},</if>
                <if test="HUSHIZXM != null">#{HUSHIZXM,jdbcType=VARCHAR},</if>
                <if test="JIZHENYXBZ != null">#{JIZHENYXBZ,jdbcType=DECIMAL},</if>
                <if test="YUCHUYXTSJ != null">#{YUCHUYXTSJ,jdbcType=TIMESTAMP},</if>
                <if test="TIBIAOMJ != null">#{TIBIAOMJ,jdbcType=DECIMAL},</if>
                <if test="YIWAISHBZ != null">#{YIWAISHBZ,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGDSF != null">#{CHUSHENGDSF,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGDSDQ != null">#{CHUSHENGDSDQ,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGDXQ != null">#{CHUSHENGDXQ,jdbcType=VARCHAR},</if>
                <if test="JIGUANSF != null">#{JIGUANSF,jdbcType=VARCHAR},</if>
                <if test="JIGUANSDQ != null">#{JIGUANSDQ,jdbcType=VARCHAR},</if>
                <if test="XIANZHUZSF != null">#{XIANZHUZSF,jdbcType=VARCHAR},</if>
                <if test="XIANZHUZSDQ != null">#{XIANZHUZSDQ,jdbcType=VARCHAR},</if>
                <if test="XIANZHUZXQ != null">#{XIANZHUZXQ,jdbcType=VARCHAR},</if>
                <if test="XIANZHUZQT != null">#{XIANZHUZQT,jdbcType=VARCHAR},</if>
                <if test="HUKOUDZQT != null">#{HUKOUDZQT,jdbcType=VARCHAR},</if>
                <if test="XIAOXILY != null">#{XIAOXILY,jdbcType=VARCHAR},</if>
                <if test="WANGLUOFWBZ != null">#{WANGLUOFWBZ,jdbcType=DECIMAL},</if>
                <if test="DABINGBZ != null">#{DABINGBZ,jdbcType=DECIMAL},</if>
                <if test="SHIFOUCSSFZ != null">#{SHIFOUCSSFZ,jdbcType=DECIMAL},</if>
                <if test="YUNCI != null">#{YUNCI,jdbcType=VARCHAR},</if>
                <if test="CHANCI != null">#{CHANCI,jdbcType=VARCHAR},</if>
                <if test="TAICI != null">#{TAICI,jdbcType=VARCHAR},</if>
                <if test="YUCHANQI != null">#{YUCHANQI,jdbcType=VARCHAR},</if>
                <if test="QUYUBH != null">#{QUYUBH,jdbcType=VARCHAR},</if>
                <if test="MOCIYJ != null">#{MOCIYJ,jdbcType=VARCHAR},</if>
                <if test="MIANFEIQLX != null">#{MIANFEIQLX,jdbcType=VARCHAR},</if>
                <if test="HUKOUYB != null">#{HUKOUYB,jdbcType=VARCHAR},</if>
                <if test="SHIBAZDBDM != null">#{SHIBAZDBDM,jdbcType=VARCHAR},</if>
                <if test="MOCIYUEJINGSJ != null">#{MOCIYUEJINGSJ,jdbcType=TIMESTAMP},</if>
                <if test="JSMOCIYJSJ != null">#{JSMOCIYJSJ,jdbcType=TIMESTAMP},</if>
                <if test="YUNZHOU != null">#{YUNZHOU,jdbcType=DECIMAL},</if>
                <if test="YUNTIAN != null">#{YUNTIAN,jdbcType=DECIMAL},</if>
                <if test="TAICHANCI != null">#{TAICHANCI,jdbcType=VARCHAR},</if>
                <if test="BUSUIFANGBZ != null">#{BUSUIFANGBZ,jdbcType=DECIMAL},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=VARCHAR},</if>
                <if test="SHIFOUSQQFX != null">#{SHIFOUSQQFX,jdbcType=VARCHAR},</if>
                <if test="SHANSHIFYBZ != null">#{SHANSHIFYBZ,jdbcType=DECIMAL},</if>
                <if test="SFJSFLZBZ != null">#{SFJSFLZBZ,jdbcType=DECIMAL},</if>
                <if test="BURUJINGLY != null">#{BURUJINGLY,jdbcType=VARCHAR},</if>
                <if test="YILIANTBZ != null">#{YILIANTBZ,jdbcType=VARCHAR},</if>
                <if test="YILIANTBH != null">#{YILIANTBH,jdbcType=VARCHAR},</if>
                <if test="YILIANTSQDID != null">#{YILIANTSQDID,jdbcType=VARCHAR},</if>
                <if test="ZHURENYS != null">#{ZHURENYS,jdbcType=VARCHAR},</if>
                <if test="ZHURENYSXM != null">#{ZHURENYSXM,jdbcType=VARCHAR},</if>
                <if test="SHENGYUBZ != null">#{SHENGYUBZ,jdbcType=DECIMAL},</if>
                <if test="BIAOZHI120 != null">#{BIAOZHI120,jdbcType=DECIMAL},</if>
                <if test="YINANBRBZ != null">#{YINANBRBZ,jdbcType=DECIMAL},</if>
                <if test="RUYUANYY != null">#{RUYUANYY,jdbcType=VARCHAR},</if>
                <if test="ZAICIRYLX != null">#{ZAICIRYLX,jdbcType=DECIMAL},</if>
                <if test="ZHUANZHENDH != null">#{ZHUANZHENDH,jdbcType=VARCHAR},</if>
                <if test="YUANRUKERQ != null">#{YUANRUKERQ,jdbcType=TIMESTAMP},</if>
                <if test="YINGERCWFJZZT != null">#{YINGERCWFJZZT,jdbcType=DECIMAL},</if>
                <if test="YINGERCWFJZTZRQ != null">#{YINGERCWFJZTZRQ,jdbcType=TIMESTAMP},</if>
                <if test="YINGERCWFJZTZR != null">#{YINGERCWFJZTZR,jdbcType=VARCHAR},</if>
                <if test="KUAISURYBZ != null">#{KUAISURYBZ,jdbcType=DECIMAL},</if>
                <if test="SHENFENZBXYY != null">#{SHENFENZBXYY,jdbcType=VARCHAR},</if>
                <if test="YDRYBZ != null">#{YDRYBZ,jdbcType=VARCHAR},</if>
                <if test="TCQBZ != null">#{TCQBZ,jdbcType=VARCHAR},</if>
                <if test="SENLINFHBRBZ != null">#{SENLINFHBRBZ,jdbcType=DECIMAL},</if>
                <if test="SENLINFHSPRQ != null">#{SENLINFHSPRQ,jdbcType=TIMESTAMP},</if>
                <if test="SENLINFHBZ != null">#{SENLINFHBZ,jdbcType=VARCHAR},</if>
                <if test="MPI != null">#{MPI,jdbcType=VARCHAR},</if>
                <if test="SHOUCIRKR != null">#{SHOUCIRKR,jdbcType=VARCHAR},</if>
                <if test="SHOUCIRKRXM != null">#{SHOUCIRKRXM,jdbcType=VARCHAR},</if>
                <if test="JIUZHENQRRQ != null">#{JIUZHENQRRQ,jdbcType=TIMESTAMP},</if>
                <if test="JIUZHENQRBZ != null">#{JIUZHENQRBZ,jdbcType=DECIMAL},</if>
                <if test="QUERENSFRQ_HIS != null">#{QUERENSFRQ_HIS,jdbcType=TIMESTAMP},</if>
                <if test="QUERENSFBZ_HIS != null">#{QUERENSFBZ_HIS,jdbcType=DECIMAL},</if>
                <if test="DENGJISY != null">#{DENGJISY,jdbcType=VARCHAR},</if>
                <if test="YUNYIXYED != null">#{YUNYIXYED,jdbcType=VARCHAR},</if>
                <if test="YUNYIQYBZ != null">#{YUNYIQYBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUANDRYELB != null">#{ZHUANDRYELB,jdbcType=VARCHAR},</if>
                <if test="LIUGUANBZ != null">#{LIUGUANBZ,jdbcType=DECIMAL},</if>
                <if test="LIUGUANSQDID != null">#{LIUGUANSQDID,jdbcType=VARCHAR},</if>
                <if test="JIUZHENID != null">#{JIUZHENID,jdbcType=VARCHAR},</if>
                <if test="FENZHENSJ != null">#{FENZHENSJ,jdbcType=TIMESTAMP},</if>
                <if test="LAIYUAN != null">#{LAIYUAN,jdbcType=VARCHAR},</if>
                <if test="FENZHENYUAN != null">#{FENZHENYUAN,jdbcType=VARCHAR},</if>
                <if test="FENZHENYUANID != null">#{FENZHENYUANID,jdbcType=VARCHAR},</if>
                <if test="FENZHENLB != null">#{FENZHENLB,jdbcType=VARCHAR},</if>
                <if test="SHIFUTZRQ != null">#{SHIFUTZRQ,jdbcType=DECIMAL},</if>
                <if test="DIANHUA != null">#{DIANHUA,jdbcType=VARCHAR},</if>
                <if test="XINGUANBZ != null">#{XINGUANBZ,jdbcType=DECIMAL},</if>
                <if test="SANDAZXBZ != null">#{SANDAZXBZ,jdbcType=DECIMAL},</if>
                <if test="QITABZNR != null">#{QITABZNR,jdbcType=VARCHAR},</if>
                <if test="XINBINGRENZYID != null">#{XINBINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="LIUGUANBRZYID != null">#{LIUGUANBRZYID,jdbcType=VARCHAR},</if>
                <if test="XIANZHUZHI != null">#{XIANZHUZHI,jdbcType=VARCHAR},</if>
                <if test="BINGANSBBZ != null">#{BINGANSBBZ,jdbcType=DECIMAL},</if>
                <if test="SHUANGXIANGJZBZ != null">#{SHUANGXIANGJZBZ,jdbcType=DECIMAL},</if>
                <if test="ZHONGZHUANKS != null">#{ZHONGZHUANKS,jdbcType=VARCHAR},</if>
                <if test="ZHONGZHUANKEMC != null">#{ZHONGZHUANKEMC,jdbcType=VARCHAR},</if>
                <if test="DIEDAOPGBZ != null">#{DIEDAOPGBZ,jdbcType=DECIMAL},</if>
                <if test="DISANFBZJ != null">#{DISANFBZJ,jdbcType=VARCHAR},</if>
                <if test="BINGRENQX != null">#{BINGRENQX,jdbcType=VARCHAR},</if>
                <if test="FENZHENJB != null">#{FENZHENJB,jdbcType=VARCHAR},</if>
                <if test="YOUXIAOZDMC != null">#{YOUXIAOZDMC,jdbcType=VARCHAR},</if>
                <if test="MINZHENGXX != null">#{MINZHENGXX,jdbcType=VARCHAR},</if>
                <if test="BINGQINGZY != null">#{BINGQINGZY,jdbcType=VARCHAR},</if>
                <if test="YANKECS != null">#{YANKECS,jdbcType=DECIMAL},</if>
                <if test="QITAYLXTBRBZ != null">#{QITAYLXTBRBZ,jdbcType=DECIMAL},</if>
                <if test="GCPLX != null">#{GCPLX,jdbcType=VARCHAR},</if>
                <if test="BUSHIYDBZYY != null">#{BUSHIYDBZYY,jdbcType=VARCHAR},</if>
                <if test="SHIFOUGZ != null">#{SHIFOUGZ,jdbcType=DECIMAL},</if>
                <if test="EMPI_YJ != null">#{EMPI_YJ,jdbcType=VARCHAR},</if>
                <if test="SANJIYS != null">#{SANJIYS,jdbcType=VARCHAR},</if>
                <if test="SANJIYSMC != null">#{SANJIYSMC,jdbcType=VARCHAR},</if>
                <if test="KEZHURYS != null">#{KEZHURYS,jdbcType=VARCHAR},</if>
                <if test="KEZHURYSXM != null">#{KEZHURYSXM,jdbcType=VARCHAR},</if>
                <if test="ERJIYS != null">#{ERJIYS,jdbcType=VARCHAR},</if>
                <if test="ERJIYSMC != null">#{ERJIYSMC,jdbcType=VARCHAR},</if>
                <if test="YIJIYS != null">#{YIJIYS,jdbcType=VARCHAR},</if>
                <if test="YIJIYSMC != null">#{YIJIYSMC,jdbcType=VARCHAR},</if>
                <if test="SHANGCICKSJ != null">#{SHANGCICKSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZONGHEBFBZ != null">#{ZONGHEBFBZ,jdbcType=DECIMAL},</if>
                <if test="ZONGHEBQ != null">#{ZONGHEBQ,jdbcType=VARCHAR},</if>
                <if test="ZONGHEBQCW != null">#{ZONGHEBQCW,jdbcType=VARCHAR},</if>
                <if test="ZONGHEBFJRSJ != null">#{ZONGHEBFJRSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZONGHEBQMC != null">#{ZONGHEBQMC,jdbcType=VARCHAR},</if>
                <if test="QIANTIANJH != null">#{QIANTIANJH,jdbcType=DECIMAL},</if>
                <if test="ZHOUQI != null">#{ZHOUQI,jdbcType=VARCHAR},</if>
                <if test="JINGQI != null">#{JINGQI,jdbcType=VARCHAR},</if>
                <if test="YXJM != null">#{YXJM,jdbcType=DECIMAL},</if>
                <if test="XINXITBZ != null">#{XINXITBZ,jdbcType=DECIMAL},</if>
                <if test="CHUANGTOUKDYBZ != null">#{CHUANGTOUKDYBZ,jdbcType=DECIMAL},</if>
                <if test="FLSBZ != null">#{FLSBZ,jdbcType=DECIMAL},</if>
                <if test="XINGBIEDM != null">#{XINGBIEDM,jdbcType=VARCHAR},</if>
                <if test="HUNYINDM != null">#{HUNYINDM,jdbcType=VARCHAR},</if>
                <if test="ZHIYEDM != null">#{ZHIYEDM,jdbcType=VARCHAR},</if>
                <if test="GUOJIDM != null">#{GUOJIDM,jdbcType=VARCHAR},</if>
                <if test="MINZUDM != null">#{MINZUDM,jdbcType=VARCHAR},</if>
                <if test="SHENGFENDM != null">#{SHENGFENDM,jdbcType=VARCHAR},</if>
                <if test="JIGUANDM != null">#{JIGUANDM,jdbcType=VARCHAR},</if>
                <if test="CHUSHENGDDM != null">#{CHUSHENGDDM,jdbcType=VARCHAR},</if>
                <if test="JIHUASYFWZH != null">#{JIHUASYFWZH,jdbcType=VARCHAR},</if>
                <if test="SHENGYULB != null">#{SHENGYULB,jdbcType=VARCHAR},</if>
                <if test="WANYUBZ != null">#{WANYUBZ,jdbcType=DECIMAL},</if>
                <if test="ZAOCHANBZ != null">#{ZAOCHANBZ,jdbcType=DECIMAL},</if>
                <if test="JIHUASYSSLB != null">#{JIHUASYSSLB,jdbcType=VARCHAR},</if>
                <if test="XUELI != null">#{XUELI,jdbcType=VARCHAR},</if>
                <if test="ZAIRUYHZBZ != null">#{ZAIRUYHZBZ,jdbcType=DECIMAL},</if>
                <if test="XUETANGSQZ != null">#{XUETANGSQZ,jdbcType=VARCHAR},</if>
                <if test="XUETANGSQZT != null">#{XUETANGSQZT,jdbcType=VARCHAR},</if>
                <if test="SHAICHADQ != null">#{SHAICHADQ,jdbcType=VARCHAR},</if>
                <if test="XIANXUEZBZ != null">#{XIANXUEZBZ,jdbcType=DECIMAL},</if>
                <if test="JCPTEMPI != null">#{JCPTEMPI,jdbcType=VARCHAR},</if>
                <if test="IPT_PSN_SP_FLAG_DETL_ID != null">#{IPT_PSN_SP_FLAG_DETL_ID,jdbcType=VARCHAR},</if>
                <if test="LINCHUANGLJID != null">#{LINCHUANGLJID,jdbcType=VARCHAR},</if>
                <if test="SHIFOUYXZZJS != null">#{SHIFOUYXZZJS,jdbcType=VARCHAR},</if>
                <if test="PEIKEKH != null">#{PEIKEKH,jdbcType=VARCHAR},</if>
                <if test="KANGFUSQZT != null">#{KANGFUSQZT,jdbcType=VARCHAR},</if>
                <if test="KANGFUZLS != null">#{KANGFUZLS,jdbcType=VARCHAR},</if>
                <if test="KANGFUZLSXM != null">#{KANGFUZLSXM,jdbcType=VARCHAR},</if>
                <if test="CHANGHUXBRBZ != null">#{CHANGHUXBRBZ,jdbcType=DECIMAL},</if>
                <if test="YOUFUBRBZ != null">#{YOUFUBRBZ,jdbcType=DECIMAL},</if>
                <if test="LIANXIREN2 != null">#{LIANXIREN2,jdbcType=VARCHAR},</if>
                <if test="LIANXIRDH2 != null">#{LIANXIRDH2,jdbcType=VARCHAR},</if>
                <if test="GUANXI2 != null">#{GUANXI2,jdbcType=VARCHAR},</if>
                <if test="YUANQIANBQID != null">#{YUANQIANBQID,jdbcType=VARCHAR},</if>
                <if test="ZHUGUANYS != null">#{ZHUGUANYS,jdbcType=VARCHAR},</if>
                <if test="LIUQIANGZKBZ != null">#{LIUQIANGZKBZ,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.ZyBingrenxx">
        update ZY_BINGRENXX
        <set>
                <if test="SHEBAOBH != null">
                    SHEBAOBH = #{SHEBAOBH,jdbcType=VARCHAR},
                </if>
                <if test="YIBAOKH != null">
                    YIBAOKH = #{YIBAOKH,jdbcType=VARCHAR},
                </if>
                <if test="JIUZHENKH != null">
                    JIUZHENKH = #{JIUZHENKH,jdbcType=VARCHAR},
                </if>
                <if test="SHEQUBH != null">
                    SHEQUBH = #{SHEQUBH,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGLB != null">
                    FEIYONGLB = #{FEIYONGLB,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGXZ != null">
                    FEIYONGXZ = #{FEIYONGXZ,jdbcType=VARCHAR},
                </if>
                <if test="YOUHUILB != null">
                    YOUHUILB = #{YOUHUILB,jdbcType=VARCHAR},
                </if>
                <if test="GONGFEIZH != null">
                    GONGFEIZH = #{GONGFEIZH,jdbcType=VARCHAR},
                </if>
                <if test="GONGFEIDW != null">
                    GONGFEIDW = #{GONGFEIDW,jdbcType=VARCHAR},
                </if>
                <if test="GONGFEIDWMC != null">
                    GONGFEIDWMC = #{GONGFEIDWMC,jdbcType=VARCHAR},
                </if>
                <if test="XINGMING != null">
                    XINGMING = #{XINGMING,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA1 != null">
                    SHURUMA1 = #{SHURUMA1,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA2 != null">
                    SHURUMA2 = #{SHURUMA2,jdbcType=VARCHAR},
                </if>
                <if test="SHURUMA3 != null">
                    SHURUMA3 = #{SHURUMA3,jdbcType=VARCHAR},
                </if>
                <if test="XINGBIE != null">
                    XINGBIE = #{XINGBIE,jdbcType=VARCHAR},
                </if>
                <if test="NIANLINGDW != null">
                    NIANLINGDW = #{NIANLINGDW,jdbcType=VARCHAR},
                </if>
                <if test="SHENFENZH != null">
                    SHENFENZH = #{SHENFENZH,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGRQ != null">
                    CHUSHENGRQ = #{CHUSHENGRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="GONGZUODW != null">
                    GONGZUODW = #{GONGZUODW,jdbcType=VARCHAR},
                </if>
                <if test="DANWEIDH != null">
                    DANWEIDH = #{DANWEIDH,jdbcType=VARCHAR},
                </if>
                <if test="DANWEIYB != null">
                    DANWEIYB = #{DANWEIYB,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGDZ != null">
                    JIATINGDZ = #{JIATINGDZ,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGDH != null">
                    JIATINGDH = #{JIATINGDH,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGYB != null">
                    JIATINGYB = #{JIATINGYB,jdbcType=VARCHAR},
                </if>
                <if test="XUEXING != null">
                    XUEXING = #{XUEXING,jdbcType=VARCHAR},
                </if>
                <if test="HUNYIN != null">
                    HUNYIN = #{HUNYIN,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYE != null">
                    ZHIYE = #{ZHIYE,jdbcType=VARCHAR},
                </if>
                <if test="GUOJI != null">
                    GUOJI = #{GUOJI,jdbcType=VARCHAR},
                </if>
                <if test="MINZU != null">
                    MINZU = #{MINZU,jdbcType=VARCHAR},
                </if>
                <if test="SHENGFEN != null">
                    SHENGFEN = #{SHENGFEN,jdbcType=VARCHAR},
                </if>
                <if test="XIANGZHENJD != null">
                    XIANGZHENJD = #{XIANGZHENJD,jdbcType=VARCHAR},
                </if>
                <if test="SHIDIQU != null">
                    SHIDIQU = #{SHIDIQU,jdbcType=VARCHAR},
                </if>
                <if test="JIGUAN != null">
                    JIGUAN = #{JIGUAN,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGDI != null">
                    CHUSHENGDI = #{CHUSHENGDI,jdbcType=VARCHAR},
                </if>
                <if test="YOUBIAN != null">
                    YOUBIAN = #{YOUBIAN,jdbcType=VARCHAR},
                </if>
                <if test="LIANXIREN != null">
                    LIANXIREN = #{LIANXIREN,jdbcType=VARCHAR},
                </if>
                <if test="GUANXI != null">
                    GUANXI = #{GUANXI,jdbcType=VARCHAR},
                </if>
                <if test="LIANXIRDZ != null">
                    LIANXIRDZ = #{LIANXIRDZ,jdbcType=VARCHAR},
                </if>
                <if test="LIANXIRDH != null">
                    LIANXIRDH = #{LIANXIRDH,jdbcType=VARCHAR},
                </if>
                <if test="LIANXIRYB != null">
                    LIANXIRYB = #{LIANXIRYB,jdbcType=VARCHAR},
                </if>
                <if test="JIWANGSHI != null">
                    JIWANGSHI = #{JIWANGSHI,jdbcType=VARCHAR},
                </if>
                <if test="GUOMINSHI != null">
                    GUOMINSHI = #{GUOMINSHI,jdbcType=VARCHAR},
                </if>
                <if test="QUYU != null">
                    QUYU = #{QUYU,jdbcType=VARCHAR},
                </if>
                <if test="WAIDIBRBZ != null">
                    WAIDIBRBZ = #{WAIDIBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIANDANGREN != null">
                    JIANDANGREN = #{JIANDANGREN,jdbcType=VARCHAR},
                </if>
                <if test="JIANDANGRQ != null">
                    JIANDANGRQ = #{JIANDANGRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YINGERBZ != null">
                    YINGERBZ = #{YINGERBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUYUANCS != null">
                    ZHUYUANCS = #{ZHUYUANCS,jdbcType=DECIMAL},
                </if>
                <if test="RUYUANRQ != null">
                    RUYUANRQ = #{RUYUANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUCHUYRQ != null">
                    YUCHUYRQ = #{YUCHUYRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUYUANRQ != null">
                    CHUYUANRQ = #{CHUYUANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZAIYUANZT != null">
                    ZAIYUANZT = #{ZAIYUANZT,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANTJ != null">
                    RUYUANTJ = #{RUYUANTJ,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANKS != null">
                    RUYUANKS = #{RUYUANKS,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANBQ != null">
                    RUYUANBQ = #{RUYUANBQ,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANCW != null">
                    RUYUANCW = #{RUYUANCW,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANKS != null">
                    DANGQIANKS = #{DANGQIANKS,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANBQ != null">
                    DANGQIANBQ = #{DANGQIANBQ,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANCW != null">
                    DANGQIANCW = #{DANGQIANCW,jdbcType=VARCHAR},
                </if>
                <if test="GUANLIKS != null">
                    GUANLIKS = #{GUANLIKS,jdbcType=VARCHAR},
                </if>
                <if test="JIECHUANGBZ != null">
                    JIECHUANGBZ = #{JIECHUANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="LIYUANQX != null">
                    LIYUANQX = #{LIYUANQX,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENZDDM != null">
                    MENZHENZDDM = #{MENZHENZDDM,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENZDMC != null">
                    MENZHENZDMC = #{MENZHENZDMC,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZDDM != null">
                    RUYUANZDDM = #{RUYUANZDDM,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZDMC != null">
                    RUYUANZDMC = #{RUYUANZDMC,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDDM != null">
                    CHUYUANZDDM = #{CHUYUANZDDM,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDMC != null">
                    CHUYUANZDMC = #{CHUYUANZDMC,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDDM2 != null">
                    CHUYUANZDDM2 = #{CHUYUANZDDM2,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDMC2 != null">
                    CHUYUANZDMC2 = #{CHUYUANZDMC2,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDMC3 != null">
                    CHUYUANZDMC3 = #{CHUYUANZDMC3,jdbcType=VARCHAR},
                </if>
                <if test="CHUYUANZDDM3 != null">
                    CHUYUANZDDM3 = #{CHUYUANZDDM3,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGBCBZ != null">
                    JIATINGBCBZ = #{JIATINGBCBZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGQING != null">
                    BINGQING = #{BINGQING,jdbcType=VARCHAR},
                </if>
                <if test="FENMIAN != null">
                    FENMIAN = #{FENMIAN,jdbcType=VARCHAR},
                </if>
                <if test="SHANGCHUANBZ != null">
                    SHANGCHUANBZ = #{SHANGCHUANBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHANGCHUANRQ != null">
                    SHANGCHUANRQ = #{SHANGCHUANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="DANBAOREN != null">
                    DANBAOREN = #{DANBAOREN,jdbcType=VARCHAR},
                </if>
                <if test="DANBAOJE != null">
                    DANBAOJE = #{DANBAOJE,jdbcType=DECIMAL},
                </if>
                <if test="JIAZHANGXM != null">
                    JIAZHANGXM = #{JIAZHANGXM,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANHAO != null">
                    ZHUYUANHAO = #{ZHUYUANHAO,jdbcType=VARCHAR},
                </if>
                <if test="BINGANHAO != null">
                    BINGANHAO = #{BINGANHAO,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="JIESUANXH != null">
                    JIESUANXH = #{JIESUANXH,jdbcType=DECIMAL},
                </if>
                <if test="NIANLING != null">
                    NIANLING = #{NIANLING,jdbcType=DECIMAL},
                </if>
                <if test="MENZHENYS != null">
                    MENZHENYS = #{MENZHENYS,jdbcType=VARCHAR},
                </if>
                <if test="SHOUZHIYS != null">
                    SHOUZHIYS = #{SHOUZHIYS,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANYS != null">
                    ZHUYUANYS = #{ZHUYUANYS,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYS != null">
                    ZHUZHIYS = #{ZHUZHIYS,jdbcType=VARCHAR},
                </if>
                <if test="YUYUEID != null">
                    YUYUEID = #{YUYUEID,jdbcType=VARCHAR},
                </if>
                <if test="SHENHEBZ != null">
                    SHENHEBZ = #{SHENHEBZ,jdbcType=DECIMAL},
                </if>
                <if test="QIGUANYZBZ != null">
                    QIGUANYZBZ = #{QIGUANYZBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIAOYILSH != null">
                    JIAOYILSH = #{JIAOYILSH,jdbcType=VARCHAR},
                </if>
                <if test="GERENBH != null">
                    GERENBH = #{GERENBH,jdbcType=VARCHAR},
                </if>
                <if test="IC != null">
                    IC = #{IC,jdbcType=VARCHAR},
                </if>
                <if test="SHIMINKWKH != null">
                    SHIMINKWKH = #{SHIMINKWKH,jdbcType=VARCHAR},
                </if>
                <if test="YILIAOZID != null">
                    YILIAOZID = #{YILIAOZID,jdbcType=VARCHAR},
                </if>
                <if test="YILIAOZM != null">
                    YILIAOZM = #{YILIAOZM,jdbcType=VARCHAR},
                </if>
                <if test="RUKERQ != null">
                    RUKERQ = #{RUKERQ,jdbcType=TIMESTAMP},
                </if>
                <if test="RUKEBZ != null">
                    RUKEBZ = #{RUKEBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHANCHUANGBZ != null">
                    ZHANCHUANGBZ = #{ZHANCHUANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="RUYUANKSMC != null">
                    RUYUANKSMC = #{RUYUANKSMC,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANBQMC != null">
                    RUYUANBQMC = #{RUYUANBQMC,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANKSMC != null">
                    DANGQIANKSMC = #{DANGQIANKSMC,jdbcType=VARCHAR},
                </if>
                <if test="DANGQIANBQMC != null">
                    DANGQIANBQMC = #{DANGQIANBQMC,jdbcType=VARCHAR},
                </if>
                <if test="GUANLIKSMC != null">
                    GUANLIKSMC = #{GUANLIKSMC,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENYSXM != null">
                    MENZHENYSXM = #{MENZHENYSXM,jdbcType=VARCHAR},
                </if>
                <if test="SHOUZHIYSXM != null">
                    SHOUZHIYSXM = #{SHOUZHIYSXM,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANYSXM != null">
                    ZHUYUANYSXM = #{ZHUYUANYSXM,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYSXM != null">
                    ZHUZHIYSXM = #{ZHUZHIYSXM,jdbcType=VARCHAR},
                </if>
                <if test="BINGLISLZT != null">
                    BINGLISLZT = #{BINGLISLZT,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGXZZHBZ != null">
                    FEIYONGXZZHBZ = #{FEIYONGXZZHBZ,jdbcType=DECIMAL},
                </if>
                <if test="FEIYONGXZZHR != null">
                    FEIYONGXZZHR = #{FEIYONGXZZHR,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGXZZHRQ != null">
                    FEIYONGXZZHRQ = #{FEIYONGXZZHRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YIBAOYLLB != null">
                    YIBAOYLLB = #{YIBAOYLLB,jdbcType=VARCHAR},
                </if>
                <if test="TESHUBZBZ != null">
                    TESHUBZBZ = #{TESHUBZBZ,jdbcType=DECIMAL},
                </if>
                <if test="TESHUBZMC != null">
                    TESHUBZMC = #{TESHUBZMC,jdbcType=VARCHAR},
                </if>
                <if test="TESHUBZBM != null">
                    TESHUBZBM = #{TESHUBZBM,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENZYZDDM != null">
                    MENZHENZYZDDM = #{MENZHENZYZDDM,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENZYZDMC != null">
                    MENZHENZYZDMC = #{MENZHENZYZDMC,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZYZDDM != null">
                    RUYUANZYZDDM = #{RUYUANZYZDDM,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZYZDMC != null">
                    RUYUANZYZDMC = #{RUYUANZYZDMC,jdbcType=VARCHAR},
                </if>
                <if test="WAIYUANZDBZ != null">
                    WAIYUANZDBZ = #{WAIYUANZDBZ,jdbcType=DECIMAL},
                </if>
                <if test="RUYUANZYZHDM != null">
                    RUYUANZYZHDM = #{RUYUANZYZHDM,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZYZHMC != null">
                    RUYUANZYZHMC = #{RUYUANZYZHMC,jdbcType=VARCHAR},
                </if>
                <if test="YAOPINBZYBZ != null">
                    YAOPINBZYBZ = #{YAOPINBZYBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUANBINGQBZ != null">
                    ZHUANBINGQBZ = #{ZHUANBINGQBZ,jdbcType=DECIMAL},
                </if>
                <if test="MUQINZYID != null">
                    MUQINZYID = #{MUQINZYID,jdbcType=VARCHAR},
                </if>
                <if test="CHANFUBZ != null">
                    CHANFUBZ = #{CHANFUBZ,jdbcType=DECIMAL},
                </if>
                <if test="LINCHUANGLJBZ != null">
                    LINCHUANGLJBZ = #{LINCHUANGLJBZ,jdbcType=DECIMAL},
                </if>
                <if test="QUXIAORYBZ != null">
                    QUXIAORYBZ = #{QUXIAORYBZ,jdbcType=DECIMAL},
                </if>
                <if test="QINGJIABZ != null">
                    QINGJIABZ = #{QINGJIABZ,jdbcType=DECIMAL},
                </if>
                <if test="YUANBINGRZYID != null">
                    YUANBINGRZYID = #{YUANBINGRZYID,jdbcType=VARCHAR},
                </if>
                <if test="LVSETDBZ != null">
                    LVSETDBZ = #{LVSETDBZ,jdbcType=DECIMAL},
                </if>
                <if test="LVSETDKQRQ != null">
                    LVSETDKQRQ = #{LVSETDKQRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YIBAOMXFJYJLS != null">
                    YIBAOMXFJYJLS = #{YIBAOMXFJYJLS,jdbcType=DECIMAL},
                </if>
                <if test="TESHUBZ != null">
                    TESHUBZ = #{TESHUBZ,jdbcType=DECIMAL},
                </if>
                <if test="YILIAOKH != null">
                    YILIAOKH = #{YILIAOKH,jdbcType=VARCHAR},
                </if>
                <if test="JIESHAOREN != null">
                    JIESHAOREN = #{JIESHAOREN,jdbcType=VARCHAR},
                </if>
                <if test="YINGERSL != null">
                    YINGERSL = #{YINGERSL,jdbcType=DECIMAL},
                </if>
                <if test="YOUHUILBLB != null">
                    YOUHUILBLB = #{YOUHUILBLB,jdbcType=VARCHAR},
                </if>
                <if test="QUXIAOYCYRQ != null">
                    QUXIAOYCYRQ = #{QUXIAOYCYRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="RUYUANZDDM2 != null">
                    RUYUANZDDM2 = #{RUYUANZDDM2,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZDMC2 != null">
                    RUYUANZDMC2 = #{RUYUANZDMC2,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZDDM3 != null">
                    RUYUANZDDM3 = #{RUYUANZDDM3,jdbcType=VARCHAR},
                </if>
                <if test="RUYUANZDMC3 != null">
                    RUYUANZDMC3 = #{RUYUANZDMC3,jdbcType=VARCHAR},
                </if>
                <if test="YIBAOBRXX != null">
                    YIBAOBRXX = #{YIBAOBRXX,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGLB2 != null">
                    FEIYONGLB2 = #{FEIYONGLB2,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGXZ2 != null">
                    FEIYONGXZ2 = #{FEIYONGXZ2,jdbcType=VARCHAR},
                </if>
                <if test="YILIAOJZDJBZ != null">
                    YILIAOJZDJBZ = #{YILIAOJZDJBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIFU1YB != null">
                    ZHIFU1YB = #{ZHIFU1YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU2YB != null">
                    ZHIFU2YB = #{ZHIFU2YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU3YB != null">
                    ZHIFU3YB = #{ZHIFU3YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU4YB != null">
                    ZHIFU4YB = #{ZHIFU4YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU5YB != null">
                    ZHIFU5YB = #{ZHIFU5YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU6YB != null">
                    ZHIFU6YB = #{ZHIFU6YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU7YB != null">
                    ZHIFU7YB = #{ZHIFU7YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU8YB != null">
                    ZHIFU8YB = #{ZHIFU8YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU9YB != null">
                    ZHIFU9YB = #{ZHIFU9YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU10YB != null">
                    ZHIFU10YB = #{ZHIFU10YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU11YB != null">
                    ZHIFU11YB = #{ZHIFU11YB,jdbcType=VARCHAR},
                </if>
                <if test="ZHIFU12YB != null">
                    ZHIFU12YB = #{ZHIFU12YB,jdbcType=VARCHAR},
                </if>
                <if test="SHUZHI1YB != null">
                    SHUZHI1YB = #{SHUZHI1YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI2YB != null">
                    SHUZHI2YB = #{SHUZHI2YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI3YB != null">
                    SHUZHI3YB = #{SHUZHI3YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI4YB != null">
                    SHUZHI4YB = #{SHUZHI4YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI5YB != null">
                    SHUZHI5YB = #{SHUZHI5YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI6YB != null">
                    SHUZHI6YB = #{SHUZHI6YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI7YB != null">
                    SHUZHI7YB = #{SHUZHI7YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI8YB != null">
                    SHUZHI8YB = #{SHUZHI8YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI9YB != null">
                    SHUZHI9YB = #{SHUZHI9YB,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHI10YB != null">
                    SHUZHI10YB = #{SHUZHI10YB,jdbcType=DECIMAL},
                </if>
                <if test="YIBAOCYDJBZ != null">
                    YIBAOCYDJBZ = #{YIBAOCYDJBZ,jdbcType=DECIMAL},
                </if>
                <if test="YIZHONGYLBZ != null">
                    YIZHONGYLBZ = #{YIZHONGYLBZ,jdbcType=DECIMAL},
                </if>
                <if test="YIZHONGYLLB != null">
                    YIZHONGYLLB = #{YIZHONGYLLB,jdbcType=VARCHAR},
                </if>
                <if test="YIZHONGYLXYJE != null">
                    YIZHONGYLXYJE = #{YIZHONGYLXYJE,jdbcType=DECIMAL},
                </if>
                <if test="YIZHONGYLBZSM != null">
                    YIZHONGYLBZSM = #{YIZHONGYLBZSM,jdbcType=VARCHAR},
                </if>
                <if test="FENMIANRQ != null">
                    FENMIANRQ = #{FENMIANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="BINGFAZHENG != null">
                    BINGFAZHENG = #{BINGFAZHENG,jdbcType=VARCHAR},
                </if>
                <if test="BUZHUKBH != null">
                    BUZHUKBH = #{BUZHUKBH,jdbcType=VARCHAR},
                </if>
                <if test="YIBAOKH2 != null">
                    YIBAOKH2 = #{YIBAOKH2,jdbcType=VARCHAR},
                </if>
                <if test="IC2 != null">
                    IC2 = #{IC2,jdbcType=VARCHAR},
                </if>
                <if test="QUXIAOYCYYY != null">
                    QUXIAOYCYYY = #{QUXIAOYCYYY,jdbcType=VARCHAR},
                </if>
                <if test="BEIZHU != null">
                    BEIZHU = #{BEIZHU,jdbcType=VARCHAR},
                </if>
                <if test="ZERENHS != null">
                    ZERENHS = #{ZERENHS,jdbcType=VARCHAR},
                </if>
                <if test="ZERENHSXM != null">
                    ZERENHSXM = #{ZERENHSXM,jdbcType=VARCHAR},
                </if>
                <if test="JIUFENBRBZ != null">
                    JIUFENBRBZ = #{JIUFENBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHENGJIANLX != null">
                    ZHENGJIANLX = #{ZHENGJIANLX,jdbcType=VARCHAR},
                </if>
                <if test="JIANHURENSFZH != null">
                    JIANHURENSFZH = #{JIANHURENSFZH,jdbcType=VARCHAR},
                </if>
                <if test="GELILX != null">
                    GELILX = #{GELILX,jdbcType=VARCHAR},
                </if>
                <if test="LASTYUCHUYRQ != null">
                    LASTYUCHUYRQ = #{LASTYUCHUYRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="LASTCHUYUANRQ != null">
                    LASTCHUYUANRQ = #{LASTCHUYUANRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YURUYUANBZ != null">
                    YURUYUANBZ = #{YURUYUANBZ,jdbcType=DECIMAL},
                </if>
                <if test="RUYUANDJLX != null">
                    RUYUANDJLX = #{RUYUANDJLX,jdbcType=VARCHAR},
                </if>
                <if test="XIANQU != null">
                    XIANQU = #{XIANQU,jdbcType=VARCHAR},
                </if>
                <if test="NIANLING1 != null">
                    NIANLING1 = #{NIANLING1,jdbcType=DECIMAL},
                </if>
                <if test="NIANLINGDW1 != null">
                    NIANLINGDW1 = #{NIANLINGDW1,jdbcType=VARCHAR},
                </if>
                <if test="GONGSHANGKFDJH != null">
                    GONGSHANGKFDJH = #{GONGSHANGKFDJH,jdbcType=VARCHAR},
                </if>
                <if test="GONGSHANGKFRQ != null">
                    GONGSHANGKFRQ = #{GONGSHANGKFRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="LINCHUANGLJDBZBZ != null">
                    LINCHUANGLJDBZBZ = #{LINCHUANGLJDBZBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENGAO != null">
                    SHENGAO = #{SHENGAO,jdbcType=DECIMAL},
                </if>
                <if test="TIZHONG != null">
                    TIZHONG = #{TIZHONG,jdbcType=DECIMAL},
                </if>
                <if test="ADLSCORE != null">
                    ADLSCORE = #{ADLSCORE,jdbcType=VARCHAR},
                </if>
                <if test="SHENHEREN != null">
                    SHENHEREN = #{SHENHEREN,jdbcType=VARCHAR},
                </if>
                <if test="SHENHERQ != null">
                    SHENHERQ = #{SHENHERQ,jdbcType=TIMESTAMP},
                </if>
                <if test="GUAZHANGBZ != null">
                    GUAZHANGBZ = #{GUAZHANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHENGJIANSMWJ != null">
                    ZHENGJIANSMWJ = #{ZHENGJIANSMWJ,jdbcType=VARCHAR},
                </if>
                <if test="BAOMIJB != null">
                    BAOMIJB = #{BAOMIJB,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUDZ != null">
                    HUKOUDZ = #{HUKOUDZ,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUSF != null">
                    HUKOUSF = #{HUKOUSF,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUSDQ != null">
                    HUKOUSDQ = #{HUKOUSDQ,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUXQ != null">
                    HUKOUXQ = #{HUKOUXQ,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUXZJD != null">
                    HUKOUXZJD = #{HUKOUXZJD,jdbcType=VARCHAR},
                </if>
                <if test="JIATINGDZLB != null">
                    JIATINGDZLB = #{JIATINGDZLB,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUDZLB != null">
                    HUKOUDZLB = #{HUKOUDZLB,jdbcType=VARCHAR},
                </if>
                <if test="JIANKANGKH != null">
                    JIANKANGKH = #{JIANKANGKH,jdbcType=VARCHAR},
                </if>
                <if test="ZHAOSHIZHBZ != null">
                    ZHAOSHIZHBZ = #{ZHAOSHIZHBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIANKANGKXX != null">
                    JIANKANGKXX = #{JIANKANGKXX,jdbcType=VARCHAR},
                </if>
                <if test="RIJIANZYBZ != null">
                    RIJIANZYBZ = #{RIJIANZYBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHUANGWEIZYBZ != null">
                    CHUANGWEIZYBZ = #{CHUANGWEIZYBZ,jdbcType=DECIMAL},
                </if>
                <if test="HUSHIZHANG != null">
                    HUSHIZHANG = #{HUSHIZHANG,jdbcType=VARCHAR},
                </if>
                <if test="HUSHIZXM != null">
                    HUSHIZXM = #{HUSHIZXM,jdbcType=VARCHAR},
                </if>
                <if test="JIZHENYXBZ != null">
                    JIZHENYXBZ = #{JIZHENYXBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUCHUYXTSJ != null">
                    YUCHUYXTSJ = #{YUCHUYXTSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="TIBIAOMJ != null">
                    TIBIAOMJ = #{TIBIAOMJ,jdbcType=DECIMAL},
                </if>
                <if test="YIWAISHBZ != null">
                    YIWAISHBZ = #{YIWAISHBZ,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGDSF != null">
                    CHUSHENGDSF = #{CHUSHENGDSF,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGDSDQ != null">
                    CHUSHENGDSDQ = #{CHUSHENGDSDQ,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGDXQ != null">
                    CHUSHENGDXQ = #{CHUSHENGDXQ,jdbcType=VARCHAR},
                </if>
                <if test="JIGUANSF != null">
                    JIGUANSF = #{JIGUANSF,jdbcType=VARCHAR},
                </if>
                <if test="JIGUANSDQ != null">
                    JIGUANSDQ = #{JIGUANSDQ,jdbcType=VARCHAR},
                </if>
                <if test="XIANZHUZSF != null">
                    XIANZHUZSF = #{XIANZHUZSF,jdbcType=VARCHAR},
                </if>
                <if test="XIANZHUZSDQ != null">
                    XIANZHUZSDQ = #{XIANZHUZSDQ,jdbcType=VARCHAR},
                </if>
                <if test="XIANZHUZXQ != null">
                    XIANZHUZXQ = #{XIANZHUZXQ,jdbcType=VARCHAR},
                </if>
                <if test="XIANZHUZQT != null">
                    XIANZHUZQT = #{XIANZHUZQT,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUDZQT != null">
                    HUKOUDZQT = #{HUKOUDZQT,jdbcType=VARCHAR},
                </if>
                <if test="XIAOXILY != null">
                    XIAOXILY = #{XIAOXILY,jdbcType=VARCHAR},
                </if>
                <if test="WANGLUOFWBZ != null">
                    WANGLUOFWBZ = #{WANGLUOFWBZ,jdbcType=DECIMAL},
                </if>
                <if test="DABINGBZ != null">
                    DABINGBZ = #{DABINGBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHIFOUCSSFZ != null">
                    SHIFOUCSSFZ = #{SHIFOUCSSFZ,jdbcType=DECIMAL},
                </if>
                <if test="YUNCI != null">
                    YUNCI = #{YUNCI,jdbcType=VARCHAR},
                </if>
                <if test="CHANCI != null">
                    CHANCI = #{CHANCI,jdbcType=VARCHAR},
                </if>
                <if test="TAICI != null">
                    TAICI = #{TAICI,jdbcType=VARCHAR},
                </if>
                <if test="YUCHANQI != null">
                    YUCHANQI = #{YUCHANQI,jdbcType=VARCHAR},
                </if>
                <if test="QUYUBH != null">
                    QUYUBH = #{QUYUBH,jdbcType=VARCHAR},
                </if>
                <if test="MOCIYJ != null">
                    MOCIYJ = #{MOCIYJ,jdbcType=VARCHAR},
                </if>
                <if test="MIANFEIQLX != null">
                    MIANFEIQLX = #{MIANFEIQLX,jdbcType=VARCHAR},
                </if>
                <if test="HUKOUYB != null">
                    HUKOUYB = #{HUKOUYB,jdbcType=VARCHAR},
                </if>
                <if test="SHIBAZDBDM != null">
                    SHIBAZDBDM = #{SHIBAZDBDM,jdbcType=VARCHAR},
                </if>
                <if test="MOCIYUEJINGSJ != null">
                    MOCIYUEJINGSJ = #{MOCIYUEJINGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JSMOCIYJSJ != null">
                    JSMOCIYJSJ = #{JSMOCIYJSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUNZHOU != null">
                    YUNZHOU = #{YUNZHOU,jdbcType=DECIMAL},
                </if>
                <if test="YUNTIAN != null">
                    YUNTIAN = #{YUNTIAN,jdbcType=DECIMAL},
                </if>
                <if test="TAICHANCI != null">
                    TAICHANCI = #{TAICHANCI,jdbcType=VARCHAR},
                </if>
                <if test="BUSUIFANGBZ != null">
                    BUSUIFANGBZ = #{BUSUIFANGBZ,jdbcType=DECIMAL},
                </if>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=VARCHAR},
                </if>
                <if test="SHIFOUSQQFX != null">
                    SHIFOUSQQFX = #{SHIFOUSQQFX,jdbcType=VARCHAR},
                </if>
                <if test="SHANSHIFYBZ != null">
                    SHANSHIFYBZ = #{SHANSHIFYBZ,jdbcType=DECIMAL},
                </if>
                <if test="SFJSFLZBZ != null">
                    SFJSFLZBZ = #{SFJSFLZBZ,jdbcType=DECIMAL},
                </if>
                <if test="BURUJINGLY != null">
                    BURUJINGLY = #{BURUJINGLY,jdbcType=VARCHAR},
                </if>
                <if test="YILIANTBZ != null">
                    YILIANTBZ = #{YILIANTBZ,jdbcType=VARCHAR},
                </if>
                <if test="YILIANTBH != null">
                    YILIANTBH = #{YILIANTBH,jdbcType=VARCHAR},
                </if>
                <if test="YILIANTSQDID != null">
                    YILIANTSQDID = #{YILIANTSQDID,jdbcType=VARCHAR},
                </if>
                <if test="ZHURENYS != null">
                    ZHURENYS = #{ZHURENYS,jdbcType=VARCHAR},
                </if>
                <if test="ZHURENYSXM != null">
                    ZHURENYSXM = #{ZHURENYSXM,jdbcType=VARCHAR},
                </if>
                <if test="SHENGYUBZ != null">
                    SHENGYUBZ = #{SHENGYUBZ,jdbcType=DECIMAL},
                </if>
                <if test="BIAOZHI120 != null">
                    BIAOZHI120 = #{BIAOZHI120,jdbcType=DECIMAL},
                </if>
                <if test="YINANBRBZ != null">
                    YINANBRBZ = #{YINANBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="RUYUANYY != null">
                    RUYUANYY = #{RUYUANYY,jdbcType=VARCHAR},
                </if>
                <if test="ZAICIRYLX != null">
                    ZAICIRYLX = #{ZAICIRYLX,jdbcType=DECIMAL},
                </if>
                <if test="ZHUANZHENDH != null">
                    ZHUANZHENDH = #{ZHUANZHENDH,jdbcType=VARCHAR},
                </if>
                <if test="YUANRUKERQ != null">
                    YUANRUKERQ = #{YUANRUKERQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YINGERCWFJZZT != null">
                    YINGERCWFJZZT = #{YINGERCWFJZZT,jdbcType=DECIMAL},
                </if>
                <if test="YINGERCWFJZTZRQ != null">
                    YINGERCWFJZTZRQ = #{YINGERCWFJZTZRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="YINGERCWFJZTZR != null">
                    YINGERCWFJZTZR = #{YINGERCWFJZTZR,jdbcType=VARCHAR},
                </if>
                <if test="KUAISURYBZ != null">
                    KUAISURYBZ = #{KUAISURYBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENFENZBXYY != null">
                    SHENFENZBXYY = #{SHENFENZBXYY,jdbcType=VARCHAR},
                </if>
                <if test="YDRYBZ != null">
                    YDRYBZ = #{YDRYBZ,jdbcType=VARCHAR},
                </if>
                <if test="TCQBZ != null">
                    TCQBZ = #{TCQBZ,jdbcType=VARCHAR},
                </if>
                <if test="SENLINFHBRBZ != null">
                    SENLINFHBRBZ = #{SENLINFHBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="SENLINFHSPRQ != null">
                    SENLINFHSPRQ = #{SENLINFHSPRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="SENLINFHBZ != null">
                    SENLINFHBZ = #{SENLINFHBZ,jdbcType=VARCHAR},
                </if>
                <if test="MPI != null">
                    MPI = #{MPI,jdbcType=VARCHAR},
                </if>
                <if test="SHOUCIRKR != null">
                    SHOUCIRKR = #{SHOUCIRKR,jdbcType=VARCHAR},
                </if>
                <if test="SHOUCIRKRXM != null">
                    SHOUCIRKRXM = #{SHOUCIRKRXM,jdbcType=VARCHAR},
                </if>
                <if test="JIUZHENQRRQ != null">
                    JIUZHENQRRQ = #{JIUZHENQRRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIUZHENQRBZ != null">
                    JIUZHENQRBZ = #{JIUZHENQRBZ,jdbcType=DECIMAL},
                </if>
                <if test="QUERENSFRQ_HIS != null">
                    QUERENSFRQ_HIS = #{QUERENSFRQ_HIS,jdbcType=TIMESTAMP},
                </if>
                <if test="QUERENSFBZ_HIS != null">
                    QUERENSFBZ_HIS = #{QUERENSFBZ_HIS,jdbcType=DECIMAL},
                </if>
                <if test="DENGJISY != null">
                    DENGJISY = #{DENGJISY,jdbcType=VARCHAR},
                </if>
                <if test="YUNYIXYED != null">
                    YUNYIXYED = #{YUNYIXYED,jdbcType=VARCHAR},
                </if>
                <if test="YUNYIQYBZ != null">
                    YUNYIQYBZ = #{YUNYIQYBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUANDRYELB != null">
                    ZHUANDRYELB = #{ZHUANDRYELB,jdbcType=VARCHAR},
                </if>
                <if test="LIUGUANBZ != null">
                    LIUGUANBZ = #{LIUGUANBZ,jdbcType=DECIMAL},
                </if>
                <if test="LIUGUANSQDID != null">
                    LIUGUANSQDID = #{LIUGUANSQDID,jdbcType=VARCHAR},
                </if>
                <if test="JIUZHENID != null">
                    JIUZHENID = #{JIUZHENID,jdbcType=VARCHAR},
                </if>
                <if test="FENZHENSJ != null">
                    FENZHENSJ = #{FENZHENSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="LAIYUAN != null">
                    LAIYUAN = #{LAIYUAN,jdbcType=VARCHAR},
                </if>
                <if test="FENZHENYUAN != null">
                    FENZHENYUAN = #{FENZHENYUAN,jdbcType=VARCHAR},
                </if>
                <if test="FENZHENYUANID != null">
                    FENZHENYUANID = #{FENZHENYUANID,jdbcType=VARCHAR},
                </if>
                <if test="FENZHENLB != null">
                    FENZHENLB = #{FENZHENLB,jdbcType=VARCHAR},
                </if>
                <if test="SHIFUTZRQ != null">
                    SHIFUTZRQ = #{SHIFUTZRQ,jdbcType=DECIMAL},
                </if>
                <if test="DIANHUA != null">
                    DIANHUA = #{DIANHUA,jdbcType=VARCHAR},
                </if>
                <if test="XINGUANBZ != null">
                    XINGUANBZ = #{XINGUANBZ,jdbcType=DECIMAL},
                </if>
                <if test="SANDAZXBZ != null">
                    SANDAZXBZ = #{SANDAZXBZ,jdbcType=DECIMAL},
                </if>
                <if test="QITABZNR != null">
                    QITABZNR = #{QITABZNR,jdbcType=VARCHAR},
                </if>
                <if test="XINBINGRENZYID != null">
                    XINBINGRENZYID = #{XINBINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="LIUGUANBRZYID != null">
                    LIUGUANBRZYID = #{LIUGUANBRZYID,jdbcType=VARCHAR},
                </if>
                <if test="XIANZHUZHI != null">
                    XIANZHUZHI = #{XIANZHUZHI,jdbcType=VARCHAR},
                </if>
                <if test="BINGANSBBZ != null">
                    BINGANSBBZ = #{BINGANSBBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUANGXIANGJZBZ != null">
                    SHUANGXIANGJZBZ = #{SHUANGXIANGJZBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHONGZHUANKS != null">
                    ZHONGZHUANKS = #{ZHONGZHUANKS,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGZHUANKEMC != null">
                    ZHONGZHUANKEMC = #{ZHONGZHUANKEMC,jdbcType=VARCHAR},
                </if>
                <if test="DIEDAOPGBZ != null">
                    DIEDAOPGBZ = #{DIEDAOPGBZ,jdbcType=DECIMAL},
                </if>
                <if test="DISANFBZJ != null">
                    DISANFBZJ = #{DISANFBZJ,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENQX != null">
                    BINGRENQX = #{BINGRENQX,jdbcType=VARCHAR},
                </if>
                <if test="FENZHENJB != null">
                    FENZHENJB = #{FENZHENJB,jdbcType=VARCHAR},
                </if>
                <if test="YOUXIAOZDMC != null">
                    YOUXIAOZDMC = #{YOUXIAOZDMC,jdbcType=VARCHAR},
                </if>
                <if test="MINZHENGXX != null">
                    MINZHENGXX = #{MINZHENGXX,jdbcType=VARCHAR},
                </if>
                <if test="BINGQINGZY != null">
                    BINGQINGZY = #{BINGQINGZY,jdbcType=VARCHAR},
                </if>
                <if test="YANKECS != null">
                    YANKECS = #{YANKECS,jdbcType=DECIMAL},
                </if>
                <if test="QITAYLXTBRBZ != null">
                    QITAYLXTBRBZ = #{QITAYLXTBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="GCPLX != null">
                    GCPLX = #{GCPLX,jdbcType=VARCHAR},
                </if>
                <if test="BUSHIYDBZYY != null">
                    BUSHIYDBZYY = #{BUSHIYDBZYY,jdbcType=VARCHAR},
                </if>
                <if test="SHIFOUGZ != null">
                    SHIFOUGZ = #{SHIFOUGZ,jdbcType=DECIMAL},
                </if>
                <if test="EMPI_YJ != null">
                    EMPI_YJ = #{EMPI_YJ,jdbcType=VARCHAR},
                </if>
                <if test="SANJIYS != null">
                    SANJIYS = #{SANJIYS,jdbcType=VARCHAR},
                </if>
                <if test="SANJIYSMC != null">
                    SANJIYSMC = #{SANJIYSMC,jdbcType=VARCHAR},
                </if>
                <if test="KEZHURYS != null">
                    KEZHURYS = #{KEZHURYS,jdbcType=VARCHAR},
                </if>
                <if test="KEZHURYSXM != null">
                    KEZHURYSXM = #{KEZHURYSXM,jdbcType=VARCHAR},
                </if>
                <if test="ERJIYS != null">
                    ERJIYS = #{ERJIYS,jdbcType=VARCHAR},
                </if>
                <if test="ERJIYSMC != null">
                    ERJIYSMC = #{ERJIYSMC,jdbcType=VARCHAR},
                </if>
                <if test="YIJIYS != null">
                    YIJIYS = #{YIJIYS,jdbcType=VARCHAR},
                </if>
                <if test="YIJIYSMC != null">
                    YIJIYSMC = #{YIJIYSMC,jdbcType=VARCHAR},
                </if>
                <if test="SHANGCICKSJ != null">
                    SHANGCICKSJ = #{SHANGCICKSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZONGHEBFBZ != null">
                    ZONGHEBFBZ = #{ZONGHEBFBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZONGHEBQ != null">
                    ZONGHEBQ = #{ZONGHEBQ,jdbcType=VARCHAR},
                </if>
                <if test="ZONGHEBQCW != null">
                    ZONGHEBQCW = #{ZONGHEBQCW,jdbcType=VARCHAR},
                </if>
                <if test="ZONGHEBFJRSJ != null">
                    ZONGHEBFJRSJ = #{ZONGHEBFJRSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZONGHEBQMC != null">
                    ZONGHEBQMC = #{ZONGHEBQMC,jdbcType=VARCHAR},
                </if>
                <if test="QIANTIANJH != null">
                    QIANTIANJH = #{QIANTIANJH,jdbcType=DECIMAL},
                </if>
                <if test="ZHOUQI != null">
                    ZHOUQI = #{ZHOUQI,jdbcType=VARCHAR},
                </if>
                <if test="JINGQI != null">
                    JINGQI = #{JINGQI,jdbcType=VARCHAR},
                </if>
                <if test="YXJM != null">
                    YXJM = #{YXJM,jdbcType=DECIMAL},
                </if>
                <if test="XINXITBZ != null">
                    XINXITBZ = #{XINXITBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHUANGTOUKDYBZ != null">
                    CHUANGTOUKDYBZ = #{CHUANGTOUKDYBZ,jdbcType=DECIMAL},
                </if>
                <if test="FLSBZ != null">
                    FLSBZ = #{FLSBZ,jdbcType=DECIMAL},
                </if>
                <if test="XINGBIEDM != null">
                    XINGBIEDM = #{XINGBIEDM,jdbcType=VARCHAR},
                </if>
                <if test="HUNYINDM != null">
                    HUNYINDM = #{HUNYINDM,jdbcType=VARCHAR},
                </if>
                <if test="ZHIYEDM != null">
                    ZHIYEDM = #{ZHIYEDM,jdbcType=VARCHAR},
                </if>
                <if test="GUOJIDM != null">
                    GUOJIDM = #{GUOJIDM,jdbcType=VARCHAR},
                </if>
                <if test="MINZUDM != null">
                    MINZUDM = #{MINZUDM,jdbcType=VARCHAR},
                </if>
                <if test="SHENGFENDM != null">
                    SHENGFENDM = #{SHENGFENDM,jdbcType=VARCHAR},
                </if>
                <if test="JIGUANDM != null">
                    JIGUANDM = #{JIGUANDM,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHENGDDM != null">
                    CHUSHENGDDM = #{CHUSHENGDDM,jdbcType=VARCHAR},
                </if>
                <if test="JIHUASYFWZH != null">
                    JIHUASYFWZH = #{JIHUASYFWZH,jdbcType=VARCHAR},
                </if>
                <if test="SHENGYULB != null">
                    SHENGYULB = #{SHENGYULB,jdbcType=VARCHAR},
                </if>
                <if test="WANYUBZ != null">
                    WANYUBZ = #{WANYUBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZAOCHANBZ != null">
                    ZAOCHANBZ = #{ZAOCHANBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIHUASYSSLB != null">
                    JIHUASYSSLB = #{JIHUASYSSLB,jdbcType=VARCHAR},
                </if>
                <if test="XUELI != null">
                    XUELI = #{XUELI,jdbcType=VARCHAR},
                </if>
                <if test="ZAIRUYHZBZ != null">
                    ZAIRUYHZBZ = #{ZAIRUYHZBZ,jdbcType=DECIMAL},
                </if>
                <if test="XUETANGSQZ != null">
                    XUETANGSQZ = #{XUETANGSQZ,jdbcType=VARCHAR},
                </if>
                <if test="XUETANGSQZT != null">
                    XUETANGSQZT = #{XUETANGSQZT,jdbcType=VARCHAR},
                </if>
                <if test="SHAICHADQ != null">
                    SHAICHADQ = #{SHAICHADQ,jdbcType=VARCHAR},
                </if>
                <if test="XIANXUEZBZ != null">
                    XIANXUEZBZ = #{XIANXUEZBZ,jdbcType=DECIMAL},
                </if>
                <if test="JCPTEMPI != null">
                    JCPTEMPI = #{JCPTEMPI,jdbcType=VARCHAR},
                </if>
                <if test="IPT_PSN_SP_FLAG_DETL_ID != null">
                    IPT_PSN_SP_FLAG_DETL_ID = #{IPT_PSN_SP_FLAG_DETL_ID,jdbcType=VARCHAR},
                </if>
                <if test="LINCHUANGLJID != null">
                    LINCHUANGLJID = #{LINCHUANGLJID,jdbcType=VARCHAR},
                </if>
                <if test="SHIFOUYXZZJS != null">
                    SHIFOUYXZZJS = #{SHIFOUYXZZJS,jdbcType=VARCHAR},
                </if>
                <if test="PEIKEKH != null">
                    PEIKEKH = #{PEIKEKH,jdbcType=VARCHAR},
                </if>
                <if test="KANGFUSQZT != null">
                    KANGFUSQZT = #{KANGFUSQZT,jdbcType=VARCHAR},
                </if>
                <if test="KANGFUZLS != null">
                    KANGFUZLS = #{KANGFUZLS,jdbcType=VARCHAR},
                </if>
                <if test="KANGFUZLSXM != null">
                    KANGFUZLSXM = #{KANGFUZLSXM,jdbcType=VARCHAR},
                </if>
                <if test="CHANGHUXBRBZ != null">
                    CHANGHUXBRBZ = #{CHANGHUXBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="YOUFUBRBZ != null">
                    YOUFUBRBZ = #{YOUFUBRBZ,jdbcType=DECIMAL},
                </if>
                <if test="LIANXIREN2 != null">
                    LIANXIREN2 = #{LIANXIREN2,jdbcType=VARCHAR},
                </if>
                <if test="LIANXIRDH2 != null">
                    LIANXIRDH2 = #{LIANXIRDH2,jdbcType=VARCHAR},
                </if>
                <if test="GUANXI2 != null">
                    GUANXI2 = #{GUANXI2,jdbcType=VARCHAR},
                </if>
                <if test="YUANQIANBQID != null">
                    YUANQIANBQID = #{YUANQIANBQID,jdbcType=VARCHAR},
                </if>
                <if test="ZHUGUANYS != null">
                    ZHUGUANYS = #{ZHUGUANYS,jdbcType=VARCHAR},
                </if>
                <if test="LIUQIANGZKBZ != null">
                    LIUQIANGZKBZ = #{LIUQIANGZKBZ,jdbcType=DECIMAL},
                </if>
        </set>
        where   BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.ZyBingrenxx">
        update ZY_BINGRENXX
        set 
            SHEBAOBH =  #{SHEBAOBH,jdbcType=VARCHAR},
            YIBAOKH =  #{YIBAOKH,jdbcType=VARCHAR},
            JIUZHENKH =  #{JIUZHENKH,jdbcType=VARCHAR},
            SHEQUBH =  #{SHEQUBH,jdbcType=VARCHAR},
            FEIYONGLB =  #{FEIYONGLB,jdbcType=VARCHAR},
            FEIYONGXZ =  #{FEIYONGXZ,jdbcType=VARCHAR},
            YOUHUILB =  #{YOUHUILB,jdbcType=VARCHAR},
            GONGFEIZH =  #{GONGFEIZH,jdbcType=VARCHAR},
            GONGFEIDW =  #{GONGFEIDW,jdbcType=VARCHAR},
            GONGFEIDWMC =  #{GONGFEIDWMC,jdbcType=VARCHAR},
            XINGMING =  #{XINGMING,jdbcType=VARCHAR},
            SHURUMA1 =  #{SHURUMA1,jdbcType=VARCHAR},
            SHURUMA2 =  #{SHURUMA2,jdbcType=VARCHAR},
            SHURUMA3 =  #{SHURUMA3,jdbcType=VARCHAR},
            XINGBIE =  #{XINGBIE,jdbcType=VARCHAR},
            NIANLINGDW =  #{NIANLINGDW,jdbcType=VARCHAR},
            SHENFENZH =  #{SHENFENZH,jdbcType=VARCHAR},
            CHUSHENGRQ =  #{CHUSHENGRQ,jdbcType=TIMESTAMP},
            GONGZUODW =  #{GONGZUODW,jdbcType=VARCHAR},
            DANWEIDH =  #{DANWEIDH,jdbcType=VARCHAR},
            DANWEIYB =  #{DANWEIYB,jdbcType=VARCHAR},
            JIATINGDZ =  #{JIATINGDZ,jdbcType=VARCHAR},
            JIATINGDH =  #{JIATINGDH,jdbcType=VARCHAR},
            JIATINGYB =  #{JIATINGYB,jdbcType=VARCHAR},
            XUEXING =  #{XUEXING,jdbcType=VARCHAR},
            HUNYIN =  #{HUNYIN,jdbcType=VARCHAR},
            ZHIYE =  #{ZHIYE,jdbcType=VARCHAR},
            GUOJI =  #{GUOJI,jdbcType=VARCHAR},
            MINZU =  #{MINZU,jdbcType=VARCHAR},
            SHENGFEN =  #{SHENGFEN,jdbcType=VARCHAR},
            XIANGZHENJD =  #{XIANGZHENJD,jdbcType=VARCHAR},
            SHIDIQU =  #{SHIDIQU,jdbcType=VARCHAR},
            JIGUAN =  #{JIGUAN,jdbcType=VARCHAR},
            CHUSHENGDI =  #{CHUSHENGDI,jdbcType=VARCHAR},
            YOUBIAN =  #{YOUBIAN,jdbcType=VARCHAR},
            LIANXIREN =  #{LIANXIREN,jdbcType=VARCHAR},
            GUANXI =  #{GUANXI,jdbcType=VARCHAR},
            LIANXIRDZ =  #{LIANXIRDZ,jdbcType=VARCHAR},
            LIANXIRDH =  #{LIANXIRDH,jdbcType=VARCHAR},
            LIANXIRYB =  #{LIANXIRYB,jdbcType=VARCHAR},
            JIWANGSHI =  #{JIWANGSHI,jdbcType=VARCHAR},
            GUOMINSHI =  #{GUOMINSHI,jdbcType=VARCHAR},
            QUYU =  #{QUYU,jdbcType=VARCHAR},
            WAIDIBRBZ =  #{WAIDIBRBZ,jdbcType=DECIMAL},
            JIANDANGREN =  #{JIANDANGREN,jdbcType=VARCHAR},
            JIANDANGRQ =  #{JIANDANGRQ,jdbcType=TIMESTAMP},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            YINGERBZ =  #{YINGERBZ,jdbcType=DECIMAL},
            ZHUYUANCS =  #{ZHUYUANCS,jdbcType=DECIMAL},
            RUYUANRQ =  #{RUYUANRQ,jdbcType=TIMESTAMP},
            YUCHUYRQ =  #{YUCHUYRQ,jdbcType=TIMESTAMP},
            CHUYUANRQ =  #{CHUYUANRQ,jdbcType=TIMESTAMP},
            ZAIYUANZT =  #{ZAIYUANZT,jdbcType=VARCHAR},
            RUYUANTJ =  #{RUYUANTJ,jdbcType=VARCHAR},
            RUYUANKS =  #{RUYUANKS,jdbcType=VARCHAR},
            RUYUANBQ =  #{RUYUANBQ,jdbcType=VARCHAR},
            RUYUANCW =  #{RUYUANCW,jdbcType=VARCHAR},
            DANGQIANKS =  #{DANGQIANKS,jdbcType=VARCHAR},
            DANGQIANBQ =  #{DANGQIANBQ,jdbcType=VARCHAR},
            DANGQIANCW =  #{DANGQIANCW,jdbcType=VARCHAR},
            GUANLIKS =  #{GUANLIKS,jdbcType=VARCHAR},
            JIECHUANGBZ =  #{JIECHUANGBZ,jdbcType=DECIMAL},
            LIYUANQX =  #{LIYUANQX,jdbcType=VARCHAR},
            MENZHENZDDM =  #{MENZHENZDDM,jdbcType=VARCHAR},
            MENZHENZDMC =  #{MENZHENZDMC,jdbcType=VARCHAR},
            RUYUANZDDM =  #{RUYUANZDDM,jdbcType=VARCHAR},
            RUYUANZDMC =  #{RUYUANZDMC,jdbcType=VARCHAR},
            CHUYUANZDDM =  #{CHUYUANZDDM,jdbcType=VARCHAR},
            CHUYUANZDMC =  #{CHUYUANZDMC,jdbcType=VARCHAR},
            CHUYUANZDDM2 =  #{CHUYUANZDDM2,jdbcType=VARCHAR},
            CHUYUANZDMC2 =  #{CHUYUANZDMC2,jdbcType=VARCHAR},
            CHUYUANZDMC3 =  #{CHUYUANZDMC3,jdbcType=VARCHAR},
            CHUYUANZDDM3 =  #{CHUYUANZDDM3,jdbcType=VARCHAR},
            JIATINGBCBZ =  #{JIATINGBCBZ,jdbcType=DECIMAL},
            BINGQING =  #{BINGQING,jdbcType=VARCHAR},
            FENMIAN =  #{FENMIAN,jdbcType=VARCHAR},
            SHANGCHUANBZ =  #{SHANGCHUANBZ,jdbcType=DECIMAL},
            SHANGCHUANRQ =  #{SHANGCHUANRQ,jdbcType=TIMESTAMP},
            DANBAOREN =  #{DANBAOREN,jdbcType=VARCHAR},
            DANBAOJE =  #{DANBAOJE,jdbcType=DECIMAL},
            JIAZHANGXM =  #{JIAZHANGXM,jdbcType=VARCHAR},
            ZHUYUANHAO =  #{ZHUYUANHAO,jdbcType=VARCHAR},
            BINGANHAO =  #{BINGANHAO,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            JIESUANXH =  #{JIESUANXH,jdbcType=DECIMAL},
            NIANLING =  #{NIANLING,jdbcType=DECIMAL},
            MENZHENYS =  #{MENZHENYS,jdbcType=VARCHAR},
            SHOUZHIYS =  #{SHOUZHIYS,jdbcType=VARCHAR},
            ZHUYUANYS =  #{ZHUYUANYS,jdbcType=VARCHAR},
            ZHUZHIYS =  #{ZHUZHIYS,jdbcType=VARCHAR},
            YUYUEID =  #{YUYUEID,jdbcType=VARCHAR},
            SHENHEBZ =  #{SHENHEBZ,jdbcType=DECIMAL},
            QIGUANYZBZ =  #{QIGUANYZBZ,jdbcType=DECIMAL},
            JIAOYILSH =  #{JIAOYILSH,jdbcType=VARCHAR},
            GERENBH =  #{GERENBH,jdbcType=VARCHAR},
            IC =  #{IC,jdbcType=VARCHAR},
            SHIMINKWKH =  #{SHIMINKWKH,jdbcType=VARCHAR},
            YILIAOZID =  #{YILIAOZID,jdbcType=VARCHAR},
            YILIAOZM =  #{YILIAOZM,jdbcType=VARCHAR},
            RUKERQ =  #{RUKERQ,jdbcType=TIMESTAMP},
            RUKEBZ =  #{RUKEBZ,jdbcType=DECIMAL},
            ZHANCHUANGBZ =  #{ZHANCHUANGBZ,jdbcType=DECIMAL},
            RUYUANKSMC =  #{RUYUANKSMC,jdbcType=VARCHAR},
            RUYUANBQMC =  #{RUYUANBQMC,jdbcType=VARCHAR},
            DANGQIANKSMC =  #{DANGQIANKSMC,jdbcType=VARCHAR},
            DANGQIANBQMC =  #{DANGQIANBQMC,jdbcType=VARCHAR},
            GUANLIKSMC =  #{GUANLIKSMC,jdbcType=VARCHAR},
            MENZHENYSXM =  #{MENZHENYSXM,jdbcType=VARCHAR},
            SHOUZHIYSXM =  #{SHOUZHIYSXM,jdbcType=VARCHAR},
            ZHUYUANYSXM =  #{ZHUYUANYSXM,jdbcType=VARCHAR},
            ZHUZHIYSXM =  #{ZHUZHIYSXM,jdbcType=VARCHAR},
            BINGLISLZT =  #{BINGLISLZT,jdbcType=VARCHAR},
            FEIYONGXZZHBZ =  #{FEIYONGXZZHBZ,jdbcType=DECIMAL},
            FEIYONGXZZHR =  #{FEIYONGXZZHR,jdbcType=VARCHAR},
            FEIYONGXZZHRQ =  #{FEIYONGXZZHRQ,jdbcType=TIMESTAMP},
            YIBAOYLLB =  #{YIBAOYLLB,jdbcType=VARCHAR},
            TESHUBZBZ =  #{TESHUBZBZ,jdbcType=DECIMAL},
            TESHUBZMC =  #{TESHUBZMC,jdbcType=VARCHAR},
            TESHUBZBM =  #{TESHUBZBM,jdbcType=VARCHAR},
            MENZHENZYZDDM =  #{MENZHENZYZDDM,jdbcType=VARCHAR},
            MENZHENZYZDMC =  #{MENZHENZYZDMC,jdbcType=VARCHAR},
            RUYUANZYZDDM =  #{RUYUANZYZDDM,jdbcType=VARCHAR},
            RUYUANZYZDMC =  #{RUYUANZYZDMC,jdbcType=VARCHAR},
            WAIYUANZDBZ =  #{WAIYUANZDBZ,jdbcType=DECIMAL},
            RUYUANZYZHDM =  #{RUYUANZYZHDM,jdbcType=VARCHAR},
            RUYUANZYZHMC =  #{RUYUANZYZHMC,jdbcType=VARCHAR},
            YAOPINBZYBZ =  #{YAOPINBZYBZ,jdbcType=DECIMAL},
            ZHUANBINGQBZ =  #{ZHUANBINGQBZ,jdbcType=DECIMAL},
            MUQINZYID =  #{MUQINZYID,jdbcType=VARCHAR},
            CHANFUBZ =  #{CHANFUBZ,jdbcType=DECIMAL},
            LINCHUANGLJBZ =  #{LINCHUANGLJBZ,jdbcType=DECIMAL},
            QUXIAORYBZ =  #{QUXIAORYBZ,jdbcType=DECIMAL},
            QINGJIABZ =  #{QINGJIABZ,jdbcType=DECIMAL},
            YUANBINGRZYID =  #{YUANBINGRZYID,jdbcType=VARCHAR},
            LVSETDBZ =  #{LVSETDBZ,jdbcType=DECIMAL},
            LVSETDKQRQ =  #{LVSETDKQRQ,jdbcType=TIMESTAMP},
            YIBAOMXFJYJLS =  #{YIBAOMXFJYJLS,jdbcType=DECIMAL},
            TESHUBZ =  #{TESHUBZ,jdbcType=DECIMAL},
            YILIAOKH =  #{YILIAOKH,jdbcType=VARCHAR},
            JIESHAOREN =  #{JIESHAOREN,jdbcType=VARCHAR},
            YINGERSL =  #{YINGERSL,jdbcType=DECIMAL},
            YOUHUILBLB =  #{YOUHUILBLB,jdbcType=VARCHAR},
            QUXIAOYCYRQ =  #{QUXIAOYCYRQ,jdbcType=TIMESTAMP},
            RUYUANZDDM2 =  #{RUYUANZDDM2,jdbcType=VARCHAR},
            RUYUANZDMC2 =  #{RUYUANZDMC2,jdbcType=VARCHAR},
            RUYUANZDDM3 =  #{RUYUANZDDM3,jdbcType=VARCHAR},
            RUYUANZDMC3 =  #{RUYUANZDMC3,jdbcType=VARCHAR},
            YIBAOBRXX =  #{YIBAOBRXX,jdbcType=VARCHAR},
            FEIYONGLB2 =  #{FEIYONGLB2,jdbcType=VARCHAR},
            FEIYONGXZ2 =  #{FEIYONGXZ2,jdbcType=VARCHAR},
            YILIAOJZDJBZ =  #{YILIAOJZDJBZ,jdbcType=DECIMAL},
            ZHIFU1YB =  #{ZHIFU1YB,jdbcType=VARCHAR},
            ZHIFU2YB =  #{ZHIFU2YB,jdbcType=VARCHAR},
            ZHIFU3YB =  #{ZHIFU3YB,jdbcType=VARCHAR},
            ZHIFU4YB =  #{ZHIFU4YB,jdbcType=VARCHAR},
            ZHIFU5YB =  #{ZHIFU5YB,jdbcType=VARCHAR},
            ZHIFU6YB =  #{ZHIFU6YB,jdbcType=VARCHAR},
            ZHIFU7YB =  #{ZHIFU7YB,jdbcType=VARCHAR},
            ZHIFU8YB =  #{ZHIFU8YB,jdbcType=VARCHAR},
            ZHIFU9YB =  #{ZHIFU9YB,jdbcType=VARCHAR},
            ZHIFU10YB =  #{ZHIFU10YB,jdbcType=VARCHAR},
            ZHIFU11YB =  #{ZHIFU11YB,jdbcType=VARCHAR},
            ZHIFU12YB =  #{ZHIFU12YB,jdbcType=VARCHAR},
            SHUZHI1YB =  #{SHUZHI1YB,jdbcType=DECIMAL},
            SHUZHI2YB =  #{SHUZHI2YB,jdbcType=DECIMAL},
            SHUZHI3YB =  #{SHUZHI3YB,jdbcType=DECIMAL},
            SHUZHI4YB =  #{SHUZHI4YB,jdbcType=DECIMAL},
            SHUZHI5YB =  #{SHUZHI5YB,jdbcType=DECIMAL},
            SHUZHI6YB =  #{SHUZHI6YB,jdbcType=DECIMAL},
            SHUZHI7YB =  #{SHUZHI7YB,jdbcType=DECIMAL},
            SHUZHI8YB =  #{SHUZHI8YB,jdbcType=DECIMAL},
            SHUZHI9YB =  #{SHUZHI9YB,jdbcType=DECIMAL},
            SHUZHI10YB =  #{SHUZHI10YB,jdbcType=DECIMAL},
            YIBAOCYDJBZ =  #{YIBAOCYDJBZ,jdbcType=DECIMAL},
            YIZHONGYLBZ =  #{YIZHONGYLBZ,jdbcType=DECIMAL},
            YIZHONGYLLB =  #{YIZHONGYLLB,jdbcType=VARCHAR},
            YIZHONGYLXYJE =  #{YIZHONGYLXYJE,jdbcType=DECIMAL},
            YIZHONGYLBZSM =  #{YIZHONGYLBZSM,jdbcType=VARCHAR},
            FENMIANRQ =  #{FENMIANRQ,jdbcType=TIMESTAMP},
            BINGFAZHENG =  #{BINGFAZHENG,jdbcType=VARCHAR},
            BUZHUKBH =  #{BUZHUKBH,jdbcType=VARCHAR},
            YIBAOKH2 =  #{YIBAOKH2,jdbcType=VARCHAR},
            IC2 =  #{IC2,jdbcType=VARCHAR},
            QUXIAOYCYYY =  #{QUXIAOYCYYY,jdbcType=VARCHAR},
            BEIZHU =  #{BEIZHU,jdbcType=VARCHAR},
            ZERENHS =  #{ZERENHS,jdbcType=VARCHAR},
            ZERENHSXM =  #{ZERENHSXM,jdbcType=VARCHAR},
            JIUFENBRBZ =  #{JIUFENBRBZ,jdbcType=DECIMAL},
            ZHENGJIANLX =  #{ZHENGJIANLX,jdbcType=VARCHAR},
            JIANHURENSFZH =  #{JIANHURENSFZH,jdbcType=VARCHAR},
            GELILX =  #{GELILX,jdbcType=VARCHAR},
            LASTYUCHUYRQ =  #{LASTYUCHUYRQ,jdbcType=TIMESTAMP},
            LASTCHUYUANRQ =  #{LASTCHUYUANRQ,jdbcType=TIMESTAMP},
            YURUYUANBZ =  #{YURUYUANBZ,jdbcType=DECIMAL},
            RUYUANDJLX =  #{RUYUANDJLX,jdbcType=VARCHAR},
            XIANQU =  #{XIANQU,jdbcType=VARCHAR},
            NIANLING1 =  #{NIANLING1,jdbcType=DECIMAL},
            NIANLINGDW1 =  #{NIANLINGDW1,jdbcType=VARCHAR},
            GONGSHANGKFDJH =  #{GONGSHANGKFDJH,jdbcType=VARCHAR},
            GONGSHANGKFRQ =  #{GONGSHANGKFRQ,jdbcType=TIMESTAMP},
            LINCHUANGLJDBZBZ =  #{LINCHUANGLJDBZBZ,jdbcType=DECIMAL},
            SHENGAO =  #{SHENGAO,jdbcType=DECIMAL},
            TIZHONG =  #{TIZHONG,jdbcType=DECIMAL},
            ADLSCORE =  #{ADLSCORE,jdbcType=VARCHAR},
            SHENHEREN =  #{SHENHEREN,jdbcType=VARCHAR},
            SHENHERQ =  #{SHENHERQ,jdbcType=TIMESTAMP},
            GUAZHANGBZ =  #{GUAZHANGBZ,jdbcType=DECIMAL},
            ZHENGJIANSMWJ =  #{ZHENGJIANSMWJ,jdbcType=VARCHAR},
            BAOMIJB =  #{BAOMIJB,jdbcType=VARCHAR},
            HUKOUDZ =  #{HUKOUDZ,jdbcType=VARCHAR},
            HUKOUSF =  #{HUKOUSF,jdbcType=VARCHAR},
            HUKOUSDQ =  #{HUKOUSDQ,jdbcType=VARCHAR},
            HUKOUXQ =  #{HUKOUXQ,jdbcType=VARCHAR},
            HUKOUXZJD =  #{HUKOUXZJD,jdbcType=VARCHAR},
            JIATINGDZLB =  #{JIATINGDZLB,jdbcType=VARCHAR},
            HUKOUDZLB =  #{HUKOUDZLB,jdbcType=VARCHAR},
            JIANKANGKH =  #{JIANKANGKH,jdbcType=VARCHAR},
            ZHAOSHIZHBZ =  #{ZHAOSHIZHBZ,jdbcType=DECIMAL},
            JIANKANGKXX =  #{JIANKANGKXX,jdbcType=VARCHAR},
            RIJIANZYBZ =  #{RIJIANZYBZ,jdbcType=DECIMAL},
            CHUANGWEIZYBZ =  #{CHUANGWEIZYBZ,jdbcType=DECIMAL},
            HUSHIZHANG =  #{HUSHIZHANG,jdbcType=VARCHAR},
            HUSHIZXM =  #{HUSHIZXM,jdbcType=VARCHAR},
            JIZHENYXBZ =  #{JIZHENYXBZ,jdbcType=DECIMAL},
            YUCHUYXTSJ =  #{YUCHUYXTSJ,jdbcType=TIMESTAMP},
            TIBIAOMJ =  #{TIBIAOMJ,jdbcType=DECIMAL},
            YIWAISHBZ =  #{YIWAISHBZ,jdbcType=VARCHAR},
            CHUSHENGDSF =  #{CHUSHENGDSF,jdbcType=VARCHAR},
            CHUSHENGDSDQ =  #{CHUSHENGDSDQ,jdbcType=VARCHAR},
            CHUSHENGDXQ =  #{CHUSHENGDXQ,jdbcType=VARCHAR},
            JIGUANSF =  #{JIGUANSF,jdbcType=VARCHAR},
            JIGUANSDQ =  #{JIGUANSDQ,jdbcType=VARCHAR},
            XIANZHUZSF =  #{XIANZHUZSF,jdbcType=VARCHAR},
            XIANZHUZSDQ =  #{XIANZHUZSDQ,jdbcType=VARCHAR},
            XIANZHUZXQ =  #{XIANZHUZXQ,jdbcType=VARCHAR},
            XIANZHUZQT =  #{XIANZHUZQT,jdbcType=VARCHAR},
            HUKOUDZQT =  #{HUKOUDZQT,jdbcType=VARCHAR},
            XIAOXILY =  #{XIAOXILY,jdbcType=VARCHAR},
            WANGLUOFWBZ =  #{WANGLUOFWBZ,jdbcType=DECIMAL},
            DABINGBZ =  #{DABINGBZ,jdbcType=DECIMAL},
            SHIFOUCSSFZ =  #{SHIFOUCSSFZ,jdbcType=DECIMAL},
            YUNCI =  #{YUNCI,jdbcType=VARCHAR},
            CHANCI =  #{CHANCI,jdbcType=VARCHAR},
            TAICI =  #{TAICI,jdbcType=VARCHAR},
            YUCHANQI =  #{YUCHANQI,jdbcType=VARCHAR},
            QUYUBH =  #{QUYUBH,jdbcType=VARCHAR},
            MOCIYJ =  #{MOCIYJ,jdbcType=VARCHAR},
            MIANFEIQLX =  #{MIANFEIQLX,jdbcType=VARCHAR},
            HUKOUYB =  #{HUKOUYB,jdbcType=VARCHAR},
            SHIBAZDBDM =  #{SHIBAZDBDM,jdbcType=VARCHAR},
            MOCIYUEJINGSJ =  #{MOCIYUEJINGSJ,jdbcType=TIMESTAMP},
            JSMOCIYJSJ =  #{JSMOCIYJSJ,jdbcType=TIMESTAMP},
            YUNZHOU =  #{YUNZHOU,jdbcType=DECIMAL},
            YUNTIAN =  #{YUNTIAN,jdbcType=DECIMAL},
            TAICHANCI =  #{TAICHANCI,jdbcType=VARCHAR},
            BUSUIFANGBZ =  #{BUSUIFANGBZ,jdbcType=DECIMAL},
            YUANQUID =  #{YUANQUID,jdbcType=VARCHAR},
            SHIFOUSQQFX =  #{SHIFOUSQQFX,jdbcType=VARCHAR},
            SHANSHIFYBZ =  #{SHANSHIFYBZ,jdbcType=DECIMAL},
            SFJSFLZBZ =  #{SFJSFLZBZ,jdbcType=DECIMAL},
            BURUJINGLY =  #{BURUJINGLY,jdbcType=VARCHAR},
            YILIANTBZ =  #{YILIANTBZ,jdbcType=VARCHAR},
            YILIANTBH =  #{YILIANTBH,jdbcType=VARCHAR},
            YILIANTSQDID =  #{YILIANTSQDID,jdbcType=VARCHAR},
            ZHURENYS =  #{ZHURENYS,jdbcType=VARCHAR},
            ZHURENYSXM =  #{ZHURENYSXM,jdbcType=VARCHAR},
            SHENGYUBZ =  #{SHENGYUBZ,jdbcType=DECIMAL},
            BIAOZHI120 =  #{BIAOZHI120,jdbcType=DECIMAL},
            YINANBRBZ =  #{YINANBRBZ,jdbcType=DECIMAL},
            RUYUANYY =  #{RUYUANYY,jdbcType=VARCHAR},
            ZAICIRYLX =  #{ZAICIRYLX,jdbcType=DECIMAL},
            ZHUANZHENDH =  #{ZHUANZHENDH,jdbcType=VARCHAR},
            YUANRUKERQ =  #{YUANRUKERQ,jdbcType=TIMESTAMP},
            YINGERCWFJZZT =  #{YINGERCWFJZZT,jdbcType=DECIMAL},
            YINGERCWFJZTZRQ =  #{YINGERCWFJZTZRQ,jdbcType=TIMESTAMP},
            YINGERCWFJZTZR =  #{YINGERCWFJZTZR,jdbcType=VARCHAR},
            KUAISURYBZ =  #{KUAISURYBZ,jdbcType=DECIMAL},
            SHENFENZBXYY =  #{SHENFENZBXYY,jdbcType=VARCHAR},
            YDRYBZ =  #{YDRYBZ,jdbcType=VARCHAR},
            TCQBZ =  #{TCQBZ,jdbcType=VARCHAR},
            SENLINFHBRBZ =  #{SENLINFHBRBZ,jdbcType=DECIMAL},
            SENLINFHSPRQ =  #{SENLINFHSPRQ,jdbcType=TIMESTAMP},
            SENLINFHBZ =  #{SENLINFHBZ,jdbcType=VARCHAR},
            MPI =  #{MPI,jdbcType=VARCHAR},
            SHOUCIRKR =  #{SHOUCIRKR,jdbcType=VARCHAR},
            SHOUCIRKRXM =  #{SHOUCIRKRXM,jdbcType=VARCHAR},
            JIUZHENQRRQ =  #{JIUZHENQRRQ,jdbcType=TIMESTAMP},
            JIUZHENQRBZ =  #{JIUZHENQRBZ,jdbcType=DECIMAL},
            QUERENSFRQ_HIS =  #{QUERENSFRQ_HIS,jdbcType=TIMESTAMP},
            QUERENSFBZ_HIS =  #{QUERENSFBZ_HIS,jdbcType=DECIMAL},
            DENGJISY =  #{DENGJISY,jdbcType=VARCHAR},
            YUNYIXYED =  #{YUNYIXYED,jdbcType=VARCHAR},
            YUNYIQYBZ =  #{YUNYIQYBZ,jdbcType=DECIMAL},
            ZHUANDRYELB =  #{ZHUANDRYELB,jdbcType=VARCHAR},
            LIUGUANBZ =  #{LIUGUANBZ,jdbcType=DECIMAL},
            LIUGUANSQDID =  #{LIUGUANSQDID,jdbcType=VARCHAR},
            JIUZHENID =  #{JIUZHENID,jdbcType=VARCHAR},
            FENZHENSJ =  #{FENZHENSJ,jdbcType=TIMESTAMP},
            LAIYUAN =  #{LAIYUAN,jdbcType=VARCHAR},
            FENZHENYUAN =  #{FENZHENYUAN,jdbcType=VARCHAR},
            FENZHENYUANID =  #{FENZHENYUANID,jdbcType=VARCHAR},
            FENZHENLB =  #{FENZHENLB,jdbcType=VARCHAR},
            SHIFUTZRQ =  #{SHIFUTZRQ,jdbcType=DECIMAL},
            DIANHUA =  #{DIANHUA,jdbcType=VARCHAR},
            XINGUANBZ =  #{XINGUANBZ,jdbcType=DECIMAL},
            SANDAZXBZ =  #{SANDAZXBZ,jdbcType=DECIMAL},
            QITABZNR =  #{QITABZNR,jdbcType=VARCHAR},
            XINBINGRENZYID =  #{XINBINGRENZYID,jdbcType=VARCHAR},
            LIUGUANBRZYID =  #{LIUGUANBRZYID,jdbcType=VARCHAR},
            XIANZHUZHI =  #{XIANZHUZHI,jdbcType=VARCHAR},
            BINGANSBBZ =  #{BINGANSBBZ,jdbcType=DECIMAL},
            SHUANGXIANGJZBZ =  #{SHUANGXIANGJZBZ,jdbcType=DECIMAL},
            ZHONGZHUANKS =  #{ZHONGZHUANKS,jdbcType=VARCHAR},
            ZHONGZHUANKEMC =  #{ZHONGZHUANKEMC,jdbcType=VARCHAR},
            DIEDAOPGBZ =  #{DIEDAOPGBZ,jdbcType=DECIMAL},
            DISANFBZJ =  #{DISANFBZJ,jdbcType=VARCHAR},
            BINGRENQX =  #{BINGRENQX,jdbcType=VARCHAR},
            FENZHENJB =  #{FENZHENJB,jdbcType=VARCHAR},
            YOUXIAOZDMC =  #{YOUXIAOZDMC,jdbcType=VARCHAR},
            MINZHENGXX =  #{MINZHENGXX,jdbcType=VARCHAR},
            BINGQINGZY =  #{BINGQINGZY,jdbcType=VARCHAR},
            YANKECS =  #{YANKECS,jdbcType=DECIMAL},
            QITAYLXTBRBZ =  #{QITAYLXTBRBZ,jdbcType=DECIMAL},
            GCPLX =  #{GCPLX,jdbcType=VARCHAR},
            BUSHIYDBZYY =  #{BUSHIYDBZYY,jdbcType=VARCHAR},
            SHIFOUGZ =  #{SHIFOUGZ,jdbcType=DECIMAL},
            EMPI_YJ =  #{EMPI_YJ,jdbcType=VARCHAR},
            SANJIYS =  #{SANJIYS,jdbcType=VARCHAR},
            SANJIYSMC =  #{SANJIYSMC,jdbcType=VARCHAR},
            KEZHURYS =  #{KEZHURYS,jdbcType=VARCHAR},
            KEZHURYSXM =  #{KEZHURYSXM,jdbcType=VARCHAR},
            ERJIYS =  #{ERJIYS,jdbcType=VARCHAR},
            ERJIYSMC =  #{ERJIYSMC,jdbcType=VARCHAR},
            YIJIYS =  #{YIJIYS,jdbcType=VARCHAR},
            YIJIYSMC =  #{YIJIYSMC,jdbcType=VARCHAR},
            SHANGCICKSJ =  #{SHANGCICKSJ,jdbcType=TIMESTAMP},
            ZONGHEBFBZ =  #{ZONGHEBFBZ,jdbcType=DECIMAL},
            ZONGHEBQ =  #{ZONGHEBQ,jdbcType=VARCHAR},
            ZONGHEBQCW =  #{ZONGHEBQCW,jdbcType=VARCHAR},
            ZONGHEBFJRSJ =  #{ZONGHEBFJRSJ,jdbcType=TIMESTAMP},
            ZONGHEBQMC =  #{ZONGHEBQMC,jdbcType=VARCHAR},
            QIANTIANJH =  #{QIANTIANJH,jdbcType=DECIMAL},
            ZHOUQI =  #{ZHOUQI,jdbcType=VARCHAR},
            JINGQI =  #{JINGQI,jdbcType=VARCHAR},
            YXJM =  #{YXJM,jdbcType=DECIMAL},
            XINXITBZ =  #{XINXITBZ,jdbcType=DECIMAL},
            CHUANGTOUKDYBZ =  #{CHUANGTOUKDYBZ,jdbcType=DECIMAL},
            FLSBZ =  #{FLSBZ,jdbcType=DECIMAL},
            XINGBIEDM =  #{XINGBIEDM,jdbcType=VARCHAR},
            HUNYINDM =  #{HUNYINDM,jdbcType=VARCHAR},
            ZHIYEDM =  #{ZHIYEDM,jdbcType=VARCHAR},
            GUOJIDM =  #{GUOJIDM,jdbcType=VARCHAR},
            MINZUDM =  #{MINZUDM,jdbcType=VARCHAR},
            SHENGFENDM =  #{SHENGFENDM,jdbcType=VARCHAR},
            JIGUANDM =  #{JIGUANDM,jdbcType=VARCHAR},
            CHUSHENGDDM =  #{CHUSHENGDDM,jdbcType=VARCHAR},
            JIHUASYFWZH =  #{JIHUASYFWZH,jdbcType=VARCHAR},
            SHENGYULB =  #{SHENGYULB,jdbcType=VARCHAR},
            WANYUBZ =  #{WANYUBZ,jdbcType=DECIMAL},
            ZAOCHANBZ =  #{ZAOCHANBZ,jdbcType=DECIMAL},
            JIHUASYSSLB =  #{JIHUASYSSLB,jdbcType=VARCHAR},
            XUELI =  #{XUELI,jdbcType=VARCHAR},
            ZAIRUYHZBZ =  #{ZAIRUYHZBZ,jdbcType=DECIMAL},
            XUETANGSQZ =  #{XUETANGSQZ,jdbcType=VARCHAR},
            XUETANGSQZT =  #{XUETANGSQZT,jdbcType=VARCHAR},
            SHAICHADQ =  #{SHAICHADQ,jdbcType=VARCHAR},
            XIANXUEZBZ =  #{XIANXUEZBZ,jdbcType=DECIMAL},
            JCPTEMPI =  #{JCPTEMPI,jdbcType=VARCHAR},
            IPT_PSN_SP_FLAG_DETL_ID =  #{IPT_PSN_SP_FLAG_DETL_ID,jdbcType=VARCHAR},
            LINCHUANGLJID =  #{LINCHUANGLJID,jdbcType=VARCHAR},
            SHIFOUYXZZJS =  #{SHIFOUYXZZJS,jdbcType=VARCHAR},
            PEIKEKH =  #{PEIKEKH,jdbcType=VARCHAR},
            KANGFUSQZT =  #{KANGFUSQZT,jdbcType=VARCHAR},
            KANGFUZLS =  #{KANGFUZLS,jdbcType=VARCHAR},
            KANGFUZLSXM =  #{KANGFUZLSXM,jdbcType=VARCHAR},
            CHANGHUXBRBZ =  #{CHANGHUXBRBZ,jdbcType=DECIMAL},
            YOUFUBRBZ =  #{YOUFUBRBZ,jdbcType=DECIMAL},
            LIANXIREN2 =  #{LIANXIREN2,jdbcType=VARCHAR},
            LIANXIRDH2 =  #{LIANXIRDH2,jdbcType=VARCHAR},
            GUANXI2 =  #{GUANXI2,jdbcType=VARCHAR},
            YUANQIANBQID =  #{YUANQIANBQID,jdbcType=VARCHAR},
            ZHUGUANYS =  #{ZHUGUANYS,jdbcType=VARCHAR},
            LIUQIANGZKBZ =  #{LIUQIANGZKBZ,jdbcType=DECIMAL}
        where   BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR} 
    </update>
</mapper>
