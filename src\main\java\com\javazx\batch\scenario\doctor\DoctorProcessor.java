package com.javazx.batch.scenario.doctor;

import com.javazx.batch.po.GyZhigongxx;
import com.javazx.batch.service.DoctorDataService;
import com.javazx.batch.service.JobTitleService;
import com.javazx.batch.vo.UserInfoReq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * 医护人员数据处理器
 * 将GyZhigongxx对象转换为UserInfoReq对象用于同步
 */
@Component
public class DoctorProcessor implements ItemProcessor<GyZhigongxx, UserInfoReq> {

    private static final Logger log = LoggerFactory.getLogger(DoctorProcessor.class);

    private final DoctorDataService doctorDataService;
    private final JobTitleService jobTitleService;

    public DoctorProcessor(DoctorDataService doctorDataService, JobTitleService jobTitleService) {
        this.doctorDataService = doctorDataService;
        this.jobTitleService = jobTitleService;
    }

    @Override
    public UserInfoReq process(GyZhigongxx gyZhigongxx) {
        if (gyZhigongxx == null) {
            return null;
        }

        log.debug("处理医护人员数据: {} - {}", gyZhigongxx.getZHIGONGID(), gyZhigongxx.getZHIGONGXM());

        // 转换为API请求对象
        UserInfoReq request = convert(gyZhigongxx);

        log.debug("完成医护人员数据处理: {}", gyZhigongxx.getZHIGONGXM());
        return request;
    }

    /**
     * 转换医护人员数据为用户信息请求
     */
    private UserInfoReq convert(GyZhigongxx gyZhigongxx) {
        UserInfoReq userInfo = new UserInfoReq();

        // 基本信息
        userInfo.setUserName(gyZhigongxx.getZHIGONGXM());
        userInfo.setJobNumber(gyZhigongxx.getZHIGONGGH());
        userInfo.setEmail(gyZhigongxx.getDIANZIYJ());
        userInfo.setPhone(gyZhigongxx.getDIANHUA());
        
        // 性别转换
        userInfo.setGender(convertGenderToLong(gyZhigongxx.getXINGBIE()));
        
        // 职称：将职称代码转换为职称名称
        String jobTitleName = convertJobTitleCodeToName(gyZhigongxx.getZHICHENG());
        userInfo.setJobTitle(jobTitleName);

        // 工作类别转换
        userInfo.setJobType(convertJobTypeByTitleCode(gyZhigongxx.getZHIGONGLB()));
        
        // 所属病区
        userInfo.setBelongWard(convertBelongWard(gyZhigongxx.getBINGQUID()));

        return userInfo;
    }

    /**
     * 将职称代码转换为职称名称
     * @param jobTitleCode 职称代码ID
     * @return 职称名称
     */
    private String convertJobTitleCodeToName(String jobTitleCode) {
        if (jobTitleCode == null || jobTitleCode.trim().isEmpty()) {
            return jobTitleCode;
        }

        try {
            return jobTitleService.getJobTitleNameByCode(jobTitleCode);
        } catch (Exception e) {
            log.warn("职称代码转换失败，返回原代码: {}", jobTitleCode, e);
            return jobTitleCode;
        }
    }

    /**
     * 根据职称代码转换工作类别
     * @param jobTitleCode 职称代码ID
     * @return 工作类别(0-全部, 1-管理员, 2-医生, 3-护士)
     */
    private Long convertJobTypeByTitleCode(String jobTitleCode) {
        if (jobTitleCode == null || jobTitleCode.trim().isEmpty()) {
            return 0L;
        }

        try {
            return jobTitleService.convertJobTypeByTitleCode(jobTitleCode);
        } catch (Exception e) {
            log.warn("职称代码转换工作类别失败，使用默认逻辑: {}", jobTitleCode, e);
            return 0L;
        }
    }

    /**
     * 根据职称名称转换工作类别（降级处理方法）
     * @param jobTitle 职称名称
     * @return 工作类别(0-全部, 1-管理员, 2-医生, 3-护士)
     */
    private Long convertJobTypeByTitleName(String jobTitle) {
        if (jobTitle == null || jobTitle.trim().isEmpty()) {
            return 0L;
        }

        String title = jobTitle.trim();

        // 医生类职称
        if (title.contains("医生") || title.contains("医师") ||
            title.contains("主治") || title.contains("主任医师") ||
            title.contains("副主任医师") || title.contains("中医") ||
            title.contains("中西医")) {
            return 2L; // 医生
        }

        // 护士类职称
        if (title.contains("护士") || title.contains("护师") ||
            title.contains("护理")) {
            return 3L; // 护士
        }

        // 管理员类职称
        if (title.contains("院长") || title.contains("科主任") ||
            title.contains("管理员") || title.contains("主任") ||
            title.contains("会计") || title.contains("政工") ||
            title.contains("经济") || title.contains("工程") ||
            title.contains("统计") || title.contains("工人")) {
            return 1L; // 管理员
        }

        return 0L; // 全部/其他
    }

    /**
     * 转换所属病区
     * @param bingquid 病区ID
     * @return 所属病区数组
     */
    private List<String> convertBelongWard(String bingquid) {
        if (StringUtils.hasLength(bingquid)) {
            return Arrays.asList(bingquid);
        }
        return new ArrayList<>();
    }

    /**
     * 转换性别为Long类型
     */
    private Long convertGenderToLong(String gender) {
        if (gender == null) {
            return 0L;
        }

        switch (gender.trim()) {
            case "男":
            case "1":
                return 1L;
            case "女":
            case "2":
                return 2L;
            default:
                return 0L;
        }
    }
}
