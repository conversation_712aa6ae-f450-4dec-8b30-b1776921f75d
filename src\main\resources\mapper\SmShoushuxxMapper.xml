<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.javazx.batch.mapper.SmShoushuxxMapper">

    <resultMap id="BaseResultMap" type="com.javazx.batch.po.SmShoushuxx">
            <id property="SHOUSHUDID" column="SHOUSHUDID" jdbcType="VARCHAR"/>
            <result property="SHOUSHUDH" column="SHOUSHUDH" jdbcType="VARCHAR"/>
            <result property="SHENQINGDID" column="SHENQINGDID" jdbcType="VARCHAR"/>
            <result property="YINGYONGID" column="YINGYONGID" jdbcType="VARCHAR"/>
            <result property="YUANQUID" column="YUANQUID" jdbcType="VARCHAR"/>
            <result property="BINGRENID" column="BINGRENID" jdbcType="VARCHAR"/>
            <result property="ZHUYUANHAO" column="ZHUYUANHAO" jdbcType="VARCHAR"/>
            <result property="BINGRENXM" column="BINGRENXM" jdbcType="VARCHAR"/>
            <result property="XINGBIE" column="XINGBIE" jdbcType="VARCHAR"/>
            <result property="NIANLING" column="NIANLING" jdbcType="DECIMAL"/>
            <result property="BINGRENKS" column="BINGRENKS" jdbcType="VARCHAR"/>
            <result property="BINGRENBQ" column="BINGRENBQ" jdbcType="VARCHAR"/>
            <result property="BINGRENCW" column="BINGRENCW" jdbcType="VARCHAR"/>
            <result property="FEIYONGXZ" column="FEIYONGXZ" jdbcType="VARCHAR"/>
            <result property="FEIYONGLB" column="FEIYONGLB" jdbcType="VARCHAR"/>
            <result property="BINGRENTZ" column="BINGRENTZ" jdbcType="DECIMAL"/>
            <result property="SHENQINGKS" column="SHENQINGKS" jdbcType="VARCHAR"/>
            <result property="ZHIXINGKS" column="ZHIXINGKS" jdbcType="VARCHAR"/>
            <result property="SHENQINGSJ" column="SHENQINGSJ" jdbcType="TIMESTAMP"/>
            <result property="YAOQIUSJ" column="YAOQIUSJ" jdbcType="TIMESTAMP"/>
            <result property="ANPAISJ" column="ANPAISJ" jdbcType="TIMESTAMP"/>
            <result property="JINXINGSJ" column="JINXINGSJ" jdbcType="TIMESTAMP"/>
            <result property="JIESHUSJ" column="JIESHUSJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHULB" column="SHOUSHULB" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMCID" column="SHOUSHUMCID" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC" column="SHOUSHUMC" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMCID1" column="SHOUSHUMCID1" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC1" column="SHOUSHUMC1" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMCID2" column="SHOUSHUMCID2" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC2" column="SHOUSHUMC2" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMCID3" column="SHOUSHUMCID3" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC3" column="SHOUSHUMC3" jdbcType="VARCHAR"/>
            <result property="SHOUSHUYS" column="SHOUSHUYS" jdbcType="VARCHAR"/>
            <result property="ZHULIYS1" column="ZHULIYS1" jdbcType="VARCHAR"/>
            <result property="ZHULIYS2" column="ZHULIYS2" jdbcType="VARCHAR"/>
            <result property="ZHULIYS3" column="ZHULIYS3" jdbcType="VARCHAR"/>
            <result property="XISHOUHS" column="XISHOUHS" jdbcType="VARCHAR"/>
            <result property="SIXIEHS1" column="SIXIEHS1" jdbcType="VARCHAR"/>
            <result property="SIXIEHS2" column="SIXIEHS2" jdbcType="VARCHAR"/>
            <result property="XUNHUIHS1" column="XUNHUIHS1" jdbcType="VARCHAR"/>
            <result property="XUNHUIHS2" column="XUNHUIHS2" jdbcType="VARCHAR"/>
            <result property="XUNHUIHS3" column="XUNHUIHS3" jdbcType="VARCHAR"/>
            <result property="MAZUIID" column="MAZUIID" jdbcType="VARCHAR"/>
            <result property="MAZUIFF" column="MAZUIFF" jdbcType="VARCHAR"/>
            <result property="MAZUIYS" column="MAZUIYS" jdbcType="VARCHAR"/>
            <result property="MAZUIYS1" column="MAZUIYS1" jdbcType="VARCHAR"/>
            <result property="MAZUIYS2" column="MAZUIYS2" jdbcType="VARCHAR"/>
            <result property="MAZUIYS3" column="MAZUIYS3" jdbcType="VARCHAR"/>
            <result property="SHOUSHUTID" column="SHOUSHUTID" jdbcType="VARCHAR"/>
            <result property="JIETAIHAO" column="JIETAIHAO" jdbcType="VARCHAR"/>
            <result property="SHOUCISSBZ" column="SHOUCISSBZ" jdbcType="DECIMAL"/>
            <result property="JIZHENBZ" column="JIZHENBZ" jdbcType="DECIMAL"/>
            <result property="ZHENTONGBSYBZ" column="ZHENTONGBSYBZ" jdbcType="DECIMAL"/>
            <result property="SHOUSHUCKLX" column="SHOUSHUCKLX" jdbcType="VARCHAR"/>
            <result property="CHUANGKOUYHQK" column="CHUANGKOUYHQK" jdbcType="VARCHAR"/>
            <result property="CHUANGKOUGRQK" column="CHUANGKOUGRQK" jdbcType="VARCHAR"/>
            <result property="SHUQIANXX" column="SHUQIANXX" jdbcType="VARCHAR"/>
            <result property="SHENQINGREN" column="SHENQINGREN" jdbcType="VARCHAR"/>
            <result property="CAOZUOYUAN" column="CAOZUOYUAN" jdbcType="VARCHAR"/>
            <result property="ZHENDUANDM1" column="ZHENDUANDM1" jdbcType="VARCHAR"/>
            <result property="ZHENDUANMC1" column="ZHENDUANMC1" jdbcType="VARCHAR"/>
            <result property="ZHENDUANDM2" column="ZHENDUANDM2" jdbcType="VARCHAR"/>
            <result property="ZHENDUANMC2" column="ZHENDUANMC2" jdbcType="VARCHAR"/>
            <result property="ZHENDUANDM3" column="ZHENDUANDM3" jdbcType="VARCHAR"/>
            <result property="ZHENDUANMC3" column="ZHENDUANMC3" jdbcType="VARCHAR"/>
            <result property="ZHENDUANDM4" column="ZHENDUANDM4" jdbcType="VARCHAR"/>
            <result property="ZHENDUANMC4" column="ZHENDUANMC4" jdbcType="VARCHAR"/>
            <result property="ZUOFEIBZ" column="ZUOFEIBZ" jdbcType="DECIMAL"/>
            <result property="ZHUANGTAIBZ" column="ZHUANGTAIBZ" jdbcType="DECIMAL"/>
            <result property="DUIHUAXX" column="DUIHUAXX" jdbcType="VARCHAR"/>
            <result property="BEIZHU" column="BEIZHU" jdbcType="VARCHAR"/>
            <result property="MENZHENZYBZ" column="MENZHENZYBZ" jdbcType="DECIMAL"/>
            <result property="XIUGAIREN" column="XIUGAIREN" jdbcType="VARCHAR"/>
            <result property="XIUGAISJ" column="XIUGAISJ" jdbcType="TIMESTAMP"/>
            <result property="NIANLINGDW" column="NIANLINGDW" jdbcType="VARCHAR"/>
            <result property="BINGRENZYID" column="BINGRENZYID" jdbcType="VARCHAR"/>
            <result property="YINGERBZ" column="YINGERBZ" jdbcType="DECIMAL"/>
            <result property="YINGERID" column="YINGERID" jdbcType="VARCHAR"/>
            <result property="XISHOUHSBZ" column="XISHOUHSBZ" jdbcType="DECIMAL"/>
            <result property="GELIBZ" column="GELIBZ" jdbcType="DECIMAL"/>
            <result property="YAOQIUST" column="YAOQIUST" jdbcType="DECIMAL"/>
            <result property="SHOUSHUJB" column="SHOUSHUJB" jdbcType="VARCHAR"/>
            <result property="SHENGAO" column="SHENGAO" jdbcType="DECIMAL"/>
            <result property="ZAOYING" column="ZAOYING" jdbcType="VARCHAR"/>
            <result property="TESHUSSBZ" column="TESHUSSBZ" jdbcType="DECIMAL"/>
            <result property="KAIDANYYID" column="KAIDANYYID" jdbcType="VARCHAR"/>
            <result property="JIANCHAXMID" column="JIANCHAXMID" jdbcType="VARCHAR"/>
            <result property="JIANCHAJKID1" column="JIANCHAJKID1" jdbcType="VARCHAR"/>
            <result property="TIWAIXHS" column="TIWAIXHS" jdbcType="VARCHAR"/>
            <result property="MAZUIAPZT" column="MAZUIAPZT" jdbcType="DECIMAL"/>
            <result property="MAZUIFF1" column="MAZUIFF1" jdbcType="VARCHAR"/>
            <result property="FUHEMZFFMC" column="FUHEMZFFMC" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW" column="SHOUSHUBW" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMCID4" column="SHOUSHUMCID4" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC4" column="SHOUSHUMC4" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMCID5" column="SHOUSHUMCID5" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC5" column="SHOUSHUMC5" jdbcType="VARCHAR"/>
            <result property="JIUZHENID" column="JIUZHENID" jdbcType="VARCHAR"/>
            <result property="SHUHOUSSMCID" column="SHUHOUSSMCID" jdbcType="VARCHAR"/>
            <result property="SHUHOUSSMC" column="SHUHOUSSMC" jdbcType="VARCHAR"/>
            <result property="RUSHISJ" column="RUSHISJ" jdbcType="TIMESTAMP"/>
            <result property="CHUSHISJ" column="CHUSHISJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHUKS" column="SHOUSHUKS" jdbcType="VARCHAR"/>
            <result property="MAZUISJ" column="MAZUISJ" jdbcType="TIMESTAMP"/>
            <result property="FEIJIHCFSSSBZ" column="FEIJIHCFSSSBZ" jdbcType="DECIMAL"/>
            <result property="SHUQIANTL" column="SHUQIANTL" jdbcType="DECIMAL"/>
            <result property="YIWUCBABZ" column="YIWUCBABZ" jdbcType="DECIMAL"/>
            <result property="YIGANGLDB" column="YIGANGLDB" jdbcType="DECIMAL"/>
            <result property="MEIDUKT" column="MEIDUKT" jdbcType="DECIMAL"/>
            <result property="AIZIBKT" column="AIZIBKT" jdbcType="DECIMAL"/>
            <result property="BINGGANKT" column="BINGGANKT" jdbcType="DECIMAL"/>
            <result property="QITATSGR" column="QITATSGR" jdbcType="VARCHAR"/>
            <result property="BUCHUANSSS" column="BUCHUANSSS" jdbcType="DECIMAL"/>
            <result property="PAICHIBZ" column="PAICHIBZ" jdbcType="DECIMAL"/>
            <result property="SHENHEBZ" column="SHENHEBZ" jdbcType="DECIMAL"/>
            <result property="SHENHESJ" column="SHENHESJ" jdbcType="TIMESTAMP"/>
            <result property="SHENHEREN" column="SHENHEREN" jdbcType="VARCHAR"/>
            <result property="BINGDONGBLBZ" column="BINGDONGBLBZ" jdbcType="DECIMAL"/>
            <result property="GELICS" column="GELICS" jdbcType="VARCHAR"/>
            <result property="YUFANGYYSJ" column="YUFANGYYSJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHUFXJB" column="SHOUSHUFXJB" jdbcType="VARCHAR"/>
            <result property="MAZUIFJ" column="MAZUIFJ" jdbcType="VARCHAR"/>
            <result property="ZHONGDASSBZ" column="ZHONGDASSBZ" jdbcType="DECIMAL"/>
            <result property="CHUANGKOUXGLY" column="CHUANGKOUXGLY" jdbcType="VARCHAR"/>
            <result property="SHOUSHUYJWCSJ" column="SHOUSHUYJWCSJ" jdbcType="TIMESTAMP"/>
            <result property="SHUXUEQSXBGBZ" column="SHUXUEQSXBGBZ" jdbcType="DECIMAL"/>
            <result property="SHUXUEQSXJG" column="SHUXUEQSXJG" jdbcType="VARCHAR"/>
            <result property="SHENGYUSHI" column="SHENGYUSHI" jdbcType="VARCHAR"/>
            <result property="HUNYIN" column="HUNYIN" jdbcType="VARCHAR"/>
            <result property="SHUQIANJCSFQQ" column="SHUQIANJCSFQQ" jdbcType="DECIMAL"/>
            <result property="SHIFOUSBLHY" column="SHIFOUSBLHY" jdbcType="DECIMAL"/>
            <result property="BIAOBEN" column="BIAOBEN" jdbcType="VARCHAR"/>
            <result property="BIAOBENJS" column="BIAOBENJS" jdbcType="DECIMAL"/>
            <result property="SHUZHONGYY" column="SHUZHONGYY" jdbcType="VARCHAR"/>
            <result property="LIUCHANYY" column="LIUCHANYY" jdbcType="VARCHAR"/>
            <result property="RONGMAOZT" column="RONGMAOZT" jdbcType="DECIMAL"/>
            <result property="SHUQIANGQ" column="SHUQIANGQ" jdbcType="DECIMAL"/>
            <result property="SHOUSHUZQK" column="SHOUSHUZQK" jdbcType="VARCHAR"/>
            <result property="JIEYUHMC" column="JIEYUHMC" jdbcType="VARCHAR"/>
            <result property="SHUHOUGQ" column="SHUHOUGQ" jdbcType="DECIMAL"/>
            <result property="CHUXUEQK" column="CHUXUEQK" jdbcType="VARCHAR"/>
            <result property="KUAKESSBZ" column="KUAKESSBZ" jdbcType="DECIMAL"/>
            <result property="GUANLIANSSDID" column="GUANLIANSSDID" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB1" column="SHOUSHUJB1" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX1" column="SHOUSHUCKLX1" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB2" column="SHOUSHUJB2" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX2" column="SHOUSHUCKLX2" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB3" column="SHOUSHUJB3" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX3" column="SHOUSHUCKLX3" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB4" column="SHOUSHUJB4" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX4" column="SHOUSHUCKLX4" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB5" column="SHOUSHUJB5" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX5" column="SHOUSHUCKLX5" jdbcType="VARCHAR"/>
            <result property="NEIJINGSSBZ" column="NEIJINGSSBZ" jdbcType="DECIMAL"/>
            <result property="NEIJINGSS" column="NEIJINGSS" jdbcType="VARCHAR"/>
            <result property="SHUHOUSSJB" column="SHUHOUSSJB" jdbcType="VARCHAR"/>
            <result property="SHUHOUCKLX" column="SHUHOUCKLX" jdbcType="VARCHAR"/>
            <result property="SHUZI1" column="SHUZI1" jdbcType="DECIMAL"/>
            <result property="SHUZI2" column="SHUZI2" jdbcType="DECIMAL"/>
            <result property="SHUZI3" column="SHUZI3" jdbcType="DECIMAL"/>
            <result property="SHUZI4" column="SHUZI4" jdbcType="DECIMAL"/>
            <result property="SHUZI5" column="SHUZI5" jdbcType="DECIMAL"/>
            <result property="SHUZI6" column="SHUZI6" jdbcType="DECIMAL"/>
            <result property="SHOUSHUJSBZ" column="SHOUSHUJSBZ" jdbcType="DECIMAL"/>
            <result property="SUXINGS" column="SUXINGS" jdbcType="VARCHAR"/>
            <result property="SHUZHONGYYSJ" column="SHUZHONGYYSJ" jdbcType="TIMESTAMP"/>
            <result property="LINCHUANGLJDRBZ" column="LINCHUANGLJDRBZ" jdbcType="DECIMAL"/>
            <result property="GELICSMC" column="GELICSMC" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY1" column="DIANZIBLSY1" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY2" column="DIANZIBLSY2" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY3" column="DIANZIBLSY3" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY4" column="DIANZIBLSY4" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY5" column="DIANZIBLSY5" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY6" column="DIANZIBLSY6" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY7" column="DIANZIBLSY7" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY8" column="DIANZIBLSY8" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY9" column="DIANZIBLSY9" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY10" column="DIANZIBLSY10" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY11" column="DIANZIBLSY11" jdbcType="VARCHAR"/>
            <result property="DIANZIBLSY12" column="DIANZIBLSY12" jdbcType="VARCHAR"/>
            <result property="SHIXUELIANG" column="SHIXUELIANG" jdbcType="VARCHAR"/>
            <result property="SHUXUELIANG" column="SHUXUELIANG" jdbcType="VARCHAR"/>
            <result property="YUSHENQBZ" column="YUSHENQBZ" jdbcType="DECIMAL"/>
            <result property="SHOUSHUYSYLZ" column="SHOUSHUYSYLZ" jdbcType="VARCHAR"/>
            <result property="SHOUSHUYJSC" column="SHOUSHUYJSC" jdbcType="VARCHAR"/>
            <result property="LINCHUANGLJID" column="LINCHUANGLJID" jdbcType="VARCHAR"/>
            <result property="WULIJJ" column="WULIJJ" jdbcType="VARCHAR"/>
            <result property="WULIQS" column="WULIQS" jdbcType="DECIMAL"/>
            <result property="DIMIANJJ" column="DIMIANJJ" jdbcType="VARCHAR"/>
            <result property="DIMIANQS" column="DIMIANQS" jdbcType="DECIMAL"/>
            <result property="LVWANGJJ" column="LVWANGJJ" jdbcType="VARCHAR"/>
            <result property="LVWANGQS" column="LVWANGQS" jdbcType="DECIMAL"/>
            <result property="GANRANLX" column="GANRANLX" jdbcType="VARCHAR"/>
            <result property="ZERENREN" column="ZERENREN" jdbcType="VARCHAR"/>
            <result property="YUFANGYYSZZJBZ" column="YUFANGYYSZZJBZ" jdbcType="DECIMAL"/>
            <result property="ZHIRUWU" column="ZHIRUWU" jdbcType="DECIMAL"/>
            <result property="SHOUSHUQJ" column="SHOUSHUQJ" jdbcType="DECIMAL"/>
            <result property="BINGLISFWZ" column="BINGLISFWZ" jdbcType="DECIMAL"/>
            <result property="BINGLIBWZXX" column="BINGLIBWZXX" jdbcType="VARCHAR"/>
            <result property="SHIFOUDLWLQX" column="SHIFOUDLWLQX" jdbcType="DECIMAL"/>
            <result property="BINGRENYLZ" column="BINGRENYLZ" jdbcType="VARCHAR"/>
            <result property="YISHENGZID" column="YISHENGZID" jdbcType="VARCHAR"/>
            <result property="ZHIDAOLS" column="ZHIDAOLS" jdbcType="VARCHAR"/>
            <result property="SIXIEHS3" column="SIXIEHS3" jdbcType="VARCHAR"/>
            <result property="JIESHENG1" column="JIESHENG1" jdbcType="VARCHAR"/>
            <result property="JIESHENG2" column="JIESHENG2" jdbcType="VARCHAR"/>
            <result property="SHUHOUSSQZ" column="SHUHOUSSQZ" jdbcType="VARCHAR"/>
            <result property="SHUHOUSSHZ" column="SHUHOUSSHZ" jdbcType="VARCHAR"/>
            <result property="XUSHENHBZ" column="XUSHENHBZ" jdbcType="DECIMAL"/>
            <result property="SHUHOUMCID1" column="SHUHOUMCID1" jdbcType="VARCHAR"/>
            <result property="SHUHOUMC1" column="SHUHOUMC1" jdbcType="VARCHAR"/>
            <result property="SHUHOUMCID2" column="SHUHOUMCID2" jdbcType="VARCHAR"/>
            <result property="SHUHOUMC2" column="SHUHOUMC2" jdbcType="VARCHAR"/>
            <result property="SHUHOUMCID3" column="SHUHOUMCID3" jdbcType="VARCHAR"/>
            <result property="SHUHOUMC3" column="SHUHOUMC3" jdbcType="VARCHAR"/>
            <result property="SHUHOUMCID4" column="SHUHOUMCID4" jdbcType="VARCHAR"/>
            <result property="SHUHOUMC4" column="SHUHOUMC4" jdbcType="VARCHAR"/>
            <result property="SHUHOUMCID5" column="SHUHOUMCID5" jdbcType="VARCHAR"/>
            <result property="SHUHOUMC5" column="SHUHOUMC5" jdbcType="VARCHAR"/>
            <result property="SHUHOUJB1" column="SHUHOUJB1" jdbcType="VARCHAR"/>
            <result property="SHUHOUCKLX1" column="SHUHOUCKLX1" jdbcType="VARCHAR"/>
            <result property="SHUHOUJB2" column="SHUHOUJB2" jdbcType="VARCHAR"/>
            <result property="SHUHOUCKLX2" column="SHUHOUCKLX2" jdbcType="VARCHAR"/>
            <result property="SHUHOUJB3" column="SHUHOUJB3" jdbcType="VARCHAR"/>
            <result property="SHUHOUCKLX3" column="SHUHOUCKLX3" jdbcType="VARCHAR"/>
            <result property="SHUHOUJB4" column="SHUHOUJB4" jdbcType="VARCHAR"/>
            <result property="SHUHOUCKLX4" column="SHUHOUCKLX4" jdbcType="VARCHAR"/>
            <result property="SHUHOUJB5" column="SHUHOUJB5" jdbcType="VARCHAR"/>
            <result property="SHUHOUCKLX5" column="SHUHOUCKLX5" jdbcType="VARCHAR"/>
            <result property="SHUHOUSSBW" column="SHUHOUSSBW" jdbcType="VARCHAR"/>
            <result property="QUSHOUSSJ" column="QUSHOUSSJ" jdbcType="TIMESTAMP"/>
            <result property="QUSHOUSJJHS" column="QUSHOUSJJHS" jdbcType="VARCHAR"/>
            <result property="QUSHOUSJJHSXM" column="QUSHOUSJJHSXM" jdbcType="VARCHAR"/>
            <result property="HUIBINGFSJ" column="HUIBINGFSJ" jdbcType="TIMESTAMP"/>
            <result property="HUIBINGFJJHS" column="HUIBINGFJJHS" jdbcType="VARCHAR"/>
            <result property="HUIBINGFJJHSXM" column="HUIBINGFJJHSXM" jdbcType="VARCHAR"/>
            <result property="MAZUIFS" column="MAZUIFS" jdbcType="DECIMAL"/>
            <result property="SHOUSHUAPR" column="SHOUSHUAPR" jdbcType="VARCHAR"/>
            <result property="SHOUSHUAPSJ" column="SHOUSHUAPSJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHUDJR" column="SHOUSHUDJR" jdbcType="VARCHAR"/>
            <result property="SHOUSHUDJSJ" column="SHOUSHUDJSJ" jdbcType="TIMESTAMP"/>
            <result property="MAZUIAPR" column="MAZUIAPR" jdbcType="VARCHAR"/>
            <result property="MAZUIAPSJ" column="MAZUIAPSJ" jdbcType="TIMESTAMP"/>
            <result property="MAZUIDJR" column="MAZUIDJR" jdbcType="VARCHAR"/>
            <result property="MAZUIDJSJ" column="MAZUIDJSJ" jdbcType="TIMESTAMP"/>
            <result property="YUJISC" column="YUJISC" jdbcType="DECIMAL"/>
            <result property="SHENHEBTGYY" column="SHENHEBTGYY" jdbcType="VARCHAR"/>
            <result property="JINGHUIFSSJ" column="JINGHUIFSSJ" jdbcType="TIMESTAMP"/>
            <result property="JINGHUIFSJJHS" column="JINGHUIFSJJHS" jdbcType="VARCHAR"/>
            <result property="JINGHUIFSJJHSXM" column="JINGHUIFSJJHSXM" jdbcType="VARCHAR"/>
            <result property="JINGSHOUSJSJ" column="JINGSHOUSJSJ" jdbcType="TIMESTAMP"/>
            <result property="JINGSHOUSJJJHS" column="JINGSHOUSJJJHS" jdbcType="VARCHAR"/>
            <result property="JINGSHOUSJJJHSXM" column="JINGSHOUSJJJHSXM" jdbcType="VARCHAR"/>
            <result property="SHOUSHULZZT" column="SHOUSHULZZT" jdbcType="VARCHAR"/>
            <result property="ZHONGZHISJ" column="ZHONGZHISJ" jdbcType="TIMESTAMP"/>
            <result property="ZHONGZHIJJHS" column="ZHONGZHIJJHS" jdbcType="VARCHAR"/>
            <result property="ZHONGZHIJJHSXM" column="ZHONGZHIJJHSXM" jdbcType="VARCHAR"/>
            <result property="CHUSHOUSSSJ" column="CHUSHOUSSSJ" jdbcType="TIMESTAMP"/>
            <result property="CHUSHOUSSJJHS" column="CHUSHOUSSJJHS" jdbcType="VARCHAR"/>
            <result property="CHUSHOUSSJJHSXM" column="CHUSHOUSSJJHSXM" jdbcType="VARCHAR"/>
            <result property="CHUHUIFSSJ" column="CHUHUIFSSJ" jdbcType="TIMESTAMP"/>
            <result property="CHUHUIFSJJHS" column="CHUHUIFSJJHS" jdbcType="VARCHAR"/>
            <result property="CHUHUIFSJJHSXM" column="CHUHUIFSJJHSXM" jdbcType="VARCHAR"/>
            <result property="FENGUANYZSHBZ" column="FENGUANYZSHBZ" jdbcType="DECIMAL"/>
            <result property="FENGUANYZ" column="FENGUANYZ" jdbcType="VARCHAR"/>
            <result property="FENGUANYZSHSJ" column="FENGUANYZSHSJ" jdbcType="TIMESTAMP"/>
            <result property="FENGUANYZSHBTGYY" column="FENGUANYZSHBTGYY" jdbcType="VARCHAR"/>
            <result property="KEZHURSHBZ" column="KEZHURSHBZ" jdbcType="DECIMAL"/>
            <result property="KEZHUREN" column="KEZHUREN" jdbcType="VARCHAR"/>
            <result property="KEZHURSHSJ" column="KEZHURSHSJ" jdbcType="TIMESTAMP"/>
            <result property="KEZHURSHBTGYY" column="KEZHURSHBTGYY" jdbcType="VARCHAR"/>
            <result property="YIWUKSHBZ" column="YIWUKSHBZ" jdbcType="DECIMAL"/>
            <result property="YIWUKE" column="YIWUKE" jdbcType="VARCHAR"/>
            <result property="YIWUKSHSJ" column="YIWUKSHSJ" jdbcType="TIMESTAMP"/>
            <result property="YIWUKSHBTGYY" column="YIWUKSHBTGYY" jdbcType="VARCHAR"/>
            <result property="SHANGBAOSY" column="SHANGBAOSY" jdbcType="VARCHAR"/>
            <result property="SHANGBAOYS" column="SHANGBAOYS" jdbcType="VARCHAR"/>
            <result property="XUJIEJWT" column="XUJIEJWT" jdbcType="VARCHAR"/>
            <result property="SHUQIANTLJG" column="SHUQIANTLJG" jdbcType="VARCHAR"/>
            <result property="YICAIQCS" column="YICAIQCS" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYSSHBZ" column="ZHUZHIYSSHBZ" jdbcType="DECIMAL"/>
            <result property="ZHUZHIYSSHR" column="ZHUZHIYSSHR" jdbcType="VARCHAR"/>
            <result property="ZHUZHIYSSHSJ" column="ZHUZHIYSSHSJ" jdbcType="TIMESTAMP"/>
            <result property="ZHUZHIYSSHBTGYY" column="ZHUZHIYSSHBTGYY" jdbcType="DECIMAL"/>
            <result property="SHUQIANGRQK" column="SHUQIANGRQK" jdbcType="VARCHAR"/>
            <result property="BINGFAZHENG" column="BINGFAZHENG" jdbcType="VARCHAR"/>
            <result property="DUOCHONGNYBZ" column="DUOCHONGNYBZ" jdbcType="DECIMAL"/>
            <result property="ZHUDAOYSSHBZ" column="ZHUDAOYSSHBZ" jdbcType="DECIMAL"/>
            <result property="ZHUDAOYSSHR" column="ZHUDAOYSSHR" jdbcType="VARCHAR"/>
            <result property="ZHUDAOYSSHSJ" column="ZHUDAOYSSHSJ" jdbcType="TIMESTAMP"/>
            <result property="MAZUIYSSHBZ" column="MAZUIYSSHBZ" jdbcType="DECIMAL"/>
            <result property="MAZUIYSSHR" column="MAZUIYSSHR" jdbcType="VARCHAR"/>
            <result property="MAZUIYSSHSJ" column="MAZUIYSSHSJ" jdbcType="TIMESTAMP"/>
            <result property="MAZUIYF" column="MAZUIYF" jdbcType="VARCHAR"/>
            <result property="SHENQINGYS" column="SHENQINGYS" jdbcType="VARCHAR"/>
            <result property="YISHENGSQRQ" column="YISHENGSQRQ" jdbcType="TIMESTAMP"/>
            <result property="ZHENDUAN" column="ZHENDUAN" jdbcType="VARCHAR"/>
            <result property="SHANGJIYSSHBZ" column="SHANGJIYSSHBZ" jdbcType="DECIMAL"/>
            <result property="SHANGJIYSSHSJ" column="SHANGJIYSSHSJ" jdbcType="TIMESTAMP"/>
            <result property="SHANGJIYSSHR" column="SHANGJIYSSHR" jdbcType="VARCHAR"/>
            <result property="SHANGJIYS" column="SHANGJIYS" jdbcType="VARCHAR"/>
            <result property="SHANGJIYSSHBTGYY" column="SHANGJIYSSHBTGYY" jdbcType="VARCHAR"/>
            <result property="SHUQIANSSJB" column="SHUQIANSSJB" jdbcType="VARCHAR"/>
            <result property="MAZUIHZDID" column="MAZUIHZDID" jdbcType="VARCHAR"/>
            <result property="SHOUSHUDM" column="SHOUSHUDM" jdbcType="VARCHAR"/>
            <result property="JINICUHS" column="JINICUHS" jdbcType="VARCHAR"/>
            <result property="JINICUSJ" column="JINICUSJ" jdbcType="TIMESTAMP"/>
            <result property="JINICUHSXM" column="JINICUHSXM" jdbcType="VARCHAR"/>
            <result property="CHUICUHS" column="CHUICUHS" jdbcType="VARCHAR"/>
            <result property="CHUICUSJ" column="CHUICUSJ" jdbcType="TIMESTAMP"/>
            <result property="CHUICUHSXM" column="CHUICUHSXM" jdbcType="VARCHAR"/>
            <result property="JUJUELX" column="JUJUELX" jdbcType="VARCHAR"/>
            <result property="JUJUEYY" column="JUJUEYY" jdbcType="VARCHAR"/>
            <result property="TAICI" column="TAICI" jdbcType="VARCHAR"/>
            <result property="BEIZHU1" column="BEIZHU1" jdbcType="VARCHAR"/>
            <result property="BEIZHU2" column="BEIZHU2" jdbcType="VARCHAR"/>
            <result property="ZHIQINGTYSZT" column="ZHIQINGTYSZT" jdbcType="DECIMAL"/>
            <result property="WEIJIZHIBZ" column="WEIJIZHIBZ" jdbcType="DECIMAL"/>
            <result property="SHUHOUZHENTONGBZ" column="SHUHOUZHENTONGBZ" jdbcType="DECIMAL"/>
            <result property="ISKANGJUNYW" column="ISKANGJUNYW" jdbcType="DECIMAL"/>
            <result property="SHOUSHUSLX" column="SHOUSHUSLX" jdbcType="VARCHAR"/>
            <result property="RIJIANSSBZ" column="RIJIANSSBZ" jdbcType="DECIMAL"/>
            <result property="ZHENDUANDM" column="ZHENDUANDM" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW2" column="SHOUSHUBW2" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW3" column="SHOUSHUBW3" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW4" column="SHOUSHUBW4" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW5" column="SHOUSHUBW5" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW1" column="SHOUSHUBW1" jdbcType="VARCHAR"/>
            <result property="MAZUIJSBZ" column="MAZUIJSBZ" jdbcType="DECIMAL"/>
            <result property="MAZUIJSREN" column="MAZUIJSREN" jdbcType="VARCHAR"/>
            <result property="MAZUIJSSJ" column="MAZUIJSSJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHUJZJSREN" column="SHOUSHUJZJSREN" jdbcType="VARCHAR"/>
            <result property="MAZUIJZJSREN" column="MAZUIJZJSREN" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJZSJ" column="SHOUSHUJZSJ" jdbcType="TIMESTAMP"/>
            <result property="QUXIAOYY" column="QUXIAOYY" jdbcType="VARCHAR"/>
            <result property="YUANYINLX" column="YUANYINLX" jdbcType="VARCHAR"/>
            <result property="QUXIAOR" column="QUXIAOR" jdbcType="VARCHAR"/>
            <result property="QUXIAOSJ" column="QUXIAOSJ" jdbcType="TIMESTAMP"/>
            <result property="SHOUSHUKSREN" column="SHOUSHUKSREN" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJSREN" column="SHOUSHUJSREN" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJZBZ" column="SHOUSHUJZBZ" jdbcType="DECIMAL"/>
            <result property="MAZUIJZBZ" column="MAZUIJZBZ" jdbcType="DECIMAL"/>
            <result property="MAZUIJZSJ" column="MAZUIJZSJ" jdbcType="TIMESTAMP"/>
            <result property="WAIYUANBZ" column="WAIYUANBZ" jdbcType="DECIMAL"/>
            <result property="ZHUANYUNFS" column="ZHUANYUNFS" jdbcType="VARCHAR"/>
            <result property="SHOUSHUXZ" column="SHOUSHUXZ" jdbcType="VARCHAR"/>
            <result property="SHOUSHUXZQT" column="SHOUSHUXZQT" jdbcType="VARCHAR"/>
            <result property="FEIJIHUAZCSS" column="FEIJIHUAZCSS" jdbcType="VARCHAR"/>
            <result property="TIWEI" column="TIWEI" jdbcType="VARCHAR"/>
            <result property="ZHIDAOYS" column="ZHIDAOYS" jdbcType="VARCHAR"/>
            <result property="XUYAOSZBD" column="XUYAOSZBD" jdbcType="DECIMAL"/>
            <result property="XUYAOBBCJ" column="XUYAOBBCJ" jdbcType="DECIMAL"/>
            <result property="SHENQINGSHZHBF" column="SHENQINGSHZHBF" jdbcType="DECIMAL"/>
            <result property="SHENQINGZICU" column="SHENQINGZICU" jdbcType="DECIMAL"/>
            <result property="SHUHOUZT" column="SHUHOUZT" jdbcType="DECIMAL"/>
            <result property="GENTAI" column="GENTAI" jdbcType="DECIMAL"/>
            <result property="SHOUSHUBZ" column="SHOUSHUBZ" jdbcType="VARCHAR"/>
            <result property="SONGFUSS" column="SONGFUSS" jdbcType="DECIMAL"/>
            <result property="TESHUJC" column="TESHUJC" jdbcType="VARCHAR"/>
            <result property="MAZUISSS" column="MAZUISSS" jdbcType="VARCHAR"/>
            <result property="MAZUIPG" column="MAZUIPG" jdbcType="VARCHAR"/>
            <result property="MAZUIPGNR" column="MAZUIPGNR" jdbcType="VARCHAR"/>
            <result property="WEICHUANGSSBZ" column="WEICHUANGSSBZ" jdbcType="DECIMAL"/>
            <result property="MAZUIPGBLJLXH" column="MAZUIPGBLJLXH" jdbcType="DECIMAL"/>
            <result property="SHOUSHUBW6" column="SHOUSHUBW6" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC6" column="SHOUSHUMC6" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID6" column="SHOUSHUID6" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW7" column="SHOUSHUBW7" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC7" column="SHOUSHUMC7" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID7" column="SHOUSHUID7" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW8" column="SHOUSHUBW8" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC8" column="SHOUSHUMC8" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID8" column="SHOUSHUID8" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW9" column="SHOUSHUBW9" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC9" column="SHOUSHUMC9" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID9" column="SHOUSHUID9" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW10" column="SHOUSHUBW10" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC10" column="SHOUSHUMC10" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID10" column="SHOUSHUID10" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW11" column="SHOUSHUBW11" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC11" column="SHOUSHUMC11" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID11" column="SHOUSHUID11" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW12" column="SHOUSHUBW12" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC12" column="SHOUSHUMC12" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID12" column="SHOUSHUID12" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW13" column="SHOUSHUBW13" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC13" column="SHOUSHUMC13" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID13" column="SHOUSHUID13" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW14" column="SHOUSHUBW14" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC14" column="SHOUSHUMC14" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID14" column="SHOUSHUID14" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW15" column="SHOUSHUBW15" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC15" column="SHOUSHUMC15" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID15" column="SHOUSHUID15" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW16" column="SHOUSHUBW16" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC16" column="SHOUSHUMC16" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID16" column="SHOUSHUID16" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW17" column="SHOUSHUBW17" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC17" column="SHOUSHUMC17" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID17" column="SHOUSHUID17" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW18" column="SHOUSHUBW18" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC18" column="SHOUSHUMC18" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID18" column="SHOUSHUID18" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW19" column="SHOUSHUBW19" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC19" column="SHOUSHUMC19" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID19" column="SHOUSHUID19" jdbcType="VARCHAR"/>
            <result property="SHOUSHUBW20" column="SHOUSHUBW20" jdbcType="VARCHAR"/>
            <result property="SHOUSHUMC20" column="SHOUSHUMC20" jdbcType="VARCHAR"/>
            <result property="SHOUSHUID20" column="SHOUSHUID20" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB6" column="SHOUSHUJB6" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX6" column="SHOUSHUCKLX6" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB7" column="SHOUSHUJB7" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX7" column="SHOUSHUCKLX7" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB8" column="SHOUSHUJB8" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX8" column="SHOUSHUCKLX8" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB9" column="SHOUSHUJB9" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX9" column="SHOUSHUCKLX9" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB10" column="SHOUSHUJB10" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX10" column="SHOUSHUCKLX10" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB11" column="SHOUSHUJB11" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX11" column="SHOUSHUCKLX11" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB12" column="SHOUSHUJB12" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX12" column="SHOUSHUCKLX12" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB13" column="SHOUSHUJB13" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX13" column="SHOUSHUCKLX13" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB14" column="SHOUSHUJB14" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX14" column="SHOUSHUCKLX14" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB15" column="SHOUSHUJB15" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX15" column="SHOUSHUCKLX15" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB16" column="SHOUSHUJB16" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX16" column="SHOUSHUCKLX16" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB17" column="SHOUSHUJB17" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX17" column="SHOUSHUCKLX17" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB18" column="SHOUSHUJB18" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX18" column="SHOUSHUCKLX18" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB19" column="SHOUSHUJB19" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX19" column="SHOUSHUCKLX19" jdbcType="VARCHAR"/>
            <result property="SHOUSHUJB20" column="SHOUSHUJB20" jdbcType="VARCHAR"/>
            <result property="SHOUSHUCKLX20" column="SHOUSHUCKLX20" jdbcType="VARCHAR"/>
            <result property="SHOUHU" column="SHOUHU" jdbcType="VARCHAR"/>
            <result property="SHOUHU1" column="SHOUHU1" jdbcType="VARCHAR"/>
            <result property="SHOUHU2" column="SHOUHU2" jdbcType="VARCHAR"/>
            <result property="SHOUHU3" column="SHOUHU3" jdbcType="VARCHAR"/>
            <result property="SHOUHU4" column="SHOUHU4" jdbcType="VARCHAR"/>
            <result property="SHOUHU5" column="SHOUHU5" jdbcType="VARCHAR"/>
            <result property="SHOUHU6" column="SHOUHU6" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        SHOUSHUDID,SHOUSHUDH,SHENQINGDID,
        YINGYONGID,YUANQUID,BINGRENID,
        ZHUYUANHAO,BINGRENXM,XINGBIE,
        NIANLING,BINGRENKS,BINGRENBQ,
        BINGRENCW,FEIYONGXZ,FEIYONGLB,
        BINGRENTZ,SHENQINGKS,ZHIXINGKS,
        SHENQINGSJ,YAOQIUSJ,ANPAISJ,
        JINXINGSJ,JIESHUSJ,SHOUSHULB,
        SHOUSHUMCID,SHOUSHUMC,SHOUSHUMCID1,
        SHOUSHUMC1,SHOUSHUMCID2,SHOUSHUMC2,
        SHOUSHUMCID3,SHOUSHUMC3,SHOUSHUYS,
        ZHULIYS1,ZHULIYS2,ZHULIYS3,
        XISHOUHS,SIXIEHS1,SIXIEHS2,
        XUNHUIHS1,XUNHUIHS2,XUNHUIHS3,
        MAZUIID,MAZUIFF,MAZUIYS,
        MAZUIYS1,MAZUIYS2,MAZUIYS3,
        SHOUSHUTID,JIETAIHAO,SHOUCISSBZ,
        JIZHENBZ,ZHENTONGBSYBZ,SHOUSHUCKLX,
        CHUANGKOUYHQK,CHUANGKOUGRQK,SHUQIANXX,
        SHENQINGREN,CAOZUOYUAN,ZHENDUANDM1,
        ZHENDUANMC1,ZHENDUANDM2,ZHENDUANMC2,
        ZHENDUANDM3,ZHENDUANMC3,ZHENDUANDM4,
        ZHENDUANMC4,ZUOFEIBZ,ZHUANGTAIBZ,
        DUIHUAXX,BEIZHU,MENZHENZYBZ,
        XIUGAIREN,XIUGAISJ,NIANLINGDW,
        BINGRENZYID,YINGERBZ,YINGERID,
        XISHOUHSBZ,GELIBZ,YAOQIUST,
        SHOUSHUJB,SHENGAO,ZAOYING,
        TESHUSSBZ,KAIDANYYID,JIANCHAXMID,
        JIANCHAJKID1,TIWAIXHS,MAZUIAPZT,
        MAZUIFF1,FUHEMZFFMC,SHOUSHUBW,
        SHOUSHUMCID4,SHOUSHUMC4,SHOUSHUMCID5,
        SHOUSHUMC5,JIUZHENID,SHUHOUSSMCID,
        SHUHOUSSMC,RUSHISJ,CHUSHISJ,
        SHOUSHUKS,MAZUISJ,FEIJIHCFSSSBZ,
        SHUQIANTL,YIWUCBABZ,YIGANGLDB,
        MEIDUKT,AIZIBKT,BINGGANKT,
        QITATSGR,BUCHUANSSS,PAICHIBZ,
        SHENHEBZ,SHENHESJ,SHENHEREN,
        BINGDONGBLBZ,GELICS,YUFANGYYSJ,
        SHOUSHUFXJB,MAZUIFJ,ZHONGDASSBZ,
        CHUANGKOUXGLY,SHOUSHUYJWCSJ,SHUXUEQSXBGBZ,
        SHUXUEQSXJG,SHENGYUSHI,HUNYIN,
        SHUQIANJCSFQQ,SHIFOUSBLHY,BIAOBEN,
        BIAOBENJS,SHUZHONGYY,LIUCHANYY,
        RONGMAOZT,SHUQIANGQ,SHOUSHUZQK,
        JIEYUHMC,SHUHOUGQ,CHUXUEQK,
        KUAKESSBZ,GUANLIANSSDID,SHOUSHUJB1,
        SHOUSHUCKLX1,SHOUSHUJB2,SHOUSHUCKLX2,
        SHOUSHUJB3,SHOUSHUCKLX3,SHOUSHUJB4,
        SHOUSHUCKLX4,SHOUSHUJB5,SHOUSHUCKLX5,
        NEIJINGSSBZ,NEIJINGSS,SHUHOUSSJB,
        SHUHOUCKLX,SHUZI1,SHUZI2,
        SHUZI3,SHUZI4,SHUZI5,
        SHUZI6,SHOUSHUJSBZ,SUXINGS,
        SHUZHONGYYSJ,LINCHUANGLJDRBZ,GELICSMC,
        DIANZIBLSY1,DIANZIBLSY2,DIANZIBLSY3,
        DIANZIBLSY4,DIANZIBLSY5,DIANZIBLSY6,
        DIANZIBLSY7,DIANZIBLSY8,DIANZIBLSY9,
        DIANZIBLSY10,DIANZIBLSY11,DIANZIBLSY12,
        SHIXUELIANG,SHUXUELIANG,YUSHENQBZ,
        SHOUSHUYSYLZ,SHOUSHUYJSC,LINCHUANGLJID,
        WULIJJ,WULIQS,DIMIANJJ,
        DIMIANQS,LVWANGJJ,LVWANGQS,
        GANRANLX,ZERENREN,YUFANGYYSZZJBZ,
        ZHIRUWU,SHOUSHUQJ,BINGLISFWZ,
        BINGLIBWZXX,SHIFOUDLWLQX,BINGRENYLZ,
        YISHENGZID,ZHIDAOLS,SIXIEHS3,
        JIESHENG1,JIESHENG2,SHUHOUSSQZ,
        SHUHOUSSHZ,XUSHENHBZ,SHUHOUMCID1,
        SHUHOUMC1,SHUHOUMCID2,SHUHOUMC2,
        SHUHOUMCID3,SHUHOUMC3,SHUHOUMCID4,
        SHUHOUMC4,SHUHOUMCID5,SHUHOUMC5,
        SHUHOUJB1,SHUHOUCKLX1,SHUHOUJB2,
        SHUHOUCKLX2,SHUHOUJB3,SHUHOUCKLX3,
        SHUHOUJB4,SHUHOUCKLX4,SHUHOUJB5,
        SHUHOUCKLX5,SHUHOUSSBW,QUSHOUSSJ,
        QUSHOUSJJHS,QUSHOUSJJHSXM,HUIBINGFSJ,
        HUIBINGFJJHS,HUIBINGFJJHSXM,MAZUIFS,
        SHOUSHUAPR,SHOUSHUAPSJ,SHOUSHUDJR,
        SHOUSHUDJSJ,MAZUIAPR,MAZUIAPSJ,
        MAZUIDJR,MAZUIDJSJ,YUJISC,
        SHENHEBTGYY,JINGHUIFSSJ,JINGHUIFSJJHS,
        JINGHUIFSJJHSXM,JINGSHOUSJSJ,JINGSHOUSJJJHS,
        JINGSHOUSJJJHSXM,SHOUSHULZZT,ZHONGZHISJ,
        ZHONGZHIJJHS,ZHONGZHIJJHSXM,CHUSHOUSSSJ,
        CHUSHOUSSJJHS,CHUSHOUSSJJHSXM,CHUHUIFSSJ,
        CHUHUIFSJJHS,CHUHUIFSJJHSXM,FENGUANYZSHBZ,
        FENGUANYZ,FENGUANYZSHSJ,FENGUANYZSHBTGYY,
        KEZHURSHBZ,KEZHUREN,KEZHURSHSJ,
        KEZHURSHBTGYY,YIWUKSHBZ,YIWUKE,
        YIWUKSHSJ,YIWUKSHBTGYY,SHANGBAOSY,
        SHANGBAOYS,XUJIEJWT,SHUQIANTLJG,
        YICAIQCS,ZHUZHIYSSHBZ,ZHUZHIYSSHR,
        ZHUZHIYSSHSJ,ZHUZHIYSSHBTGYY,SHUQIANGRQK,
        BINGFAZHENG,DUOCHONGNYBZ,ZHUDAOYSSHBZ,
        ZHUDAOYSSHR,ZHUDAOYSSHSJ,MAZUIYSSHBZ,
        MAZUIYSSHR,MAZUIYSSHSJ,MAZUIYF,
        SHENQINGYS,YISHENGSQRQ,ZHENDUAN,
        SHANGJIYSSHBZ,SHANGJIYSSHSJ,SHANGJIYSSHR,
        SHANGJIYS,SHANGJIYSSHBTGYY,SHUQIANSSJB,
        MAZUIHZDID,SHOUSHUDM,JINICUHS,
        JINICUSJ,JINICUHSXM,CHUICUHS,
        CHUICUSJ,CHUICUHSXM,JUJUELX,
        JUJUEYY,TAICI,BEIZHU1,
        BEIZHU2,ZHIQINGTYSZT,WEIJIZHIBZ,
        SHUHOUZHENTONGBZ,ISKANGJUNYW,SHOUSHUSLX,
        RIJIANSSBZ,ZHENDUANDM,SHOUSHUBW2,
        SHOUSHUBW3,SHOUSHUBW4,SHOUSHUBW5,
        SHOUSHUBW1,MAZUIJSBZ,MAZUIJSREN,
        MAZUIJSSJ,SHOUSHUJZJSREN,MAZUIJZJSREN,
        SHOUSHUJZSJ,QUXIAOYY,YUANYINLX,
        QUXIAOR,QUXIAOSJ,SHOUSHUKSREN,
        SHOUSHUJSREN,SHOUSHUJZBZ,MAZUIJZBZ,
        MAZUIJZSJ,WAIYUANBZ,ZHUANYUNFS,
        SHOUSHUXZ,SHOUSHUXZQT,FEIJIHUAZCSS,
        TIWEI,ZHIDAOYS,XUYAOSZBD,
        XUYAOBBCJ,SHENQINGSHZHBF,SHENQINGZICU,
        SHUHOUZT,GENTAI,SHOUSHUBZ,
        SONGFUSS,TESHUJC,MAZUISSS,
        MAZUIPG,MAZUIPGNR,WEICHUANGSSBZ,
        MAZUIPGBLJLXH,SHOUSHUBW6,SHOUSHUMC6,
        SHOUSHUID6,SHOUSHUBW7,SHOUSHUMC7,
        SHOUSHUID7,SHOUSHUBW8,SHOUSHUMC8,
        SHOUSHUID8,SHOUSHUBW9,SHOUSHUMC9,
        SHOUSHUID9,SHOUSHUBW10,SHOUSHUMC10,
        SHOUSHUID10,SHOUSHUBW11,SHOUSHUMC11,
        SHOUSHUID11,SHOUSHUBW12,SHOUSHUMC12,
        SHOUSHUID12,SHOUSHUBW13,SHOUSHUMC13,
        SHOUSHUID13,SHOUSHUBW14,SHOUSHUMC14,
        SHOUSHUID14,SHOUSHUBW15,SHOUSHUMC15,
        SHOUSHUID15,SHOUSHUBW16,SHOUSHUMC16,
        SHOUSHUID16,SHOUSHUBW17,SHOUSHUMC17,
        SHOUSHUID17,SHOUSHUBW18,SHOUSHUMC18,
        SHOUSHUID18,SHOUSHUBW19,SHOUSHUMC19,
        SHOUSHUID19,SHOUSHUBW20,SHOUSHUMC20,
        SHOUSHUID20,SHOUSHUJB6,SHOUSHUCKLX6,
        SHOUSHUJB7,SHOUSHUCKLX7,SHOUSHUJB8,
        SHOUSHUCKLX8,SHOUSHUJB9,SHOUSHUCKLX9,
        SHOUSHUJB10,SHOUSHUCKLX10,SHOUSHUJB11,
        SHOUSHUCKLX11,SHOUSHUJB12,SHOUSHUCKLX12,
        SHOUSHUJB13,SHOUSHUCKLX13,SHOUSHUJB14,
        SHOUSHUCKLX14,SHOUSHUJB15,SHOUSHUCKLX15,
        SHOUSHUJB16,SHOUSHUCKLX16,SHOUSHUJB17,
        SHOUSHUCKLX17,SHOUSHUJB18,SHOUSHUCKLX18,
        SHOUSHUJB19,SHOUSHUCKLX19,SHOUSHUJB20,
        SHOUSHUCKLX20,SHOUHU,SHOUHU1,
        SHOUHU2,SHOUHU3,SHOUHU4,
        SHOUHU5,SHOUHU6
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from SM_SHOUSHUXX
        where  SHOUSHUDID = #{SHOUSHUDID,jdbcType=VARCHAR} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from SM_SHOUSHUXX
        where  SHOUSHUDID = #{SHOUSHUDID,jdbcType=VARCHAR} 
    </delete>
    <insert id="insert" keyColumn="SHOUSHUDID" keyProperty="SHOUSHUDID" parameterType="com.javazx.batch.po.SmShoushuxx" useGeneratedKeys="true">
        insert into SM_SHOUSHUXX
        ( SHOUSHUDID,SHOUSHUDH,SHENQINGDID
        ,YINGYONGID,YUANQUID,BINGRENID
        ,ZHUYUANHAO,BINGRENXM,XINGBIE
        ,NIANLING,BINGRENKS,BINGRENBQ
        ,BINGRENCW,FEIYONGXZ,FEIYONGLB
        ,BINGRENTZ,SHENQINGKS,ZHIXINGKS
        ,SHENQINGSJ,YAOQIUSJ,ANPAISJ
        ,JINXINGSJ,JIESHUSJ,SHOUSHULB
        ,SHOUSHUMCID,SHOUSHUMC,SHOUSHUMCID1
        ,SHOUSHUMC1,SHOUSHUMCID2,SHOUSHUMC2
        ,SHOUSHUMCID3,SHOUSHUMC3,SHOUSHUYS
        ,ZHULIYS1,ZHULIYS2,ZHULIYS3
        ,XISHOUHS,SIXIEHS1,SIXIEHS2
        ,XUNHUIHS1,XUNHUIHS2,XUNHUIHS3
        ,MAZUIID,MAZUIFF,MAZUIYS
        ,MAZUIYS1,MAZUIYS2,MAZUIYS3
        ,SHOUSHUTID,JIETAIHAO,SHOUCISSBZ
        ,JIZHENBZ,ZHENTONGBSYBZ,SHOUSHUCKLX
        ,CHUANGKOUYHQK,CHUANGKOUGRQK,SHUQIANXX
        ,SHENQINGREN,CAOZUOYUAN,ZHENDUANDM1
        ,ZHENDUANMC1,ZHENDUANDM2,ZHENDUANMC2
        ,ZHENDUANDM3,ZHENDUANMC3,ZHENDUANDM4
        ,ZHENDUANMC4,ZUOFEIBZ,ZHUANGTAIBZ
        ,DUIHUAXX,BEIZHU,MENZHENZYBZ
        ,XIUGAIREN,XIUGAISJ,NIANLINGDW
        ,BINGRENZYID,YINGERBZ,YINGERID
        ,XISHOUHSBZ,GELIBZ,YAOQIUST
        ,SHOUSHUJB,SHENGAO,ZAOYING
        ,TESHUSSBZ,KAIDANYYID,JIANCHAXMID
        ,JIANCHAJKID1,TIWAIXHS,MAZUIAPZT
        ,MAZUIFF1,FUHEMZFFMC,SHOUSHUBW
        ,SHOUSHUMCID4,SHOUSHUMC4,SHOUSHUMCID5
        ,SHOUSHUMC5,JIUZHENID,SHUHOUSSMCID
        ,SHUHOUSSMC,RUSHISJ,CHUSHISJ
        ,SHOUSHUKS,MAZUISJ,FEIJIHCFSSSBZ
        ,SHUQIANTL,YIWUCBABZ,YIGANGLDB
        ,MEIDUKT,AIZIBKT,BINGGANKT
        ,QITATSGR,BUCHUANSSS,PAICHIBZ
        ,SHENHEBZ,SHENHESJ,SHENHEREN
        ,BINGDONGBLBZ,GELICS,YUFANGYYSJ
        ,SHOUSHUFXJB,MAZUIFJ,ZHONGDASSBZ
        ,CHUANGKOUXGLY,SHOUSHUYJWCSJ,SHUXUEQSXBGBZ
        ,SHUXUEQSXJG,SHENGYUSHI,HUNYIN
        ,SHUQIANJCSFQQ,SHIFOUSBLHY,BIAOBEN
        ,BIAOBENJS,SHUZHONGYY,LIUCHANYY
        ,RONGMAOZT,SHUQIANGQ,SHOUSHUZQK
        ,JIEYUHMC,SHUHOUGQ,CHUXUEQK
        ,KUAKESSBZ,GUANLIANSSDID,SHOUSHUJB1
        ,SHOUSHUCKLX1,SHOUSHUJB2,SHOUSHUCKLX2
        ,SHOUSHUJB3,SHOUSHUCKLX3,SHOUSHUJB4
        ,SHOUSHUCKLX4,SHOUSHUJB5,SHOUSHUCKLX5
        ,NEIJINGSSBZ,NEIJINGSS,SHUHOUSSJB
        ,SHUHOUCKLX,SHUZI1,SHUZI2
        ,SHUZI3,SHUZI4,SHUZI5
        ,SHUZI6,SHOUSHUJSBZ,SUXINGS
        ,SHUZHONGYYSJ,LINCHUANGLJDRBZ,GELICSMC
        ,DIANZIBLSY1,DIANZIBLSY2,DIANZIBLSY3
        ,DIANZIBLSY4,DIANZIBLSY5,DIANZIBLSY6
        ,DIANZIBLSY7,DIANZIBLSY8,DIANZIBLSY9
        ,DIANZIBLSY10,DIANZIBLSY11,DIANZIBLSY12
        ,SHIXUELIANG,SHUXUELIANG,YUSHENQBZ
        ,SHOUSHUYSYLZ,SHOUSHUYJSC,LINCHUANGLJID
        ,WULIJJ,WULIQS,DIMIANJJ
        ,DIMIANQS,LVWANGJJ,LVWANGQS
        ,GANRANLX,ZERENREN,YUFANGYYSZZJBZ
        ,ZHIRUWU,SHOUSHUQJ,BINGLISFWZ
        ,BINGLIBWZXX,SHIFOUDLWLQX,BINGRENYLZ
        ,YISHENGZID,ZHIDAOLS,SIXIEHS3
        ,JIESHENG1,JIESHENG2,SHUHOUSSQZ
        ,SHUHOUSSHZ,XUSHENHBZ,SHUHOUMCID1
        ,SHUHOUMC1,SHUHOUMCID2,SHUHOUMC2
        ,SHUHOUMCID3,SHUHOUMC3,SHUHOUMCID4
        ,SHUHOUMC4,SHUHOUMCID5,SHUHOUMC5
        ,SHUHOUJB1,SHUHOUCKLX1,SHUHOUJB2
        ,SHUHOUCKLX2,SHUHOUJB3,SHUHOUCKLX3
        ,SHUHOUJB4,SHUHOUCKLX4,SHUHOUJB5
        ,SHUHOUCKLX5,SHUHOUSSBW,QUSHOUSSJ
        ,QUSHOUSJJHS,QUSHOUSJJHSXM,HUIBINGFSJ
        ,HUIBINGFJJHS,HUIBINGFJJHSXM,MAZUIFS
        ,SHOUSHUAPR,SHOUSHUAPSJ,SHOUSHUDJR
        ,SHOUSHUDJSJ,MAZUIAPR,MAZUIAPSJ
        ,MAZUIDJR,MAZUIDJSJ,YUJISC
        ,SHENHEBTGYY,JINGHUIFSSJ,JINGHUIFSJJHS
        ,JINGHUIFSJJHSXM,JINGSHOUSJSJ,JINGSHOUSJJJHS
        ,JINGSHOUSJJJHSXM,SHOUSHULZZT,ZHONGZHISJ
        ,ZHONGZHIJJHS,ZHONGZHIJJHSXM,CHUSHOUSSSJ
        ,CHUSHOUSSJJHS,CHUSHOUSSJJHSXM,CHUHUIFSSJ
        ,CHUHUIFSJJHS,CHUHUIFSJJHSXM,FENGUANYZSHBZ
        ,FENGUANYZ,FENGUANYZSHSJ,FENGUANYZSHBTGYY
        ,KEZHURSHBZ,KEZHUREN,KEZHURSHSJ
        ,KEZHURSHBTGYY,YIWUKSHBZ,YIWUKE
        ,YIWUKSHSJ,YIWUKSHBTGYY,SHANGBAOSY
        ,SHANGBAOYS,XUJIEJWT,SHUQIANTLJG
        ,YICAIQCS,ZHUZHIYSSHBZ,ZHUZHIYSSHR
        ,ZHUZHIYSSHSJ,ZHUZHIYSSHBTGYY,SHUQIANGRQK
        ,BINGFAZHENG,DUOCHONGNYBZ,ZHUDAOYSSHBZ
        ,ZHUDAOYSSHR,ZHUDAOYSSHSJ,MAZUIYSSHBZ
        ,MAZUIYSSHR,MAZUIYSSHSJ,MAZUIYF
        ,SHENQINGYS,YISHENGSQRQ,ZHENDUAN
        ,SHANGJIYSSHBZ,SHANGJIYSSHSJ,SHANGJIYSSHR
        ,SHANGJIYS,SHANGJIYSSHBTGYY,SHUQIANSSJB
        ,MAZUIHZDID,SHOUSHUDM,JINICUHS
        ,JINICUSJ,JINICUHSXM,CHUICUHS
        ,CHUICUSJ,CHUICUHSXM,JUJUELX
        ,JUJUEYY,TAICI,BEIZHU1
        ,BEIZHU2,ZHIQINGTYSZT,WEIJIZHIBZ
        ,SHUHOUZHENTONGBZ,ISKANGJUNYW,SHOUSHUSLX
        ,RIJIANSSBZ,ZHENDUANDM,SHOUSHUBW2
        ,SHOUSHUBW3,SHOUSHUBW4,SHOUSHUBW5
        ,SHOUSHUBW1,MAZUIJSBZ,MAZUIJSREN
        ,MAZUIJSSJ,SHOUSHUJZJSREN,MAZUIJZJSREN
        ,SHOUSHUJZSJ,QUXIAOYY,YUANYINLX
        ,QUXIAOR,QUXIAOSJ,SHOUSHUKSREN
        ,SHOUSHUJSREN,SHOUSHUJZBZ,MAZUIJZBZ
        ,MAZUIJZSJ,WAIYUANBZ,ZHUANYUNFS
        ,SHOUSHUXZ,SHOUSHUXZQT,FEIJIHUAZCSS
        ,TIWEI,ZHIDAOYS,XUYAOSZBD
        ,XUYAOBBCJ,SHENQINGSHZHBF,SHENQINGZICU
        ,SHUHOUZT,GENTAI,SHOUSHUBZ
        ,SONGFUSS,TESHUJC,MAZUISSS
        ,MAZUIPG,MAZUIPGNR,WEICHUANGSSBZ
        ,MAZUIPGBLJLXH,SHOUSHUBW6,SHOUSHUMC6
        ,SHOUSHUID6,SHOUSHUBW7,SHOUSHUMC7
        ,SHOUSHUID7,SHOUSHUBW8,SHOUSHUMC8
        ,SHOUSHUID8,SHOUSHUBW9,SHOUSHUMC9
        ,SHOUSHUID9,SHOUSHUBW10,SHOUSHUMC10
        ,SHOUSHUID10,SHOUSHUBW11,SHOUSHUMC11
        ,SHOUSHUID11,SHOUSHUBW12,SHOUSHUMC12
        ,SHOUSHUID12,SHOUSHUBW13,SHOUSHUMC13
        ,SHOUSHUID13,SHOUSHUBW14,SHOUSHUMC14
        ,SHOUSHUID14,SHOUSHUBW15,SHOUSHUMC15
        ,SHOUSHUID15,SHOUSHUBW16,SHOUSHUMC16
        ,SHOUSHUID16,SHOUSHUBW17,SHOUSHUMC17
        ,SHOUSHUID17,SHOUSHUBW18,SHOUSHUMC18
        ,SHOUSHUID18,SHOUSHUBW19,SHOUSHUMC19
        ,SHOUSHUID19,SHOUSHUBW20,SHOUSHUMC20
        ,SHOUSHUID20,SHOUSHUJB6,SHOUSHUCKLX6
        ,SHOUSHUJB7,SHOUSHUCKLX7,SHOUSHUJB8
        ,SHOUSHUCKLX8,SHOUSHUJB9,SHOUSHUCKLX9
        ,SHOUSHUJB10,SHOUSHUCKLX10,SHOUSHUJB11
        ,SHOUSHUCKLX11,SHOUSHUJB12,SHOUSHUCKLX12
        ,SHOUSHUJB13,SHOUSHUCKLX13,SHOUSHUJB14
        ,SHOUSHUCKLX14,SHOUSHUJB15,SHOUSHUCKLX15
        ,SHOUSHUJB16,SHOUSHUCKLX16,SHOUSHUJB17
        ,SHOUSHUCKLX17,SHOUSHUJB18,SHOUSHUCKLX18
        ,SHOUSHUJB19,SHOUSHUCKLX19,SHOUSHUJB20
        ,SHOUSHUCKLX20,SHOUHU,SHOUHU1
        ,SHOUHU2,SHOUHU3,SHOUHU4
        ,SHOUHU5,SHOUHU6)
        values (#{SHOUSHUDID,jdbcType=VARCHAR},#{SHOUSHUDH,jdbcType=VARCHAR},#{SHENQINGDID,jdbcType=VARCHAR}
        ,#{YINGYONGID,jdbcType=VARCHAR},#{YUANQUID,jdbcType=VARCHAR},#{BINGRENID,jdbcType=VARCHAR}
        ,#{ZHUYUANHAO,jdbcType=VARCHAR},#{BINGRENXM,jdbcType=VARCHAR},#{XINGBIE,jdbcType=VARCHAR}
        ,#{NIANLING,jdbcType=DECIMAL},#{BINGRENKS,jdbcType=VARCHAR},#{BINGRENBQ,jdbcType=VARCHAR}
        ,#{BINGRENCW,jdbcType=VARCHAR},#{FEIYONGXZ,jdbcType=VARCHAR},#{FEIYONGLB,jdbcType=VARCHAR}
        ,#{BINGRENTZ,jdbcType=DECIMAL},#{SHENQINGKS,jdbcType=VARCHAR},#{ZHIXINGKS,jdbcType=VARCHAR}
        ,#{SHENQINGSJ,jdbcType=TIMESTAMP},#{YAOQIUSJ,jdbcType=TIMESTAMP},#{ANPAISJ,jdbcType=TIMESTAMP}
        ,#{JINXINGSJ,jdbcType=TIMESTAMP},#{JIESHUSJ,jdbcType=TIMESTAMP},#{SHOUSHULB,jdbcType=VARCHAR}
        ,#{SHOUSHUMCID,jdbcType=VARCHAR},#{SHOUSHUMC,jdbcType=VARCHAR},#{SHOUSHUMCID1,jdbcType=VARCHAR}
        ,#{SHOUSHUMC1,jdbcType=VARCHAR},#{SHOUSHUMCID2,jdbcType=VARCHAR},#{SHOUSHUMC2,jdbcType=VARCHAR}
        ,#{SHOUSHUMCID3,jdbcType=VARCHAR},#{SHOUSHUMC3,jdbcType=VARCHAR},#{SHOUSHUYS,jdbcType=VARCHAR}
        ,#{ZHULIYS1,jdbcType=VARCHAR},#{ZHULIYS2,jdbcType=VARCHAR},#{ZHULIYS3,jdbcType=VARCHAR}
        ,#{XISHOUHS,jdbcType=VARCHAR},#{SIXIEHS1,jdbcType=VARCHAR},#{SIXIEHS2,jdbcType=VARCHAR}
        ,#{XUNHUIHS1,jdbcType=VARCHAR},#{XUNHUIHS2,jdbcType=VARCHAR},#{XUNHUIHS3,jdbcType=VARCHAR}
        ,#{MAZUIID,jdbcType=VARCHAR},#{MAZUIFF,jdbcType=VARCHAR},#{MAZUIYS,jdbcType=VARCHAR}
        ,#{MAZUIYS1,jdbcType=VARCHAR},#{MAZUIYS2,jdbcType=VARCHAR},#{MAZUIYS3,jdbcType=VARCHAR}
        ,#{SHOUSHUTID,jdbcType=VARCHAR},#{JIETAIHAO,jdbcType=VARCHAR},#{SHOUCISSBZ,jdbcType=DECIMAL}
        ,#{JIZHENBZ,jdbcType=DECIMAL},#{ZHENTONGBSYBZ,jdbcType=DECIMAL},#{SHOUSHUCKLX,jdbcType=VARCHAR}
        ,#{CHUANGKOUYHQK,jdbcType=VARCHAR},#{CHUANGKOUGRQK,jdbcType=VARCHAR},#{SHUQIANXX,jdbcType=VARCHAR}
        ,#{SHENQINGREN,jdbcType=VARCHAR},#{CAOZUOYUAN,jdbcType=VARCHAR},#{ZHENDUANDM1,jdbcType=VARCHAR}
        ,#{ZHENDUANMC1,jdbcType=VARCHAR},#{ZHENDUANDM2,jdbcType=VARCHAR},#{ZHENDUANMC2,jdbcType=VARCHAR}
        ,#{ZHENDUANDM3,jdbcType=VARCHAR},#{ZHENDUANMC3,jdbcType=VARCHAR},#{ZHENDUANDM4,jdbcType=VARCHAR}
        ,#{ZHENDUANMC4,jdbcType=VARCHAR},#{ZUOFEIBZ,jdbcType=DECIMAL},#{ZHUANGTAIBZ,jdbcType=DECIMAL}
        ,#{DUIHUAXX,jdbcType=VARCHAR},#{BEIZHU,jdbcType=VARCHAR},#{MENZHENZYBZ,jdbcType=DECIMAL}
        ,#{XIUGAIREN,jdbcType=VARCHAR},#{XIUGAISJ,jdbcType=TIMESTAMP},#{NIANLINGDW,jdbcType=VARCHAR}
        ,#{BINGRENZYID,jdbcType=VARCHAR},#{YINGERBZ,jdbcType=DECIMAL},#{YINGERID,jdbcType=VARCHAR}
        ,#{XISHOUHSBZ,jdbcType=DECIMAL},#{GELIBZ,jdbcType=DECIMAL},#{YAOQIUST,jdbcType=DECIMAL}
        ,#{SHOUSHUJB,jdbcType=VARCHAR},#{SHENGAO,jdbcType=DECIMAL},#{ZAOYING,jdbcType=VARCHAR}
        ,#{TESHUSSBZ,jdbcType=DECIMAL},#{KAIDANYYID,jdbcType=VARCHAR},#{JIANCHAXMID,jdbcType=VARCHAR}
        ,#{JIANCHAJKID1,jdbcType=VARCHAR},#{TIWAIXHS,jdbcType=VARCHAR},#{MAZUIAPZT,jdbcType=DECIMAL}
        ,#{MAZUIFF1,jdbcType=VARCHAR},#{FUHEMZFFMC,jdbcType=VARCHAR},#{SHOUSHUBW,jdbcType=VARCHAR}
        ,#{SHOUSHUMCID4,jdbcType=VARCHAR},#{SHOUSHUMC4,jdbcType=VARCHAR},#{SHOUSHUMCID5,jdbcType=VARCHAR}
        ,#{SHOUSHUMC5,jdbcType=VARCHAR},#{JIUZHENID,jdbcType=VARCHAR},#{SHUHOUSSMCID,jdbcType=VARCHAR}
        ,#{SHUHOUSSMC,jdbcType=VARCHAR},#{RUSHISJ,jdbcType=TIMESTAMP},#{CHUSHISJ,jdbcType=TIMESTAMP}
        ,#{SHOUSHUKS,jdbcType=VARCHAR},#{MAZUISJ,jdbcType=TIMESTAMP},#{FEIJIHCFSSSBZ,jdbcType=DECIMAL}
        ,#{SHUQIANTL,jdbcType=DECIMAL},#{YIWUCBABZ,jdbcType=DECIMAL},#{YIGANGLDB,jdbcType=DECIMAL}
        ,#{MEIDUKT,jdbcType=DECIMAL},#{AIZIBKT,jdbcType=DECIMAL},#{BINGGANKT,jdbcType=DECIMAL}
        ,#{QITATSGR,jdbcType=VARCHAR},#{BUCHUANSSS,jdbcType=DECIMAL},#{PAICHIBZ,jdbcType=DECIMAL}
        ,#{SHENHEBZ,jdbcType=DECIMAL},#{SHENHESJ,jdbcType=TIMESTAMP},#{SHENHEREN,jdbcType=VARCHAR}
        ,#{BINGDONGBLBZ,jdbcType=DECIMAL},#{GELICS,jdbcType=VARCHAR},#{YUFANGYYSJ,jdbcType=TIMESTAMP}
        ,#{SHOUSHUFXJB,jdbcType=VARCHAR},#{MAZUIFJ,jdbcType=VARCHAR},#{ZHONGDASSBZ,jdbcType=DECIMAL}
        ,#{CHUANGKOUXGLY,jdbcType=VARCHAR},#{SHOUSHUYJWCSJ,jdbcType=TIMESTAMP},#{SHUXUEQSXBGBZ,jdbcType=DECIMAL}
        ,#{SHUXUEQSXJG,jdbcType=VARCHAR},#{SHENGYUSHI,jdbcType=VARCHAR},#{HUNYIN,jdbcType=VARCHAR}
        ,#{SHUQIANJCSFQQ,jdbcType=DECIMAL},#{SHIFOUSBLHY,jdbcType=DECIMAL},#{BIAOBEN,jdbcType=VARCHAR}
        ,#{BIAOBENJS,jdbcType=DECIMAL},#{SHUZHONGYY,jdbcType=VARCHAR},#{LIUCHANYY,jdbcType=VARCHAR}
        ,#{RONGMAOZT,jdbcType=DECIMAL},#{SHUQIANGQ,jdbcType=DECIMAL},#{SHOUSHUZQK,jdbcType=VARCHAR}
        ,#{JIEYUHMC,jdbcType=VARCHAR},#{SHUHOUGQ,jdbcType=DECIMAL},#{CHUXUEQK,jdbcType=VARCHAR}
        ,#{KUAKESSBZ,jdbcType=DECIMAL},#{GUANLIANSSDID,jdbcType=VARCHAR},#{SHOUSHUJB1,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX1,jdbcType=VARCHAR},#{SHOUSHUJB2,jdbcType=VARCHAR},#{SHOUSHUCKLX2,jdbcType=VARCHAR}
        ,#{SHOUSHUJB3,jdbcType=VARCHAR},#{SHOUSHUCKLX3,jdbcType=VARCHAR},#{SHOUSHUJB4,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX4,jdbcType=VARCHAR},#{SHOUSHUJB5,jdbcType=VARCHAR},#{SHOUSHUCKLX5,jdbcType=VARCHAR}
        ,#{NEIJINGSSBZ,jdbcType=DECIMAL},#{NEIJINGSS,jdbcType=VARCHAR},#{SHUHOUSSJB,jdbcType=VARCHAR}
        ,#{SHUHOUCKLX,jdbcType=VARCHAR},#{SHUZI1,jdbcType=DECIMAL},#{SHUZI2,jdbcType=DECIMAL}
        ,#{SHUZI3,jdbcType=DECIMAL},#{SHUZI4,jdbcType=DECIMAL},#{SHUZI5,jdbcType=DECIMAL}
        ,#{SHUZI6,jdbcType=DECIMAL},#{SHOUSHUJSBZ,jdbcType=DECIMAL},#{SUXINGS,jdbcType=VARCHAR}
        ,#{SHUZHONGYYSJ,jdbcType=TIMESTAMP},#{LINCHUANGLJDRBZ,jdbcType=DECIMAL},#{GELICSMC,jdbcType=VARCHAR}
        ,#{DIANZIBLSY1,jdbcType=VARCHAR},#{DIANZIBLSY2,jdbcType=VARCHAR},#{DIANZIBLSY3,jdbcType=VARCHAR}
        ,#{DIANZIBLSY4,jdbcType=VARCHAR},#{DIANZIBLSY5,jdbcType=VARCHAR},#{DIANZIBLSY6,jdbcType=VARCHAR}
        ,#{DIANZIBLSY7,jdbcType=VARCHAR},#{DIANZIBLSY8,jdbcType=VARCHAR},#{DIANZIBLSY9,jdbcType=VARCHAR}
        ,#{DIANZIBLSY10,jdbcType=VARCHAR},#{DIANZIBLSY11,jdbcType=VARCHAR},#{DIANZIBLSY12,jdbcType=VARCHAR}
        ,#{SHIXUELIANG,jdbcType=VARCHAR},#{SHUXUELIANG,jdbcType=VARCHAR},#{YUSHENQBZ,jdbcType=DECIMAL}
        ,#{SHOUSHUYSYLZ,jdbcType=VARCHAR},#{SHOUSHUYJSC,jdbcType=VARCHAR},#{LINCHUANGLJID,jdbcType=VARCHAR}
        ,#{WULIJJ,jdbcType=VARCHAR},#{WULIQS,jdbcType=DECIMAL},#{DIMIANJJ,jdbcType=VARCHAR}
        ,#{DIMIANQS,jdbcType=DECIMAL},#{LVWANGJJ,jdbcType=VARCHAR},#{LVWANGQS,jdbcType=DECIMAL}
        ,#{GANRANLX,jdbcType=VARCHAR},#{ZERENREN,jdbcType=VARCHAR},#{YUFANGYYSZZJBZ,jdbcType=DECIMAL}
        ,#{ZHIRUWU,jdbcType=DECIMAL},#{SHOUSHUQJ,jdbcType=DECIMAL},#{BINGLISFWZ,jdbcType=DECIMAL}
        ,#{BINGLIBWZXX,jdbcType=VARCHAR},#{SHIFOUDLWLQX,jdbcType=DECIMAL},#{BINGRENYLZ,jdbcType=VARCHAR}
        ,#{YISHENGZID,jdbcType=VARCHAR},#{ZHIDAOLS,jdbcType=VARCHAR},#{SIXIEHS3,jdbcType=VARCHAR}
        ,#{JIESHENG1,jdbcType=VARCHAR},#{JIESHENG2,jdbcType=VARCHAR},#{SHUHOUSSQZ,jdbcType=VARCHAR}
        ,#{SHUHOUSSHZ,jdbcType=VARCHAR},#{XUSHENHBZ,jdbcType=DECIMAL},#{SHUHOUMCID1,jdbcType=VARCHAR}
        ,#{SHUHOUMC1,jdbcType=VARCHAR},#{SHUHOUMCID2,jdbcType=VARCHAR},#{SHUHOUMC2,jdbcType=VARCHAR}
        ,#{SHUHOUMCID3,jdbcType=VARCHAR},#{SHUHOUMC3,jdbcType=VARCHAR},#{SHUHOUMCID4,jdbcType=VARCHAR}
        ,#{SHUHOUMC4,jdbcType=VARCHAR},#{SHUHOUMCID5,jdbcType=VARCHAR},#{SHUHOUMC5,jdbcType=VARCHAR}
        ,#{SHUHOUJB1,jdbcType=VARCHAR},#{SHUHOUCKLX1,jdbcType=VARCHAR},#{SHUHOUJB2,jdbcType=VARCHAR}
        ,#{SHUHOUCKLX2,jdbcType=VARCHAR},#{SHUHOUJB3,jdbcType=VARCHAR},#{SHUHOUCKLX3,jdbcType=VARCHAR}
        ,#{SHUHOUJB4,jdbcType=VARCHAR},#{SHUHOUCKLX4,jdbcType=VARCHAR},#{SHUHOUJB5,jdbcType=VARCHAR}
        ,#{SHUHOUCKLX5,jdbcType=VARCHAR},#{SHUHOUSSBW,jdbcType=VARCHAR},#{QUSHOUSSJ,jdbcType=TIMESTAMP}
        ,#{QUSHOUSJJHS,jdbcType=VARCHAR},#{QUSHOUSJJHSXM,jdbcType=VARCHAR},#{HUIBINGFSJ,jdbcType=TIMESTAMP}
        ,#{HUIBINGFJJHS,jdbcType=VARCHAR},#{HUIBINGFJJHSXM,jdbcType=VARCHAR},#{MAZUIFS,jdbcType=DECIMAL}
        ,#{SHOUSHUAPR,jdbcType=VARCHAR},#{SHOUSHUAPSJ,jdbcType=TIMESTAMP},#{SHOUSHUDJR,jdbcType=VARCHAR}
        ,#{SHOUSHUDJSJ,jdbcType=TIMESTAMP},#{MAZUIAPR,jdbcType=VARCHAR},#{MAZUIAPSJ,jdbcType=TIMESTAMP}
        ,#{MAZUIDJR,jdbcType=VARCHAR},#{MAZUIDJSJ,jdbcType=TIMESTAMP},#{YUJISC,jdbcType=DECIMAL}
        ,#{SHENHEBTGYY,jdbcType=VARCHAR},#{JINGHUIFSSJ,jdbcType=TIMESTAMP},#{JINGHUIFSJJHS,jdbcType=VARCHAR}
        ,#{JINGHUIFSJJHSXM,jdbcType=VARCHAR},#{JINGSHOUSJSJ,jdbcType=TIMESTAMP},#{JINGSHOUSJJJHS,jdbcType=VARCHAR}
        ,#{JINGSHOUSJJJHSXM,jdbcType=VARCHAR},#{SHOUSHULZZT,jdbcType=VARCHAR},#{ZHONGZHISJ,jdbcType=TIMESTAMP}
        ,#{ZHONGZHIJJHS,jdbcType=VARCHAR},#{ZHONGZHIJJHSXM,jdbcType=VARCHAR},#{CHUSHOUSSSJ,jdbcType=TIMESTAMP}
        ,#{CHUSHOUSSJJHS,jdbcType=VARCHAR},#{CHUSHOUSSJJHSXM,jdbcType=VARCHAR},#{CHUHUIFSSJ,jdbcType=TIMESTAMP}
        ,#{CHUHUIFSJJHS,jdbcType=VARCHAR},#{CHUHUIFSJJHSXM,jdbcType=VARCHAR},#{FENGUANYZSHBZ,jdbcType=DECIMAL}
        ,#{FENGUANYZ,jdbcType=VARCHAR},#{FENGUANYZSHSJ,jdbcType=TIMESTAMP},#{FENGUANYZSHBTGYY,jdbcType=VARCHAR}
        ,#{KEZHURSHBZ,jdbcType=DECIMAL},#{KEZHUREN,jdbcType=VARCHAR},#{KEZHURSHSJ,jdbcType=TIMESTAMP}
        ,#{KEZHURSHBTGYY,jdbcType=VARCHAR},#{YIWUKSHBZ,jdbcType=DECIMAL},#{YIWUKE,jdbcType=VARCHAR}
        ,#{YIWUKSHSJ,jdbcType=TIMESTAMP},#{YIWUKSHBTGYY,jdbcType=VARCHAR},#{SHANGBAOSY,jdbcType=VARCHAR}
        ,#{SHANGBAOYS,jdbcType=VARCHAR},#{XUJIEJWT,jdbcType=VARCHAR},#{SHUQIANTLJG,jdbcType=VARCHAR}
        ,#{YICAIQCS,jdbcType=VARCHAR},#{ZHUZHIYSSHBZ,jdbcType=DECIMAL},#{ZHUZHIYSSHR,jdbcType=VARCHAR}
        ,#{ZHUZHIYSSHSJ,jdbcType=TIMESTAMP},#{ZHUZHIYSSHBTGYY,jdbcType=DECIMAL},#{SHUQIANGRQK,jdbcType=VARCHAR}
        ,#{BINGFAZHENG,jdbcType=VARCHAR},#{DUOCHONGNYBZ,jdbcType=DECIMAL},#{ZHUDAOYSSHBZ,jdbcType=DECIMAL}
        ,#{ZHUDAOYSSHR,jdbcType=VARCHAR},#{ZHUDAOYSSHSJ,jdbcType=TIMESTAMP},#{MAZUIYSSHBZ,jdbcType=DECIMAL}
        ,#{MAZUIYSSHR,jdbcType=VARCHAR},#{MAZUIYSSHSJ,jdbcType=TIMESTAMP},#{MAZUIYF,jdbcType=VARCHAR}
        ,#{SHENQINGYS,jdbcType=VARCHAR},#{YISHENGSQRQ,jdbcType=TIMESTAMP},#{ZHENDUAN,jdbcType=VARCHAR}
        ,#{SHANGJIYSSHBZ,jdbcType=DECIMAL},#{SHANGJIYSSHSJ,jdbcType=TIMESTAMP},#{SHANGJIYSSHR,jdbcType=VARCHAR}
        ,#{SHANGJIYS,jdbcType=VARCHAR},#{SHANGJIYSSHBTGYY,jdbcType=VARCHAR},#{SHUQIANSSJB,jdbcType=VARCHAR}
        ,#{MAZUIHZDID,jdbcType=VARCHAR},#{SHOUSHUDM,jdbcType=VARCHAR},#{JINICUHS,jdbcType=VARCHAR}
        ,#{JINICUSJ,jdbcType=TIMESTAMP},#{JINICUHSXM,jdbcType=VARCHAR},#{CHUICUHS,jdbcType=VARCHAR}
        ,#{CHUICUSJ,jdbcType=TIMESTAMP},#{CHUICUHSXM,jdbcType=VARCHAR},#{JUJUELX,jdbcType=VARCHAR}
        ,#{JUJUEYY,jdbcType=VARCHAR},#{TAICI,jdbcType=VARCHAR},#{BEIZHU1,jdbcType=VARCHAR}
        ,#{BEIZHU2,jdbcType=VARCHAR},#{ZHIQINGTYSZT,jdbcType=DECIMAL},#{WEIJIZHIBZ,jdbcType=DECIMAL}
        ,#{SHUHOUZHENTONGBZ,jdbcType=DECIMAL},#{ISKANGJUNYW,jdbcType=DECIMAL},#{SHOUSHUSLX,jdbcType=VARCHAR}
        ,#{RIJIANSSBZ,jdbcType=DECIMAL},#{ZHENDUANDM,jdbcType=VARCHAR},#{SHOUSHUBW2,jdbcType=VARCHAR}
        ,#{SHOUSHUBW3,jdbcType=VARCHAR},#{SHOUSHUBW4,jdbcType=VARCHAR},#{SHOUSHUBW5,jdbcType=VARCHAR}
        ,#{SHOUSHUBW1,jdbcType=VARCHAR},#{MAZUIJSBZ,jdbcType=DECIMAL},#{MAZUIJSREN,jdbcType=VARCHAR}
        ,#{MAZUIJSSJ,jdbcType=TIMESTAMP},#{SHOUSHUJZJSREN,jdbcType=VARCHAR},#{MAZUIJZJSREN,jdbcType=VARCHAR}
        ,#{SHOUSHUJZSJ,jdbcType=TIMESTAMP},#{QUXIAOYY,jdbcType=VARCHAR},#{YUANYINLX,jdbcType=VARCHAR}
        ,#{QUXIAOR,jdbcType=VARCHAR},#{QUXIAOSJ,jdbcType=TIMESTAMP},#{SHOUSHUKSREN,jdbcType=VARCHAR}
        ,#{SHOUSHUJSREN,jdbcType=VARCHAR},#{SHOUSHUJZBZ,jdbcType=DECIMAL},#{MAZUIJZBZ,jdbcType=DECIMAL}
        ,#{MAZUIJZSJ,jdbcType=TIMESTAMP},#{WAIYUANBZ,jdbcType=DECIMAL},#{ZHUANYUNFS,jdbcType=VARCHAR}
        ,#{SHOUSHUXZ,jdbcType=VARCHAR},#{SHOUSHUXZQT,jdbcType=VARCHAR},#{FEIJIHUAZCSS,jdbcType=VARCHAR}
        ,#{TIWEI,jdbcType=VARCHAR},#{ZHIDAOYS,jdbcType=VARCHAR},#{XUYAOSZBD,jdbcType=DECIMAL}
        ,#{XUYAOBBCJ,jdbcType=DECIMAL},#{SHENQINGSHZHBF,jdbcType=DECIMAL},#{SHENQINGZICU,jdbcType=DECIMAL}
        ,#{SHUHOUZT,jdbcType=DECIMAL},#{GENTAI,jdbcType=DECIMAL},#{SHOUSHUBZ,jdbcType=VARCHAR}
        ,#{SONGFUSS,jdbcType=DECIMAL},#{TESHUJC,jdbcType=VARCHAR},#{MAZUISSS,jdbcType=VARCHAR}
        ,#{MAZUIPG,jdbcType=VARCHAR},#{MAZUIPGNR,jdbcType=VARCHAR},#{WEICHUANGSSBZ,jdbcType=DECIMAL}
        ,#{MAZUIPGBLJLXH,jdbcType=DECIMAL},#{SHOUSHUBW6,jdbcType=VARCHAR},#{SHOUSHUMC6,jdbcType=VARCHAR}
        ,#{SHOUSHUID6,jdbcType=VARCHAR},#{SHOUSHUBW7,jdbcType=VARCHAR},#{SHOUSHUMC7,jdbcType=VARCHAR}
        ,#{SHOUSHUID7,jdbcType=VARCHAR},#{SHOUSHUBW8,jdbcType=VARCHAR},#{SHOUSHUMC8,jdbcType=VARCHAR}
        ,#{SHOUSHUID8,jdbcType=VARCHAR},#{SHOUSHUBW9,jdbcType=VARCHAR},#{SHOUSHUMC9,jdbcType=VARCHAR}
        ,#{SHOUSHUID9,jdbcType=VARCHAR},#{SHOUSHUBW10,jdbcType=VARCHAR},#{SHOUSHUMC10,jdbcType=VARCHAR}
        ,#{SHOUSHUID10,jdbcType=VARCHAR},#{SHOUSHUBW11,jdbcType=VARCHAR},#{SHOUSHUMC11,jdbcType=VARCHAR}
        ,#{SHOUSHUID11,jdbcType=VARCHAR},#{SHOUSHUBW12,jdbcType=VARCHAR},#{SHOUSHUMC12,jdbcType=VARCHAR}
        ,#{SHOUSHUID12,jdbcType=VARCHAR},#{SHOUSHUBW13,jdbcType=VARCHAR},#{SHOUSHUMC13,jdbcType=VARCHAR}
        ,#{SHOUSHUID13,jdbcType=VARCHAR},#{SHOUSHUBW14,jdbcType=VARCHAR},#{SHOUSHUMC14,jdbcType=VARCHAR}
        ,#{SHOUSHUID14,jdbcType=VARCHAR},#{SHOUSHUBW15,jdbcType=VARCHAR},#{SHOUSHUMC15,jdbcType=VARCHAR}
        ,#{SHOUSHUID15,jdbcType=VARCHAR},#{SHOUSHUBW16,jdbcType=VARCHAR},#{SHOUSHUMC16,jdbcType=VARCHAR}
        ,#{SHOUSHUID16,jdbcType=VARCHAR},#{SHOUSHUBW17,jdbcType=VARCHAR},#{SHOUSHUMC17,jdbcType=VARCHAR}
        ,#{SHOUSHUID17,jdbcType=VARCHAR},#{SHOUSHUBW18,jdbcType=VARCHAR},#{SHOUSHUMC18,jdbcType=VARCHAR}
        ,#{SHOUSHUID18,jdbcType=VARCHAR},#{SHOUSHUBW19,jdbcType=VARCHAR},#{SHOUSHUMC19,jdbcType=VARCHAR}
        ,#{SHOUSHUID19,jdbcType=VARCHAR},#{SHOUSHUBW20,jdbcType=VARCHAR},#{SHOUSHUMC20,jdbcType=VARCHAR}
        ,#{SHOUSHUID20,jdbcType=VARCHAR},#{SHOUSHUJB6,jdbcType=VARCHAR},#{SHOUSHUCKLX6,jdbcType=VARCHAR}
        ,#{SHOUSHUJB7,jdbcType=VARCHAR},#{SHOUSHUCKLX7,jdbcType=VARCHAR},#{SHOUSHUJB8,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX8,jdbcType=VARCHAR},#{SHOUSHUJB9,jdbcType=VARCHAR},#{SHOUSHUCKLX9,jdbcType=VARCHAR}
        ,#{SHOUSHUJB10,jdbcType=VARCHAR},#{SHOUSHUCKLX10,jdbcType=VARCHAR},#{SHOUSHUJB11,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX11,jdbcType=VARCHAR},#{SHOUSHUJB12,jdbcType=VARCHAR},#{SHOUSHUCKLX12,jdbcType=VARCHAR}
        ,#{SHOUSHUJB13,jdbcType=VARCHAR},#{SHOUSHUCKLX13,jdbcType=VARCHAR},#{SHOUSHUJB14,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX14,jdbcType=VARCHAR},#{SHOUSHUJB15,jdbcType=VARCHAR},#{SHOUSHUCKLX15,jdbcType=VARCHAR}
        ,#{SHOUSHUJB16,jdbcType=VARCHAR},#{SHOUSHUCKLX16,jdbcType=VARCHAR},#{SHOUSHUJB17,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX17,jdbcType=VARCHAR},#{SHOUSHUJB18,jdbcType=VARCHAR},#{SHOUSHUCKLX18,jdbcType=VARCHAR}
        ,#{SHOUSHUJB19,jdbcType=VARCHAR},#{SHOUSHUCKLX19,jdbcType=VARCHAR},#{SHOUSHUJB20,jdbcType=VARCHAR}
        ,#{SHOUSHUCKLX20,jdbcType=VARCHAR},#{SHOUHU,jdbcType=VARCHAR},#{SHOUHU1,jdbcType=VARCHAR}
        ,#{SHOUHU2,jdbcType=VARCHAR},#{SHOUHU3,jdbcType=VARCHAR},#{SHOUHU4,jdbcType=VARCHAR}
        ,#{SHOUHU5,jdbcType=VARCHAR},#{SHOUHU6,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="SHOUSHUDID" keyProperty="SHOUSHUDID" parameterType="com.javazx.batch.po.SmShoushuxx" useGeneratedKeys="true">
        insert into SM_SHOUSHUXX
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="SHOUSHUDID != null">SHOUSHUDID,</if>
                <if test="SHOUSHUDH != null">SHOUSHUDH,</if>
                <if test="SHENQINGDID != null">SHENQINGDID,</if>
                <if test="YINGYONGID != null">YINGYONGID,</if>
                <if test="YUANQUID != null">YUANQUID,</if>
                <if test="BINGRENID != null">BINGRENID,</if>
                <if test="ZHUYUANHAO != null">ZHUYUANHAO,</if>
                <if test="BINGRENXM != null">BINGRENXM,</if>
                <if test="XINGBIE != null">XINGBIE,</if>
                <if test="NIANLING != null">NIANLING,</if>
                <if test="BINGRENKS != null">BINGRENKS,</if>
                <if test="BINGRENBQ != null">BINGRENBQ,</if>
                <if test="BINGRENCW != null">BINGRENCW,</if>
                <if test="FEIYONGXZ != null">FEIYONGXZ,</if>
                <if test="FEIYONGLB != null">FEIYONGLB,</if>
                <if test="BINGRENTZ != null">BINGRENTZ,</if>
                <if test="SHENQINGKS != null">SHENQINGKS,</if>
                <if test="ZHIXINGKS != null">ZHIXINGKS,</if>
                <if test="SHENQINGSJ != null">SHENQINGSJ,</if>
                <if test="YAOQIUSJ != null">YAOQIUSJ,</if>
                <if test="ANPAISJ != null">ANPAISJ,</if>
                <if test="JINXINGSJ != null">JINXINGSJ,</if>
                <if test="JIESHUSJ != null">JIESHUSJ,</if>
                <if test="SHOUSHULB != null">SHOUSHULB,</if>
                <if test="SHOUSHUMCID != null">SHOUSHUMCID,</if>
                <if test="SHOUSHUMC != null">SHOUSHUMC,</if>
                <if test="SHOUSHUMCID1 != null">SHOUSHUMCID1,</if>
                <if test="SHOUSHUMC1 != null">SHOUSHUMC1,</if>
                <if test="SHOUSHUMCID2 != null">SHOUSHUMCID2,</if>
                <if test="SHOUSHUMC2 != null">SHOUSHUMC2,</if>
                <if test="SHOUSHUMCID3 != null">SHOUSHUMCID3,</if>
                <if test="SHOUSHUMC3 != null">SHOUSHUMC3,</if>
                <if test="SHOUSHUYS != null">SHOUSHUYS,</if>
                <if test="ZHULIYS1 != null">ZHULIYS1,</if>
                <if test="ZHULIYS2 != null">ZHULIYS2,</if>
                <if test="ZHULIYS3 != null">ZHULIYS3,</if>
                <if test="XISHOUHS != null">XISHOUHS,</if>
                <if test="SIXIEHS1 != null">SIXIEHS1,</if>
                <if test="SIXIEHS2 != null">SIXIEHS2,</if>
                <if test="XUNHUIHS1 != null">XUNHUIHS1,</if>
                <if test="XUNHUIHS2 != null">XUNHUIHS2,</if>
                <if test="XUNHUIHS3 != null">XUNHUIHS3,</if>
                <if test="MAZUIID != null">MAZUIID,</if>
                <if test="MAZUIFF != null">MAZUIFF,</if>
                <if test="MAZUIYS != null">MAZUIYS,</if>
                <if test="MAZUIYS1 != null">MAZUIYS1,</if>
                <if test="MAZUIYS2 != null">MAZUIYS2,</if>
                <if test="MAZUIYS3 != null">MAZUIYS3,</if>
                <if test="SHOUSHUTID != null">SHOUSHUTID,</if>
                <if test="JIETAIHAO != null">JIETAIHAO,</if>
                <if test="SHOUCISSBZ != null">SHOUCISSBZ,</if>
                <if test="JIZHENBZ != null">JIZHENBZ,</if>
                <if test="ZHENTONGBSYBZ != null">ZHENTONGBSYBZ,</if>
                <if test="SHOUSHUCKLX != null">SHOUSHUCKLX,</if>
                <if test="CHUANGKOUYHQK != null">CHUANGKOUYHQK,</if>
                <if test="CHUANGKOUGRQK != null">CHUANGKOUGRQK,</if>
                <if test="SHUQIANXX != null">SHUQIANXX,</if>
                <if test="SHENQINGREN != null">SHENQINGREN,</if>
                <if test="CAOZUOYUAN != null">CAOZUOYUAN,</if>
                <if test="ZHENDUANDM1 != null">ZHENDUANDM1,</if>
                <if test="ZHENDUANMC1 != null">ZHENDUANMC1,</if>
                <if test="ZHENDUANDM2 != null">ZHENDUANDM2,</if>
                <if test="ZHENDUANMC2 != null">ZHENDUANMC2,</if>
                <if test="ZHENDUANDM3 != null">ZHENDUANDM3,</if>
                <if test="ZHENDUANMC3 != null">ZHENDUANMC3,</if>
                <if test="ZHENDUANDM4 != null">ZHENDUANDM4,</if>
                <if test="ZHENDUANMC4 != null">ZHENDUANMC4,</if>
                <if test="ZUOFEIBZ != null">ZUOFEIBZ,</if>
                <if test="ZHUANGTAIBZ != null">ZHUANGTAIBZ,</if>
                <if test="DUIHUAXX != null">DUIHUAXX,</if>
                <if test="BEIZHU != null">BEIZHU,</if>
                <if test="MENZHENZYBZ != null">MENZHENZYBZ,</if>
                <if test="XIUGAIREN != null">XIUGAIREN,</if>
                <if test="XIUGAISJ != null">XIUGAISJ,</if>
                <if test="NIANLINGDW != null">NIANLINGDW,</if>
                <if test="BINGRENZYID != null">BINGRENZYID,</if>
                <if test="YINGERBZ != null">YINGERBZ,</if>
                <if test="YINGERID != null">YINGERID,</if>
                <if test="XISHOUHSBZ != null">XISHOUHSBZ,</if>
                <if test="GELIBZ != null">GELIBZ,</if>
                <if test="YAOQIUST != null">YAOQIUST,</if>
                <if test="SHOUSHUJB != null">SHOUSHUJB,</if>
                <if test="SHENGAO != null">SHENGAO,</if>
                <if test="ZAOYING != null">ZAOYING,</if>
                <if test="TESHUSSBZ != null">TESHUSSBZ,</if>
                <if test="KAIDANYYID != null">KAIDANYYID,</if>
                <if test="JIANCHAXMID != null">JIANCHAXMID,</if>
                <if test="JIANCHAJKID1 != null">JIANCHAJKID1,</if>
                <if test="TIWAIXHS != null">TIWAIXHS,</if>
                <if test="MAZUIAPZT != null">MAZUIAPZT,</if>
                <if test="MAZUIFF1 != null">MAZUIFF1,</if>
                <if test="FUHEMZFFMC != null">FUHEMZFFMC,</if>
                <if test="SHOUSHUBW != null">SHOUSHUBW,</if>
                <if test="SHOUSHUMCID4 != null">SHOUSHUMCID4,</if>
                <if test="SHOUSHUMC4 != null">SHOUSHUMC4,</if>
                <if test="SHOUSHUMCID5 != null">SHOUSHUMCID5,</if>
                <if test="SHOUSHUMC5 != null">SHOUSHUMC5,</if>
                <if test="JIUZHENID != null">JIUZHENID,</if>
                <if test="SHUHOUSSMCID != null">SHUHOUSSMCID,</if>
                <if test="SHUHOUSSMC != null">SHUHOUSSMC,</if>
                <if test="RUSHISJ != null">RUSHISJ,</if>
                <if test="CHUSHISJ != null">CHUSHISJ,</if>
                <if test="SHOUSHUKS != null">SHOUSHUKS,</if>
                <if test="MAZUISJ != null">MAZUISJ,</if>
                <if test="FEIJIHCFSSSBZ != null">FEIJIHCFSSSBZ,</if>
                <if test="SHUQIANTL != null">SHUQIANTL,</if>
                <if test="YIWUCBABZ != null">YIWUCBABZ,</if>
                <if test="YIGANGLDB != null">YIGANGLDB,</if>
                <if test="MEIDUKT != null">MEIDUKT,</if>
                <if test="AIZIBKT != null">AIZIBKT,</if>
                <if test="BINGGANKT != null">BINGGANKT,</if>
                <if test="QITATSGR != null">QITATSGR,</if>
                <if test="BUCHUANSSS != null">BUCHUANSSS,</if>
                <if test="PAICHIBZ != null">PAICHIBZ,</if>
                <if test="SHENHEBZ != null">SHENHEBZ,</if>
                <if test="SHENHESJ != null">SHENHESJ,</if>
                <if test="SHENHEREN != null">SHENHEREN,</if>
                <if test="BINGDONGBLBZ != null">BINGDONGBLBZ,</if>
                <if test="GELICS != null">GELICS,</if>
                <if test="YUFANGYYSJ != null">YUFANGYYSJ,</if>
                <if test="SHOUSHUFXJB != null">SHOUSHUFXJB,</if>
                <if test="MAZUIFJ != null">MAZUIFJ,</if>
                <if test="ZHONGDASSBZ != null">ZHONGDASSBZ,</if>
                <if test="CHUANGKOUXGLY != null">CHUANGKOUXGLY,</if>
                <if test="SHOUSHUYJWCSJ != null">SHOUSHUYJWCSJ,</if>
                <if test="SHUXUEQSXBGBZ != null">SHUXUEQSXBGBZ,</if>
                <if test="SHUXUEQSXJG != null">SHUXUEQSXJG,</if>
                <if test="SHENGYUSHI != null">SHENGYUSHI,</if>
                <if test="HUNYIN != null">HUNYIN,</if>
                <if test="SHUQIANJCSFQQ != null">SHUQIANJCSFQQ,</if>
                <if test="SHIFOUSBLHY != null">SHIFOUSBLHY,</if>
                <if test="BIAOBEN != null">BIAOBEN,</if>
                <if test="BIAOBENJS != null">BIAOBENJS,</if>
                <if test="SHUZHONGYY != null">SHUZHONGYY,</if>
                <if test="LIUCHANYY != null">LIUCHANYY,</if>
                <if test="RONGMAOZT != null">RONGMAOZT,</if>
                <if test="SHUQIANGQ != null">SHUQIANGQ,</if>
                <if test="SHOUSHUZQK != null">SHOUSHUZQK,</if>
                <if test="JIEYUHMC != null">JIEYUHMC,</if>
                <if test="SHUHOUGQ != null">SHUHOUGQ,</if>
                <if test="CHUXUEQK != null">CHUXUEQK,</if>
                <if test="KUAKESSBZ != null">KUAKESSBZ,</if>
                <if test="GUANLIANSSDID != null">GUANLIANSSDID,</if>
                <if test="SHOUSHUJB1 != null">SHOUSHUJB1,</if>
                <if test="SHOUSHUCKLX1 != null">SHOUSHUCKLX1,</if>
                <if test="SHOUSHUJB2 != null">SHOUSHUJB2,</if>
                <if test="SHOUSHUCKLX2 != null">SHOUSHUCKLX2,</if>
                <if test="SHOUSHUJB3 != null">SHOUSHUJB3,</if>
                <if test="SHOUSHUCKLX3 != null">SHOUSHUCKLX3,</if>
                <if test="SHOUSHUJB4 != null">SHOUSHUJB4,</if>
                <if test="SHOUSHUCKLX4 != null">SHOUSHUCKLX4,</if>
                <if test="SHOUSHUJB5 != null">SHOUSHUJB5,</if>
                <if test="SHOUSHUCKLX5 != null">SHOUSHUCKLX5,</if>
                <if test="NEIJINGSSBZ != null">NEIJINGSSBZ,</if>
                <if test="NEIJINGSS != null">NEIJINGSS,</if>
                <if test="SHUHOUSSJB != null">SHUHOUSSJB,</if>
                <if test="SHUHOUCKLX != null">SHUHOUCKLX,</if>
                <if test="SHUZI1 != null">SHUZI1,</if>
                <if test="SHUZI2 != null">SHUZI2,</if>
                <if test="SHUZI3 != null">SHUZI3,</if>
                <if test="SHUZI4 != null">SHUZI4,</if>
                <if test="SHUZI5 != null">SHUZI5,</if>
                <if test="SHUZI6 != null">SHUZI6,</if>
                <if test="SHOUSHUJSBZ != null">SHOUSHUJSBZ,</if>
                <if test="SUXINGS != null">SUXINGS,</if>
                <if test="SHUZHONGYYSJ != null">SHUZHONGYYSJ,</if>
                <if test="LINCHUANGLJDRBZ != null">LINCHUANGLJDRBZ,</if>
                <if test="GELICSMC != null">GELICSMC,</if>
                <if test="DIANZIBLSY1 != null">DIANZIBLSY1,</if>
                <if test="DIANZIBLSY2 != null">DIANZIBLSY2,</if>
                <if test="DIANZIBLSY3 != null">DIANZIBLSY3,</if>
                <if test="DIANZIBLSY4 != null">DIANZIBLSY4,</if>
                <if test="DIANZIBLSY5 != null">DIANZIBLSY5,</if>
                <if test="DIANZIBLSY6 != null">DIANZIBLSY6,</if>
                <if test="DIANZIBLSY7 != null">DIANZIBLSY7,</if>
                <if test="DIANZIBLSY8 != null">DIANZIBLSY8,</if>
                <if test="DIANZIBLSY9 != null">DIANZIBLSY9,</if>
                <if test="DIANZIBLSY10 != null">DIANZIBLSY10,</if>
                <if test="DIANZIBLSY11 != null">DIANZIBLSY11,</if>
                <if test="DIANZIBLSY12 != null">DIANZIBLSY12,</if>
                <if test="SHIXUELIANG != null">SHIXUELIANG,</if>
                <if test="SHUXUELIANG != null">SHUXUELIANG,</if>
                <if test="YUSHENQBZ != null">YUSHENQBZ,</if>
                <if test="SHOUSHUYSYLZ != null">SHOUSHUYSYLZ,</if>
                <if test="SHOUSHUYJSC != null">SHOUSHUYJSC,</if>
                <if test="LINCHUANGLJID != null">LINCHUANGLJID,</if>
                <if test="WULIJJ != null">WULIJJ,</if>
                <if test="WULIQS != null">WULIQS,</if>
                <if test="DIMIANJJ != null">DIMIANJJ,</if>
                <if test="DIMIANQS != null">DIMIANQS,</if>
                <if test="LVWANGJJ != null">LVWANGJJ,</if>
                <if test="LVWANGQS != null">LVWANGQS,</if>
                <if test="GANRANLX != null">GANRANLX,</if>
                <if test="ZERENREN != null">ZERENREN,</if>
                <if test="YUFANGYYSZZJBZ != null">YUFANGYYSZZJBZ,</if>
                <if test="ZHIRUWU != null">ZHIRUWU,</if>
                <if test="SHOUSHUQJ != null">SHOUSHUQJ,</if>
                <if test="BINGLISFWZ != null">BINGLISFWZ,</if>
                <if test="BINGLIBWZXX != null">BINGLIBWZXX,</if>
                <if test="SHIFOUDLWLQX != null">SHIFOUDLWLQX,</if>
                <if test="BINGRENYLZ != null">BINGRENYLZ,</if>
                <if test="YISHENGZID != null">YISHENGZID,</if>
                <if test="ZHIDAOLS != null">ZHIDAOLS,</if>
                <if test="SIXIEHS3 != null">SIXIEHS3,</if>
                <if test="JIESHENG1 != null">JIESHENG1,</if>
                <if test="JIESHENG2 != null">JIESHENG2,</if>
                <if test="SHUHOUSSQZ != null">SHUHOUSSQZ,</if>
                <if test="SHUHOUSSHZ != null">SHUHOUSSHZ,</if>
                <if test="XUSHENHBZ != null">XUSHENHBZ,</if>
                <if test="SHUHOUMCID1 != null">SHUHOUMCID1,</if>
                <if test="SHUHOUMC1 != null">SHUHOUMC1,</if>
                <if test="SHUHOUMCID2 != null">SHUHOUMCID2,</if>
                <if test="SHUHOUMC2 != null">SHUHOUMC2,</if>
                <if test="SHUHOUMCID3 != null">SHUHOUMCID3,</if>
                <if test="SHUHOUMC3 != null">SHUHOUMC3,</if>
                <if test="SHUHOUMCID4 != null">SHUHOUMCID4,</if>
                <if test="SHUHOUMC4 != null">SHUHOUMC4,</if>
                <if test="SHUHOUMCID5 != null">SHUHOUMCID5,</if>
                <if test="SHUHOUMC5 != null">SHUHOUMC5,</if>
                <if test="SHUHOUJB1 != null">SHUHOUJB1,</if>
                <if test="SHUHOUCKLX1 != null">SHUHOUCKLX1,</if>
                <if test="SHUHOUJB2 != null">SHUHOUJB2,</if>
                <if test="SHUHOUCKLX2 != null">SHUHOUCKLX2,</if>
                <if test="SHUHOUJB3 != null">SHUHOUJB3,</if>
                <if test="SHUHOUCKLX3 != null">SHUHOUCKLX3,</if>
                <if test="SHUHOUJB4 != null">SHUHOUJB4,</if>
                <if test="SHUHOUCKLX4 != null">SHUHOUCKLX4,</if>
                <if test="SHUHOUJB5 != null">SHUHOUJB5,</if>
                <if test="SHUHOUCKLX5 != null">SHUHOUCKLX5,</if>
                <if test="SHUHOUSSBW != null">SHUHOUSSBW,</if>
                <if test="QUSHOUSSJ != null">QUSHOUSSJ,</if>
                <if test="QUSHOUSJJHS != null">QUSHOUSJJHS,</if>
                <if test="QUSHOUSJJHSXM != null">QUSHOUSJJHSXM,</if>
                <if test="HUIBINGFSJ != null">HUIBINGFSJ,</if>
                <if test="HUIBINGFJJHS != null">HUIBINGFJJHS,</if>
                <if test="HUIBINGFJJHSXM != null">HUIBINGFJJHSXM,</if>
                <if test="MAZUIFS != null">MAZUIFS,</if>
                <if test="SHOUSHUAPR != null">SHOUSHUAPR,</if>
                <if test="SHOUSHUAPSJ != null">SHOUSHUAPSJ,</if>
                <if test="SHOUSHUDJR != null">SHOUSHUDJR,</if>
                <if test="SHOUSHUDJSJ != null">SHOUSHUDJSJ,</if>
                <if test="MAZUIAPR != null">MAZUIAPR,</if>
                <if test="MAZUIAPSJ != null">MAZUIAPSJ,</if>
                <if test="MAZUIDJR != null">MAZUIDJR,</if>
                <if test="MAZUIDJSJ != null">MAZUIDJSJ,</if>
                <if test="YUJISC != null">YUJISC,</if>
                <if test="SHENHEBTGYY != null">SHENHEBTGYY,</if>
                <if test="JINGHUIFSSJ != null">JINGHUIFSSJ,</if>
                <if test="JINGHUIFSJJHS != null">JINGHUIFSJJHS,</if>
                <if test="JINGHUIFSJJHSXM != null">JINGHUIFSJJHSXM,</if>
                <if test="JINGSHOUSJSJ != null">JINGSHOUSJSJ,</if>
                <if test="JINGSHOUSJJJHS != null">JINGSHOUSJJJHS,</if>
                <if test="JINGSHOUSJJJHSXM != null">JINGSHOUSJJJHSXM,</if>
                <if test="SHOUSHULZZT != null">SHOUSHULZZT,</if>
                <if test="ZHONGZHISJ != null">ZHONGZHISJ,</if>
                <if test="ZHONGZHIJJHS != null">ZHONGZHIJJHS,</if>
                <if test="ZHONGZHIJJHSXM != null">ZHONGZHIJJHSXM,</if>
                <if test="CHUSHOUSSSJ != null">CHUSHOUSSSJ,</if>
                <if test="CHUSHOUSSJJHS != null">CHUSHOUSSJJHS,</if>
                <if test="CHUSHOUSSJJHSXM != null">CHUSHOUSSJJHSXM,</if>
                <if test="CHUHUIFSSJ != null">CHUHUIFSSJ,</if>
                <if test="CHUHUIFSJJHS != null">CHUHUIFSJJHS,</if>
                <if test="CHUHUIFSJJHSXM != null">CHUHUIFSJJHSXM,</if>
                <if test="FENGUANYZSHBZ != null">FENGUANYZSHBZ,</if>
                <if test="FENGUANYZ != null">FENGUANYZ,</if>
                <if test="FENGUANYZSHSJ != null">FENGUANYZSHSJ,</if>
                <if test="FENGUANYZSHBTGYY != null">FENGUANYZSHBTGYY,</if>
                <if test="KEZHURSHBZ != null">KEZHURSHBZ,</if>
                <if test="KEZHUREN != null">KEZHUREN,</if>
                <if test="KEZHURSHSJ != null">KEZHURSHSJ,</if>
                <if test="KEZHURSHBTGYY != null">KEZHURSHBTGYY,</if>
                <if test="YIWUKSHBZ != null">YIWUKSHBZ,</if>
                <if test="YIWUKE != null">YIWUKE,</if>
                <if test="YIWUKSHSJ != null">YIWUKSHSJ,</if>
                <if test="YIWUKSHBTGYY != null">YIWUKSHBTGYY,</if>
                <if test="SHANGBAOSY != null">SHANGBAOSY,</if>
                <if test="SHANGBAOYS != null">SHANGBAOYS,</if>
                <if test="XUJIEJWT != null">XUJIEJWT,</if>
                <if test="SHUQIANTLJG != null">SHUQIANTLJG,</if>
                <if test="YICAIQCS != null">YICAIQCS,</if>
                <if test="ZHUZHIYSSHBZ != null">ZHUZHIYSSHBZ,</if>
                <if test="ZHUZHIYSSHR != null">ZHUZHIYSSHR,</if>
                <if test="ZHUZHIYSSHSJ != null">ZHUZHIYSSHSJ,</if>
                <if test="ZHUZHIYSSHBTGYY != null">ZHUZHIYSSHBTGYY,</if>
                <if test="SHUQIANGRQK != null">SHUQIANGRQK,</if>
                <if test="BINGFAZHENG != null">BINGFAZHENG,</if>
                <if test="DUOCHONGNYBZ != null">DUOCHONGNYBZ,</if>
                <if test="ZHUDAOYSSHBZ != null">ZHUDAOYSSHBZ,</if>
                <if test="ZHUDAOYSSHR != null">ZHUDAOYSSHR,</if>
                <if test="ZHUDAOYSSHSJ != null">ZHUDAOYSSHSJ,</if>
                <if test="MAZUIYSSHBZ != null">MAZUIYSSHBZ,</if>
                <if test="MAZUIYSSHR != null">MAZUIYSSHR,</if>
                <if test="MAZUIYSSHSJ != null">MAZUIYSSHSJ,</if>
                <if test="MAZUIYF != null">MAZUIYF,</if>
                <if test="SHENQINGYS != null">SHENQINGYS,</if>
                <if test="YISHENGSQRQ != null">YISHENGSQRQ,</if>
                <if test="ZHENDUAN != null">ZHENDUAN,</if>
                <if test="SHANGJIYSSHBZ != null">SHANGJIYSSHBZ,</if>
                <if test="SHANGJIYSSHSJ != null">SHANGJIYSSHSJ,</if>
                <if test="SHANGJIYSSHR != null">SHANGJIYSSHR,</if>
                <if test="SHANGJIYS != null">SHANGJIYS,</if>
                <if test="SHANGJIYSSHBTGYY != null">SHANGJIYSSHBTGYY,</if>
                <if test="SHUQIANSSJB != null">SHUQIANSSJB,</if>
                <if test="MAZUIHZDID != null">MAZUIHZDID,</if>
                <if test="SHOUSHUDM != null">SHOUSHUDM,</if>
                <if test="JINICUHS != null">JINICUHS,</if>
                <if test="JINICUSJ != null">JINICUSJ,</if>
                <if test="JINICUHSXM != null">JINICUHSXM,</if>
                <if test="CHUICUHS != null">CHUICUHS,</if>
                <if test="CHUICUSJ != null">CHUICUSJ,</if>
                <if test="CHUICUHSXM != null">CHUICUHSXM,</if>
                <if test="JUJUELX != null">JUJUELX,</if>
                <if test="JUJUEYY != null">JUJUEYY,</if>
                <if test="TAICI != null">TAICI,</if>
                <if test="BEIZHU1 != null">BEIZHU1,</if>
                <if test="BEIZHU2 != null">BEIZHU2,</if>
                <if test="ZHIQINGTYSZT != null">ZHIQINGTYSZT,</if>
                <if test="WEIJIZHIBZ != null">WEIJIZHIBZ,</if>
                <if test="SHUHOUZHENTONGBZ != null">SHUHOUZHENTONGBZ,</if>
                <if test="ISKANGJUNYW != null">ISKANGJUNYW,</if>
                <if test="SHOUSHUSLX != null">SHOUSHUSLX,</if>
                <if test="RIJIANSSBZ != null">RIJIANSSBZ,</if>
                <if test="ZHENDUANDM != null">ZHENDUANDM,</if>
                <if test="SHOUSHUBW2 != null">SHOUSHUBW2,</if>
                <if test="SHOUSHUBW3 != null">SHOUSHUBW3,</if>
                <if test="SHOUSHUBW4 != null">SHOUSHUBW4,</if>
                <if test="SHOUSHUBW5 != null">SHOUSHUBW5,</if>
                <if test="SHOUSHUBW1 != null">SHOUSHUBW1,</if>
                <if test="MAZUIJSBZ != null">MAZUIJSBZ,</if>
                <if test="MAZUIJSREN != null">MAZUIJSREN,</if>
                <if test="MAZUIJSSJ != null">MAZUIJSSJ,</if>
                <if test="SHOUSHUJZJSREN != null">SHOUSHUJZJSREN,</if>
                <if test="MAZUIJZJSREN != null">MAZUIJZJSREN,</if>
                <if test="SHOUSHUJZSJ != null">SHOUSHUJZSJ,</if>
                <if test="QUXIAOYY != null">QUXIAOYY,</if>
                <if test="YUANYINLX != null">YUANYINLX,</if>
                <if test="QUXIAOR != null">QUXIAOR,</if>
                <if test="QUXIAOSJ != null">QUXIAOSJ,</if>
                <if test="SHOUSHUKSREN != null">SHOUSHUKSREN,</if>
                <if test="SHOUSHUJSREN != null">SHOUSHUJSREN,</if>
                <if test="SHOUSHUJZBZ != null">SHOUSHUJZBZ,</if>
                <if test="MAZUIJZBZ != null">MAZUIJZBZ,</if>
                <if test="MAZUIJZSJ != null">MAZUIJZSJ,</if>
                <if test="WAIYUANBZ != null">WAIYUANBZ,</if>
                <if test="ZHUANYUNFS != null">ZHUANYUNFS,</if>
                <if test="SHOUSHUXZ != null">SHOUSHUXZ,</if>
                <if test="SHOUSHUXZQT != null">SHOUSHUXZQT,</if>
                <if test="FEIJIHUAZCSS != null">FEIJIHUAZCSS,</if>
                <if test="TIWEI != null">TIWEI,</if>
                <if test="ZHIDAOYS != null">ZHIDAOYS,</if>
                <if test="XUYAOSZBD != null">XUYAOSZBD,</if>
                <if test="XUYAOBBCJ != null">XUYAOBBCJ,</if>
                <if test="SHENQINGSHZHBF != null">SHENQINGSHZHBF,</if>
                <if test="SHENQINGZICU != null">SHENQINGZICU,</if>
                <if test="SHUHOUZT != null">SHUHOUZT,</if>
                <if test="GENTAI != null">GENTAI,</if>
                <if test="SHOUSHUBZ != null">SHOUSHUBZ,</if>
                <if test="SONGFUSS != null">SONGFUSS,</if>
                <if test="TESHUJC != null">TESHUJC,</if>
                <if test="MAZUISSS != null">MAZUISSS,</if>
                <if test="MAZUIPG != null">MAZUIPG,</if>
                <if test="MAZUIPGNR != null">MAZUIPGNR,</if>
                <if test="WEICHUANGSSBZ != null">WEICHUANGSSBZ,</if>
                <if test="MAZUIPGBLJLXH != null">MAZUIPGBLJLXH,</if>
                <if test="SHOUSHUBW6 != null">SHOUSHUBW6,</if>
                <if test="SHOUSHUMC6 != null">SHOUSHUMC6,</if>
                <if test="SHOUSHUID6 != null">SHOUSHUID6,</if>
                <if test="SHOUSHUBW7 != null">SHOUSHUBW7,</if>
                <if test="SHOUSHUMC7 != null">SHOUSHUMC7,</if>
                <if test="SHOUSHUID7 != null">SHOUSHUID7,</if>
                <if test="SHOUSHUBW8 != null">SHOUSHUBW8,</if>
                <if test="SHOUSHUMC8 != null">SHOUSHUMC8,</if>
                <if test="SHOUSHUID8 != null">SHOUSHUID8,</if>
                <if test="SHOUSHUBW9 != null">SHOUSHUBW9,</if>
                <if test="SHOUSHUMC9 != null">SHOUSHUMC9,</if>
                <if test="SHOUSHUID9 != null">SHOUSHUID9,</if>
                <if test="SHOUSHUBW10 != null">SHOUSHUBW10,</if>
                <if test="SHOUSHUMC10 != null">SHOUSHUMC10,</if>
                <if test="SHOUSHUID10 != null">SHOUSHUID10,</if>
                <if test="SHOUSHUBW11 != null">SHOUSHUBW11,</if>
                <if test="SHOUSHUMC11 != null">SHOUSHUMC11,</if>
                <if test="SHOUSHUID11 != null">SHOUSHUID11,</if>
                <if test="SHOUSHUBW12 != null">SHOUSHUBW12,</if>
                <if test="SHOUSHUMC12 != null">SHOUSHUMC12,</if>
                <if test="SHOUSHUID12 != null">SHOUSHUID12,</if>
                <if test="SHOUSHUBW13 != null">SHOUSHUBW13,</if>
                <if test="SHOUSHUMC13 != null">SHOUSHUMC13,</if>
                <if test="SHOUSHUID13 != null">SHOUSHUID13,</if>
                <if test="SHOUSHUBW14 != null">SHOUSHUBW14,</if>
                <if test="SHOUSHUMC14 != null">SHOUSHUMC14,</if>
                <if test="SHOUSHUID14 != null">SHOUSHUID14,</if>
                <if test="SHOUSHUBW15 != null">SHOUSHUBW15,</if>
                <if test="SHOUSHUMC15 != null">SHOUSHUMC15,</if>
                <if test="SHOUSHUID15 != null">SHOUSHUID15,</if>
                <if test="SHOUSHUBW16 != null">SHOUSHUBW16,</if>
                <if test="SHOUSHUMC16 != null">SHOUSHUMC16,</if>
                <if test="SHOUSHUID16 != null">SHOUSHUID16,</if>
                <if test="SHOUSHUBW17 != null">SHOUSHUBW17,</if>
                <if test="SHOUSHUMC17 != null">SHOUSHUMC17,</if>
                <if test="SHOUSHUID17 != null">SHOUSHUID17,</if>
                <if test="SHOUSHUBW18 != null">SHOUSHUBW18,</if>
                <if test="SHOUSHUMC18 != null">SHOUSHUMC18,</if>
                <if test="SHOUSHUID18 != null">SHOUSHUID18,</if>
                <if test="SHOUSHUBW19 != null">SHOUSHUBW19,</if>
                <if test="SHOUSHUMC19 != null">SHOUSHUMC19,</if>
                <if test="SHOUSHUID19 != null">SHOUSHUID19,</if>
                <if test="SHOUSHUBW20 != null">SHOUSHUBW20,</if>
                <if test="SHOUSHUMC20 != null">SHOUSHUMC20,</if>
                <if test="SHOUSHUID20 != null">SHOUSHUID20,</if>
                <if test="SHOUSHUJB6 != null">SHOUSHUJB6,</if>
                <if test="SHOUSHUCKLX6 != null">SHOUSHUCKLX6,</if>
                <if test="SHOUSHUJB7 != null">SHOUSHUJB7,</if>
                <if test="SHOUSHUCKLX7 != null">SHOUSHUCKLX7,</if>
                <if test="SHOUSHUJB8 != null">SHOUSHUJB8,</if>
                <if test="SHOUSHUCKLX8 != null">SHOUSHUCKLX8,</if>
                <if test="SHOUSHUJB9 != null">SHOUSHUJB9,</if>
                <if test="SHOUSHUCKLX9 != null">SHOUSHUCKLX9,</if>
                <if test="SHOUSHUJB10 != null">SHOUSHUJB10,</if>
                <if test="SHOUSHUCKLX10 != null">SHOUSHUCKLX10,</if>
                <if test="SHOUSHUJB11 != null">SHOUSHUJB11,</if>
                <if test="SHOUSHUCKLX11 != null">SHOUSHUCKLX11,</if>
                <if test="SHOUSHUJB12 != null">SHOUSHUJB12,</if>
                <if test="SHOUSHUCKLX12 != null">SHOUSHUCKLX12,</if>
                <if test="SHOUSHUJB13 != null">SHOUSHUJB13,</if>
                <if test="SHOUSHUCKLX13 != null">SHOUSHUCKLX13,</if>
                <if test="SHOUSHUJB14 != null">SHOUSHUJB14,</if>
                <if test="SHOUSHUCKLX14 != null">SHOUSHUCKLX14,</if>
                <if test="SHOUSHUJB15 != null">SHOUSHUJB15,</if>
                <if test="SHOUSHUCKLX15 != null">SHOUSHUCKLX15,</if>
                <if test="SHOUSHUJB16 != null">SHOUSHUJB16,</if>
                <if test="SHOUSHUCKLX16 != null">SHOUSHUCKLX16,</if>
                <if test="SHOUSHUJB17 != null">SHOUSHUJB17,</if>
                <if test="SHOUSHUCKLX17 != null">SHOUSHUCKLX17,</if>
                <if test="SHOUSHUJB18 != null">SHOUSHUJB18,</if>
                <if test="SHOUSHUCKLX18 != null">SHOUSHUCKLX18,</if>
                <if test="SHOUSHUJB19 != null">SHOUSHUJB19,</if>
                <if test="SHOUSHUCKLX19 != null">SHOUSHUCKLX19,</if>
                <if test="SHOUSHUJB20 != null">SHOUSHUJB20,</if>
                <if test="SHOUSHUCKLX20 != null">SHOUSHUCKLX20,</if>
                <if test="SHOUHU != null">SHOUHU,</if>
                <if test="SHOUHU1 != null">SHOUHU1,</if>
                <if test="SHOUHU2 != null">SHOUHU2,</if>
                <if test="SHOUHU3 != null">SHOUHU3,</if>
                <if test="SHOUHU4 != null">SHOUHU4,</if>
                <if test="SHOUHU5 != null">SHOUHU5,</if>
                <if test="SHOUHU6 != null">SHOUHU6,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="SHOUSHUDID != null">#{SHOUSHUDID,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUDH != null">#{SHOUSHUDH,jdbcType=VARCHAR},</if>
                <if test="SHENQINGDID != null">#{SHENQINGDID,jdbcType=VARCHAR},</if>
                <if test="YINGYONGID != null">#{YINGYONGID,jdbcType=VARCHAR},</if>
                <if test="YUANQUID != null">#{YUANQUID,jdbcType=VARCHAR},</if>
                <if test="BINGRENID != null">#{BINGRENID,jdbcType=VARCHAR},</if>
                <if test="ZHUYUANHAO != null">#{ZHUYUANHAO,jdbcType=VARCHAR},</if>
                <if test="BINGRENXM != null">#{BINGRENXM,jdbcType=VARCHAR},</if>
                <if test="XINGBIE != null">#{XINGBIE,jdbcType=VARCHAR},</if>
                <if test="NIANLING != null">#{NIANLING,jdbcType=DECIMAL},</if>
                <if test="BINGRENKS != null">#{BINGRENKS,jdbcType=VARCHAR},</if>
                <if test="BINGRENBQ != null">#{BINGRENBQ,jdbcType=VARCHAR},</if>
                <if test="BINGRENCW != null">#{BINGRENCW,jdbcType=VARCHAR},</if>
                <if test="FEIYONGXZ != null">#{FEIYONGXZ,jdbcType=VARCHAR},</if>
                <if test="FEIYONGLB != null">#{FEIYONGLB,jdbcType=VARCHAR},</if>
                <if test="BINGRENTZ != null">#{BINGRENTZ,jdbcType=DECIMAL},</if>
                <if test="SHENQINGKS != null">#{SHENQINGKS,jdbcType=VARCHAR},</if>
                <if test="ZHIXINGKS != null">#{ZHIXINGKS,jdbcType=VARCHAR},</if>
                <if test="SHENQINGSJ != null">#{SHENQINGSJ,jdbcType=TIMESTAMP},</if>
                <if test="YAOQIUSJ != null">#{YAOQIUSJ,jdbcType=TIMESTAMP},</if>
                <if test="ANPAISJ != null">#{ANPAISJ,jdbcType=TIMESTAMP},</if>
                <if test="JINXINGSJ != null">#{JINXINGSJ,jdbcType=TIMESTAMP},</if>
                <if test="JIESHUSJ != null">#{JIESHUSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHULB != null">#{SHOUSHULB,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMCID != null">#{SHOUSHUMCID,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC != null">#{SHOUSHUMC,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMCID1 != null">#{SHOUSHUMCID1,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC1 != null">#{SHOUSHUMC1,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMCID2 != null">#{SHOUSHUMCID2,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC2 != null">#{SHOUSHUMC2,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMCID3 != null">#{SHOUSHUMCID3,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC3 != null">#{SHOUSHUMC3,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUYS != null">#{SHOUSHUYS,jdbcType=VARCHAR},</if>
                <if test="ZHULIYS1 != null">#{ZHULIYS1,jdbcType=VARCHAR},</if>
                <if test="ZHULIYS2 != null">#{ZHULIYS2,jdbcType=VARCHAR},</if>
                <if test="ZHULIYS3 != null">#{ZHULIYS3,jdbcType=VARCHAR},</if>
                <if test="XISHOUHS != null">#{XISHOUHS,jdbcType=VARCHAR},</if>
                <if test="SIXIEHS1 != null">#{SIXIEHS1,jdbcType=VARCHAR},</if>
                <if test="SIXIEHS2 != null">#{SIXIEHS2,jdbcType=VARCHAR},</if>
                <if test="XUNHUIHS1 != null">#{XUNHUIHS1,jdbcType=VARCHAR},</if>
                <if test="XUNHUIHS2 != null">#{XUNHUIHS2,jdbcType=VARCHAR},</if>
                <if test="XUNHUIHS3 != null">#{XUNHUIHS3,jdbcType=VARCHAR},</if>
                <if test="MAZUIID != null">#{MAZUIID,jdbcType=VARCHAR},</if>
                <if test="MAZUIFF != null">#{MAZUIFF,jdbcType=VARCHAR},</if>
                <if test="MAZUIYS != null">#{MAZUIYS,jdbcType=VARCHAR},</if>
                <if test="MAZUIYS1 != null">#{MAZUIYS1,jdbcType=VARCHAR},</if>
                <if test="MAZUIYS2 != null">#{MAZUIYS2,jdbcType=VARCHAR},</if>
                <if test="MAZUIYS3 != null">#{MAZUIYS3,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUTID != null">#{SHOUSHUTID,jdbcType=VARCHAR},</if>
                <if test="JIETAIHAO != null">#{JIETAIHAO,jdbcType=VARCHAR},</if>
                <if test="SHOUCISSBZ != null">#{SHOUCISSBZ,jdbcType=DECIMAL},</if>
                <if test="JIZHENBZ != null">#{JIZHENBZ,jdbcType=DECIMAL},</if>
                <if test="ZHENTONGBSYBZ != null">#{ZHENTONGBSYBZ,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUCKLX != null">#{SHOUSHUCKLX,jdbcType=VARCHAR},</if>
                <if test="CHUANGKOUYHQK != null">#{CHUANGKOUYHQK,jdbcType=VARCHAR},</if>
                <if test="CHUANGKOUGRQK != null">#{CHUANGKOUGRQK,jdbcType=VARCHAR},</if>
                <if test="SHUQIANXX != null">#{SHUQIANXX,jdbcType=VARCHAR},</if>
                <if test="SHENQINGREN != null">#{SHENQINGREN,jdbcType=VARCHAR},</if>
                <if test="CAOZUOYUAN != null">#{CAOZUOYUAN,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANDM1 != null">#{ZHENDUANDM1,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANMC1 != null">#{ZHENDUANMC1,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANDM2 != null">#{ZHENDUANDM2,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANMC2 != null">#{ZHENDUANMC2,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANDM3 != null">#{ZHENDUANDM3,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANMC3 != null">#{ZHENDUANMC3,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANDM4 != null">#{ZHENDUANDM4,jdbcType=VARCHAR},</if>
                <if test="ZHENDUANMC4 != null">#{ZHENDUANMC4,jdbcType=VARCHAR},</if>
                <if test="ZUOFEIBZ != null">#{ZUOFEIBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUANGTAIBZ != null">#{ZHUANGTAIBZ,jdbcType=DECIMAL},</if>
                <if test="DUIHUAXX != null">#{DUIHUAXX,jdbcType=VARCHAR},</if>
                <if test="BEIZHU != null">#{BEIZHU,jdbcType=VARCHAR},</if>
                <if test="MENZHENZYBZ != null">#{MENZHENZYBZ,jdbcType=DECIMAL},</if>
                <if test="XIUGAIREN != null">#{XIUGAIREN,jdbcType=VARCHAR},</if>
                <if test="XIUGAISJ != null">#{XIUGAISJ,jdbcType=TIMESTAMP},</if>
                <if test="NIANLINGDW != null">#{NIANLINGDW,jdbcType=VARCHAR},</if>
                <if test="BINGRENZYID != null">#{BINGRENZYID,jdbcType=VARCHAR},</if>
                <if test="YINGERBZ != null">#{YINGERBZ,jdbcType=DECIMAL},</if>
                <if test="YINGERID != null">#{YINGERID,jdbcType=VARCHAR},</if>
                <if test="XISHOUHSBZ != null">#{XISHOUHSBZ,jdbcType=DECIMAL},</if>
                <if test="GELIBZ != null">#{GELIBZ,jdbcType=DECIMAL},</if>
                <if test="YAOQIUST != null">#{YAOQIUST,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUJB != null">#{SHOUSHUJB,jdbcType=VARCHAR},</if>
                <if test="SHENGAO != null">#{SHENGAO,jdbcType=DECIMAL},</if>
                <if test="ZAOYING != null">#{ZAOYING,jdbcType=VARCHAR},</if>
                <if test="TESHUSSBZ != null">#{TESHUSSBZ,jdbcType=DECIMAL},</if>
                <if test="KAIDANYYID != null">#{KAIDANYYID,jdbcType=VARCHAR},</if>
                <if test="JIANCHAXMID != null">#{JIANCHAXMID,jdbcType=VARCHAR},</if>
                <if test="JIANCHAJKID1 != null">#{JIANCHAJKID1,jdbcType=VARCHAR},</if>
                <if test="TIWAIXHS != null">#{TIWAIXHS,jdbcType=VARCHAR},</if>
                <if test="MAZUIAPZT != null">#{MAZUIAPZT,jdbcType=DECIMAL},</if>
                <if test="MAZUIFF1 != null">#{MAZUIFF1,jdbcType=VARCHAR},</if>
                <if test="FUHEMZFFMC != null">#{FUHEMZFFMC,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW != null">#{SHOUSHUBW,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMCID4 != null">#{SHOUSHUMCID4,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC4 != null">#{SHOUSHUMC4,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMCID5 != null">#{SHOUSHUMCID5,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC5 != null">#{SHOUSHUMC5,jdbcType=VARCHAR},</if>
                <if test="JIUZHENID != null">#{JIUZHENID,jdbcType=VARCHAR},</if>
                <if test="SHUHOUSSMCID != null">#{SHUHOUSSMCID,jdbcType=VARCHAR},</if>
                <if test="SHUHOUSSMC != null">#{SHUHOUSSMC,jdbcType=VARCHAR},</if>
                <if test="RUSHISJ != null">#{RUSHISJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUSHISJ != null">#{CHUSHISJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHUKS != null">#{SHOUSHUKS,jdbcType=VARCHAR},</if>
                <if test="MAZUISJ != null">#{MAZUISJ,jdbcType=TIMESTAMP},</if>
                <if test="FEIJIHCFSSSBZ != null">#{FEIJIHCFSSSBZ,jdbcType=DECIMAL},</if>
                <if test="SHUQIANTL != null">#{SHUQIANTL,jdbcType=DECIMAL},</if>
                <if test="YIWUCBABZ != null">#{YIWUCBABZ,jdbcType=DECIMAL},</if>
                <if test="YIGANGLDB != null">#{YIGANGLDB,jdbcType=DECIMAL},</if>
                <if test="MEIDUKT != null">#{MEIDUKT,jdbcType=DECIMAL},</if>
                <if test="AIZIBKT != null">#{AIZIBKT,jdbcType=DECIMAL},</if>
                <if test="BINGGANKT != null">#{BINGGANKT,jdbcType=DECIMAL},</if>
                <if test="QITATSGR != null">#{QITATSGR,jdbcType=VARCHAR},</if>
                <if test="BUCHUANSSS != null">#{BUCHUANSSS,jdbcType=DECIMAL},</if>
                <if test="PAICHIBZ != null">#{PAICHIBZ,jdbcType=DECIMAL},</if>
                <if test="SHENHEBZ != null">#{SHENHEBZ,jdbcType=DECIMAL},</if>
                <if test="SHENHESJ != null">#{SHENHESJ,jdbcType=TIMESTAMP},</if>
                <if test="SHENHEREN != null">#{SHENHEREN,jdbcType=VARCHAR},</if>
                <if test="BINGDONGBLBZ != null">#{BINGDONGBLBZ,jdbcType=DECIMAL},</if>
                <if test="GELICS != null">#{GELICS,jdbcType=VARCHAR},</if>
                <if test="YUFANGYYSJ != null">#{YUFANGYYSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHUFXJB != null">#{SHOUSHUFXJB,jdbcType=VARCHAR},</if>
                <if test="MAZUIFJ != null">#{MAZUIFJ,jdbcType=VARCHAR},</if>
                <if test="ZHONGDASSBZ != null">#{ZHONGDASSBZ,jdbcType=DECIMAL},</if>
                <if test="CHUANGKOUXGLY != null">#{CHUANGKOUXGLY,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUYJWCSJ != null">#{SHOUSHUYJWCSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHUXUEQSXBGBZ != null">#{SHUXUEQSXBGBZ,jdbcType=DECIMAL},</if>
                <if test="SHUXUEQSXJG != null">#{SHUXUEQSXJG,jdbcType=VARCHAR},</if>
                <if test="SHENGYUSHI != null">#{SHENGYUSHI,jdbcType=VARCHAR},</if>
                <if test="HUNYIN != null">#{HUNYIN,jdbcType=VARCHAR},</if>
                <if test="SHUQIANJCSFQQ != null">#{SHUQIANJCSFQQ,jdbcType=DECIMAL},</if>
                <if test="SHIFOUSBLHY != null">#{SHIFOUSBLHY,jdbcType=DECIMAL},</if>
                <if test="BIAOBEN != null">#{BIAOBEN,jdbcType=VARCHAR},</if>
                <if test="BIAOBENJS != null">#{BIAOBENJS,jdbcType=DECIMAL},</if>
                <if test="SHUZHONGYY != null">#{SHUZHONGYY,jdbcType=VARCHAR},</if>
                <if test="LIUCHANYY != null">#{LIUCHANYY,jdbcType=VARCHAR},</if>
                <if test="RONGMAOZT != null">#{RONGMAOZT,jdbcType=DECIMAL},</if>
                <if test="SHUQIANGQ != null">#{SHUQIANGQ,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUZQK != null">#{SHOUSHUZQK,jdbcType=VARCHAR},</if>
                <if test="JIEYUHMC != null">#{JIEYUHMC,jdbcType=VARCHAR},</if>
                <if test="SHUHOUGQ != null">#{SHUHOUGQ,jdbcType=DECIMAL},</if>
                <if test="CHUXUEQK != null">#{CHUXUEQK,jdbcType=VARCHAR},</if>
                <if test="KUAKESSBZ != null">#{KUAKESSBZ,jdbcType=DECIMAL},</if>
                <if test="GUANLIANSSDID != null">#{GUANLIANSSDID,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB1 != null">#{SHOUSHUJB1,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX1 != null">#{SHOUSHUCKLX1,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB2 != null">#{SHOUSHUJB2,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX2 != null">#{SHOUSHUCKLX2,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB3 != null">#{SHOUSHUJB3,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX3 != null">#{SHOUSHUCKLX3,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB4 != null">#{SHOUSHUJB4,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX4 != null">#{SHOUSHUCKLX4,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB5 != null">#{SHOUSHUJB5,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX5 != null">#{SHOUSHUCKLX5,jdbcType=VARCHAR},</if>
                <if test="NEIJINGSSBZ != null">#{NEIJINGSSBZ,jdbcType=DECIMAL},</if>
                <if test="NEIJINGSS != null">#{NEIJINGSS,jdbcType=VARCHAR},</if>
                <if test="SHUHOUSSJB != null">#{SHUHOUSSJB,jdbcType=VARCHAR},</if>
                <if test="SHUHOUCKLX != null">#{SHUHOUCKLX,jdbcType=VARCHAR},</if>
                <if test="SHUZI1 != null">#{SHUZI1,jdbcType=DECIMAL},</if>
                <if test="SHUZI2 != null">#{SHUZI2,jdbcType=DECIMAL},</if>
                <if test="SHUZI3 != null">#{SHUZI3,jdbcType=DECIMAL},</if>
                <if test="SHUZI4 != null">#{SHUZI4,jdbcType=DECIMAL},</if>
                <if test="SHUZI5 != null">#{SHUZI5,jdbcType=DECIMAL},</if>
                <if test="SHUZI6 != null">#{SHUZI6,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUJSBZ != null">#{SHOUSHUJSBZ,jdbcType=DECIMAL},</if>
                <if test="SUXINGS != null">#{SUXINGS,jdbcType=VARCHAR},</if>
                <if test="SHUZHONGYYSJ != null">#{SHUZHONGYYSJ,jdbcType=TIMESTAMP},</if>
                <if test="LINCHUANGLJDRBZ != null">#{LINCHUANGLJDRBZ,jdbcType=DECIMAL},</if>
                <if test="GELICSMC != null">#{GELICSMC,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY1 != null">#{DIANZIBLSY1,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY2 != null">#{DIANZIBLSY2,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY3 != null">#{DIANZIBLSY3,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY4 != null">#{DIANZIBLSY4,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY5 != null">#{DIANZIBLSY5,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY6 != null">#{DIANZIBLSY6,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY7 != null">#{DIANZIBLSY7,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY8 != null">#{DIANZIBLSY8,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY9 != null">#{DIANZIBLSY9,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY10 != null">#{DIANZIBLSY10,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY11 != null">#{DIANZIBLSY11,jdbcType=VARCHAR},</if>
                <if test="DIANZIBLSY12 != null">#{DIANZIBLSY12,jdbcType=VARCHAR},</if>
                <if test="SHIXUELIANG != null">#{SHIXUELIANG,jdbcType=VARCHAR},</if>
                <if test="SHUXUELIANG != null">#{SHUXUELIANG,jdbcType=VARCHAR},</if>
                <if test="YUSHENQBZ != null">#{YUSHENQBZ,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUYSYLZ != null">#{SHOUSHUYSYLZ,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUYJSC != null">#{SHOUSHUYJSC,jdbcType=VARCHAR},</if>
                <if test="LINCHUANGLJID != null">#{LINCHUANGLJID,jdbcType=VARCHAR},</if>
                <if test="WULIJJ != null">#{WULIJJ,jdbcType=VARCHAR},</if>
                <if test="WULIQS != null">#{WULIQS,jdbcType=DECIMAL},</if>
                <if test="DIMIANJJ != null">#{DIMIANJJ,jdbcType=VARCHAR},</if>
                <if test="DIMIANQS != null">#{DIMIANQS,jdbcType=DECIMAL},</if>
                <if test="LVWANGJJ != null">#{LVWANGJJ,jdbcType=VARCHAR},</if>
                <if test="LVWANGQS != null">#{LVWANGQS,jdbcType=DECIMAL},</if>
                <if test="GANRANLX != null">#{GANRANLX,jdbcType=VARCHAR},</if>
                <if test="ZERENREN != null">#{ZERENREN,jdbcType=VARCHAR},</if>
                <if test="YUFANGYYSZZJBZ != null">#{YUFANGYYSZZJBZ,jdbcType=DECIMAL},</if>
                <if test="ZHIRUWU != null">#{ZHIRUWU,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUQJ != null">#{SHOUSHUQJ,jdbcType=DECIMAL},</if>
                <if test="BINGLISFWZ != null">#{BINGLISFWZ,jdbcType=DECIMAL},</if>
                <if test="BINGLIBWZXX != null">#{BINGLIBWZXX,jdbcType=VARCHAR},</if>
                <if test="SHIFOUDLWLQX != null">#{SHIFOUDLWLQX,jdbcType=DECIMAL},</if>
                <if test="BINGRENYLZ != null">#{BINGRENYLZ,jdbcType=VARCHAR},</if>
                <if test="YISHENGZID != null">#{YISHENGZID,jdbcType=VARCHAR},</if>
                <if test="ZHIDAOLS != null">#{ZHIDAOLS,jdbcType=VARCHAR},</if>
                <if test="SIXIEHS3 != null">#{SIXIEHS3,jdbcType=VARCHAR},</if>
                <if test="JIESHENG1 != null">#{JIESHENG1,jdbcType=VARCHAR},</if>
                <if test="JIESHENG2 != null">#{JIESHENG2,jdbcType=VARCHAR},</if>
                <if test="SHUHOUSSQZ != null">#{SHUHOUSSQZ,jdbcType=VARCHAR},</if>
                <if test="SHUHOUSSHZ != null">#{SHUHOUSSHZ,jdbcType=VARCHAR},</if>
                <if test="XUSHENHBZ != null">#{XUSHENHBZ,jdbcType=DECIMAL},</if>
                <if test="SHUHOUMCID1 != null">#{SHUHOUMCID1,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMC1 != null">#{SHUHOUMC1,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMCID2 != null">#{SHUHOUMCID2,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMC2 != null">#{SHUHOUMC2,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMCID3 != null">#{SHUHOUMCID3,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMC3 != null">#{SHUHOUMC3,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMCID4 != null">#{SHUHOUMCID4,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMC4 != null">#{SHUHOUMC4,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMCID5 != null">#{SHUHOUMCID5,jdbcType=VARCHAR},</if>
                <if test="SHUHOUMC5 != null">#{SHUHOUMC5,jdbcType=VARCHAR},</if>
                <if test="SHUHOUJB1 != null">#{SHUHOUJB1,jdbcType=VARCHAR},</if>
                <if test="SHUHOUCKLX1 != null">#{SHUHOUCKLX1,jdbcType=VARCHAR},</if>
                <if test="SHUHOUJB2 != null">#{SHUHOUJB2,jdbcType=VARCHAR},</if>
                <if test="SHUHOUCKLX2 != null">#{SHUHOUCKLX2,jdbcType=VARCHAR},</if>
                <if test="SHUHOUJB3 != null">#{SHUHOUJB3,jdbcType=VARCHAR},</if>
                <if test="SHUHOUCKLX3 != null">#{SHUHOUCKLX3,jdbcType=VARCHAR},</if>
                <if test="SHUHOUJB4 != null">#{SHUHOUJB4,jdbcType=VARCHAR},</if>
                <if test="SHUHOUCKLX4 != null">#{SHUHOUCKLX4,jdbcType=VARCHAR},</if>
                <if test="SHUHOUJB5 != null">#{SHUHOUJB5,jdbcType=VARCHAR},</if>
                <if test="SHUHOUCKLX5 != null">#{SHUHOUCKLX5,jdbcType=VARCHAR},</if>
                <if test="SHUHOUSSBW != null">#{SHUHOUSSBW,jdbcType=VARCHAR},</if>
                <if test="QUSHOUSSJ != null">#{QUSHOUSSJ,jdbcType=TIMESTAMP},</if>
                <if test="QUSHOUSJJHS != null">#{QUSHOUSJJHS,jdbcType=VARCHAR},</if>
                <if test="QUSHOUSJJHSXM != null">#{QUSHOUSJJHSXM,jdbcType=VARCHAR},</if>
                <if test="HUIBINGFSJ != null">#{HUIBINGFSJ,jdbcType=TIMESTAMP},</if>
                <if test="HUIBINGFJJHS != null">#{HUIBINGFJJHS,jdbcType=VARCHAR},</if>
                <if test="HUIBINGFJJHSXM != null">#{HUIBINGFJJHSXM,jdbcType=VARCHAR},</if>
                <if test="MAZUIFS != null">#{MAZUIFS,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUAPR != null">#{SHOUSHUAPR,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUAPSJ != null">#{SHOUSHUAPSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHUDJR != null">#{SHOUSHUDJR,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUDJSJ != null">#{SHOUSHUDJSJ,jdbcType=TIMESTAMP},</if>
                <if test="MAZUIAPR != null">#{MAZUIAPR,jdbcType=VARCHAR},</if>
                <if test="MAZUIAPSJ != null">#{MAZUIAPSJ,jdbcType=TIMESTAMP},</if>
                <if test="MAZUIDJR != null">#{MAZUIDJR,jdbcType=VARCHAR},</if>
                <if test="MAZUIDJSJ != null">#{MAZUIDJSJ,jdbcType=TIMESTAMP},</if>
                <if test="YUJISC != null">#{YUJISC,jdbcType=DECIMAL},</if>
                <if test="SHENHEBTGYY != null">#{SHENHEBTGYY,jdbcType=VARCHAR},</if>
                <if test="JINGHUIFSSJ != null">#{JINGHUIFSSJ,jdbcType=TIMESTAMP},</if>
                <if test="JINGHUIFSJJHS != null">#{JINGHUIFSJJHS,jdbcType=VARCHAR},</if>
                <if test="JINGHUIFSJJHSXM != null">#{JINGHUIFSJJHSXM,jdbcType=VARCHAR},</if>
                <if test="JINGSHOUSJSJ != null">#{JINGSHOUSJSJ,jdbcType=TIMESTAMP},</if>
                <if test="JINGSHOUSJJJHS != null">#{JINGSHOUSJJJHS,jdbcType=VARCHAR},</if>
                <if test="JINGSHOUSJJJHSXM != null">#{JINGSHOUSJJJHSXM,jdbcType=VARCHAR},</if>
                <if test="SHOUSHULZZT != null">#{SHOUSHULZZT,jdbcType=VARCHAR},</if>
                <if test="ZHONGZHISJ != null">#{ZHONGZHISJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHONGZHIJJHS != null">#{ZHONGZHIJJHS,jdbcType=VARCHAR},</if>
                <if test="ZHONGZHIJJHSXM != null">#{ZHONGZHIJJHSXM,jdbcType=VARCHAR},</if>
                <if test="CHUSHOUSSSJ != null">#{CHUSHOUSSSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUSHOUSSJJHS != null">#{CHUSHOUSSJJHS,jdbcType=VARCHAR},</if>
                <if test="CHUSHOUSSJJHSXM != null">#{CHUSHOUSSJJHSXM,jdbcType=VARCHAR},</if>
                <if test="CHUHUIFSSJ != null">#{CHUHUIFSSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUHUIFSJJHS != null">#{CHUHUIFSJJHS,jdbcType=VARCHAR},</if>
                <if test="CHUHUIFSJJHSXM != null">#{CHUHUIFSJJHSXM,jdbcType=VARCHAR},</if>
                <if test="FENGUANYZSHBZ != null">#{FENGUANYZSHBZ,jdbcType=DECIMAL},</if>
                <if test="FENGUANYZ != null">#{FENGUANYZ,jdbcType=VARCHAR},</if>
                <if test="FENGUANYZSHSJ != null">#{FENGUANYZSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="FENGUANYZSHBTGYY != null">#{FENGUANYZSHBTGYY,jdbcType=VARCHAR},</if>
                <if test="KEZHURSHBZ != null">#{KEZHURSHBZ,jdbcType=DECIMAL},</if>
                <if test="KEZHUREN != null">#{KEZHUREN,jdbcType=VARCHAR},</if>
                <if test="KEZHURSHSJ != null">#{KEZHURSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="KEZHURSHBTGYY != null">#{KEZHURSHBTGYY,jdbcType=VARCHAR},</if>
                <if test="YIWUKSHBZ != null">#{YIWUKSHBZ,jdbcType=DECIMAL},</if>
                <if test="YIWUKE != null">#{YIWUKE,jdbcType=VARCHAR},</if>
                <if test="YIWUKSHSJ != null">#{YIWUKSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="YIWUKSHBTGYY != null">#{YIWUKSHBTGYY,jdbcType=VARCHAR},</if>
                <if test="SHANGBAOSY != null">#{SHANGBAOSY,jdbcType=VARCHAR},</if>
                <if test="SHANGBAOYS != null">#{SHANGBAOYS,jdbcType=VARCHAR},</if>
                <if test="XUJIEJWT != null">#{XUJIEJWT,jdbcType=VARCHAR},</if>
                <if test="SHUQIANTLJG != null">#{SHUQIANTLJG,jdbcType=VARCHAR},</if>
                <if test="YICAIQCS != null">#{YICAIQCS,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYSSHBZ != null">#{ZHUZHIYSSHBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUZHIYSSHR != null">#{ZHUZHIYSSHR,jdbcType=VARCHAR},</if>
                <if test="ZHUZHIYSSHSJ != null">#{ZHUZHIYSSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="ZHUZHIYSSHBTGYY != null">#{ZHUZHIYSSHBTGYY,jdbcType=DECIMAL},</if>
                <if test="SHUQIANGRQK != null">#{SHUQIANGRQK,jdbcType=VARCHAR},</if>
                <if test="BINGFAZHENG != null">#{BINGFAZHENG,jdbcType=VARCHAR},</if>
                <if test="DUOCHONGNYBZ != null">#{DUOCHONGNYBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUDAOYSSHBZ != null">#{ZHUDAOYSSHBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUDAOYSSHR != null">#{ZHUDAOYSSHR,jdbcType=VARCHAR},</if>
                <if test="ZHUDAOYSSHSJ != null">#{ZHUDAOYSSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="MAZUIYSSHBZ != null">#{MAZUIYSSHBZ,jdbcType=DECIMAL},</if>
                <if test="MAZUIYSSHR != null">#{MAZUIYSSHR,jdbcType=VARCHAR},</if>
                <if test="MAZUIYSSHSJ != null">#{MAZUIYSSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="MAZUIYF != null">#{MAZUIYF,jdbcType=VARCHAR},</if>
                <if test="SHENQINGYS != null">#{SHENQINGYS,jdbcType=VARCHAR},</if>
                <if test="YISHENGSQRQ != null">#{YISHENGSQRQ,jdbcType=TIMESTAMP},</if>
                <if test="ZHENDUAN != null">#{ZHENDUAN,jdbcType=VARCHAR},</if>
                <if test="SHANGJIYSSHBZ != null">#{SHANGJIYSSHBZ,jdbcType=DECIMAL},</if>
                <if test="SHANGJIYSSHSJ != null">#{SHANGJIYSSHSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHANGJIYSSHR != null">#{SHANGJIYSSHR,jdbcType=VARCHAR},</if>
                <if test="SHANGJIYS != null">#{SHANGJIYS,jdbcType=VARCHAR},</if>
                <if test="SHANGJIYSSHBTGYY != null">#{SHANGJIYSSHBTGYY,jdbcType=VARCHAR},</if>
                <if test="SHUQIANSSJB != null">#{SHUQIANSSJB,jdbcType=VARCHAR},</if>
                <if test="MAZUIHZDID != null">#{MAZUIHZDID,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUDM != null">#{SHOUSHUDM,jdbcType=VARCHAR},</if>
                <if test="JINICUHS != null">#{JINICUHS,jdbcType=VARCHAR},</if>
                <if test="JINICUSJ != null">#{JINICUSJ,jdbcType=TIMESTAMP},</if>
                <if test="JINICUHSXM != null">#{JINICUHSXM,jdbcType=VARCHAR},</if>
                <if test="CHUICUHS != null">#{CHUICUHS,jdbcType=VARCHAR},</if>
                <if test="CHUICUSJ != null">#{CHUICUSJ,jdbcType=TIMESTAMP},</if>
                <if test="CHUICUHSXM != null">#{CHUICUHSXM,jdbcType=VARCHAR},</if>
                <if test="JUJUELX != null">#{JUJUELX,jdbcType=VARCHAR},</if>
                <if test="JUJUEYY != null">#{JUJUEYY,jdbcType=VARCHAR},</if>
                <if test="TAICI != null">#{TAICI,jdbcType=VARCHAR},</if>
                <if test="BEIZHU1 != null">#{BEIZHU1,jdbcType=VARCHAR},</if>
                <if test="BEIZHU2 != null">#{BEIZHU2,jdbcType=VARCHAR},</if>
                <if test="ZHIQINGTYSZT != null">#{ZHIQINGTYSZT,jdbcType=DECIMAL},</if>
                <if test="WEIJIZHIBZ != null">#{WEIJIZHIBZ,jdbcType=DECIMAL},</if>
                <if test="SHUHOUZHENTONGBZ != null">#{SHUHOUZHENTONGBZ,jdbcType=DECIMAL},</if>
                <if test="ISKANGJUNYW != null">#{ISKANGJUNYW,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUSLX != null">#{SHOUSHUSLX,jdbcType=VARCHAR},</if>
                <if test="RIJIANSSBZ != null">#{RIJIANSSBZ,jdbcType=DECIMAL},</if>
                <if test="ZHENDUANDM != null">#{ZHENDUANDM,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW2 != null">#{SHOUSHUBW2,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW3 != null">#{SHOUSHUBW3,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW4 != null">#{SHOUSHUBW4,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW5 != null">#{SHOUSHUBW5,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW1 != null">#{SHOUSHUBW1,jdbcType=VARCHAR},</if>
                <if test="MAZUIJSBZ != null">#{MAZUIJSBZ,jdbcType=DECIMAL},</if>
                <if test="MAZUIJSREN != null">#{MAZUIJSREN,jdbcType=VARCHAR},</if>
                <if test="MAZUIJSSJ != null">#{MAZUIJSSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHUJZJSREN != null">#{SHOUSHUJZJSREN,jdbcType=VARCHAR},</if>
                <if test="MAZUIJZJSREN != null">#{MAZUIJZJSREN,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJZSJ != null">#{SHOUSHUJZSJ,jdbcType=TIMESTAMP},</if>
                <if test="QUXIAOYY != null">#{QUXIAOYY,jdbcType=VARCHAR},</if>
                <if test="YUANYINLX != null">#{YUANYINLX,jdbcType=VARCHAR},</if>
                <if test="QUXIAOR != null">#{QUXIAOR,jdbcType=VARCHAR},</if>
                <if test="QUXIAOSJ != null">#{QUXIAOSJ,jdbcType=TIMESTAMP},</if>
                <if test="SHOUSHUKSREN != null">#{SHOUSHUKSREN,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJSREN != null">#{SHOUSHUJSREN,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJZBZ != null">#{SHOUSHUJZBZ,jdbcType=DECIMAL},</if>
                <if test="MAZUIJZBZ != null">#{MAZUIJZBZ,jdbcType=DECIMAL},</if>
                <if test="MAZUIJZSJ != null">#{MAZUIJZSJ,jdbcType=TIMESTAMP},</if>
                <if test="WAIYUANBZ != null">#{WAIYUANBZ,jdbcType=DECIMAL},</if>
                <if test="ZHUANYUNFS != null">#{ZHUANYUNFS,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUXZ != null">#{SHOUSHUXZ,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUXZQT != null">#{SHOUSHUXZQT,jdbcType=VARCHAR},</if>
                <if test="FEIJIHUAZCSS != null">#{FEIJIHUAZCSS,jdbcType=VARCHAR},</if>
                <if test="TIWEI != null">#{TIWEI,jdbcType=VARCHAR},</if>
                <if test="ZHIDAOYS != null">#{ZHIDAOYS,jdbcType=VARCHAR},</if>
                <if test="XUYAOSZBD != null">#{XUYAOSZBD,jdbcType=DECIMAL},</if>
                <if test="XUYAOBBCJ != null">#{XUYAOBBCJ,jdbcType=DECIMAL},</if>
                <if test="SHENQINGSHZHBF != null">#{SHENQINGSHZHBF,jdbcType=DECIMAL},</if>
                <if test="SHENQINGZICU != null">#{SHENQINGZICU,jdbcType=DECIMAL},</if>
                <if test="SHUHOUZT != null">#{SHUHOUZT,jdbcType=DECIMAL},</if>
                <if test="GENTAI != null">#{GENTAI,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUBZ != null">#{SHOUSHUBZ,jdbcType=VARCHAR},</if>
                <if test="SONGFUSS != null">#{SONGFUSS,jdbcType=DECIMAL},</if>
                <if test="TESHUJC != null">#{TESHUJC,jdbcType=VARCHAR},</if>
                <if test="MAZUISSS != null">#{MAZUISSS,jdbcType=VARCHAR},</if>
                <if test="MAZUIPG != null">#{MAZUIPG,jdbcType=VARCHAR},</if>
                <if test="MAZUIPGNR != null">#{MAZUIPGNR,jdbcType=VARCHAR},</if>
                <if test="WEICHUANGSSBZ != null">#{WEICHUANGSSBZ,jdbcType=DECIMAL},</if>
                <if test="MAZUIPGBLJLXH != null">#{MAZUIPGBLJLXH,jdbcType=DECIMAL},</if>
                <if test="SHOUSHUBW6 != null">#{SHOUSHUBW6,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC6 != null">#{SHOUSHUMC6,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID6 != null">#{SHOUSHUID6,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW7 != null">#{SHOUSHUBW7,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC7 != null">#{SHOUSHUMC7,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID7 != null">#{SHOUSHUID7,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW8 != null">#{SHOUSHUBW8,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC8 != null">#{SHOUSHUMC8,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID8 != null">#{SHOUSHUID8,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW9 != null">#{SHOUSHUBW9,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC9 != null">#{SHOUSHUMC9,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID9 != null">#{SHOUSHUID9,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW10 != null">#{SHOUSHUBW10,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC10 != null">#{SHOUSHUMC10,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID10 != null">#{SHOUSHUID10,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW11 != null">#{SHOUSHUBW11,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC11 != null">#{SHOUSHUMC11,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID11 != null">#{SHOUSHUID11,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW12 != null">#{SHOUSHUBW12,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC12 != null">#{SHOUSHUMC12,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID12 != null">#{SHOUSHUID12,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW13 != null">#{SHOUSHUBW13,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC13 != null">#{SHOUSHUMC13,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID13 != null">#{SHOUSHUID13,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW14 != null">#{SHOUSHUBW14,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC14 != null">#{SHOUSHUMC14,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID14 != null">#{SHOUSHUID14,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW15 != null">#{SHOUSHUBW15,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC15 != null">#{SHOUSHUMC15,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID15 != null">#{SHOUSHUID15,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW16 != null">#{SHOUSHUBW16,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC16 != null">#{SHOUSHUMC16,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID16 != null">#{SHOUSHUID16,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW17 != null">#{SHOUSHUBW17,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC17 != null">#{SHOUSHUMC17,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID17 != null">#{SHOUSHUID17,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW18 != null">#{SHOUSHUBW18,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC18 != null">#{SHOUSHUMC18,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID18 != null">#{SHOUSHUID18,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW19 != null">#{SHOUSHUBW19,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC19 != null">#{SHOUSHUMC19,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID19 != null">#{SHOUSHUID19,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUBW20 != null">#{SHOUSHUBW20,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUMC20 != null">#{SHOUSHUMC20,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUID20 != null">#{SHOUSHUID20,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB6 != null">#{SHOUSHUJB6,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX6 != null">#{SHOUSHUCKLX6,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB7 != null">#{SHOUSHUJB7,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX7 != null">#{SHOUSHUCKLX7,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB8 != null">#{SHOUSHUJB8,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX8 != null">#{SHOUSHUCKLX8,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB9 != null">#{SHOUSHUJB9,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX9 != null">#{SHOUSHUCKLX9,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB10 != null">#{SHOUSHUJB10,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX10 != null">#{SHOUSHUCKLX10,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB11 != null">#{SHOUSHUJB11,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX11 != null">#{SHOUSHUCKLX11,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB12 != null">#{SHOUSHUJB12,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX12 != null">#{SHOUSHUCKLX12,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB13 != null">#{SHOUSHUJB13,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX13 != null">#{SHOUSHUCKLX13,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB14 != null">#{SHOUSHUJB14,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX14 != null">#{SHOUSHUCKLX14,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB15 != null">#{SHOUSHUJB15,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX15 != null">#{SHOUSHUCKLX15,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB16 != null">#{SHOUSHUJB16,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX16 != null">#{SHOUSHUCKLX16,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB17 != null">#{SHOUSHUJB17,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX17 != null">#{SHOUSHUCKLX17,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB18 != null">#{SHOUSHUJB18,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX18 != null">#{SHOUSHUCKLX18,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB19 != null">#{SHOUSHUJB19,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX19 != null">#{SHOUSHUCKLX19,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUJB20 != null">#{SHOUSHUJB20,jdbcType=VARCHAR},</if>
                <if test="SHOUSHUCKLX20 != null">#{SHOUSHUCKLX20,jdbcType=VARCHAR},</if>
                <if test="SHOUHU != null">#{SHOUHU,jdbcType=VARCHAR},</if>
                <if test="SHOUHU1 != null">#{SHOUHU1,jdbcType=VARCHAR},</if>
                <if test="SHOUHU2 != null">#{SHOUHU2,jdbcType=VARCHAR},</if>
                <if test="SHOUHU3 != null">#{SHOUHU3,jdbcType=VARCHAR},</if>
                <if test="SHOUHU4 != null">#{SHOUHU4,jdbcType=VARCHAR},</if>
                <if test="SHOUHU5 != null">#{SHOUHU5,jdbcType=VARCHAR},</if>
                <if test="SHOUHU6 != null">#{SHOUHU6,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.javazx.batch.po.SmShoushuxx">
        update SM_SHOUSHUXX
        <set>
                <if test="SHOUSHUDH != null">
                    SHOUSHUDH = #{SHOUSHUDH,jdbcType=VARCHAR},
                </if>
                <if test="SHENQINGDID != null">
                    SHENQINGDID = #{SHENQINGDID,jdbcType=VARCHAR},
                </if>
                <if test="YINGYONGID != null">
                    YINGYONGID = #{YINGYONGID,jdbcType=VARCHAR},
                </if>
                <if test="YUANQUID != null">
                    YUANQUID = #{YUANQUID,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENID != null">
                    BINGRENID = #{BINGRENID,jdbcType=VARCHAR},
                </if>
                <if test="ZHUYUANHAO != null">
                    ZHUYUANHAO = #{ZHUYUANHAO,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENXM != null">
                    BINGRENXM = #{BINGRENXM,jdbcType=VARCHAR},
                </if>
                <if test="XINGBIE != null">
                    XINGBIE = #{XINGBIE,jdbcType=VARCHAR},
                </if>
                <if test="NIANLING != null">
                    NIANLING = #{NIANLING,jdbcType=DECIMAL},
                </if>
                <if test="BINGRENKS != null">
                    BINGRENKS = #{BINGRENKS,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENBQ != null">
                    BINGRENBQ = #{BINGRENBQ,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENCW != null">
                    BINGRENCW = #{BINGRENCW,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGXZ != null">
                    FEIYONGXZ = #{FEIYONGXZ,jdbcType=VARCHAR},
                </if>
                <if test="FEIYONGLB != null">
                    FEIYONGLB = #{FEIYONGLB,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENTZ != null">
                    BINGRENTZ = #{BINGRENTZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENQINGKS != null">
                    SHENQINGKS = #{SHENQINGKS,jdbcType=VARCHAR},
                </if>
                <if test="ZHIXINGKS != null">
                    ZHIXINGKS = #{ZHIXINGKS,jdbcType=VARCHAR},
                </if>
                <if test="SHENQINGSJ != null">
                    SHENQINGSJ = #{SHENQINGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YAOQIUSJ != null">
                    YAOQIUSJ = #{YAOQIUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ANPAISJ != null">
                    ANPAISJ = #{ANPAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JINXINGSJ != null">
                    JINXINGSJ = #{JINXINGSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JIESHUSJ != null">
                    JIESHUSJ = #{JIESHUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHULB != null">
                    SHOUSHULB = #{SHOUSHULB,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMCID != null">
                    SHOUSHUMCID = #{SHOUSHUMCID,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC != null">
                    SHOUSHUMC = #{SHOUSHUMC,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMCID1 != null">
                    SHOUSHUMCID1 = #{SHOUSHUMCID1,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC1 != null">
                    SHOUSHUMC1 = #{SHOUSHUMC1,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMCID2 != null">
                    SHOUSHUMCID2 = #{SHOUSHUMCID2,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC2 != null">
                    SHOUSHUMC2 = #{SHOUSHUMC2,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMCID3 != null">
                    SHOUSHUMCID3 = #{SHOUSHUMCID3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC3 != null">
                    SHOUSHUMC3 = #{SHOUSHUMC3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUYS != null">
                    SHOUSHUYS = #{SHOUSHUYS,jdbcType=VARCHAR},
                </if>
                <if test="ZHULIYS1 != null">
                    ZHULIYS1 = #{ZHULIYS1,jdbcType=VARCHAR},
                </if>
                <if test="ZHULIYS2 != null">
                    ZHULIYS2 = #{ZHULIYS2,jdbcType=VARCHAR},
                </if>
                <if test="ZHULIYS3 != null">
                    ZHULIYS3 = #{ZHULIYS3,jdbcType=VARCHAR},
                </if>
                <if test="XISHOUHS != null">
                    XISHOUHS = #{XISHOUHS,jdbcType=VARCHAR},
                </if>
                <if test="SIXIEHS1 != null">
                    SIXIEHS1 = #{SIXIEHS1,jdbcType=VARCHAR},
                </if>
                <if test="SIXIEHS2 != null">
                    SIXIEHS2 = #{SIXIEHS2,jdbcType=VARCHAR},
                </if>
                <if test="XUNHUIHS1 != null">
                    XUNHUIHS1 = #{XUNHUIHS1,jdbcType=VARCHAR},
                </if>
                <if test="XUNHUIHS2 != null">
                    XUNHUIHS2 = #{XUNHUIHS2,jdbcType=VARCHAR},
                </if>
                <if test="XUNHUIHS3 != null">
                    XUNHUIHS3 = #{XUNHUIHS3,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIID != null">
                    MAZUIID = #{MAZUIID,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIFF != null">
                    MAZUIFF = #{MAZUIFF,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIYS != null">
                    MAZUIYS = #{MAZUIYS,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIYS1 != null">
                    MAZUIYS1 = #{MAZUIYS1,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIYS2 != null">
                    MAZUIYS2 = #{MAZUIYS2,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIYS3 != null">
                    MAZUIYS3 = #{MAZUIYS3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUTID != null">
                    SHOUSHUTID = #{SHOUSHUTID,jdbcType=VARCHAR},
                </if>
                <if test="JIETAIHAO != null">
                    JIETAIHAO = #{JIETAIHAO,jdbcType=VARCHAR},
                </if>
                <if test="SHOUCISSBZ != null">
                    SHOUCISSBZ = #{SHOUCISSBZ,jdbcType=DECIMAL},
                </if>
                <if test="JIZHENBZ != null">
                    JIZHENBZ = #{JIZHENBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHENTONGBSYBZ != null">
                    ZHENTONGBSYBZ = #{ZHENTONGBSYBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUCKLX != null">
                    SHOUSHUCKLX = #{SHOUSHUCKLX,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGKOUYHQK != null">
                    CHUANGKOUYHQK = #{CHUANGKOUYHQK,jdbcType=VARCHAR},
                </if>
                <if test="CHUANGKOUGRQK != null">
                    CHUANGKOUGRQK = #{CHUANGKOUGRQK,jdbcType=VARCHAR},
                </if>
                <if test="SHUQIANXX != null">
                    SHUQIANXX = #{SHUQIANXX,jdbcType=VARCHAR},
                </if>
                <if test="SHENQINGREN != null">
                    SHENQINGREN = #{SHENQINGREN,jdbcType=VARCHAR},
                </if>
                <if test="CAOZUOYUAN != null">
                    CAOZUOYUAN = #{CAOZUOYUAN,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANDM1 != null">
                    ZHENDUANDM1 = #{ZHENDUANDM1,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANMC1 != null">
                    ZHENDUANMC1 = #{ZHENDUANMC1,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANDM2 != null">
                    ZHENDUANDM2 = #{ZHENDUANDM2,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANMC2 != null">
                    ZHENDUANMC2 = #{ZHENDUANMC2,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANDM3 != null">
                    ZHENDUANDM3 = #{ZHENDUANDM3,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANMC3 != null">
                    ZHENDUANMC3 = #{ZHENDUANMC3,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANDM4 != null">
                    ZHENDUANDM4 = #{ZHENDUANDM4,jdbcType=VARCHAR},
                </if>
                <if test="ZHENDUANMC4 != null">
                    ZHENDUANMC4 = #{ZHENDUANMC4,jdbcType=VARCHAR},
                </if>
                <if test="ZUOFEIBZ != null">
                    ZUOFEIBZ = #{ZUOFEIBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUANGTAIBZ != null">
                    ZHUANGTAIBZ = #{ZHUANGTAIBZ,jdbcType=DECIMAL},
                </if>
                <if test="DUIHUAXX != null">
                    DUIHUAXX = #{DUIHUAXX,jdbcType=VARCHAR},
                </if>
                <if test="BEIZHU != null">
                    BEIZHU = #{BEIZHU,jdbcType=VARCHAR},
                </if>
                <if test="MENZHENZYBZ != null">
                    MENZHENZYBZ = #{MENZHENZYBZ,jdbcType=DECIMAL},
                </if>
                <if test="XIUGAIREN != null">
                    XIUGAIREN = #{XIUGAIREN,jdbcType=VARCHAR},
                </if>
                <if test="XIUGAISJ != null">
                    XIUGAISJ = #{XIUGAISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="NIANLINGDW != null">
                    NIANLINGDW = #{NIANLINGDW,jdbcType=VARCHAR},
                </if>
                <if test="BINGRENZYID != null">
                    BINGRENZYID = #{BINGRENZYID,jdbcType=VARCHAR},
                </if>
                <if test="YINGERBZ != null">
                    YINGERBZ = #{YINGERBZ,jdbcType=DECIMAL},
                </if>
                <if test="YINGERID != null">
                    YINGERID = #{YINGERID,jdbcType=VARCHAR},
                </if>
                <if test="XISHOUHSBZ != null">
                    XISHOUHSBZ = #{XISHOUHSBZ,jdbcType=DECIMAL},
                </if>
                <if test="GELIBZ != null">
                    GELIBZ = #{GELIBZ,jdbcType=DECIMAL},
                </if>
                <if test="YAOQIUST != null">
                    YAOQIUST = #{YAOQIUST,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUJB != null">
                    SHOUSHUJB = #{SHOUSHUJB,jdbcType=VARCHAR},
                </if>
                <if test="SHENGAO != null">
                    SHENGAO = #{SHENGAO,jdbcType=DECIMAL},
                </if>
                <if test="ZAOYING != null">
                    ZAOYING = #{ZAOYING,jdbcType=VARCHAR},
                </if>
                <if test="TESHUSSBZ != null">
                    TESHUSSBZ = #{TESHUSSBZ,jdbcType=DECIMAL},
                </if>
                <if test="KAIDANYYID != null">
                    KAIDANYYID = #{KAIDANYYID,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAXMID != null">
                    JIANCHAXMID = #{JIANCHAXMID,jdbcType=VARCHAR},
                </if>
                <if test="JIANCHAJKID1 != null">
                    JIANCHAJKID1 = #{JIANCHAJKID1,jdbcType=VARCHAR},
                </if>
                <if test="TIWAIXHS != null">
                    TIWAIXHS = #{TIWAIXHS,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIAPZT != null">
                    MAZUIAPZT = #{MAZUIAPZT,jdbcType=DECIMAL},
                </if>
                <if test="MAZUIFF1 != null">
                    MAZUIFF1 = #{MAZUIFF1,jdbcType=VARCHAR},
                </if>
                <if test="FUHEMZFFMC != null">
                    FUHEMZFFMC = #{FUHEMZFFMC,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW != null">
                    SHOUSHUBW = #{SHOUSHUBW,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMCID4 != null">
                    SHOUSHUMCID4 = #{SHOUSHUMCID4,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC4 != null">
                    SHOUSHUMC4 = #{SHOUSHUMC4,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMCID5 != null">
                    SHOUSHUMCID5 = #{SHOUSHUMCID5,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC5 != null">
                    SHOUSHUMC5 = #{SHOUSHUMC5,jdbcType=VARCHAR},
                </if>
                <if test="JIUZHENID != null">
                    JIUZHENID = #{JIUZHENID,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUSSMCID != null">
                    SHUHOUSSMCID = #{SHUHOUSSMCID,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUSSMC != null">
                    SHUHOUSSMC = #{SHUHOUSSMC,jdbcType=VARCHAR},
                </if>
                <if test="RUSHISJ != null">
                    RUSHISJ = #{RUSHISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUSHISJ != null">
                    CHUSHISJ = #{CHUSHISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHUKS != null">
                    SHOUSHUKS = #{SHOUSHUKS,jdbcType=VARCHAR},
                </if>
                <if test="MAZUISJ != null">
                    MAZUISJ = #{MAZUISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="FEIJIHCFSSSBZ != null">
                    FEIJIHCFSSSBZ = #{FEIJIHCFSSSBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUQIANTL != null">
                    SHUQIANTL = #{SHUQIANTL,jdbcType=DECIMAL},
                </if>
                <if test="YIWUCBABZ != null">
                    YIWUCBABZ = #{YIWUCBABZ,jdbcType=DECIMAL},
                </if>
                <if test="YIGANGLDB != null">
                    YIGANGLDB = #{YIGANGLDB,jdbcType=DECIMAL},
                </if>
                <if test="MEIDUKT != null">
                    MEIDUKT = #{MEIDUKT,jdbcType=DECIMAL},
                </if>
                <if test="AIZIBKT != null">
                    AIZIBKT = #{AIZIBKT,jdbcType=DECIMAL},
                </if>
                <if test="BINGGANKT != null">
                    BINGGANKT = #{BINGGANKT,jdbcType=DECIMAL},
                </if>
                <if test="QITATSGR != null">
                    QITATSGR = #{QITATSGR,jdbcType=VARCHAR},
                </if>
                <if test="BUCHUANSSS != null">
                    BUCHUANSSS = #{BUCHUANSSS,jdbcType=DECIMAL},
                </if>
                <if test="PAICHIBZ != null">
                    PAICHIBZ = #{PAICHIBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENHEBZ != null">
                    SHENHEBZ = #{SHENHEBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHENHESJ != null">
                    SHENHESJ = #{SHENHESJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHENHEREN != null">
                    SHENHEREN = #{SHENHEREN,jdbcType=VARCHAR},
                </if>
                <if test="BINGDONGBLBZ != null">
                    BINGDONGBLBZ = #{BINGDONGBLBZ,jdbcType=DECIMAL},
                </if>
                <if test="GELICS != null">
                    GELICS = #{GELICS,jdbcType=VARCHAR},
                </if>
                <if test="YUFANGYYSJ != null">
                    YUFANGYYSJ = #{YUFANGYYSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHUFXJB != null">
                    SHOUSHUFXJB = #{SHOUSHUFXJB,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIFJ != null">
                    MAZUIFJ = #{MAZUIFJ,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGDASSBZ != null">
                    ZHONGDASSBZ = #{ZHONGDASSBZ,jdbcType=DECIMAL},
                </if>
                <if test="CHUANGKOUXGLY != null">
                    CHUANGKOUXGLY = #{CHUANGKOUXGLY,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUYJWCSJ != null">
                    SHOUSHUYJWCSJ = #{SHOUSHUYJWCSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHUXUEQSXBGBZ != null">
                    SHUXUEQSXBGBZ = #{SHUXUEQSXBGBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUXUEQSXJG != null">
                    SHUXUEQSXJG = #{SHUXUEQSXJG,jdbcType=VARCHAR},
                </if>
                <if test="SHENGYUSHI != null">
                    SHENGYUSHI = #{SHENGYUSHI,jdbcType=VARCHAR},
                </if>
                <if test="HUNYIN != null">
                    HUNYIN = #{HUNYIN,jdbcType=VARCHAR},
                </if>
                <if test="SHUQIANJCSFQQ != null">
                    SHUQIANJCSFQQ = #{SHUQIANJCSFQQ,jdbcType=DECIMAL},
                </if>
                <if test="SHIFOUSBLHY != null">
                    SHIFOUSBLHY = #{SHIFOUSBLHY,jdbcType=DECIMAL},
                </if>
                <if test="BIAOBEN != null">
                    BIAOBEN = #{BIAOBEN,jdbcType=VARCHAR},
                </if>
                <if test="BIAOBENJS != null">
                    BIAOBENJS = #{BIAOBENJS,jdbcType=DECIMAL},
                </if>
                <if test="SHUZHONGYY != null">
                    SHUZHONGYY = #{SHUZHONGYY,jdbcType=VARCHAR},
                </if>
                <if test="LIUCHANYY != null">
                    LIUCHANYY = #{LIUCHANYY,jdbcType=VARCHAR},
                </if>
                <if test="RONGMAOZT != null">
                    RONGMAOZT = #{RONGMAOZT,jdbcType=DECIMAL},
                </if>
                <if test="SHUQIANGQ != null">
                    SHUQIANGQ = #{SHUQIANGQ,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUZQK != null">
                    SHOUSHUZQK = #{SHOUSHUZQK,jdbcType=VARCHAR},
                </if>
                <if test="JIEYUHMC != null">
                    JIEYUHMC = #{JIEYUHMC,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUGQ != null">
                    SHUHOUGQ = #{SHUHOUGQ,jdbcType=DECIMAL},
                </if>
                <if test="CHUXUEQK != null">
                    CHUXUEQK = #{CHUXUEQK,jdbcType=VARCHAR},
                </if>
                <if test="KUAKESSBZ != null">
                    KUAKESSBZ = #{KUAKESSBZ,jdbcType=DECIMAL},
                </if>
                <if test="GUANLIANSSDID != null">
                    GUANLIANSSDID = #{GUANLIANSSDID,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB1 != null">
                    SHOUSHUJB1 = #{SHOUSHUJB1,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX1 != null">
                    SHOUSHUCKLX1 = #{SHOUSHUCKLX1,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB2 != null">
                    SHOUSHUJB2 = #{SHOUSHUJB2,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX2 != null">
                    SHOUSHUCKLX2 = #{SHOUSHUCKLX2,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB3 != null">
                    SHOUSHUJB3 = #{SHOUSHUJB3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX3 != null">
                    SHOUSHUCKLX3 = #{SHOUSHUCKLX3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB4 != null">
                    SHOUSHUJB4 = #{SHOUSHUJB4,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX4 != null">
                    SHOUSHUCKLX4 = #{SHOUSHUCKLX4,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB5 != null">
                    SHOUSHUJB5 = #{SHOUSHUJB5,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX5 != null">
                    SHOUSHUCKLX5 = #{SHOUSHUCKLX5,jdbcType=VARCHAR},
                </if>
                <if test="NEIJINGSSBZ != null">
                    NEIJINGSSBZ = #{NEIJINGSSBZ,jdbcType=DECIMAL},
                </if>
                <if test="NEIJINGSS != null">
                    NEIJINGSS = #{NEIJINGSS,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUSSJB != null">
                    SHUHOUSSJB = #{SHUHOUSSJB,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUCKLX != null">
                    SHUHOUCKLX = #{SHUHOUCKLX,jdbcType=VARCHAR},
                </if>
                <if test="SHUZI1 != null">
                    SHUZI1 = #{SHUZI1,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI2 != null">
                    SHUZI2 = #{SHUZI2,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI3 != null">
                    SHUZI3 = #{SHUZI3,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI4 != null">
                    SHUZI4 = #{SHUZI4,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI5 != null">
                    SHUZI5 = #{SHUZI5,jdbcType=DECIMAL},
                </if>
                <if test="SHUZI6 != null">
                    SHUZI6 = #{SHUZI6,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUJSBZ != null">
                    SHOUSHUJSBZ = #{SHOUSHUJSBZ,jdbcType=DECIMAL},
                </if>
                <if test="SUXINGS != null">
                    SUXINGS = #{SUXINGS,jdbcType=VARCHAR},
                </if>
                <if test="SHUZHONGYYSJ != null">
                    SHUZHONGYYSJ = #{SHUZHONGYYSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="LINCHUANGLJDRBZ != null">
                    LINCHUANGLJDRBZ = #{LINCHUANGLJDRBZ,jdbcType=DECIMAL},
                </if>
                <if test="GELICSMC != null">
                    GELICSMC = #{GELICSMC,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY1 != null">
                    DIANZIBLSY1 = #{DIANZIBLSY1,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY2 != null">
                    DIANZIBLSY2 = #{DIANZIBLSY2,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY3 != null">
                    DIANZIBLSY3 = #{DIANZIBLSY3,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY4 != null">
                    DIANZIBLSY4 = #{DIANZIBLSY4,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY5 != null">
                    DIANZIBLSY5 = #{DIANZIBLSY5,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY6 != null">
                    DIANZIBLSY6 = #{DIANZIBLSY6,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY7 != null">
                    DIANZIBLSY7 = #{DIANZIBLSY7,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY8 != null">
                    DIANZIBLSY8 = #{DIANZIBLSY8,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY9 != null">
                    DIANZIBLSY9 = #{DIANZIBLSY9,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY10 != null">
                    DIANZIBLSY10 = #{DIANZIBLSY10,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY11 != null">
                    DIANZIBLSY11 = #{DIANZIBLSY11,jdbcType=VARCHAR},
                </if>
                <if test="DIANZIBLSY12 != null">
                    DIANZIBLSY12 = #{DIANZIBLSY12,jdbcType=VARCHAR},
                </if>
                <if test="SHIXUELIANG != null">
                    SHIXUELIANG = #{SHIXUELIANG,jdbcType=VARCHAR},
                </if>
                <if test="SHUXUELIANG != null">
                    SHUXUELIANG = #{SHUXUELIANG,jdbcType=VARCHAR},
                </if>
                <if test="YUSHENQBZ != null">
                    YUSHENQBZ = #{YUSHENQBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUYSYLZ != null">
                    SHOUSHUYSYLZ = #{SHOUSHUYSYLZ,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUYJSC != null">
                    SHOUSHUYJSC = #{SHOUSHUYJSC,jdbcType=VARCHAR},
                </if>
                <if test="LINCHUANGLJID != null">
                    LINCHUANGLJID = #{LINCHUANGLJID,jdbcType=VARCHAR},
                </if>
                <if test="WULIJJ != null">
                    WULIJJ = #{WULIJJ,jdbcType=VARCHAR},
                </if>
                <if test="WULIQS != null">
                    WULIQS = #{WULIQS,jdbcType=DECIMAL},
                </if>
                <if test="DIMIANJJ != null">
                    DIMIANJJ = #{DIMIANJJ,jdbcType=VARCHAR},
                </if>
                <if test="DIMIANQS != null">
                    DIMIANQS = #{DIMIANQS,jdbcType=DECIMAL},
                </if>
                <if test="LVWANGJJ != null">
                    LVWANGJJ = #{LVWANGJJ,jdbcType=VARCHAR},
                </if>
                <if test="LVWANGQS != null">
                    LVWANGQS = #{LVWANGQS,jdbcType=DECIMAL},
                </if>
                <if test="GANRANLX != null">
                    GANRANLX = #{GANRANLX,jdbcType=VARCHAR},
                </if>
                <if test="ZERENREN != null">
                    ZERENREN = #{ZERENREN,jdbcType=VARCHAR},
                </if>
                <if test="YUFANGYYSZZJBZ != null">
                    YUFANGYYSZZJBZ = #{YUFANGYYSZZJBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHIRUWU != null">
                    ZHIRUWU = #{ZHIRUWU,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUQJ != null">
                    SHOUSHUQJ = #{SHOUSHUQJ,jdbcType=DECIMAL},
                </if>
                <if test="BINGLISFWZ != null">
                    BINGLISFWZ = #{BINGLISFWZ,jdbcType=DECIMAL},
                </if>
                <if test="BINGLIBWZXX != null">
                    BINGLIBWZXX = #{BINGLIBWZXX,jdbcType=VARCHAR},
                </if>
                <if test="SHIFOUDLWLQX != null">
                    SHIFOUDLWLQX = #{SHIFOUDLWLQX,jdbcType=DECIMAL},
                </if>
                <if test="BINGRENYLZ != null">
                    BINGRENYLZ = #{BINGRENYLZ,jdbcType=VARCHAR},
                </if>
                <if test="YISHENGZID != null">
                    YISHENGZID = #{YISHENGZID,jdbcType=VARCHAR},
                </if>
                <if test="ZHIDAOLS != null">
                    ZHIDAOLS = #{ZHIDAOLS,jdbcType=VARCHAR},
                </if>
                <if test="SIXIEHS3 != null">
                    SIXIEHS3 = #{SIXIEHS3,jdbcType=VARCHAR},
                </if>
                <if test="JIESHENG1 != null">
                    JIESHENG1 = #{JIESHENG1,jdbcType=VARCHAR},
                </if>
                <if test="JIESHENG2 != null">
                    JIESHENG2 = #{JIESHENG2,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUSSQZ != null">
                    SHUHOUSSQZ = #{SHUHOUSSQZ,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUSSHZ != null">
                    SHUHOUSSHZ = #{SHUHOUSSHZ,jdbcType=VARCHAR},
                </if>
                <if test="XUSHENHBZ != null">
                    XUSHENHBZ = #{XUSHENHBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUHOUMCID1 != null">
                    SHUHOUMCID1 = #{SHUHOUMCID1,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMC1 != null">
                    SHUHOUMC1 = #{SHUHOUMC1,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMCID2 != null">
                    SHUHOUMCID2 = #{SHUHOUMCID2,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMC2 != null">
                    SHUHOUMC2 = #{SHUHOUMC2,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMCID3 != null">
                    SHUHOUMCID3 = #{SHUHOUMCID3,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMC3 != null">
                    SHUHOUMC3 = #{SHUHOUMC3,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMCID4 != null">
                    SHUHOUMCID4 = #{SHUHOUMCID4,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMC4 != null">
                    SHUHOUMC4 = #{SHUHOUMC4,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMCID5 != null">
                    SHUHOUMCID5 = #{SHUHOUMCID5,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUMC5 != null">
                    SHUHOUMC5 = #{SHUHOUMC5,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUJB1 != null">
                    SHUHOUJB1 = #{SHUHOUJB1,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUCKLX1 != null">
                    SHUHOUCKLX1 = #{SHUHOUCKLX1,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUJB2 != null">
                    SHUHOUJB2 = #{SHUHOUJB2,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUCKLX2 != null">
                    SHUHOUCKLX2 = #{SHUHOUCKLX2,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUJB3 != null">
                    SHUHOUJB3 = #{SHUHOUJB3,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUCKLX3 != null">
                    SHUHOUCKLX3 = #{SHUHOUCKLX3,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUJB4 != null">
                    SHUHOUJB4 = #{SHUHOUJB4,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUCKLX4 != null">
                    SHUHOUCKLX4 = #{SHUHOUCKLX4,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUJB5 != null">
                    SHUHOUJB5 = #{SHUHOUJB5,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUCKLX5 != null">
                    SHUHOUCKLX5 = #{SHUHOUCKLX5,jdbcType=VARCHAR},
                </if>
                <if test="SHUHOUSSBW != null">
                    SHUHOUSSBW = #{SHUHOUSSBW,jdbcType=VARCHAR},
                </if>
                <if test="QUSHOUSSJ != null">
                    QUSHOUSSJ = #{QUSHOUSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="QUSHOUSJJHS != null">
                    QUSHOUSJJHS = #{QUSHOUSJJHS,jdbcType=VARCHAR},
                </if>
                <if test="QUSHOUSJJHSXM != null">
                    QUSHOUSJJHSXM = #{QUSHOUSJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="HUIBINGFSJ != null">
                    HUIBINGFSJ = #{HUIBINGFSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="HUIBINGFJJHS != null">
                    HUIBINGFJJHS = #{HUIBINGFJJHS,jdbcType=VARCHAR},
                </if>
                <if test="HUIBINGFJJHSXM != null">
                    HUIBINGFJJHSXM = #{HUIBINGFJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIFS != null">
                    MAZUIFS = #{MAZUIFS,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUAPR != null">
                    SHOUSHUAPR = #{SHOUSHUAPR,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUAPSJ != null">
                    SHOUSHUAPSJ = #{SHOUSHUAPSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHUDJR != null">
                    SHOUSHUDJR = #{SHOUSHUDJR,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUDJSJ != null">
                    SHOUSHUDJSJ = #{SHOUSHUDJSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="MAZUIAPR != null">
                    MAZUIAPR = #{MAZUIAPR,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIAPSJ != null">
                    MAZUIAPSJ = #{MAZUIAPSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="MAZUIDJR != null">
                    MAZUIDJR = #{MAZUIDJR,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIDJSJ != null">
                    MAZUIDJSJ = #{MAZUIDJSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YUJISC != null">
                    YUJISC = #{YUJISC,jdbcType=DECIMAL},
                </if>
                <if test="SHENHEBTGYY != null">
                    SHENHEBTGYY = #{SHENHEBTGYY,jdbcType=VARCHAR},
                </if>
                <if test="JINGHUIFSSJ != null">
                    JINGHUIFSSJ = #{JINGHUIFSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JINGHUIFSJJHS != null">
                    JINGHUIFSJJHS = #{JINGHUIFSJJHS,jdbcType=VARCHAR},
                </if>
                <if test="JINGHUIFSJJHSXM != null">
                    JINGHUIFSJJHSXM = #{JINGHUIFSJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="JINGSHOUSJSJ != null">
                    JINGSHOUSJSJ = #{JINGSHOUSJSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JINGSHOUSJJJHS != null">
                    JINGSHOUSJJJHS = #{JINGSHOUSJJJHS,jdbcType=VARCHAR},
                </if>
                <if test="JINGSHOUSJJJHSXM != null">
                    JINGSHOUSJJJHSXM = #{JINGSHOUSJJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHULZZT != null">
                    SHOUSHULZZT = #{SHOUSHULZZT,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGZHISJ != null">
                    ZHONGZHISJ = #{ZHONGZHISJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHONGZHIJJHS != null">
                    ZHONGZHIJJHS = #{ZHONGZHIJJHS,jdbcType=VARCHAR},
                </if>
                <if test="ZHONGZHIJJHSXM != null">
                    ZHONGZHIJJHSXM = #{ZHONGZHIJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHOUSSSJ != null">
                    CHUSHOUSSSJ = #{CHUSHOUSSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUSHOUSSJJHS != null">
                    CHUSHOUSSJJHS = #{CHUSHOUSSJJHS,jdbcType=VARCHAR},
                </if>
                <if test="CHUSHOUSSJJHSXM != null">
                    CHUSHOUSSJJHSXM = #{CHUSHOUSSJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="CHUHUIFSSJ != null">
                    CHUHUIFSSJ = #{CHUHUIFSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUHUIFSJJHS != null">
                    CHUHUIFSJJHS = #{CHUHUIFSJJHS,jdbcType=VARCHAR},
                </if>
                <if test="CHUHUIFSJJHSXM != null">
                    CHUHUIFSJJHSXM = #{CHUHUIFSJJHSXM,jdbcType=VARCHAR},
                </if>
                <if test="FENGUANYZSHBZ != null">
                    FENGUANYZSHBZ = #{FENGUANYZSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="FENGUANYZ != null">
                    FENGUANYZ = #{FENGUANYZ,jdbcType=VARCHAR},
                </if>
                <if test="FENGUANYZSHSJ != null">
                    FENGUANYZSHSJ = #{FENGUANYZSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="FENGUANYZSHBTGYY != null">
                    FENGUANYZSHBTGYY = #{FENGUANYZSHBTGYY,jdbcType=VARCHAR},
                </if>
                <if test="KEZHURSHBZ != null">
                    KEZHURSHBZ = #{KEZHURSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="KEZHUREN != null">
                    KEZHUREN = #{KEZHUREN,jdbcType=VARCHAR},
                </if>
                <if test="KEZHURSHSJ != null">
                    KEZHURSHSJ = #{KEZHURSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="KEZHURSHBTGYY != null">
                    KEZHURSHBTGYY = #{KEZHURSHBTGYY,jdbcType=VARCHAR},
                </if>
                <if test="YIWUKSHBZ != null">
                    YIWUKSHBZ = #{YIWUKSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="YIWUKE != null">
                    YIWUKE = #{YIWUKE,jdbcType=VARCHAR},
                </if>
                <if test="YIWUKSHSJ != null">
                    YIWUKSHSJ = #{YIWUKSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="YIWUKSHBTGYY != null">
                    YIWUKSHBTGYY = #{YIWUKSHBTGYY,jdbcType=VARCHAR},
                </if>
                <if test="SHANGBAOSY != null">
                    SHANGBAOSY = #{SHANGBAOSY,jdbcType=VARCHAR},
                </if>
                <if test="SHANGBAOYS != null">
                    SHANGBAOYS = #{SHANGBAOYS,jdbcType=VARCHAR},
                </if>
                <if test="XUJIEJWT != null">
                    XUJIEJWT = #{XUJIEJWT,jdbcType=VARCHAR},
                </if>
                <if test="SHUQIANTLJG != null">
                    SHUQIANTLJG = #{SHUQIANTLJG,jdbcType=VARCHAR},
                </if>
                <if test="YICAIQCS != null">
                    YICAIQCS = #{YICAIQCS,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYSSHBZ != null">
                    ZHUZHIYSSHBZ = #{ZHUZHIYSSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUZHIYSSHR != null">
                    ZHUZHIYSSHR = #{ZHUZHIYSSHR,jdbcType=VARCHAR},
                </if>
                <if test="ZHUZHIYSSHSJ != null">
                    ZHUZHIYSSHSJ = #{ZHUZHIYSSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHUZHIYSSHBTGYY != null">
                    ZHUZHIYSSHBTGYY = #{ZHUZHIYSSHBTGYY,jdbcType=DECIMAL},
                </if>
                <if test="SHUQIANGRQK != null">
                    SHUQIANGRQK = #{SHUQIANGRQK,jdbcType=VARCHAR},
                </if>
                <if test="BINGFAZHENG != null">
                    BINGFAZHENG = #{BINGFAZHENG,jdbcType=VARCHAR},
                </if>
                <if test="DUOCHONGNYBZ != null">
                    DUOCHONGNYBZ = #{DUOCHONGNYBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUDAOYSSHBZ != null">
                    ZHUDAOYSSHBZ = #{ZHUDAOYSSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUDAOYSSHR != null">
                    ZHUDAOYSSHR = #{ZHUDAOYSSHR,jdbcType=VARCHAR},
                </if>
                <if test="ZHUDAOYSSHSJ != null">
                    ZHUDAOYSSHSJ = #{ZHUDAOYSSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="MAZUIYSSHBZ != null">
                    MAZUIYSSHBZ = #{MAZUIYSSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="MAZUIYSSHR != null">
                    MAZUIYSSHR = #{MAZUIYSSHR,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIYSSHSJ != null">
                    MAZUIYSSHSJ = #{MAZUIYSSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="MAZUIYF != null">
                    MAZUIYF = #{MAZUIYF,jdbcType=VARCHAR},
                </if>
                <if test="SHENQINGYS != null">
                    SHENQINGYS = #{SHENQINGYS,jdbcType=VARCHAR},
                </if>
                <if test="YISHENGSQRQ != null">
                    YISHENGSQRQ = #{YISHENGSQRQ,jdbcType=TIMESTAMP},
                </if>
                <if test="ZHENDUAN != null">
                    ZHENDUAN = #{ZHENDUAN,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIYSSHBZ != null">
                    SHANGJIYSSHBZ = #{SHANGJIYSSHBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHANGJIYSSHSJ != null">
                    SHANGJIYSSHSJ = #{SHANGJIYSSHSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHANGJIYSSHR != null">
                    SHANGJIYSSHR = #{SHANGJIYSSHR,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIYS != null">
                    SHANGJIYS = #{SHANGJIYS,jdbcType=VARCHAR},
                </if>
                <if test="SHANGJIYSSHBTGYY != null">
                    SHANGJIYSSHBTGYY = #{SHANGJIYSSHBTGYY,jdbcType=VARCHAR},
                </if>
                <if test="SHUQIANSSJB != null">
                    SHUQIANSSJB = #{SHUQIANSSJB,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIHZDID != null">
                    MAZUIHZDID = #{MAZUIHZDID,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUDM != null">
                    SHOUSHUDM = #{SHOUSHUDM,jdbcType=VARCHAR},
                </if>
                <if test="JINICUHS != null">
                    JINICUHS = #{JINICUHS,jdbcType=VARCHAR},
                </if>
                <if test="JINICUSJ != null">
                    JINICUSJ = #{JINICUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="JINICUHSXM != null">
                    JINICUHSXM = #{JINICUHSXM,jdbcType=VARCHAR},
                </if>
                <if test="CHUICUHS != null">
                    CHUICUHS = #{CHUICUHS,jdbcType=VARCHAR},
                </if>
                <if test="CHUICUSJ != null">
                    CHUICUSJ = #{CHUICUSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="CHUICUHSXM != null">
                    CHUICUHSXM = #{CHUICUHSXM,jdbcType=VARCHAR},
                </if>
                <if test="JUJUELX != null">
                    JUJUELX = #{JUJUELX,jdbcType=VARCHAR},
                </if>
                <if test="JUJUEYY != null">
                    JUJUEYY = #{JUJUEYY,jdbcType=VARCHAR},
                </if>
                <if test="TAICI != null">
                    TAICI = #{TAICI,jdbcType=VARCHAR},
                </if>
                <if test="BEIZHU1 != null">
                    BEIZHU1 = #{BEIZHU1,jdbcType=VARCHAR},
                </if>
                <if test="BEIZHU2 != null">
                    BEIZHU2 = #{BEIZHU2,jdbcType=VARCHAR},
                </if>
                <if test="ZHIQINGTYSZT != null">
                    ZHIQINGTYSZT = #{ZHIQINGTYSZT,jdbcType=DECIMAL},
                </if>
                <if test="WEIJIZHIBZ != null">
                    WEIJIZHIBZ = #{WEIJIZHIBZ,jdbcType=DECIMAL},
                </if>
                <if test="SHUHOUZHENTONGBZ != null">
                    SHUHOUZHENTONGBZ = #{SHUHOUZHENTONGBZ,jdbcType=DECIMAL},
                </if>
                <if test="ISKANGJUNYW != null">
                    ISKANGJUNYW = #{ISKANGJUNYW,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUSLX != null">
                    SHOUSHUSLX = #{SHOUSHUSLX,jdbcType=VARCHAR},
                </if>
                <if test="RIJIANSSBZ != null">
                    RIJIANSSBZ = #{RIJIANSSBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHENDUANDM != null">
                    ZHENDUANDM = #{ZHENDUANDM,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW2 != null">
                    SHOUSHUBW2 = #{SHOUSHUBW2,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW3 != null">
                    SHOUSHUBW3 = #{SHOUSHUBW3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW4 != null">
                    SHOUSHUBW4 = #{SHOUSHUBW4,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW5 != null">
                    SHOUSHUBW5 = #{SHOUSHUBW5,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW1 != null">
                    SHOUSHUBW1 = #{SHOUSHUBW1,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIJSBZ != null">
                    MAZUIJSBZ = #{MAZUIJSBZ,jdbcType=DECIMAL},
                </if>
                <if test="MAZUIJSREN != null">
                    MAZUIJSREN = #{MAZUIJSREN,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIJSSJ != null">
                    MAZUIJSSJ = #{MAZUIJSSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHUJZJSREN != null">
                    SHOUSHUJZJSREN = #{SHOUSHUJZJSREN,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIJZJSREN != null">
                    MAZUIJZJSREN = #{MAZUIJZJSREN,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJZSJ != null">
                    SHOUSHUJZSJ = #{SHOUSHUJZSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="QUXIAOYY != null">
                    QUXIAOYY = #{QUXIAOYY,jdbcType=VARCHAR},
                </if>
                <if test="YUANYINLX != null">
                    YUANYINLX = #{YUANYINLX,jdbcType=VARCHAR},
                </if>
                <if test="QUXIAOR != null">
                    QUXIAOR = #{QUXIAOR,jdbcType=VARCHAR},
                </if>
                <if test="QUXIAOSJ != null">
                    QUXIAOSJ = #{QUXIAOSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="SHOUSHUKSREN != null">
                    SHOUSHUKSREN = #{SHOUSHUKSREN,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJSREN != null">
                    SHOUSHUJSREN = #{SHOUSHUJSREN,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJZBZ != null">
                    SHOUSHUJZBZ = #{SHOUSHUJZBZ,jdbcType=DECIMAL},
                </if>
                <if test="MAZUIJZBZ != null">
                    MAZUIJZBZ = #{MAZUIJZBZ,jdbcType=DECIMAL},
                </if>
                <if test="MAZUIJZSJ != null">
                    MAZUIJZSJ = #{MAZUIJZSJ,jdbcType=TIMESTAMP},
                </if>
                <if test="WAIYUANBZ != null">
                    WAIYUANBZ = #{WAIYUANBZ,jdbcType=DECIMAL},
                </if>
                <if test="ZHUANYUNFS != null">
                    ZHUANYUNFS = #{ZHUANYUNFS,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUXZ != null">
                    SHOUSHUXZ = #{SHOUSHUXZ,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUXZQT != null">
                    SHOUSHUXZQT = #{SHOUSHUXZQT,jdbcType=VARCHAR},
                </if>
                <if test="FEIJIHUAZCSS != null">
                    FEIJIHUAZCSS = #{FEIJIHUAZCSS,jdbcType=VARCHAR},
                </if>
                <if test="TIWEI != null">
                    TIWEI = #{TIWEI,jdbcType=VARCHAR},
                </if>
                <if test="ZHIDAOYS != null">
                    ZHIDAOYS = #{ZHIDAOYS,jdbcType=VARCHAR},
                </if>
                <if test="XUYAOSZBD != null">
                    XUYAOSZBD = #{XUYAOSZBD,jdbcType=DECIMAL},
                </if>
                <if test="XUYAOBBCJ != null">
                    XUYAOBBCJ = #{XUYAOBBCJ,jdbcType=DECIMAL},
                </if>
                <if test="SHENQINGSHZHBF != null">
                    SHENQINGSHZHBF = #{SHENQINGSHZHBF,jdbcType=DECIMAL},
                </if>
                <if test="SHENQINGZICU != null">
                    SHENQINGZICU = #{SHENQINGZICU,jdbcType=DECIMAL},
                </if>
                <if test="SHUHOUZT != null">
                    SHUHOUZT = #{SHUHOUZT,jdbcType=DECIMAL},
                </if>
                <if test="GENTAI != null">
                    GENTAI = #{GENTAI,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUBZ != null">
                    SHOUSHUBZ = #{SHOUSHUBZ,jdbcType=VARCHAR},
                </if>
                <if test="SONGFUSS != null">
                    SONGFUSS = #{SONGFUSS,jdbcType=DECIMAL},
                </if>
                <if test="TESHUJC != null">
                    TESHUJC = #{TESHUJC,jdbcType=VARCHAR},
                </if>
                <if test="MAZUISSS != null">
                    MAZUISSS = #{MAZUISSS,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIPG != null">
                    MAZUIPG = #{MAZUIPG,jdbcType=VARCHAR},
                </if>
                <if test="MAZUIPGNR != null">
                    MAZUIPGNR = #{MAZUIPGNR,jdbcType=VARCHAR},
                </if>
                <if test="WEICHUANGSSBZ != null">
                    WEICHUANGSSBZ = #{WEICHUANGSSBZ,jdbcType=DECIMAL},
                </if>
                <if test="MAZUIPGBLJLXH != null">
                    MAZUIPGBLJLXH = #{MAZUIPGBLJLXH,jdbcType=DECIMAL},
                </if>
                <if test="SHOUSHUBW6 != null">
                    SHOUSHUBW6 = #{SHOUSHUBW6,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC6 != null">
                    SHOUSHUMC6 = #{SHOUSHUMC6,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID6 != null">
                    SHOUSHUID6 = #{SHOUSHUID6,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW7 != null">
                    SHOUSHUBW7 = #{SHOUSHUBW7,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC7 != null">
                    SHOUSHUMC7 = #{SHOUSHUMC7,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID7 != null">
                    SHOUSHUID7 = #{SHOUSHUID7,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW8 != null">
                    SHOUSHUBW8 = #{SHOUSHUBW8,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC8 != null">
                    SHOUSHUMC8 = #{SHOUSHUMC8,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID8 != null">
                    SHOUSHUID8 = #{SHOUSHUID8,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW9 != null">
                    SHOUSHUBW9 = #{SHOUSHUBW9,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC9 != null">
                    SHOUSHUMC9 = #{SHOUSHUMC9,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID9 != null">
                    SHOUSHUID9 = #{SHOUSHUID9,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW10 != null">
                    SHOUSHUBW10 = #{SHOUSHUBW10,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC10 != null">
                    SHOUSHUMC10 = #{SHOUSHUMC10,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID10 != null">
                    SHOUSHUID10 = #{SHOUSHUID10,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW11 != null">
                    SHOUSHUBW11 = #{SHOUSHUBW11,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC11 != null">
                    SHOUSHUMC11 = #{SHOUSHUMC11,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID11 != null">
                    SHOUSHUID11 = #{SHOUSHUID11,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW12 != null">
                    SHOUSHUBW12 = #{SHOUSHUBW12,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC12 != null">
                    SHOUSHUMC12 = #{SHOUSHUMC12,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID12 != null">
                    SHOUSHUID12 = #{SHOUSHUID12,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW13 != null">
                    SHOUSHUBW13 = #{SHOUSHUBW13,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC13 != null">
                    SHOUSHUMC13 = #{SHOUSHUMC13,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID13 != null">
                    SHOUSHUID13 = #{SHOUSHUID13,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW14 != null">
                    SHOUSHUBW14 = #{SHOUSHUBW14,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC14 != null">
                    SHOUSHUMC14 = #{SHOUSHUMC14,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID14 != null">
                    SHOUSHUID14 = #{SHOUSHUID14,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW15 != null">
                    SHOUSHUBW15 = #{SHOUSHUBW15,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC15 != null">
                    SHOUSHUMC15 = #{SHOUSHUMC15,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID15 != null">
                    SHOUSHUID15 = #{SHOUSHUID15,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW16 != null">
                    SHOUSHUBW16 = #{SHOUSHUBW16,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC16 != null">
                    SHOUSHUMC16 = #{SHOUSHUMC16,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID16 != null">
                    SHOUSHUID16 = #{SHOUSHUID16,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW17 != null">
                    SHOUSHUBW17 = #{SHOUSHUBW17,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC17 != null">
                    SHOUSHUMC17 = #{SHOUSHUMC17,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID17 != null">
                    SHOUSHUID17 = #{SHOUSHUID17,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW18 != null">
                    SHOUSHUBW18 = #{SHOUSHUBW18,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC18 != null">
                    SHOUSHUMC18 = #{SHOUSHUMC18,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID18 != null">
                    SHOUSHUID18 = #{SHOUSHUID18,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW19 != null">
                    SHOUSHUBW19 = #{SHOUSHUBW19,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC19 != null">
                    SHOUSHUMC19 = #{SHOUSHUMC19,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID19 != null">
                    SHOUSHUID19 = #{SHOUSHUID19,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUBW20 != null">
                    SHOUSHUBW20 = #{SHOUSHUBW20,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUMC20 != null">
                    SHOUSHUMC20 = #{SHOUSHUMC20,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUID20 != null">
                    SHOUSHUID20 = #{SHOUSHUID20,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB6 != null">
                    SHOUSHUJB6 = #{SHOUSHUJB6,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX6 != null">
                    SHOUSHUCKLX6 = #{SHOUSHUCKLX6,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB7 != null">
                    SHOUSHUJB7 = #{SHOUSHUJB7,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX7 != null">
                    SHOUSHUCKLX7 = #{SHOUSHUCKLX7,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB8 != null">
                    SHOUSHUJB8 = #{SHOUSHUJB8,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX8 != null">
                    SHOUSHUCKLX8 = #{SHOUSHUCKLX8,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB9 != null">
                    SHOUSHUJB9 = #{SHOUSHUJB9,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX9 != null">
                    SHOUSHUCKLX9 = #{SHOUSHUCKLX9,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB10 != null">
                    SHOUSHUJB10 = #{SHOUSHUJB10,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX10 != null">
                    SHOUSHUCKLX10 = #{SHOUSHUCKLX10,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB11 != null">
                    SHOUSHUJB11 = #{SHOUSHUJB11,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX11 != null">
                    SHOUSHUCKLX11 = #{SHOUSHUCKLX11,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB12 != null">
                    SHOUSHUJB12 = #{SHOUSHUJB12,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX12 != null">
                    SHOUSHUCKLX12 = #{SHOUSHUCKLX12,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB13 != null">
                    SHOUSHUJB13 = #{SHOUSHUJB13,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX13 != null">
                    SHOUSHUCKLX13 = #{SHOUSHUCKLX13,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB14 != null">
                    SHOUSHUJB14 = #{SHOUSHUJB14,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX14 != null">
                    SHOUSHUCKLX14 = #{SHOUSHUCKLX14,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB15 != null">
                    SHOUSHUJB15 = #{SHOUSHUJB15,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX15 != null">
                    SHOUSHUCKLX15 = #{SHOUSHUCKLX15,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB16 != null">
                    SHOUSHUJB16 = #{SHOUSHUJB16,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX16 != null">
                    SHOUSHUCKLX16 = #{SHOUSHUCKLX16,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB17 != null">
                    SHOUSHUJB17 = #{SHOUSHUJB17,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX17 != null">
                    SHOUSHUCKLX17 = #{SHOUSHUCKLX17,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB18 != null">
                    SHOUSHUJB18 = #{SHOUSHUJB18,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX18 != null">
                    SHOUSHUCKLX18 = #{SHOUSHUCKLX18,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB19 != null">
                    SHOUSHUJB19 = #{SHOUSHUJB19,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX19 != null">
                    SHOUSHUCKLX19 = #{SHOUSHUCKLX19,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUJB20 != null">
                    SHOUSHUJB20 = #{SHOUSHUJB20,jdbcType=VARCHAR},
                </if>
                <if test="SHOUSHUCKLX20 != null">
                    SHOUSHUCKLX20 = #{SHOUSHUCKLX20,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU != null">
                    SHOUHU = #{SHOUHU,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU1 != null">
                    SHOUHU1 = #{SHOUHU1,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU2 != null">
                    SHOUHU2 = #{SHOUHU2,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU3 != null">
                    SHOUHU3 = #{SHOUHU3,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU4 != null">
                    SHOUHU4 = #{SHOUHU4,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU5 != null">
                    SHOUHU5 = #{SHOUHU5,jdbcType=VARCHAR},
                </if>
                <if test="SHOUHU6 != null">
                    SHOUHU6 = #{SHOUHU6,jdbcType=VARCHAR},
                </if>
        </set>
        where   SHOUSHUDID = #{SHOUSHUDID,jdbcType=VARCHAR} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.javazx.batch.po.SmShoushuxx">
        update SM_SHOUSHUXX
        set 
            SHOUSHUDH =  #{SHOUSHUDH,jdbcType=VARCHAR},
            SHENQINGDID =  #{SHENQINGDID,jdbcType=VARCHAR},
            YINGYONGID =  #{YINGYONGID,jdbcType=VARCHAR},
            YUANQUID =  #{YUANQUID,jdbcType=VARCHAR},
            BINGRENID =  #{BINGRENID,jdbcType=VARCHAR},
            ZHUYUANHAO =  #{ZHUYUANHAO,jdbcType=VARCHAR},
            BINGRENXM =  #{BINGRENXM,jdbcType=VARCHAR},
            XINGBIE =  #{XINGBIE,jdbcType=VARCHAR},
            NIANLING =  #{NIANLING,jdbcType=DECIMAL},
            BINGRENKS =  #{BINGRENKS,jdbcType=VARCHAR},
            BINGRENBQ =  #{BINGRENBQ,jdbcType=VARCHAR},
            BINGRENCW =  #{BINGRENCW,jdbcType=VARCHAR},
            FEIYONGXZ =  #{FEIYONGXZ,jdbcType=VARCHAR},
            FEIYONGLB =  #{FEIYONGLB,jdbcType=VARCHAR},
            BINGRENTZ =  #{BINGRENTZ,jdbcType=DECIMAL},
            SHENQINGKS =  #{SHENQINGKS,jdbcType=VARCHAR},
            ZHIXINGKS =  #{ZHIXINGKS,jdbcType=VARCHAR},
            SHENQINGSJ =  #{SHENQINGSJ,jdbcType=TIMESTAMP},
            YAOQIUSJ =  #{YAOQIUSJ,jdbcType=TIMESTAMP},
            ANPAISJ =  #{ANPAISJ,jdbcType=TIMESTAMP},
            JINXINGSJ =  #{JINXINGSJ,jdbcType=TIMESTAMP},
            JIESHUSJ =  #{JIESHUSJ,jdbcType=TIMESTAMP},
            SHOUSHULB =  #{SHOUSHULB,jdbcType=VARCHAR},
            SHOUSHUMCID =  #{SHOUSHUMCID,jdbcType=VARCHAR},
            SHOUSHUMC =  #{SHOUSHUMC,jdbcType=VARCHAR},
            SHOUSHUMCID1 =  #{SHOUSHUMCID1,jdbcType=VARCHAR},
            SHOUSHUMC1 =  #{SHOUSHUMC1,jdbcType=VARCHAR},
            SHOUSHUMCID2 =  #{SHOUSHUMCID2,jdbcType=VARCHAR},
            SHOUSHUMC2 =  #{SHOUSHUMC2,jdbcType=VARCHAR},
            SHOUSHUMCID3 =  #{SHOUSHUMCID3,jdbcType=VARCHAR},
            SHOUSHUMC3 =  #{SHOUSHUMC3,jdbcType=VARCHAR},
            SHOUSHUYS =  #{SHOUSHUYS,jdbcType=VARCHAR},
            ZHULIYS1 =  #{ZHULIYS1,jdbcType=VARCHAR},
            ZHULIYS2 =  #{ZHULIYS2,jdbcType=VARCHAR},
            ZHULIYS3 =  #{ZHULIYS3,jdbcType=VARCHAR},
            XISHOUHS =  #{XISHOUHS,jdbcType=VARCHAR},
            SIXIEHS1 =  #{SIXIEHS1,jdbcType=VARCHAR},
            SIXIEHS2 =  #{SIXIEHS2,jdbcType=VARCHAR},
            XUNHUIHS1 =  #{XUNHUIHS1,jdbcType=VARCHAR},
            XUNHUIHS2 =  #{XUNHUIHS2,jdbcType=VARCHAR},
            XUNHUIHS3 =  #{XUNHUIHS3,jdbcType=VARCHAR},
            MAZUIID =  #{MAZUIID,jdbcType=VARCHAR},
            MAZUIFF =  #{MAZUIFF,jdbcType=VARCHAR},
            MAZUIYS =  #{MAZUIYS,jdbcType=VARCHAR},
            MAZUIYS1 =  #{MAZUIYS1,jdbcType=VARCHAR},
            MAZUIYS2 =  #{MAZUIYS2,jdbcType=VARCHAR},
            MAZUIYS3 =  #{MAZUIYS3,jdbcType=VARCHAR},
            SHOUSHUTID =  #{SHOUSHUTID,jdbcType=VARCHAR},
            JIETAIHAO =  #{JIETAIHAO,jdbcType=VARCHAR},
            SHOUCISSBZ =  #{SHOUCISSBZ,jdbcType=DECIMAL},
            JIZHENBZ =  #{JIZHENBZ,jdbcType=DECIMAL},
            ZHENTONGBSYBZ =  #{ZHENTONGBSYBZ,jdbcType=DECIMAL},
            SHOUSHUCKLX =  #{SHOUSHUCKLX,jdbcType=VARCHAR},
            CHUANGKOUYHQK =  #{CHUANGKOUYHQK,jdbcType=VARCHAR},
            CHUANGKOUGRQK =  #{CHUANGKOUGRQK,jdbcType=VARCHAR},
            SHUQIANXX =  #{SHUQIANXX,jdbcType=VARCHAR},
            SHENQINGREN =  #{SHENQINGREN,jdbcType=VARCHAR},
            CAOZUOYUAN =  #{CAOZUOYUAN,jdbcType=VARCHAR},
            ZHENDUANDM1 =  #{ZHENDUANDM1,jdbcType=VARCHAR},
            ZHENDUANMC1 =  #{ZHENDUANMC1,jdbcType=VARCHAR},
            ZHENDUANDM2 =  #{ZHENDUANDM2,jdbcType=VARCHAR},
            ZHENDUANMC2 =  #{ZHENDUANMC2,jdbcType=VARCHAR},
            ZHENDUANDM3 =  #{ZHENDUANDM3,jdbcType=VARCHAR},
            ZHENDUANMC3 =  #{ZHENDUANMC3,jdbcType=VARCHAR},
            ZHENDUANDM4 =  #{ZHENDUANDM4,jdbcType=VARCHAR},
            ZHENDUANMC4 =  #{ZHENDUANMC4,jdbcType=VARCHAR},
            ZUOFEIBZ =  #{ZUOFEIBZ,jdbcType=DECIMAL},
            ZHUANGTAIBZ =  #{ZHUANGTAIBZ,jdbcType=DECIMAL},
            DUIHUAXX =  #{DUIHUAXX,jdbcType=VARCHAR},
            BEIZHU =  #{BEIZHU,jdbcType=VARCHAR},
            MENZHENZYBZ =  #{MENZHENZYBZ,jdbcType=DECIMAL},
            XIUGAIREN =  #{XIUGAIREN,jdbcType=VARCHAR},
            XIUGAISJ =  #{XIUGAISJ,jdbcType=TIMESTAMP},
            NIANLINGDW =  #{NIANLINGDW,jdbcType=VARCHAR},
            BINGRENZYID =  #{BINGRENZYID,jdbcType=VARCHAR},
            YINGERBZ =  #{YINGERBZ,jdbcType=DECIMAL},
            YINGERID =  #{YINGERID,jdbcType=VARCHAR},
            XISHOUHSBZ =  #{XISHOUHSBZ,jdbcType=DECIMAL},
            GELIBZ =  #{GELIBZ,jdbcType=DECIMAL},
            YAOQIUST =  #{YAOQIUST,jdbcType=DECIMAL},
            SHOUSHUJB =  #{SHOUSHUJB,jdbcType=VARCHAR},
            SHENGAO =  #{SHENGAO,jdbcType=DECIMAL},
            ZAOYING =  #{ZAOYING,jdbcType=VARCHAR},
            TESHUSSBZ =  #{TESHUSSBZ,jdbcType=DECIMAL},
            KAIDANYYID =  #{KAIDANYYID,jdbcType=VARCHAR},
            JIANCHAXMID =  #{JIANCHAXMID,jdbcType=VARCHAR},
            JIANCHAJKID1 =  #{JIANCHAJKID1,jdbcType=VARCHAR},
            TIWAIXHS =  #{TIWAIXHS,jdbcType=VARCHAR},
            MAZUIAPZT =  #{MAZUIAPZT,jdbcType=DECIMAL},
            MAZUIFF1 =  #{MAZUIFF1,jdbcType=VARCHAR},
            FUHEMZFFMC =  #{FUHEMZFFMC,jdbcType=VARCHAR},
            SHOUSHUBW =  #{SHOUSHUBW,jdbcType=VARCHAR},
            SHOUSHUMCID4 =  #{SHOUSHUMCID4,jdbcType=VARCHAR},
            SHOUSHUMC4 =  #{SHOUSHUMC4,jdbcType=VARCHAR},
            SHOUSHUMCID5 =  #{SHOUSHUMCID5,jdbcType=VARCHAR},
            SHOUSHUMC5 =  #{SHOUSHUMC5,jdbcType=VARCHAR},
            JIUZHENID =  #{JIUZHENID,jdbcType=VARCHAR},
            SHUHOUSSMCID =  #{SHUHOUSSMCID,jdbcType=VARCHAR},
            SHUHOUSSMC =  #{SHUHOUSSMC,jdbcType=VARCHAR},
            RUSHISJ =  #{RUSHISJ,jdbcType=TIMESTAMP},
            CHUSHISJ =  #{CHUSHISJ,jdbcType=TIMESTAMP},
            SHOUSHUKS =  #{SHOUSHUKS,jdbcType=VARCHAR},
            MAZUISJ =  #{MAZUISJ,jdbcType=TIMESTAMP},
            FEIJIHCFSSSBZ =  #{FEIJIHCFSSSBZ,jdbcType=DECIMAL},
            SHUQIANTL =  #{SHUQIANTL,jdbcType=DECIMAL},
            YIWUCBABZ =  #{YIWUCBABZ,jdbcType=DECIMAL},
            YIGANGLDB =  #{YIGANGLDB,jdbcType=DECIMAL},
            MEIDUKT =  #{MEIDUKT,jdbcType=DECIMAL},
            AIZIBKT =  #{AIZIBKT,jdbcType=DECIMAL},
            BINGGANKT =  #{BINGGANKT,jdbcType=DECIMAL},
            QITATSGR =  #{QITATSGR,jdbcType=VARCHAR},
            BUCHUANSSS =  #{BUCHUANSSS,jdbcType=DECIMAL},
            PAICHIBZ =  #{PAICHIBZ,jdbcType=DECIMAL},
            SHENHEBZ =  #{SHENHEBZ,jdbcType=DECIMAL},
            SHENHESJ =  #{SHENHESJ,jdbcType=TIMESTAMP},
            SHENHEREN =  #{SHENHEREN,jdbcType=VARCHAR},
            BINGDONGBLBZ =  #{BINGDONGBLBZ,jdbcType=DECIMAL},
            GELICS =  #{GELICS,jdbcType=VARCHAR},
            YUFANGYYSJ =  #{YUFANGYYSJ,jdbcType=TIMESTAMP},
            SHOUSHUFXJB =  #{SHOUSHUFXJB,jdbcType=VARCHAR},
            MAZUIFJ =  #{MAZUIFJ,jdbcType=VARCHAR},
            ZHONGDASSBZ =  #{ZHONGDASSBZ,jdbcType=DECIMAL},
            CHUANGKOUXGLY =  #{CHUANGKOUXGLY,jdbcType=VARCHAR},
            SHOUSHUYJWCSJ =  #{SHOUSHUYJWCSJ,jdbcType=TIMESTAMP},
            SHUXUEQSXBGBZ =  #{SHUXUEQSXBGBZ,jdbcType=DECIMAL},
            SHUXUEQSXJG =  #{SHUXUEQSXJG,jdbcType=VARCHAR},
            SHENGYUSHI =  #{SHENGYUSHI,jdbcType=VARCHAR},
            HUNYIN =  #{HUNYIN,jdbcType=VARCHAR},
            SHUQIANJCSFQQ =  #{SHUQIANJCSFQQ,jdbcType=DECIMAL},
            SHIFOUSBLHY =  #{SHIFOUSBLHY,jdbcType=DECIMAL},
            BIAOBEN =  #{BIAOBEN,jdbcType=VARCHAR},
            BIAOBENJS =  #{BIAOBENJS,jdbcType=DECIMAL},
            SHUZHONGYY =  #{SHUZHONGYY,jdbcType=VARCHAR},
            LIUCHANYY =  #{LIUCHANYY,jdbcType=VARCHAR},
            RONGMAOZT =  #{RONGMAOZT,jdbcType=DECIMAL},
            SHUQIANGQ =  #{SHUQIANGQ,jdbcType=DECIMAL},
            SHOUSHUZQK =  #{SHOUSHUZQK,jdbcType=VARCHAR},
            JIEYUHMC =  #{JIEYUHMC,jdbcType=VARCHAR},
            SHUHOUGQ =  #{SHUHOUGQ,jdbcType=DECIMAL},
            CHUXUEQK =  #{CHUXUEQK,jdbcType=VARCHAR},
            KUAKESSBZ =  #{KUAKESSBZ,jdbcType=DECIMAL},
            GUANLIANSSDID =  #{GUANLIANSSDID,jdbcType=VARCHAR},
            SHOUSHUJB1 =  #{SHOUSHUJB1,jdbcType=VARCHAR},
            SHOUSHUCKLX1 =  #{SHOUSHUCKLX1,jdbcType=VARCHAR},
            SHOUSHUJB2 =  #{SHOUSHUJB2,jdbcType=VARCHAR},
            SHOUSHUCKLX2 =  #{SHOUSHUCKLX2,jdbcType=VARCHAR},
            SHOUSHUJB3 =  #{SHOUSHUJB3,jdbcType=VARCHAR},
            SHOUSHUCKLX3 =  #{SHOUSHUCKLX3,jdbcType=VARCHAR},
            SHOUSHUJB4 =  #{SHOUSHUJB4,jdbcType=VARCHAR},
            SHOUSHUCKLX4 =  #{SHOUSHUCKLX4,jdbcType=VARCHAR},
            SHOUSHUJB5 =  #{SHOUSHUJB5,jdbcType=VARCHAR},
            SHOUSHUCKLX5 =  #{SHOUSHUCKLX5,jdbcType=VARCHAR},
            NEIJINGSSBZ =  #{NEIJINGSSBZ,jdbcType=DECIMAL},
            NEIJINGSS =  #{NEIJINGSS,jdbcType=VARCHAR},
            SHUHOUSSJB =  #{SHUHOUSSJB,jdbcType=VARCHAR},
            SHUHOUCKLX =  #{SHUHOUCKLX,jdbcType=VARCHAR},
            SHUZI1 =  #{SHUZI1,jdbcType=DECIMAL},
            SHUZI2 =  #{SHUZI2,jdbcType=DECIMAL},
            SHUZI3 =  #{SHUZI3,jdbcType=DECIMAL},
            SHUZI4 =  #{SHUZI4,jdbcType=DECIMAL},
            SHUZI5 =  #{SHUZI5,jdbcType=DECIMAL},
            SHUZI6 =  #{SHUZI6,jdbcType=DECIMAL},
            SHOUSHUJSBZ =  #{SHOUSHUJSBZ,jdbcType=DECIMAL},
            SUXINGS =  #{SUXINGS,jdbcType=VARCHAR},
            SHUZHONGYYSJ =  #{SHUZHONGYYSJ,jdbcType=TIMESTAMP},
            LINCHUANGLJDRBZ =  #{LINCHUANGLJDRBZ,jdbcType=DECIMAL},
            GELICSMC =  #{GELICSMC,jdbcType=VARCHAR},
            DIANZIBLSY1 =  #{DIANZIBLSY1,jdbcType=VARCHAR},
            DIANZIBLSY2 =  #{DIANZIBLSY2,jdbcType=VARCHAR},
            DIANZIBLSY3 =  #{DIANZIBLSY3,jdbcType=VARCHAR},
            DIANZIBLSY4 =  #{DIANZIBLSY4,jdbcType=VARCHAR},
            DIANZIBLSY5 =  #{DIANZIBLSY5,jdbcType=VARCHAR},
            DIANZIBLSY6 =  #{DIANZIBLSY6,jdbcType=VARCHAR},
            DIANZIBLSY7 =  #{DIANZIBLSY7,jdbcType=VARCHAR},
            DIANZIBLSY8 =  #{DIANZIBLSY8,jdbcType=VARCHAR},
            DIANZIBLSY9 =  #{DIANZIBLSY9,jdbcType=VARCHAR},
            DIANZIBLSY10 =  #{DIANZIBLSY10,jdbcType=VARCHAR},
            DIANZIBLSY11 =  #{DIANZIBLSY11,jdbcType=VARCHAR},
            DIANZIBLSY12 =  #{DIANZIBLSY12,jdbcType=VARCHAR},
            SHIXUELIANG =  #{SHIXUELIANG,jdbcType=VARCHAR},
            SHUXUELIANG =  #{SHUXUELIANG,jdbcType=VARCHAR},
            YUSHENQBZ =  #{YUSHENQBZ,jdbcType=DECIMAL},
            SHOUSHUYSYLZ =  #{SHOUSHUYSYLZ,jdbcType=VARCHAR},
            SHOUSHUYJSC =  #{SHOUSHUYJSC,jdbcType=VARCHAR},
            LINCHUANGLJID =  #{LINCHUANGLJID,jdbcType=VARCHAR},
            WULIJJ =  #{WULIJJ,jdbcType=VARCHAR},
            WULIQS =  #{WULIQS,jdbcType=DECIMAL},
            DIMIANJJ =  #{DIMIANJJ,jdbcType=VARCHAR},
            DIMIANQS =  #{DIMIANQS,jdbcType=DECIMAL},
            LVWANGJJ =  #{LVWANGJJ,jdbcType=VARCHAR},
            LVWANGQS =  #{LVWANGQS,jdbcType=DECIMAL},
            GANRANLX =  #{GANRANLX,jdbcType=VARCHAR},
            ZERENREN =  #{ZERENREN,jdbcType=VARCHAR},
            YUFANGYYSZZJBZ =  #{YUFANGYYSZZJBZ,jdbcType=DECIMAL},
            ZHIRUWU =  #{ZHIRUWU,jdbcType=DECIMAL},
            SHOUSHUQJ =  #{SHOUSHUQJ,jdbcType=DECIMAL},
            BINGLISFWZ =  #{BINGLISFWZ,jdbcType=DECIMAL},
            BINGLIBWZXX =  #{BINGLIBWZXX,jdbcType=VARCHAR},
            SHIFOUDLWLQX =  #{SHIFOUDLWLQX,jdbcType=DECIMAL},
            BINGRENYLZ =  #{BINGRENYLZ,jdbcType=VARCHAR},
            YISHENGZID =  #{YISHENGZID,jdbcType=VARCHAR},
            ZHIDAOLS =  #{ZHIDAOLS,jdbcType=VARCHAR},
            SIXIEHS3 =  #{SIXIEHS3,jdbcType=VARCHAR},
            JIESHENG1 =  #{JIESHENG1,jdbcType=VARCHAR},
            JIESHENG2 =  #{JIESHENG2,jdbcType=VARCHAR},
            SHUHOUSSQZ =  #{SHUHOUSSQZ,jdbcType=VARCHAR},
            SHUHOUSSHZ =  #{SHUHOUSSHZ,jdbcType=VARCHAR},
            XUSHENHBZ =  #{XUSHENHBZ,jdbcType=DECIMAL},
            SHUHOUMCID1 =  #{SHUHOUMCID1,jdbcType=VARCHAR},
            SHUHOUMC1 =  #{SHUHOUMC1,jdbcType=VARCHAR},
            SHUHOUMCID2 =  #{SHUHOUMCID2,jdbcType=VARCHAR},
            SHUHOUMC2 =  #{SHUHOUMC2,jdbcType=VARCHAR},
            SHUHOUMCID3 =  #{SHUHOUMCID3,jdbcType=VARCHAR},
            SHUHOUMC3 =  #{SHUHOUMC3,jdbcType=VARCHAR},
            SHUHOUMCID4 =  #{SHUHOUMCID4,jdbcType=VARCHAR},
            SHUHOUMC4 =  #{SHUHOUMC4,jdbcType=VARCHAR},
            SHUHOUMCID5 =  #{SHUHOUMCID5,jdbcType=VARCHAR},
            SHUHOUMC5 =  #{SHUHOUMC5,jdbcType=VARCHAR},
            SHUHOUJB1 =  #{SHUHOUJB1,jdbcType=VARCHAR},
            SHUHOUCKLX1 =  #{SHUHOUCKLX1,jdbcType=VARCHAR},
            SHUHOUJB2 =  #{SHUHOUJB2,jdbcType=VARCHAR},
            SHUHOUCKLX2 =  #{SHUHOUCKLX2,jdbcType=VARCHAR},
            SHUHOUJB3 =  #{SHUHOUJB3,jdbcType=VARCHAR},
            SHUHOUCKLX3 =  #{SHUHOUCKLX3,jdbcType=VARCHAR},
            SHUHOUJB4 =  #{SHUHOUJB4,jdbcType=VARCHAR},
            SHUHOUCKLX4 =  #{SHUHOUCKLX4,jdbcType=VARCHAR},
            SHUHOUJB5 =  #{SHUHOUJB5,jdbcType=VARCHAR},
            SHUHOUCKLX5 =  #{SHUHOUCKLX5,jdbcType=VARCHAR},
            SHUHOUSSBW =  #{SHUHOUSSBW,jdbcType=VARCHAR},
            QUSHOUSSJ =  #{QUSHOUSSJ,jdbcType=TIMESTAMP},
            QUSHOUSJJHS =  #{QUSHOUSJJHS,jdbcType=VARCHAR},
            QUSHOUSJJHSXM =  #{QUSHOUSJJHSXM,jdbcType=VARCHAR},
            HUIBINGFSJ =  #{HUIBINGFSJ,jdbcType=TIMESTAMP},
            HUIBINGFJJHS =  #{HUIBINGFJJHS,jdbcType=VARCHAR},
            HUIBINGFJJHSXM =  #{HUIBINGFJJHSXM,jdbcType=VARCHAR},
            MAZUIFS =  #{MAZUIFS,jdbcType=DECIMAL},
            SHOUSHUAPR =  #{SHOUSHUAPR,jdbcType=VARCHAR},
            SHOUSHUAPSJ =  #{SHOUSHUAPSJ,jdbcType=TIMESTAMP},
            SHOUSHUDJR =  #{SHOUSHUDJR,jdbcType=VARCHAR},
            SHOUSHUDJSJ =  #{SHOUSHUDJSJ,jdbcType=TIMESTAMP},
            MAZUIAPR =  #{MAZUIAPR,jdbcType=VARCHAR},
            MAZUIAPSJ =  #{MAZUIAPSJ,jdbcType=TIMESTAMP},
            MAZUIDJR =  #{MAZUIDJR,jdbcType=VARCHAR},
            MAZUIDJSJ =  #{MAZUIDJSJ,jdbcType=TIMESTAMP},
            YUJISC =  #{YUJISC,jdbcType=DECIMAL},
            SHENHEBTGYY =  #{SHENHEBTGYY,jdbcType=VARCHAR},
            JINGHUIFSSJ =  #{JINGHUIFSSJ,jdbcType=TIMESTAMP},
            JINGHUIFSJJHS =  #{JINGHUIFSJJHS,jdbcType=VARCHAR},
            JINGHUIFSJJHSXM =  #{JINGHUIFSJJHSXM,jdbcType=VARCHAR},
            JINGSHOUSJSJ =  #{JINGSHOUSJSJ,jdbcType=TIMESTAMP},
            JINGSHOUSJJJHS =  #{JINGSHOUSJJJHS,jdbcType=VARCHAR},
            JINGSHOUSJJJHSXM =  #{JINGSHOUSJJJHSXM,jdbcType=VARCHAR},
            SHOUSHULZZT =  #{SHOUSHULZZT,jdbcType=VARCHAR},
            ZHONGZHISJ =  #{ZHONGZHISJ,jdbcType=TIMESTAMP},
            ZHONGZHIJJHS =  #{ZHONGZHIJJHS,jdbcType=VARCHAR},
            ZHONGZHIJJHSXM =  #{ZHONGZHIJJHSXM,jdbcType=VARCHAR},
            CHUSHOUSSSJ =  #{CHUSHOUSSSJ,jdbcType=TIMESTAMP},
            CHUSHOUSSJJHS =  #{CHUSHOUSSJJHS,jdbcType=VARCHAR},
            CHUSHOUSSJJHSXM =  #{CHUSHOUSSJJHSXM,jdbcType=VARCHAR},
            CHUHUIFSSJ =  #{CHUHUIFSSJ,jdbcType=TIMESTAMP},
            CHUHUIFSJJHS =  #{CHUHUIFSJJHS,jdbcType=VARCHAR},
            CHUHUIFSJJHSXM =  #{CHUHUIFSJJHSXM,jdbcType=VARCHAR},
            FENGUANYZSHBZ =  #{FENGUANYZSHBZ,jdbcType=DECIMAL},
            FENGUANYZ =  #{FENGUANYZ,jdbcType=VARCHAR},
            FENGUANYZSHSJ =  #{FENGUANYZSHSJ,jdbcType=TIMESTAMP},
            FENGUANYZSHBTGYY =  #{FENGUANYZSHBTGYY,jdbcType=VARCHAR},
            KEZHURSHBZ =  #{KEZHURSHBZ,jdbcType=DECIMAL},
            KEZHUREN =  #{KEZHUREN,jdbcType=VARCHAR},
            KEZHURSHSJ =  #{KEZHURSHSJ,jdbcType=TIMESTAMP},
            KEZHURSHBTGYY =  #{KEZHURSHBTGYY,jdbcType=VARCHAR},
            YIWUKSHBZ =  #{YIWUKSHBZ,jdbcType=DECIMAL},
            YIWUKE =  #{YIWUKE,jdbcType=VARCHAR},
            YIWUKSHSJ =  #{YIWUKSHSJ,jdbcType=TIMESTAMP},
            YIWUKSHBTGYY =  #{YIWUKSHBTGYY,jdbcType=VARCHAR},
            SHANGBAOSY =  #{SHANGBAOSY,jdbcType=VARCHAR},
            SHANGBAOYS =  #{SHANGBAOYS,jdbcType=VARCHAR},
            XUJIEJWT =  #{XUJIEJWT,jdbcType=VARCHAR},
            SHUQIANTLJG =  #{SHUQIANTLJG,jdbcType=VARCHAR},
            YICAIQCS =  #{YICAIQCS,jdbcType=VARCHAR},
            ZHUZHIYSSHBZ =  #{ZHUZHIYSSHBZ,jdbcType=DECIMAL},
            ZHUZHIYSSHR =  #{ZHUZHIYSSHR,jdbcType=VARCHAR},
            ZHUZHIYSSHSJ =  #{ZHUZHIYSSHSJ,jdbcType=TIMESTAMP},
            ZHUZHIYSSHBTGYY =  #{ZHUZHIYSSHBTGYY,jdbcType=DECIMAL},
            SHUQIANGRQK =  #{SHUQIANGRQK,jdbcType=VARCHAR},
            BINGFAZHENG =  #{BINGFAZHENG,jdbcType=VARCHAR},
            DUOCHONGNYBZ =  #{DUOCHONGNYBZ,jdbcType=DECIMAL},
            ZHUDAOYSSHBZ =  #{ZHUDAOYSSHBZ,jdbcType=DECIMAL},
            ZHUDAOYSSHR =  #{ZHUDAOYSSHR,jdbcType=VARCHAR},
            ZHUDAOYSSHSJ =  #{ZHUDAOYSSHSJ,jdbcType=TIMESTAMP},
            MAZUIYSSHBZ =  #{MAZUIYSSHBZ,jdbcType=DECIMAL},
            MAZUIYSSHR =  #{MAZUIYSSHR,jdbcType=VARCHAR},
            MAZUIYSSHSJ =  #{MAZUIYSSHSJ,jdbcType=TIMESTAMP},
            MAZUIYF =  #{MAZUIYF,jdbcType=VARCHAR},
            SHENQINGYS =  #{SHENQINGYS,jdbcType=VARCHAR},
            YISHENGSQRQ =  #{YISHENGSQRQ,jdbcType=TIMESTAMP},
            ZHENDUAN =  #{ZHENDUAN,jdbcType=VARCHAR},
            SHANGJIYSSHBZ =  #{SHANGJIYSSHBZ,jdbcType=DECIMAL},
            SHANGJIYSSHSJ =  #{SHANGJIYSSHSJ,jdbcType=TIMESTAMP},
            SHANGJIYSSHR =  #{SHANGJIYSSHR,jdbcType=VARCHAR},
            SHANGJIYS =  #{SHANGJIYS,jdbcType=VARCHAR},
            SHANGJIYSSHBTGYY =  #{SHANGJIYSSHBTGYY,jdbcType=VARCHAR},
            SHUQIANSSJB =  #{SHUQIANSSJB,jdbcType=VARCHAR},
            MAZUIHZDID =  #{MAZUIHZDID,jdbcType=VARCHAR},
            SHOUSHUDM =  #{SHOUSHUDM,jdbcType=VARCHAR},
            JINICUHS =  #{JINICUHS,jdbcType=VARCHAR},
            JINICUSJ =  #{JINICUSJ,jdbcType=TIMESTAMP},
            JINICUHSXM =  #{JINICUHSXM,jdbcType=VARCHAR},
            CHUICUHS =  #{CHUICUHS,jdbcType=VARCHAR},
            CHUICUSJ =  #{CHUICUSJ,jdbcType=TIMESTAMP},
            CHUICUHSXM =  #{CHUICUHSXM,jdbcType=VARCHAR},
            JUJUELX =  #{JUJUELX,jdbcType=VARCHAR},
            JUJUEYY =  #{JUJUEYY,jdbcType=VARCHAR},
            TAICI =  #{TAICI,jdbcType=VARCHAR},
            BEIZHU1 =  #{BEIZHU1,jdbcType=VARCHAR},
            BEIZHU2 =  #{BEIZHU2,jdbcType=VARCHAR},
            ZHIQINGTYSZT =  #{ZHIQINGTYSZT,jdbcType=DECIMAL},
            WEIJIZHIBZ =  #{WEIJIZHIBZ,jdbcType=DECIMAL},
            SHUHOUZHENTONGBZ =  #{SHUHOUZHENTONGBZ,jdbcType=DECIMAL},
            ISKANGJUNYW =  #{ISKANGJUNYW,jdbcType=DECIMAL},
            SHOUSHUSLX =  #{SHOUSHUSLX,jdbcType=VARCHAR},
            RIJIANSSBZ =  #{RIJIANSSBZ,jdbcType=DECIMAL},
            ZHENDUANDM =  #{ZHENDUANDM,jdbcType=VARCHAR},
            SHOUSHUBW2 =  #{SHOUSHUBW2,jdbcType=VARCHAR},
            SHOUSHUBW3 =  #{SHOUSHUBW3,jdbcType=VARCHAR},
            SHOUSHUBW4 =  #{SHOUSHUBW4,jdbcType=VARCHAR},
            SHOUSHUBW5 =  #{SHOUSHUBW5,jdbcType=VARCHAR},
            SHOUSHUBW1 =  #{SHOUSHUBW1,jdbcType=VARCHAR},
            MAZUIJSBZ =  #{MAZUIJSBZ,jdbcType=DECIMAL},
            MAZUIJSREN =  #{MAZUIJSREN,jdbcType=VARCHAR},
            MAZUIJSSJ =  #{MAZUIJSSJ,jdbcType=TIMESTAMP},
            SHOUSHUJZJSREN =  #{SHOUSHUJZJSREN,jdbcType=VARCHAR},
            MAZUIJZJSREN =  #{MAZUIJZJSREN,jdbcType=VARCHAR},
            SHOUSHUJZSJ =  #{SHOUSHUJZSJ,jdbcType=TIMESTAMP},
            QUXIAOYY =  #{QUXIAOYY,jdbcType=VARCHAR},
            YUANYINLX =  #{YUANYINLX,jdbcType=VARCHAR},
            QUXIAOR =  #{QUXIAOR,jdbcType=VARCHAR},
            QUXIAOSJ =  #{QUXIAOSJ,jdbcType=TIMESTAMP},
            SHOUSHUKSREN =  #{SHOUSHUKSREN,jdbcType=VARCHAR},
            SHOUSHUJSREN =  #{SHOUSHUJSREN,jdbcType=VARCHAR},
            SHOUSHUJZBZ =  #{SHOUSHUJZBZ,jdbcType=DECIMAL},
            MAZUIJZBZ =  #{MAZUIJZBZ,jdbcType=DECIMAL},
            MAZUIJZSJ =  #{MAZUIJZSJ,jdbcType=TIMESTAMP},
            WAIYUANBZ =  #{WAIYUANBZ,jdbcType=DECIMAL},
            ZHUANYUNFS =  #{ZHUANYUNFS,jdbcType=VARCHAR},
            SHOUSHUXZ =  #{SHOUSHUXZ,jdbcType=VARCHAR},
            SHOUSHUXZQT =  #{SHOUSHUXZQT,jdbcType=VARCHAR},
            FEIJIHUAZCSS =  #{FEIJIHUAZCSS,jdbcType=VARCHAR},
            TIWEI =  #{TIWEI,jdbcType=VARCHAR},
            ZHIDAOYS =  #{ZHIDAOYS,jdbcType=VARCHAR},
            XUYAOSZBD =  #{XUYAOSZBD,jdbcType=DECIMAL},
            XUYAOBBCJ =  #{XUYAOBBCJ,jdbcType=DECIMAL},
            SHENQINGSHZHBF =  #{SHENQINGSHZHBF,jdbcType=DECIMAL},
            SHENQINGZICU =  #{SHENQINGZICU,jdbcType=DECIMAL},
            SHUHOUZT =  #{SHUHOUZT,jdbcType=DECIMAL},
            GENTAI =  #{GENTAI,jdbcType=DECIMAL},
            SHOUSHUBZ =  #{SHOUSHUBZ,jdbcType=VARCHAR},
            SONGFUSS =  #{SONGFUSS,jdbcType=DECIMAL},
            TESHUJC =  #{TESHUJC,jdbcType=VARCHAR},
            MAZUISSS =  #{MAZUISSS,jdbcType=VARCHAR},
            MAZUIPG =  #{MAZUIPG,jdbcType=VARCHAR},
            MAZUIPGNR =  #{MAZUIPGNR,jdbcType=VARCHAR},
            WEICHUANGSSBZ =  #{WEICHUANGSSBZ,jdbcType=DECIMAL},
            MAZUIPGBLJLXH =  #{MAZUIPGBLJLXH,jdbcType=DECIMAL},
            SHOUSHUBW6 =  #{SHOUSHUBW6,jdbcType=VARCHAR},
            SHOUSHUMC6 =  #{SHOUSHUMC6,jdbcType=VARCHAR},
            SHOUSHUID6 =  #{SHOUSHUID6,jdbcType=VARCHAR},
            SHOUSHUBW7 =  #{SHOUSHUBW7,jdbcType=VARCHAR},
            SHOUSHUMC7 =  #{SHOUSHUMC7,jdbcType=VARCHAR},
            SHOUSHUID7 =  #{SHOUSHUID7,jdbcType=VARCHAR},
            SHOUSHUBW8 =  #{SHOUSHUBW8,jdbcType=VARCHAR},
            SHOUSHUMC8 =  #{SHOUSHUMC8,jdbcType=VARCHAR},
            SHOUSHUID8 =  #{SHOUSHUID8,jdbcType=VARCHAR},
            SHOUSHUBW9 =  #{SHOUSHUBW9,jdbcType=VARCHAR},
            SHOUSHUMC9 =  #{SHOUSHUMC9,jdbcType=VARCHAR},
            SHOUSHUID9 =  #{SHOUSHUID9,jdbcType=VARCHAR},
            SHOUSHUBW10 =  #{SHOUSHUBW10,jdbcType=VARCHAR},
            SHOUSHUMC10 =  #{SHOUSHUMC10,jdbcType=VARCHAR},
            SHOUSHUID10 =  #{SHOUSHUID10,jdbcType=VARCHAR},
            SHOUSHUBW11 =  #{SHOUSHUBW11,jdbcType=VARCHAR},
            SHOUSHUMC11 =  #{SHOUSHUMC11,jdbcType=VARCHAR},
            SHOUSHUID11 =  #{SHOUSHUID11,jdbcType=VARCHAR},
            SHOUSHUBW12 =  #{SHOUSHUBW12,jdbcType=VARCHAR},
            SHOUSHUMC12 =  #{SHOUSHUMC12,jdbcType=VARCHAR},
            SHOUSHUID12 =  #{SHOUSHUID12,jdbcType=VARCHAR},
            SHOUSHUBW13 =  #{SHOUSHUBW13,jdbcType=VARCHAR},
            SHOUSHUMC13 =  #{SHOUSHUMC13,jdbcType=VARCHAR},
            SHOUSHUID13 =  #{SHOUSHUID13,jdbcType=VARCHAR},
            SHOUSHUBW14 =  #{SHOUSHUBW14,jdbcType=VARCHAR},
            SHOUSHUMC14 =  #{SHOUSHUMC14,jdbcType=VARCHAR},
            SHOUSHUID14 =  #{SHOUSHUID14,jdbcType=VARCHAR},
            SHOUSHUBW15 =  #{SHOUSHUBW15,jdbcType=VARCHAR},
            SHOUSHUMC15 =  #{SHOUSHUMC15,jdbcType=VARCHAR},
            SHOUSHUID15 =  #{SHOUSHUID15,jdbcType=VARCHAR},
            SHOUSHUBW16 =  #{SHOUSHUBW16,jdbcType=VARCHAR},
            SHOUSHUMC16 =  #{SHOUSHUMC16,jdbcType=VARCHAR},
            SHOUSHUID16 =  #{SHOUSHUID16,jdbcType=VARCHAR},
            SHOUSHUBW17 =  #{SHOUSHUBW17,jdbcType=VARCHAR},
            SHOUSHUMC17 =  #{SHOUSHUMC17,jdbcType=VARCHAR},
            SHOUSHUID17 =  #{SHOUSHUID17,jdbcType=VARCHAR},
            SHOUSHUBW18 =  #{SHOUSHUBW18,jdbcType=VARCHAR},
            SHOUSHUMC18 =  #{SHOUSHUMC18,jdbcType=VARCHAR},
            SHOUSHUID18 =  #{SHOUSHUID18,jdbcType=VARCHAR},
            SHOUSHUBW19 =  #{SHOUSHUBW19,jdbcType=VARCHAR},
            SHOUSHUMC19 =  #{SHOUSHUMC19,jdbcType=VARCHAR},
            SHOUSHUID19 =  #{SHOUSHUID19,jdbcType=VARCHAR},
            SHOUSHUBW20 =  #{SHOUSHUBW20,jdbcType=VARCHAR},
            SHOUSHUMC20 =  #{SHOUSHUMC20,jdbcType=VARCHAR},
            SHOUSHUID20 =  #{SHOUSHUID20,jdbcType=VARCHAR},
            SHOUSHUJB6 =  #{SHOUSHUJB6,jdbcType=VARCHAR},
            SHOUSHUCKLX6 =  #{SHOUSHUCKLX6,jdbcType=VARCHAR},
            SHOUSHUJB7 =  #{SHOUSHUJB7,jdbcType=VARCHAR},
            SHOUSHUCKLX7 =  #{SHOUSHUCKLX7,jdbcType=VARCHAR},
            SHOUSHUJB8 =  #{SHOUSHUJB8,jdbcType=VARCHAR},
            SHOUSHUCKLX8 =  #{SHOUSHUCKLX8,jdbcType=VARCHAR},
            SHOUSHUJB9 =  #{SHOUSHUJB9,jdbcType=VARCHAR},
            SHOUSHUCKLX9 =  #{SHOUSHUCKLX9,jdbcType=VARCHAR},
            SHOUSHUJB10 =  #{SHOUSHUJB10,jdbcType=VARCHAR},
            SHOUSHUCKLX10 =  #{SHOUSHUCKLX10,jdbcType=VARCHAR},
            SHOUSHUJB11 =  #{SHOUSHUJB11,jdbcType=VARCHAR},
            SHOUSHUCKLX11 =  #{SHOUSHUCKLX11,jdbcType=VARCHAR},
            SHOUSHUJB12 =  #{SHOUSHUJB12,jdbcType=VARCHAR},
            SHOUSHUCKLX12 =  #{SHOUSHUCKLX12,jdbcType=VARCHAR},
            SHOUSHUJB13 =  #{SHOUSHUJB13,jdbcType=VARCHAR},
            SHOUSHUCKLX13 =  #{SHOUSHUCKLX13,jdbcType=VARCHAR},
            SHOUSHUJB14 =  #{SHOUSHUJB14,jdbcType=VARCHAR},
            SHOUSHUCKLX14 =  #{SHOUSHUCKLX14,jdbcType=VARCHAR},
            SHOUSHUJB15 =  #{SHOUSHUJB15,jdbcType=VARCHAR},
            SHOUSHUCKLX15 =  #{SHOUSHUCKLX15,jdbcType=VARCHAR},
            SHOUSHUJB16 =  #{SHOUSHUJB16,jdbcType=VARCHAR},
            SHOUSHUCKLX16 =  #{SHOUSHUCKLX16,jdbcType=VARCHAR},
            SHOUSHUJB17 =  #{SHOUSHUJB17,jdbcType=VARCHAR},
            SHOUSHUCKLX17 =  #{SHOUSHUCKLX17,jdbcType=VARCHAR},
            SHOUSHUJB18 =  #{SHOUSHUJB18,jdbcType=VARCHAR},
            SHOUSHUCKLX18 =  #{SHOUSHUCKLX18,jdbcType=VARCHAR},
            SHOUSHUJB19 =  #{SHOUSHUJB19,jdbcType=VARCHAR},
            SHOUSHUCKLX19 =  #{SHOUSHUCKLX19,jdbcType=VARCHAR},
            SHOUSHUJB20 =  #{SHOUSHUJB20,jdbcType=VARCHAR},
            SHOUSHUCKLX20 =  #{SHOUSHUCKLX20,jdbcType=VARCHAR},
            SHOUHU =  #{SHOUHU,jdbcType=VARCHAR},
            SHOUHU1 =  #{SHOUHU1,jdbcType=VARCHAR},
            SHOUHU2 =  #{SHOUHU2,jdbcType=VARCHAR},
            SHOUHU3 =  #{SHOUHU3,jdbcType=VARCHAR},
            SHOUHU4 =  #{SHOUHU4,jdbcType=VARCHAR},
            SHOUHU5 =  #{SHOUHU5,jdbcType=VARCHAR},
            SHOUHU6 =  #{SHOUHU6,jdbcType=VARCHAR}
        where   SHOUSHUDID = #{SHOUSHUDID,jdbcType=VARCHAR} 
    </update>
</mapper>
